<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>3.1.1</version>
    </parent>

    <groupId>com.genlight</groupId>
    <artifactId>epilcure</artifactId>
    <version>${revision}</version>
    <packaging>pom</packaging>

    <modules>
        <module>epilcure-api</module>
        <module>epilcure-common</module>
        <module>epilcure-gateway</module>
        <module>epilcure-service</module>
        <module>epilcure-logs</module>
    </modules>

    <properties>
        <java.version>18</java.version>
        <revision>1.0.1-SNAPSHOT</revision>
        <querydsl.version>5.0.0</querydsl.version>
        <mapstruct.version>1.5.5.Final</mapstruct.version>
        <aspectjweaver.version>1.9.19</aspectjweaver.version>
        <nimbus-jose-jwt.version>9.24.4</nimbus-jose-jwt.version>
        <springdoc-openapi.version>2.1.0</springdoc-openapi.version>
        <spring-boot.version>3.1.1</spring-boot.version>
        <spring-cloud-alibaba.version>2022.0.0.0-RC2</spring-cloud-alibaba.version>
        <spring-cloud-dependencies.version>2022.0.3</spring-cloud-dependencies.version>
        <rocketmq-spring-boot.version>2.2.3</rocketmq-spring-boot.version>
        <jib-maven-plugin.version>3.3.2</jib-maven-plugin.version>
        <flatten-maven-plugin.version>1.5.0</flatten-maven-plugin.version>
        <minio.version>8.5.4</minio.version>
        <seata.version>1.6.1</seata.version>
        <kryo-shaded.version>4.0.3</kryo-shaded.version>
        <kryo-serializers.version>0.45</kryo-serializers.version>
        <aliyun-java-sdk-core.version>4.6.2</aliyun-java-sdk-core.version>
        <biz.source_code.dsp.version>1.0.0</biz.source_code.dsp.version>
        <logstash-logback-encoder.version>7.4</logstash-logback-encoder.version>
        <commons-net.version>3.8.0</commons-net.version>
        <commons-io.version>2.11.0</commons-io.version>
        <shardingsphere.version>5.3.2</shardingsphere.version>
        <snakeyaml.version>1.33</snakeyaml.version>
        <easyexcel.version>3.3.2</easyexcel.version>
        <redisson.version>3.22.1</redisson.version>
        <jtransforms.version>2.4.0</jtransforms.version>
        <apk-parser.version>2.6.10</apk-parser.version>
        <nacos-logback-adapter.version>1.0.0</nacos-logback-adapter.version>
        <pinyin4j.version>2.5.1</pinyin4j.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.querydsl</groupId>
                <artifactId>querydsl-kotlin</artifactId>
                <version>${querydsl.version}</version>
            </dependency>
            <dependency>
                <groupId>com.nimbusds</groupId>
                <artifactId>nimbus-jose-jwt</artifactId>
                <version>${nimbus-jose-jwt.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct</artifactId>
                <version>${mapstruct.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springdoc</groupId>
                <artifactId>springdoc-openapi-starter-webflux-ui</artifactId>
                <version>${springdoc-openapi.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springdoc</groupId>
                <artifactId>springdoc-openapi-starter-webmvc-ui</artifactId>
                <version>${springdoc-openapi.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-alibaba-dependencies</artifactId>
                <version>${spring-cloud-alibaba.version}</version>
                <scope>import</scope>
                <type>pom</type>
            </dependency>
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring-cloud-dependencies.version}</version>
                <scope>import</scope>
                <type>pom</type>
            </dependency>
            <dependency>
                <groupId>org.apache.rocketmq</groupId>
                <artifactId>rocketmq-spring-boot-starter</artifactId>
                <version>${rocketmq-spring-boot.version}</version>
            </dependency>
            <dependency>
                <groupId>com.aliyun</groupId>
                <artifactId>aliyun-java-sdk-core</artifactId>
                <version>${aliyun-java-sdk-core.version}</version>
            </dependency>
            <dependency>
                <groupId>io.minio</groupId>
                <artifactId>minio</artifactId>
                <version>${minio.version}</version>
            </dependency>
            <dependency>
                <groupId>org.aspectj</groupId>
                <artifactId>aspectjweaver</artifactId>
                <version>${aspectjweaver.version}</version>
            </dependency>
            <dependency>
                <groupId>io.seata</groupId>
                <artifactId>seata-spring-boot-starter</artifactId>
                <version>${seata.version}</version>
            </dependency>
            <dependency>
                <groupId>com.esotericsoftware</groupId>
                <artifactId>kryo-shaded</artifactId>
                <version>${kryo-shaded.version}</version>
            </dependency>
            <dependency>
                <groupId>de.javakaffee</groupId>
                <artifactId>kryo-serializers</artifactId>
                <version>${kryo-serializers.version}</version>
            </dependency>
            <dependency>
                <groupId>net.logstash.logback</groupId>
                <artifactId>logstash-logback-encoder</artifactId>
                <version>${logstash-logback-encoder.version}</version>
            </dependency>
            <dependency>
                <groupId>biz.source_code.dsp</groupId>
                <artifactId>dsp-collection</artifactId>
                <version>${biz.source_code.dsp.version}</version>
            </dependency>
            <dependency>
                <groupId>commons-net</groupId>
                <artifactId>commons-net</artifactId>
                <version>${commons-net.version}</version>
            </dependency>
            <dependency>
                <groupId>commons-io</groupId>
                <artifactId>commons-io</artifactId>
                <version>${commons-io.version}</version>
            </dependency>
            <dependency>
                <groupId>org.yaml</groupId>
                <artifactId>snakeyaml</artifactId>
                <version>${snakeyaml.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.shardingsphere</groupId>
                <artifactId>shardingsphere-jdbc-core-spring-boot-starter</artifactId>
                <version>${shardingsphere.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>easyexcel</artifactId>
                <version>${easyexcel.version}</version>
            </dependency>
            <dependency>
                <groupId>org.redisson</groupId>
                <artifactId>redisson-spring-boot-starter</artifactId>
                <version>${redisson.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.rwl</groupId>
                <artifactId>jtransforms</artifactId>
                <version>${jtransforms.version}</version>
            </dependency>
            <dependency>
                <groupId>net.dongliu</groupId>
                <artifactId>apk-parser</artifactId>
                <version>${apk-parser.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.nacos</groupId>
                <artifactId>logback-adapter</artifactId>
                <version>${nacos-logback-adapter.version}</version>
            </dependency>
            <dependency>
                <groupId>com.belerweb</groupId>
                <artifactId>pinyin4j</artifactId>
                <version>${pinyin4j.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>
        <dependency>
            <groupId>org.yaml</groupId>
            <artifactId>snakeyaml</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <build>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
            </resource>
        </resources>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <fork>true</fork>
                    <annotationProcessorPaths>
                        <annotationProcessorPath>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                            <version>${lombok.version}</version>
                        </annotationProcessorPath>
                        <annotationProcessorPath>
                            <groupId>org.mapstruct</groupId>
                            <artifactId>mapstruct-processor</artifactId>
                            <version>${mapstruct.version}</version>
                        </annotationProcessorPath>
                        <annotationProcessorPath>
                            <groupId>org.hibernate</groupId>
                            <artifactId>hibernate-jpamodelgen</artifactId>
                            <version>${hibernate.version}</version>
                        </annotationProcessorPath>
                        <annotationProcessorPath>
                            <groupId>org.springframework.boot</groupId>
                            <artifactId>spring-boot-configuration-processor</artifactId>
                            <version>${spring-boot.version}</version>
                        </annotationProcessorPath>
                    </annotationProcessorPaths>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>flatten-maven-plugin</artifactId>
                <version>${flatten-maven-plugin.version}</version>
                <executions>
                    <execution>
                        <id>flatten</id>
                        <phase>process-resources</phase>
                        <goals>
                            <goal>flatten</goal>
                        </goals>
                        <configuration>
                            <updatePomFile>true</updatePomFile>
                            <flattenMode>resolveCiFriendliesOnly</flattenMode>
                        </configuration>
                    </execution>
                    <execution>
                        <id>flatten.clean</id>
                        <phase>clean</phase>
                        <goals>
                            <goal>clean</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>com.google.cloud.tools</groupId>
                    <artifactId>jib-maven-plugin</artifactId>
                    <version>${jib-maven-plugin.version}</version>
                    <executions>
                        <execution>
                            <id>build-image</id>
                            <phase>package</phase>
                            <goals>
                                <goal>build</goal>
                            </goals>
                        </execution>
                    </executions>
                    <configuration>
                        <from>
                            <image>
                                harbor.cloud.com/docker.io/library/openjdk:18.0.1-jdk@sha256:c3f692d7245baad99d96b213e6c5f24529d16132ba74fa19d62db2aaf9f85586
                            </image>
                        </from>
                        <to>
                            <image>${docker.registry}/${docker.registry.project}/${project.artifactId}</image>
                            <auth>
                                <username>${registry.user}</username>
                                <password>${registry.password}</password>
                            </auth>
                            <tags>
                                ${project.version}
                            </tags>
                        </to>
                        <container>
                            <jvmFlags>
                                <jvmFlag>-Xms256m</jvmFlag>
                                <jvmFlag>-Xmx512m</jvmFlag>
                                <jvmFlag>--add-opens=java.base/java.lang=ALL-UNNAMED</jvmFlag>
                                <jvmFlag>--add-opens=java.base/java.util=ALL-UNNAMED</jvmFlag>
                                <jvmFlag>--add-opens=java.base/java.text=ALL-UNNAMED</jvmFlag>
                                <jvmFlag>--add-opens=java.base/java.time=ALL-UNNAMED</jvmFlag>
                                <jvmFlag>--add-opens=java.base/java.lang.reflect=ALL-UNNAMED</jvmFlag>
                            </jvmFlags>
                            <volumes>
                                <volume>/logs</volume>
                            </volumes>
                            <environment>
                                <TZ>Asia/Shanghai</TZ>
                            </environment>
                            <creationTime>USE_CURRENT_TIMESTAMP</creationTime>
                        </container>
                        <allowInsecureRegistries>true</allowInsecureRegistries>
                    </configuration>
                </plugin>
            </plugins>
        </pluginManagement>
    </build>

    <profiles>
        <profile>
            <id>dev</id>
            <properties>
                <mysqlhost>**************:31924</mysqlhost>
                <profiles.active>dev</profiles.active>
                <nacos.config.namespace/>
                <nacos.discovery.namespace>8af988c5-4539-4c32-a18a-59ee2becd496</nacos.discovery.namespace>
                <dockerImg.registry>harbor.cloud.com</dockerImg.registry>
                <docker.registry>harbor.cloud.com</docker.registry>
                <docker.registry.project>epilcure</docker.registry.project>
                <registry.user>admin</registry.user>
                <registry.password>Passw0rd</registry.password>
            </properties>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
        </profile>
        <profile>
            <id>test</id>
            <properties>
                <profiles.active>test</profiles.active>
                <nacos.config.namespace/>
                <nacos.discovery.namespace>edffa73c-c4fa-4aa0-81d6-274bacfc6264</nacos.discovery.namespace>
                <docker.registry>harbor.cloud.com</docker.registry>
                <docker.registry.project>epilcure</docker.registry.project>
                <registry.user>admin</registry.user>
                <registry.password>Passw0rd</registry.password>
            </properties>
        </profile>
        <profile>
            <id>animal</id>
            <properties>
                <profiles.active>animal</profiles.active>
                <nacos.config.namespace/>
                <nacos.discovery.namespace>084fe688-1793-4bdd-888a-8394cbe00190</nacos.discovery.namespace>
                <docker.registry>harbor.cloud.com</docker.registry>
                <docker.registry.project>epilcure_animal</docker.registry.project>
                <registry.user>admin</registry.user>
                <registry.password>Passw0rd</registry.password>
            </properties>
        </profile>
        <profile>
            <id>animal_test</id>
            <properties>
                <profiles.active>animal_test</profiles.active>
                <nacos.config.namespace/>
                <nacos.discovery.namespace>04b9f140-2246-4e9a-bd75-179ac39dc7fb</nacos.discovery.namespace>
                <docker.registry>harbor.cloud.com</docker.registry>
                <docker.registry.project>epilcure_animal_test</docker.registry.project>
                <registry.user>admin</registry.user>
                <registry.password>Passw0rd</registry.password>
            </properties>
        </profile>
        <profile>
            <id>moldCheck</id>
            <properties>
                <profiles.active>moldCheck</profiles.active>
                <nacos.config.namespace>c823494f-cbbf-457c-b1e5-7322e08e4a99</nacos.config.namespace>
                <nacos.discovery.namespace>c823494f-cbbf-457c-b1e5-7322e08e4a99</nacos.discovery.namespace>
                <docker.registry>harbor.cloud.com</docker.registry>
                <docker.registry.project>epilcure_mold_check</docker.registry.project>
                <registry.user>admin</registry.user>
                <registry.password>Passw0rd</registry.password>
            </properties>
        </profile>
        <profile>
            <id>main_test</id>
            <properties>
                <profiles.active>prod</profiles.active>
                <nacos.namespace/>
                <dockerImg.registry>harbor.cloud.com</dockerImg.registry>
                <docker.registry>harbor.cloud.com</docker.registry>
                <docker.registry.project>epilcure_main</docker.registry.project>
                <registry.user>admin</registry.user>
                <registry.password>Passw0rd</registry.password>
            </properties>
        </profile>
        <profile>
            <id>factory</id>
            <properties>
                <profiles.active>factory</profiles.active>
                <nacos.config.namespace/>
                <nacos.discovery.namespace>c4fd87c1-efaa-47ca-80d5-0871e41af63e</nacos.discovery.namespace>
                <docker.registry>harbor.cloud.com</docker.registry>
                <docker.registry.project>epilcure_factory</docker.registry.project>
                <registry.user>admin</registry.user>
                <registry.password>Passw0rd</registry.password>
            </properties>
        </profile>
        <profile>
            <id>prod</id>
            <properties>
                <profiles.active>prod</profiles.active>
                <nacos.namespace/>
                <dockerImg.registry>harbor.cloud.com</dockerImg.registry>
                <docker.registry>registry.cn-hangzhou.aliyuncs.com</docker.registry>
                <docker.registry.project>epilcure</docker.registry.project>
                <registry.user>hznwyl</registry.user>
                <registry.password>Neuhill@123</registry.password>
                <NACOS_HOST>nacos</NACOS_HOST>
                <NACOS_PORT>8848</NACOS_PORT>
            </properties>
        </profile>
        <profile>
            <id>prod-isbs</id>
            <properties>
                <profiles.active>prod</profiles.active>
                <nacos.namespace/>
                <dockerImg.registry>harbor.cloud.com</dockerImg.registry>
                <docker.registry>registry.cn-hangzhou.aliyuncs.com</docker.registry>
                <docker.registry.project>epilcure-check</docker.registry.project>
                <registry.user>hznwyl</registry.user>
                <registry.password>Neuhill@123</registry.password>
                <NACOS_HOST>nacos</NACOS_HOST>
                <NACOS_PORT>8848</NACOS_PORT>
            </properties>
        </profile>
        <profile>
            <id>prod-check</id>
            <properties>
                <profiles.active>prod</profiles.active>
                <nacos.namespace/>
                <docker.registry>registry.cn-hangzhou.aliyuncs.com</docker.registry>
                <docker.registry.project>epilcure-check</docker.registry.project>
                <registry.user>hznwyl</registry.user>
                <registry.password>Neuhill@123</registry.password>
                <NACOS_HOST>nacos-service</NACOS_HOST>
                <NACOS_PORT>8848</NACOS_PORT>
            </properties>
        </profile>
    </profiles>


    <distributionManagement>
        <repository>
            <id>nexus.cloud.com</id>
            <url>http://nexus.cloud.com/repository/maven-releases/</url>
        </repository>
        <snapshotRepository>
            <id>nexus.cloud.com</id>
            <url>http://nexus.cloud.com/repository/maven-snapshots/</url>
        </snapshotRepository>
    </distributionManagement>

    <repositories>
        <repository>
            <id>public.nexus.cloud.com</id>
            <name>Nexus Managed Repository</name>
            <url>http://nexus.cloud.com/repository/maven-public</url>
            <releases>
                <enabled>true</enabled>
                <checksumPolicy>fail</checksumPolicy>
            </releases>
            <snapshots>
                <enabled>true</enabled>
                <updatePolicy>always</updatePolicy>
                <checksumPolicy>fail</checksumPolicy>
            </snapshots>
        </repository>
    </repositories>

    <pluginRepositories>
        <pluginRepository>
            <id>public.nexus.cloud.com</id>
            <name>Nexus Managed Repository</name>
            <url>http://nexus.cloud.com/repository/maven-public/</url>
            <releases>
                <enabled>true</enabled>
                <checksumPolicy>fail</checksumPolicy>
            </releases>
            <snapshots>
                <enabled>true</enabled>
                <updatePolicy>always</updatePolicy>
                <checksumPolicy>fail</checksumPolicy>
            </snapshots>
        </pluginRepository>
    </pluginRepositories>
</project>
