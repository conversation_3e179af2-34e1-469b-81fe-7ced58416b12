package com.genlight.epilcure.logs.utils;

import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import io.netty.util.ByteProcessor;

public class LineBuffer {

    private final ByteBuf mBuffer;

    private int mStartIndex = 0;

    public LineBuffer(ByteBuf buf) {
        mBuffer = buf.asReadOnly();
    }

    /**
     * 是否可以读取
     *
     * @return Boolean
     */
    public boolean isReadable() {
        return mBuffer.isReadable();
    }

    /**
     * 读取数据，遇到换行符就截取转换为字符串
     *
     * @return String 截取的字符串
     */
    public String readLine() {
        // 查找换行符
        if (isReadable()) {
            if (mStartIndex == 0) {
                mStartIndex = mBuffer.forEachByte(ByteProcessor.FIND_CRLF);
                ByteBuf buffer = Unpooled.buffer(mStartIndex);
                mBuffer.readBytes(buffer, 0, mStartIndex);
                mStartIndex += 1;
                return verifyString(buffer);
            } else {
                int firstIndex = mBuffer.forEachByte(mStartIndex, mBuffer.readableBytes() - 1, ByteProcessor.FIND_CRLF);
                if (firstIndex == -1) {
                    int remainLength = mBuffer.readableBytes() - 1;
                    // 读取到末尾
                    mBuffer.readerIndex(mStartIndex);
                    if (remainLength <= 0) {
                        return null;
                    }
                    ByteBuf buffer = Unpooled.buffer(remainLength);
                    mBuffer.readBytes(buffer, 0, remainLength);
                    mStartIndex += remainLength;
                    return verifyString(buffer);
                } else {
                    int length = firstIndex - mStartIndex;
                    ByteBuf buffer = Unpooled.buffer(length);
                    // 跳过换行符
                    mBuffer.readerIndex(mStartIndex);
                    mBuffer.readBytes(buffer, 0, length);
                    mStartIndex = firstIndex + 1;
                    return verifyString(buffer);
                }
            }
        } else {
            return null;
        }
    }

    // 校验下数据，如果全部是无效字节，则返回 NULL。包含无效字节，则直接转换为字符串。
    private String verifyString(ByteBuf buffer) {
        // 全部都是 NUL 时
        if (!Utils.isContainsVaildData(buffer)) {
            return null;
        }
        return new String(buffer.array());
    }

}
