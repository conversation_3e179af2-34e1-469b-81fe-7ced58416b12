package com.genlight.epilcure.logs.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;

@Data
@RefreshScope
@ConfigurationProperties("logs")
public class LogsProperties {

    private LogsParse logsParse = new LogsParse();

    @Data
    public static class LogsParse {
        private String topic;
    }
}
