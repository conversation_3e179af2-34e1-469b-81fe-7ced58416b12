package com.genlight.epilcure.logs.controller;

import com.genlight.epilcure.common.core.mvc.response.JsonResult;
import com.genlight.epilcure.logs.LogsConstants;
import com.genlight.epilcure.logs.service.LogsService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

/**
 * <AUTHOR>
 * @version 1.0.0
 */
@RestController
@RequestMapping("/api/logs")
@Tag(name = "LogsController", description = "客户端日志相关接口")
public class LogsController {

    @Resource
    private LogsService logsService;

    @Operation(summary = "上传客户端日志文件（zip文件）")
    @PostMapping(value = "/upload/logsFile", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public JsonResult<Void> uploadLogsFile(@RequestPart MultipartFile logsFile) {
        logsService.uploadLogsFile(logsFile, LogsConstants.LOGS_DIR);
        return JsonResult.ok();
    }

}