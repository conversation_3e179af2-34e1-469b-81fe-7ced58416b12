package com.genlight.epilcure.logs.utils;

import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import io.netty.util.ByteProcessor;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.util.Arrays;
import java.util.zip.GZIPInputStream;

public class Utils {

    // 压缩文件头
    private static final byte[] GZIP_FILE_HEADER = new byte[]{0x1F, (byte) 0x8B, 0x08};

    /**
     * 判断文件是否是 GZIP 压缩文件
     *
     * @param file 输入的压缩文件
     * @return Boolean 是否是 GZIP 压缩文件
     * @throws IOException 数据读取失败时
     */
    public static boolean isGZipFile(MultipartFile file) throws IOException {
        byte[] copyBytes = Arrays.copyOfRange(file.getBytes(), 0, 3);
        return Arrays.equals(copyBytes, GZIP_FILE_HEADER);
    }

    /**
     * 解压压缩文件
     *
     * @param inputStream 压缩文件输入流
     * @return 解压信息
     */
    public static ByteBuf unGZip(InputStream inputStream) {
        ByteBuf byteBuf = Unpooled.buffer();
        ByteBuf insideBuf = Unpooled.buffer();
        try (GZIPInputStream gzipInputStream = new GZIPInputStream(inputStream)) {
            byte[] buffer = new byte[8192];
            int n;
            while ((n = gzipInputStream.read(buffer)) >= 0) {
                insideBuf.capacity(n);
                insideBuf.readerIndex(0);
                insideBuf.writerIndex(0);
                insideBuf.writeBytes(buffer, 0, n);

                // 如果整个 buffer 都为无效数据，则直接返回
                if (!isContainsVaildData(insideBuf)) {
                    return byteBuf;
                }

                byteBuf.writeBytes(insideBuf);
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return byteBuf;
    }

    public static boolean isContainsVaildData(ByteBuf buffer) {
        // 移动一下截取缓冲区的写入位置，确保可以进行校验
        buffer.writerIndex(buffer.capacity());
        int firstNulIndex = buffer.forEachByte(ByteProcessor.FIND_NUL);
        int firstNonNulIndex = buffer.forEachByte(ByteProcessor.FIND_NON_NUL);

        // 全部都是 NUL 时
        return firstNulIndex != 0 || firstNonNulIndex != -1;
    }
}
