package com.genlight.epilcure.logs;

import lombok.extern.slf4j.Slf4j;
import net.bytebuddy.agent.ByteBuddyAgent;
import org.aspectj.weaver.loadtime.Agent;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.instrument.InstrumentationSavingAgent;

import java.lang.instrument.Instrumentation;

@Slf4j
@SpringBootApplication(scanBasePackages = "com.genlight.epilcure")
public class EpilcureLogsApplication {

    public static void main(String[] args) {
        Instrumentation instrumentation = ByteBuddyAgent.install();
        Agent.agentmain("", instrumentation);
        InstrumentationSavingAgent.agentmain("", instrumentation);
        log.info("test");
        SpringApplication.run(EpilcureLogsApplication.class, args);


    }

}
