package com.genlight.epilcure.logs.listener;

import com.genlight.epilcure.logs.service.LogsService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RocketMQMessageListener(topic = "${rocketmq.consumer.logs_parse.topic}", consumerGroup = "${rocketmq.consumer.logs_parse.group}")
public class ClientLogsListener implements RocketMQListener<String> {

    @Resource
    private LogsService logsService;

    @Override
    public void onMessage(String fileName) {
        boolean parseResult = logsService.parseLogsFile(fileName);
    }
}
