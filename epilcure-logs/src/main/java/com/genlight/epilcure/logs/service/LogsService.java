package com.genlight.epilcure.logs.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.genlight.epilcure.common.core.service.exception.ServiceException;
import com.genlight.epilcure.common.core.util.MinioUtils;
import com.genlight.epilcure.logs.properties.LogsProperties;
import com.genlight.epilcure.logs.utils.LineBuffer;
import com.genlight.epilcure.logs.utils.Utils;
import io.netty.buffer.ByteBuf;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.genlight.epilcure.logs.LogsConstants.*;

/**
 * <AUTHOR>
 * @version 1.0.0
 */
@Slf4j
@Service
public class LogsService {

    @Resource
    private MinioUtils minioUtils;

    @Resource
    private LogsProperties logsProperties;

    @Resource
    private RocketMQTemplate rocketMQTemplate;

    @Resource
    private ObjectMapper objectMapper;

    private Logger logger;

    private List<String> keywords;

    /**
     * 上传日志文件
     *
     * @param logsFile 日志文件
     * @param fileDir  日志存储目录
     */
    public void uploadLogsFile(MultipartFile logsFile, String fileDir) {
        try {
            if (!Utils.isGZipFile(logsFile)) {
                throw new ServiceException("ZIP 文件头错误");
            }
        } catch (Exception e) {
            logError("日志文件头识别失败", e.getMessage());
            throw new RuntimeException(e);
        }

        String fileName = minioUtils.upload(fileDir, logsFile);
        if (!StringUtils.hasText(fileName)) {
            throw new ServiceException("文件上传失败");
        }
        // 发送消息，来解压和解析日志文件
        rocketMQTemplate.convertAndSend(logsProperties.getLogsParse().getTopic(), fileName);
    }

    /**
     * 解析日志文件
     *
     * @param fileName 日志文件
     * @return boolean 解析结果
     */
    public boolean parseLogsFile(String fileName) {
        // 获取文件，进行解压和解析
        try (InputStream inputStream = minioUtils.getObject(fileName)) {
            // 解压数据，并读取到缓冲区
            ByteBuf byteBuf = Utils.unGZip(inputStream);
            if (byteBuf.hasArray()) {
                // 逐行读取，如果当前行无法解析，则记入日志继续读取下一条。
                LineBuffer lineBuffer = new LineBuffer(byteBuf);
                while (lineBuffer.isReadable()) {
                    String line = lineBuffer.readLine();
                    boolean parseResult = parse2LogByLine(line);
                    if (!parseResult) {
                        logError("日志行解析失败", "日志文件：" + fileName + "，日志行：" + line);
                    }
                }
            }
            return true;
        } catch (Exception e) {
            logError("日志文件解压失败", e.getMessage());
            return false;
        }
    }

    // 按行解析日志
    private boolean parse2LogByLine(String line) {
        try {
            if (!StringUtils.hasText(line)) {
                return true;
            }

            // 初始化关键字列表
            if (keywords == null) {
                keywords = Arrays.asList(
                        APP_NAME, MESSAGE, RECORD_DATE,
                        INTERNAL_AVAILABLE, MEMORY_AVAILABLE, MEMORY_TOTAL);
            }

            Map<String, Object> jsonMap = objectMapper.readValue(line, new TypeReference<>() {
            });

            if (logger == null) {
                logger = LoggerFactory.getLogger(this.getClass());
            }
            // 清理旧的 MDC
            MDC.clear();
            // 使用应用名称替换索引
            MDC.put(APP_NAME_MDC, (String) jsonMap.get(APP_NAME));
            MDC.put(RECORD_DATE, (String) jsonMap.get(RECORD_DATE));
            MDC.put(CLIENT_LOGS_MDC, CLIENT_LOGS);
            // 遍历全部 Key，设置为 MDC 字段
            for (String key : jsonMap.keySet()) {
                // 不重复添加筛选关键字
                if (!keywords.contains(key)) {
                    MDC.put(key, (String) jsonMap.get(key));
                }
            }

            logger.info(line);
            return true;
        } catch (Exception e) {
            // 防止上面崩溃前填充的信息，污染错误日志。
            MDC.clear();
            logError("日志行解析失败", e.getMessage());
        }
        return false;
    }

    // 记录错误日志
    private void logError(String error, String extra) {
        Map<String, Object> message = new HashMap<>();
        message.put("error", error);
        if (!StringUtils.hasText(extra)) {
            message.put("extra", "null");
        } else {
            message.put("extra", extra);
        }

        try {
            log.error(objectMapper.writeValueAsString(message));
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }

}