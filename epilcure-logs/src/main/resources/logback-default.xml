<?xml version="1.0" encoding="UTF-8"?>
<!--
1. scan="true": 当此属性设置为true时，配置文件如果发生改变，将会被重新加载，默认值为true。
2. debug="false": 当此属性设置为true时，将打印出logback内部日志信息，实时查看logback运行状态。默认值为false。
3. scanPeriod="60 seconds": 设置监测配置文件是否有修改的时间间隔，如果没有给出时间单位，默认单位是毫秒。当scan为true时，此属性生效。默认的时间间隔为1分钟。
-->
<configuration>

    <!--Spring boot 提供的一些转换说明符，处理颜色输出，异常格式等-->
    <conversionRule conversionWord="clr" converterClass="org.springframework.boot.logging.logback.ColorConverter"/>
    <conversionRule conversionWord="wex"
                    converterClass="org.springframework.boot.logging.logback.WhitespaceThrowableProxyConverter"/>
    <conversionRule conversionWord="wEx"
                    converterClass="org.springframework.boot.logging.logback.ExtendedWhitespaceThrowableProxyConverter"/>

    <!--读取Spring boot中环境配置的属性-->
    <springProperty name="APP_NAME" scope="context" source="spring.application.name"/>
    <springProperty name="APP_ACTIVE" scope="context" source="spring.profiles.active"/>
    <springProperty name="LOGSTASH_DESTINATION" scope="context" source="logstash.destination"
                    defaultValue="localhost:8080"/>
    <springProperty name="LOGSTASH_WRITE_BUFFER_SIZE" scope="context" source="logstash.writeBufferSize"
                    defaultValue="8192"/>
    <springProperty name="LOGSTASH_KEEPALIVE_DURATION" scope="context" source="logstash.keepAliveDuration"
                    defaultValue="5 minutes"/>
    <springProperty name="LOGSTASH_CONNECTION_TIMEOUT" scope="context" source="logstash.connectionTimeout"
                    defaultValue="5 seconds"/>
    <springProperty name="LOGSTASH_RECONNECTION_DELAY" scope="context" source="logstash.reconnectionDelay"
                    defaultValue="30 seconds"/>
    <springProperty name="LOGSTASH_TIMESTAMP_PATTERN" scope="context" source="logstash.timestampPattern"
                    defaultValue="yyyy-MM-dd HH:mm:ss.SSS"/>

    <!--定义日志输出对象使用的属性-->
    <property name="CONSOLE_LOG_PATTERN"
              value="${CONSOLE_LOG_PATTERN:-%clr(%d{${LOG_DATEFORMAT_PATTERN:-yyyy-MM-dd HH:mm:ss.SSS}}){faint} %highlight(${LOG_LEVEL_PATTERN:-%5p}) %clr(${PID:- }){magenta} %clr(---){faint} %clr([%15.15t]){faint} %clr(%-40.40logger{39}){cyan} %clr(:){faint} %highlight(%m%n${LOG_EXCEPTION_CONVERSION_WORD:-%wEx})}"/>
    <property name="CONSOLE_LOG_CHARSET" value="${CONSOLE_LOG_CHARSET:-${file.encoding:-UTF-8}}"/>
    <property name="FILE_LOG_PATTERN"
              value="${FILE_LOG_PATTERN:-%d{${LOG_DATEFORMAT_PATTERN:-yyyy-MM-dd HH:mm:ss.SSS}} ${LOG_LEVEL_PATTERN:-%5p} ${PID:- } --- [%t] %-40.40logger{39} : %m%n${LOG_EXCEPTION_CONVERSION_WORD:-%wEx}}"/>
    <property name="FILE_LOG_CHARSET" value="${FILE_LOG_CHARSET:-${file.encoding:-UTF-8}}"/>
    <property name="LOG_FILE_INFO"
              value="${LOG_FILE:-${LOG_PATH:-${LOG_TEMP:-${user.dir:-/tmp}}}/logs/${APP_NAME}/${APP_NAME}.info.log}"/>
    <property name="LOG_FILE_ERROR"
              value="${LOG_FILE:-${LOG_PATH:-${LOG_TEMP:-${user.dir:-/tmp}}}/logs/${APP_NAME}/${APP_NAME}.error.log}"/>
    <property name="LOG_ASYNC_QUEUE_SIZE" value="${LOG_ASYNC_QUEUE_SIZE:-256}"/>
    <property name="LOG_ASYNC_DISCARDING_THRESHOLD" value="${LOG_ASYNC_DISCARDING_THRESHOLD:-0}"/>

    <!-- 控制台输出 -->
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>${CONSOLE_LOG_PATTERN}</pattern>
            <charset>${CONSOLE_LOG_CHARSET}</charset>
        </encoder>
    </appender>

    <!-- 异步控制台输出 -->
    <appender name="ASYNC-CONSOLE" class="ch.qos.logback.classic.AsyncAppender">
        <appender-ref ref="CONSOLE"/>
        <queueSize>${LOG_ASYNC_QUEUE_SIZE}</queueSize>
        <discardingThreshold>${LOG_ASYNC_DISCARDING_THRESHOLD}</discardingThreshold>
    </appender>

    <!-- INFO级别的文件输出 -->
    <appender name="FILE_INFO" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_FILE_INFO}</file>
        <encoder>
            <pattern>${FILE_LOG_PATTERN}</pattern>
            <charset>${FILE_LOG_CHARSET}</charset>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>INFO</level>
        </filter>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${LOGBACK_ROLLINGPOLICY_FILE_NAME_PATTERN:-${LOG_FILE_INFO}.%d{yyyy-MM-dd}.%i.gz}
            </fileNamePattern>
            <cleanHistoryOnStart>${LOGBACK_ROLLINGPOLICY_CLEAN_HISTORY_ON_START:-false}</cleanHistoryOnStart>
            <maxFileSize>${LOGBACK_ROLLINGPOLICY_MAX_FILE_SIZE:-15MB}</maxFileSize>
            <totalSizeCap>${LOGBACK_ROLLINGPOLICY_TOTAL_SIZE_CAP:-0}</totalSizeCap>
            <maxHistory>${LOGBACK_ROLLINGPOLICY_MAX_HISTORY:-7}</maxHistory>
        </rollingPolicy>
    </appender>

    <!-- 异步INFO级别的文件输出 -->
    <appender name="ASYNC-FILE_INFO" class="ch.qos.logback.classic.AsyncAppender">
        <appender-ref ref="FILE_INFO"/>
        <queueSize>${LOG_ASYNC_QUEUE_SIZE}</queueSize>
        <discardingThreshold>${LOG_ASYNC_DISCARDING_THRESHOLD}</discardingThreshold>
    </appender>

    <!-- ERROR级别的文件输出 -->
    <appender name="FILE_ERROR" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_FILE_ERROR}</file>
        <encoder>
            <pattern>${FILE_LOG_PATTERN}</pattern>
            <charset>${FILE_LOG_CHARSET}</charset>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>ERROR</level>
        </filter>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${LOGBACK_ROLLINGPOLICY_FILE_NAME_PATTERN:-${LOG_FILE_ERROR}.%d{yyyy-MM-dd}.%i.gz}
            </fileNamePattern>
            <cleanHistoryOnStart>${LOGBACK_ROLLINGPOLICY_CLEAN_HISTORY_ON_START:-false}</cleanHistoryOnStart>
            <maxFileSize>${LOGBACK_ROLLINGPOLICY_MAX_FILE_SIZE:-15MB}</maxFileSize>
            <totalSizeCap>${LOGBACK_ROLLINGPOLICY_TOTAL_SIZE_CAP:-0}</totalSizeCap>
            <maxHistory>${LOGBACK_ROLLINGPOLICY_MAX_HISTORY:-30}</maxHistory>
        </rollingPolicy>
    </appender>

    <!-- 异步ERROR级别的文件输出 -->
    <appender name="ASYNC-FILE_ERROR" class="ch.qos.logback.classic.AsyncAppender">
        <appender-ref ref="FILE_ERROR"/>
        <queueSize>${LOG_ASYNC_QUEUE_SIZE}</queueSize>
        <discardingThreshold>${LOG_ASYNC_DISCARDING_THRESHOLD}</discardingThreshold>
    </appender>

    <!-- Logstash日志输出 -->
    <appender name="LOG_LOGSTASH" class="net.logstash.logback.appender.LogstashTcpSocketAppender">
        <destination>${LOGSTASH_DESTINATION}</destination>
        <writeTimeout>${LOGSTASH_WRITE_BUFFER_SIZE}</writeTimeout>
        <keepAliveDuration>${LOGSTASH_KEEPALIVE_DURATION}</keepAliveDuration>
        <connectionTimeout>${LOGSTASH_CONNECTION_TIMEOUT}</connectionTimeout>
        <reconnectionDelay>${LOGSTASH_RECONNECTION_DELAY}</reconnectionDelay>
        <encoder charset="UTF-8" class="net.logstash.logback.encoder.LogstashEncoder">
            <timestampPattern>${LOGSTASH_TIMESTAMP_PATTERN}</timestampPattern>
            <includeCallerData>false</includeCallerData>
            <includeContext>false</includeContext>
            <throwableConverter class="net.logstash.logback.stacktrace.ShortenedThrowableConverter">
                <maxLength>2048</maxLength>
                <maxDepthPerThrowable>30</maxDepthPerThrowable>
                <shortenedClassNameLength>20</shortenedClassNameLength>
                <inlineHash>true</inlineHash>
                <rootCauseFirst>true</rootCauseFirst>
                <lineSeparator>\\n</lineSeparator>
                <exclude>sun\.reflect\..*\.invoke.*</exclude>
                <exclude>net\.sf\.cglib\.proxy\.MethodProxy\.invoke</exclude>
            </throwableConverter>
        </encoder>
    </appender>

    <springProfile name="dev | test | moldCheck">
        <root level="INFO">
            <appender-ref ref="ASYNC-CONSOLE"/>
            <appender-ref ref="ASYNC-FILE_INFO"/>
            <appender-ref ref="ASYNC-FILE_ERROR"/>
            <appender-ref ref="LOG_LOGSTASH"/>
        </root>
    </springProfile>

    <springProfile name="prod">
        <root level="INFO">
            <appender-ref ref="ASYNC-CONSOLE"/>
            <appender-ref ref="ASYNC-FILE_INFO"/>
            <appender-ref ref="ASYNC-FILE_ERROR"/>
            <appender-ref ref="LOG_LOGSTASH"/>
        </root>
    </springProfile>
</configuration>
