package com.genlight.epilcure.api.scheme.feign;

import com.genlight.epilcure.api.scheme.pojo.dto.EegSchemeDTO;
import com.genlight.epilcure.api.scheme.pojo.vo.EegSchemeVO;
import io.swagger.v3.oas.annotations.Hidden;
import jakarta.validation.Valid;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(value = "scheme-service", contextId = "feignEegSchemeController")
@Hidden
public interface IEegSchemeController {
    @PostMapping("/feign/scheme/eegSchemes")
    EegSchemeVO add(@RequestBody @Valid EegSchemeDTO eegSchemeDTO);
}
