package com.genlight.epilcure.api.user.feign;

import com.genlight.epilcure.api.user.pojo.bo.LoginBO;
import com.genlight.epilcure.api.user.pojo.vo.UserVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @version 1.0.0
 */
@Tag(name = "IUserController", description = "内部调用用户的相关接口")
@FeignClient(value = "user-service", contextId = "feignUserController")
public interface IUserController {

    @Operation(summary = "用户登录")
    @PostMapping("/feign/users/loginByMobile/{mobile}")
    UserVO loginByMobile(@PathVariable String mobile);

    @Operation(summary = "用户登录成功")
    @PostMapping("/feign/users/loginSuccess")
    void loginSuccess(@RequestBody LoginBO login);

    @GetMapping("/feign/users/{id}")
    UserVO findById(@PathVariable Long id);
}
