package com.genlight.epilcure.api.user.pojo.vo;

import com.fasterxml.jackson.annotation.JsonView;
import com.genlight.epilcure.common.core.dao.enums.Status;
import com.genlight.epilcure.common.core.pojo.view.DetailView;
import com.genlight.epilcure.common.core.pojo.vo.TreeVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
@SuperBuilder
@NoArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@Schema(description = "资源视图对象")
public class ResVO extends TreeVO<ResVO> {

    @Schema(description = "名称")
    private String name;

    @Schema(description = "代码")
    private String code;

    @Schema(description = "序号")
    private Integer seq;

    @Schema(description = "状态（0：禁用，1：启用）")
    private Status status;

    @Schema(description = "描述信息")
    private String description;

    @JsonView(DetailView.class)
    @Schema(description = "上级资源")
    private ResVO parent;
}
