package com.genlight.epilcure.api.user.pojo.vo;

import com.genlight.epilcure.common.core.dao.enums.Gender;
import com.genlight.epilcure.common.core.dao.enums.Status;
import com.genlight.epilcure.common.core.pojo.vo.BaseVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;

import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
@SuperBuilder
@NoArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@Schema(description = "用户视图对象")
public class UserVO extends BaseVO {

    @Schema(description = "名字")
    private String name;

    @Schema(description = "手机号")
    private String mobile;

    @Schema(description = "密码")
    private String password;

    @Schema(description = "性别")
    private Gender gender;

    @Schema(description = "状态（0：禁用，1：启用，3：删除）")
    private Status status;

    @Schema(description = "是否能禁用")
    private Boolean disable;

    @Schema(description = "最后登录时间")
    private Date loginTime;

    @Schema(description = "最后登录IP")
    private String loginIp;

    @Schema(description = "最后登录区域")
    private String loginRegion;

    @Schema(description = "描述信息")
    private String description;

    @Schema(description = "所属组织")
    private OrgVO org;

    @Schema(description = "所属部门")
    private DeptVO dept;

    @Schema(description = "有权限操作的组织")
    private Set<Long> orgIds;

    @Schema(description = "所有权限")
    private List<PermissionVO> permissions;

    @Schema(description = "是否是工程师")
    private Boolean engineer;
}
