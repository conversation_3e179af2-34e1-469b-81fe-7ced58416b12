package com.genlight.epilcure.api.user.pojo.dto;

import com.genlight.epilcure.common.core.constants.RegexConstants;
import com.genlight.epilcure.common.core.dao.enums.Status;
import com.genlight.epilcure.common.core.pojo.dto.BaseDTO;
import com.genlight.epilcure.common.core.pojo.valid.Add;
import com.genlight.epilcure.common.core.pojo.valid.TextLength;
import com.genlight.epilcure.common.core.pojo.valid.Update;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
@SuperBuilder
@NoArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@Schema(description = "注册信息的传输对象")
public class RegisterDTO extends BaseDTO {

    @Schema(description = "名字", maxLength = 32)
    @NotBlank(groups = Add.class, message = "用户的名称不能为空！")
    @TextLength(min = 1, max = 32, groups = {Add.class, Update.class}, message = "用户名称不能为空字符串且最大长度为32！")
    private String name;

    @Schema(description = "手机号", maxLength = 16)
    @NotBlank(groups = Add.class, message = "用户的手机号不能为空！")
    @TextLength(min = 1, max = 16, groups = {Add.class, Update.class}, message = "用户手机号不能为空字符串且最大长度为16！")
    @Pattern(regexp = RegexConstants.REGEX_MOBILE, message = "用户手机号不合法")
    private String mobile;

    @Schema(description = "项目")
    @NotEmpty(groups = Add.class, message = "项目不能为空！")
    private List<Long> projects;

    @Schema(description = "状态（0：禁用，1：启用，3：删除）")
    private Status status;

    @Schema(description = "描述信息", maxLength = 1024)
    @TextLength(max = 1024, groups = {Add.class, Update.class}, message = "描述信息的最大长度为1024！")
    private String description;

    @Schema(description = "组织ID")
    private Long orgId;

    @Schema(description = "部门ID")
    @NotNull(groups = Add.class, message = "用户的所属部门不能为空！")
    private Long deptId;

    @Schema(description = "角色ID")
    @NotNull(groups = Add.class, message = "用户的角色不能为空！")
    private Long roleId;
}
