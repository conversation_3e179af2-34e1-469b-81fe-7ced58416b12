package com.genlight.epilcure.api.user.feign;

import com.genlight.epilcure.api.user.pojo.vo.DeptVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

/**
 * <AUTHOR>
 * @version 1.0.0
 */
@Tag(name = "IDeptController", description = "内部调用部门的相关接口")
@FeignClient(value = "user-service", contextId = "feignDeptController")
public interface IDeptController {

    @GetMapping("/feign/depts/{id}")
    @Operation(summary = "根据ID查询部门")
    DeptVO findDeptById(@PathVariable Long id);

    @GetMapping("/feign/depts/exists/{id}")
    @Operation(summary = "根据ID查询部门是否存在")
    Boolean existsDeptById(@PathVariable Long id);
}
