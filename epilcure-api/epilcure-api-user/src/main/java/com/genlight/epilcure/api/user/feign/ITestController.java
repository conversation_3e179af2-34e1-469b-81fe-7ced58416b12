package com.genlight.epilcure.api.user.feign;

import com.genlight.epilcure.api.user.pojo.vo.TestVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

/**
 * <AUTHOR>
 * @version 1.0.0
 */
@Tag(name = "ITestController", description = "内部调用测试用户的相关接口")
@FeignClient(value = "user-service", contextId = "feignTestController")
public interface ITestController {

    @GetMapping("/feign/tests/{id}")
    @Operation(summary = "根据ID查询测试名单")
    TestVO findTestById(@PathVariable Long id);

    @GetMapping("/feign/tests/exists/{id}")
    @Operation(summary = "根据ID查询测试名单是否存在")
    int existsTestById(@PathVariable Long id);

    @GetMapping("/feign/tests/{testId}/exists/{userId}")
    @Operation(summary = "根据ID查询测试名单是否存在")
    int existsUserByTestId(@PathVariable Long testId, @PathVariable Long userId);
}
