package com.genlight.epilcure.api.user.pojo.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.genlight.epilcure.common.core.dao.enums.Status;
import com.genlight.epilcure.common.core.pojo.dto.BaseDTO;
import com.genlight.epilcure.common.core.pojo.valid.Add;
import com.genlight.epilcure.common.core.pojo.valid.NotNullIf;
import com.genlight.epilcure.common.core.pojo.valid.TextLength;
import com.genlight.epilcure.common.core.pojo.valid.Update;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
@SuperBuilder
@NoArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@Schema(description = "权限的传输对象")
@NotNullIf.List({
        @NotNullIf(checkField = "resId", dependField = "resCode", groups = Add.class, message = "资源代码为空时资源ID不能为空！"),
        @NotNullIf(checkField = "resCode", dependField = "resId", groups = Add.class, message = "资源ID为空时资源代码不能为空！"),
        @NotNullIf(checkField = "actionId", dependField = "actionCode", groups = Add.class, message = "动作代码为空时动作ID不能为空！"),
        @NotNullIf(checkField = "actionCode", dependField = "actionId", groups = Add.class, message = "动作ID为空时动作代码不能为空！")
})
public class PermissionDTO extends BaseDTO {

    @Schema(description = "名称", maxLength = 128)
//    @NotBlank(groups = Add.class, message = "权限的名称不能为空！")
    @TextLength(min = 1, max = 128, groups = {Add.class, Update.class}, message = "权限名称不能为空字符串且最大长度为128！")
    private String name;

    @Schema(description = "状态（0：禁用，1：启用）")
    private Status status;

    @Schema(description = "描述信息", maxLength = 1024)
    @TextLength(max = 1024, groups = {Add.class, Update.class}, message = "描述信息的最大长度为1024！")
    private String description;

    @Schema(description = "权限Code")
    private String code;

    @Schema(description = "资源ID")
    private Long resId;

    @Schema(description = "资源Code")
    private String resCode;

    @Schema(description = "动作ID")
    private Long actionId;

    @Schema(description = "动作Code")
    private String actionCode;

    @JsonIgnore
    @Schema(hidden = true)
    private Long roleId;

    @JsonIgnore
    @Schema(hidden = true)
    private boolean roleEquals;
}
