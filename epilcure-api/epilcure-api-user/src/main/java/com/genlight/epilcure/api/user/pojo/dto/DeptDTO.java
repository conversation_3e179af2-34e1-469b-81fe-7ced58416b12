package com.genlight.epilcure.api.user.pojo.dto;

import com.genlight.epilcure.common.core.dao.enums.Status;
import com.genlight.epilcure.common.core.pojo.valid.Add;
import com.genlight.epilcure.common.core.pojo.valid.NotNullIf;
import com.genlight.epilcure.common.core.pojo.valid.TextLength;
import com.genlight.epilcure.common.core.pojo.valid.Update;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
@SuperBuilder
@NoArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@Schema(description = "部门的传输对象")
@NotNullIf(checkField = "orgId", dependField = "parentId", groups = Add.class, message = "上级部门为空时组织ID不能为空！")
public class DeptDTO implements Serializable {

    @Schema(description = "名称", maxLength = 32)
    @NotBlank(groups = Add.class, message = "部门的名称不能为空！")
    @TextLength(min = 1, max = 32, groups = {Add.class, Update.class}, message = "部门名称不能为空字符串且最大长度为32！")
    private String name;

    @Schema(description = "序号")
    private Integer seq;

    @Schema(description = "电话", maxLength = 16)
    @TextLength(min = 1, max = 16, groups = {Add.class, Update.class}, message = "部门电话不能为空字符串且最大长度为16！")
    private String phone;

    @Schema(description = "状态（0：禁用，1：启用）")
    private Status status;

    @Schema(description = "描述信息", maxLength = 1024)
    @TextLength(max = 1024, groups = {Add.class, Update.class}, message = "描述信息的最大长度为1024！")
    private String description;

    @Schema(description = "上级部门")
    private Long parentId;

    @Schema(description = "组织ID")
    private Long orgId;
}
