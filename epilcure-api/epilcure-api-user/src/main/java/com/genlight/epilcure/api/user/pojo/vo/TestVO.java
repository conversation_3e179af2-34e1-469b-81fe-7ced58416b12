package com.genlight.epilcure.api.user.pojo.vo;

import com.genlight.epilcure.common.core.dao.enums.Status;
import com.genlight.epilcure.common.core.pojo.vo.BaseVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
@SuperBuilder
@NoArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@Schema(description = "测试名单视图对象")
public class TestVO extends BaseVO {

    @Schema(description = "测试名单名称")
    private String name;

    @Schema(description = "测试名单所属项目")
    private List<Long> projects;

    @Schema(description = "状态（0：禁用，1：启用，3：删除）")
    private Status status;

    @Schema(description = "描述信息", maxLength = 1024)
    private String description;
}
