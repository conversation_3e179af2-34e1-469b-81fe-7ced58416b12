package com.genlight.epilcure.api.user.pojo.dto;

import com.fasterxml.jackson.annotation.JsonView;
import com.genlight.epilcure.common.core.dao.enums.Status;
import com.genlight.epilcure.common.core.pojo.dto.AddressDTO;
import com.genlight.epilcure.common.core.pojo.dto.BaseDTO;
import com.genlight.epilcure.common.core.pojo.valid.Add;
import com.genlight.epilcure.common.core.pojo.valid.TextLength;
import com.genlight.epilcure.common.core.pojo.valid.Update;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
@SuperBuilder
@NoArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@Schema(description = "组织的传输对象")
public class OrgDTO extends BaseDTO {

    @Schema(description = "名称", maxLength = 32)
    @NotBlank(groups = Add.class, message = "组织的名称不能为空！")
    @TextLength(min = 1, max = 32, groups = {Add.class, Update.class}, message = "组织名称不能为空字符串且最大长度为32！")
    private String name;

    @Schema(description = "序号")
    private Integer seq;

    @Schema(description = "电话", maxLength = 16)
    @TextLength(min = 1, max = 16, groups = {Add.class, Update.class}, message = "组织电话不能为空字符串且最大长度为16！")
    private String phone;

    @Schema(description = "状态（0：禁用，1：启用）")
    private Status status;

    @Schema(description = "描述信息", maxLength = 1024)
    @TextLength(max = 1024, groups = {Add.class, Update.class}, message = "描述信息的最大长度为1024！")
    private String description;

    @Schema(description = "上级组织")
    private Long parentId;

    @Valid
    @JsonView(Add.class)
    @Schema(description = "地址信息")
    @NotNull(groups = {Add.class}, message = "组织地址信息不允许为空")
    private AddressDTO address;

    @Schema(description = "部门名称")
    private String deptName;

    @Schema(description = "医院编码")
    private String customCode;
}
