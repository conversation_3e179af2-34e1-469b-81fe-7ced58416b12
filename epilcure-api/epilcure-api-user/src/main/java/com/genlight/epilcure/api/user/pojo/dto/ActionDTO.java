package com.genlight.epilcure.api.user.pojo.dto;

import com.genlight.epilcure.common.core.dao.enums.Status;
import com.genlight.epilcure.common.core.pojo.dto.BaseDTO;
import com.genlight.epilcure.common.core.pojo.valid.Add;
import com.genlight.epilcure.common.core.pojo.valid.TextLength;
import com.genlight.epilcure.common.core.pojo.valid.Update;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
@SuperBuilder
@NoArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@Schema(description = "动作的传输对象")
public class ActionDTO extends BaseDTO {

    @Schema(description = "名称", maxLength = 32)
    @NotBlank(groups = Add.class, message = "动作的名称不能为空！")
    @TextLength(min = 1, max = 32, groups = {Add.class, Update.class}, message = "动作名称不能为空字符串且最大长度为32！")
    private String name;

    @Schema(description = "代码", maxLength = 128)
    @NotBlank(groups = Add.class, message = "动作的代码不能为空！")
    @TextLength(min = 1, max = 128, groups = {Add.class, Update.class}, message = "动作代码不能为空字符串且最大长度为128！")
    private String code;

    @Schema(description = "状态（0：禁用，1：启用）")
    private Status status;

    @Schema(description = "描述信息", maxLength = 1024)
    @TextLength(max = 1024, groups = {Add.class, Update.class}, message = "描述信息的最大长度为1024！")
    private String description;
}
