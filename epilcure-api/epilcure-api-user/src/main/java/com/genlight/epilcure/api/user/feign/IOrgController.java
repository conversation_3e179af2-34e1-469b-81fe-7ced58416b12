package com.genlight.epilcure.api.user.feign;

import com.genlight.epilcure.api.user.pojo.vo.OrgVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 */
@Tag(name = "IOrgController", description = "内部调用组织的相关接口")
@FeignClient(value = "user-service", contextId = "feignOrgController")
public interface IOrgController {

    @GetMapping("/feign/orges/{id}")
    @Operation(summary = "根据ID查询组织")
    OrgVO findOrgById(@PathVariable Long id);

    @GetMapping("/feign/orges/exists/{id}")
    @Operation(summary = "根据ID查询组织是否存在")
    Boolean existsOrgById(@PathVariable Long id);

    @GetMapping("/feign/orges")
    @Operation(summary = "查询所有组织")
    List<OrgVO> findOrges();
}
