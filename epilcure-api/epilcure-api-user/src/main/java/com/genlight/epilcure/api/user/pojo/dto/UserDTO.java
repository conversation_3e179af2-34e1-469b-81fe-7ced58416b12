package com.genlight.epilcure.api.user.pojo.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.genlight.epilcure.common.core.constants.RegexConstants;
import com.genlight.epilcure.common.core.dao.enums.Gender;
import com.genlight.epilcure.common.core.dao.enums.Status;
import com.genlight.epilcure.common.core.pojo.dto.BaseDTO;
import com.genlight.epilcure.common.core.pojo.valid.Add;
import com.genlight.epilcure.common.core.pojo.valid.TextLength;
import com.genlight.epilcure.common.core.pojo.valid.Update;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
@SuperBuilder
@NoArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@Schema(description = "用户的传输对象")
public class UserDTO extends BaseDTO {

    @Schema(description = "名字", maxLength = 32)
    @NotBlank(groups = Add.class, message = "用户的名称不能为空！")
    @TextLength(min = 1, max = 32, groups = {Add.class, Update.class}, message = "用户名称不能为空字符串且最大长度为32！")
    private String name;

    @Schema(description = "手机号", maxLength = 16)
    @NotBlank(groups = Add.class, message = "用户的手机号不能为空！")
    @TextLength(min = 1, max = 16, groups = {Add.class, Update.class}, message = "用户手机号不能为空字符串且最大长度为16！")
    @Pattern(regexp = RegexConstants.REGEX_MOBILE, message = "用户手机号不合法")
    private String mobile;

    @Schema(description = "密码", maxLength = 12)
    @NotBlank(groups = Add.class, message = "用户的密码不能为空！")
    @TextLength(min = 1, max = 12, groups = {Add.class, Update.class}, message = "用户密码不能为空字符串且最大长度为12！")
    @Pattern(regexp = RegexConstants.REGEX_PASSWORD, message = "密码必须包含数字和字母，长度为6~12")
    private String password;

    @Schema(description = "性别")
    @NotNull(groups = Add.class, message = "用户的性别不能为空！")
    private Gender gender;

    @Schema(description = "状态（0：禁用，1：启用，3：删除）")
    private Status status;

    @JsonIgnore
    @Schema(hidden = true, description = "是否能禁用")
    private Boolean disable;

    @Schema(description = "描述信息", maxLength = 1024)
    @TextLength(max = 1024, groups = {Add.class, Update.class}, message = "描述信息的最大长度为1024！")
    private String description;

    @Schema(description = "组织ID")
    private Long orgId;

    @Schema(description = "部门ID")
    private Long deptId;

    @Schema(description = "原密码")
    @Pattern(regexp = RegexConstants.REGEX_PASSWORD, message = "密码必须包含数字和字母，长度为6~12")
    private String oldPassword;

    @Schema(description = "验证码ID")
    private String captchaId;

    @Schema(description = "验证码")
    private String captcha;

    @Schema(description = "是否短信通知")
    private boolean smsNotify;

    @JsonIgnore
    @Schema(hidden = true)
    private Long roleId;

    @JsonIgnore
    @Schema(hidden = true)
    private boolean roleEquals;

    @JsonIgnore
    @Schema(hidden = true)
    private Long testId;

    @JsonIgnore
    @Schema(hidden = true)
    private boolean testEquals;
}
