package com.genlight.epilcure.api.user.pojo.vo;

import com.genlight.epilcure.common.core.dao.enums.Status;
import com.genlight.epilcure.common.core.pojo.vo.BaseVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
@SuperBuilder
@NoArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@Schema(description = "用户视图对象")
public class RegisterVO extends BaseVO {

    @Schema(description = "名字")
    private String name;

    @Schema(description = "手机号")
    private String mobile;

    @Schema(description = "项目")
    private List<Long> projects;

    @Schema(description = "状态（0：禁用，1：启用，3：删除）")
    private Status status;

    @Schema(description = "描述信息")
    private String description;

    @Schema(description = "所属组织")
    private OrgVO org;

    @Schema(description = "所属部门")
    private DeptVO dept;

    @Schema(description = "拥有角色")
    private RoleVO role;

    @Schema(description = "录入者")
    private UserVO operator;
}
