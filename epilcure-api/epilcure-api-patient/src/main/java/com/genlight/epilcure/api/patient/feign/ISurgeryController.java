package com.genlight.epilcure.api.patient.feign;

import com.genlight.epilcure.api.patient.pojo.dto.SurgeryDTO;
import com.genlight.epilcure.api.patient.pojo.vo.SurgeryVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(value = "patient-service", contextId = "feignSurgeryController")
public interface ISurgeryController {
    @GetMapping("/feign/surgerys/findByPatientId/{id}")
    SurgeryVO findByPatientId(@PathVariable Long id);

    @PutMapping("/feign/surgerys/update_Impedance")
    void updateSurgeryImpedance(@RequestBody SurgeryDTO surgeryDTO);
}
