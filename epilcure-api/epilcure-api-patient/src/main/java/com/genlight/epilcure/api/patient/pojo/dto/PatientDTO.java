package com.genlight.epilcure.api.patient.pojo.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonView;
import com.genlight.epilcure.api.patient.enums.IdCardType;
import com.genlight.epilcure.common.core.constants.RegexConstants;
import com.genlight.epilcure.common.core.dao.enums.Gender;
import com.genlight.epilcure.common.core.dao.enums.Status;
import com.genlight.epilcure.common.core.pojo.dto.AddressDTO;
import com.genlight.epilcure.common.core.pojo.dto.BaseDTO;
import com.genlight.epilcure.common.core.pojo.valid.Add;
import com.genlight.epilcure.common.core.pojo.valid.Find;
import com.genlight.epilcure.common.core.pojo.valid.Update;
import com.genlight.epilcure.common.core.pojo.view.BasicView;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Null;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.groups.Default;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
@SuperBuilder
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "查询,修改,添加患者条件")
public class PatientDTO extends BaseDTO {

    @NotBlank(message = "患者名字不能为空")
    @Schema(description = "患者名字")
    private String name;

    @NotNull(message = "证件类型不能为空", groups = Add.class)
    @Schema(description = "证件类型")
    private IdCardType idCardType;

    @NotBlank(message = "证件信息不能为空", groups = Add.class)
    @Schema(description = "证件号")
    private String idCard;

    @Schema(description = "性别")
    @JsonView({Add.class, EpilcureAddPatient.class, Update.class})
    private Gender sex;

    @Schema(description = "生日")
    @JsonView({Add.class, EpilcureAddPatient.class, Update.class})
    private Date birthday;

    @Pattern(regexp = RegexConstants.REGEX_MOBILE, message = "手机号码不合法")
    @Schema(description = "手机号码")
    private String mobile;

    @Null(message = "复诊时间,由每次报告分配")
    @Schema(description = "复诊时间")
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
    @JsonView(BasicView.class)
    private Date scheduledDate;

    @Null(message = "手术时间,由合同分配")
    @Schema(description = "手术时间")
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
    @JsonView(BasicView.class)
    private Date surgeryDate;

    @NotNull(message = "档案Id不能为空", groups = Add.class)
    @JsonView(Add.class)
    @Schema(description = "档案Id")
    private Long archivesId;

    @Schema(description = "刺激器签名")
    @JsonView(BasicView.class)
    private String bcmSn;

    @NotNull(message = "患者地址信息不能为空", groups = {EpilcureAddPatient.class, Add.class})
    @Schema(description = "地址信息")
    @JsonView({Add.class, EpilcureAddPatient.class, Update.class})
    @Valid
    private AddressDTO address;

    @Schema(description = "是否只查询有程控记录的患者")
    @JsonView(BasicView.class)
    private Boolean findHasScheme;

    @JsonIgnore
    @Schema(hidden = true)
    private Long testId;

    @Schema(description = "状态")
    @JsonView(Find.class)
    private Status status;

    @JsonIgnore
    @Schema(hidden = true)
    private boolean testEquals;

    @Schema(description = "程控时间", hidden = true)
    @Null(message = "服务器自动分配程控时间")
    private Date lastSchemeDate;

    @Schema(description = "排序时间", hidden = true)
    @Null(message = "服务器自动分配排序时间")
    private Date sortTime;

    public interface EpilcureAddPatient extends Default {
    }
}
