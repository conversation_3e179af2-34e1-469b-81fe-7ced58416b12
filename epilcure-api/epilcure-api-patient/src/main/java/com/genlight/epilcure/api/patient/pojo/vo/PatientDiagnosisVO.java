package com.genlight.epilcure.api.patient.pojo.vo;

import com.genlight.epilcure.common.core.pojo.vo.BaseVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR>
 * @Date 2022/6/12 13:36
 * @Version 1.0.0
 **/
@Schema(description = "诊断View Object")
@SuperBuilder
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Data
public class PatientDiagnosisVO extends BaseVO {
    @Schema(description = "诊断Id")
    private Long diagnosisId;
}
