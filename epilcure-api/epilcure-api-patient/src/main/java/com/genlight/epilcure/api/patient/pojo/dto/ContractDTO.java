package com.genlight.epilcure.api.patient.pojo.dto;

import com.fasterxml.jackson.annotation.JsonView;
import com.genlight.epilcure.common.core.constants.RegexConstants;
import com.genlight.epilcure.common.core.dao.enums.Status;
import com.genlight.epilcure.common.core.pojo.dto.BaseDTO;
import com.genlight.epilcure.common.core.pojo.valid.Add;
import com.genlight.epilcure.common.core.pojo.valid.Find;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Null;
import jakarta.validation.constraints.Pattern;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;
import org.hibernate.validator.constraints.Length;

@Getter
@Setter
@SuperBuilder
@NoArgsConstructor
@Schema(description = "联系人信息")
public class ContractDTO extends BaseDTO {
    @JsonView(Add.class)
    @NotNull(message = "患者Id不能为空", groups = Add.class)
    @Schema(description = "患者Id")
    private Long patientId;

    @JsonView(Find.class)
    @Null(message = "工程师Id由系统分配")
    @Schema(description = "工程师Id")
    private Long adviserId;

    @NotNull(message = "联系人姓名不能为空")
    @Schema(description = "联系人姓名")
    private String kinName;

    @NotNull(message = "联系人手机不能为空")
    @Pattern(regexp = RegexConstants.REGEX_MOBILE, message = "联系人手机号码不合法")
    @Schema(description = "联系人手机号")
    private String kinMobile;

    @Schema(description = "联系人关系")
    private String kinRelationShip;

    @JsonView(Find.class)
    @Schema(description = "根据状态查询")
    private Status status;

    @Schema(description = "备注")
    @Length(max = 40, message = "备注信息长度不能超过{max}")
    private String remark;
}
