package com.genlight.epilcure.api.patient.feign;

import com.genlight.epilcure.api.patient.pojo.dto.*;
import com.genlight.epilcure.api.patient.pojo.vo.ArchivesVO;
import com.genlight.epilcure.common.core.mvc.annotation.MultiRequestBody;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;

@FeignClient(value = "patient-service", contextId = "feignArchivesController")
public interface IArchivesController {
    @GetMapping("/feign/patient/archives/{id}")
    ArchivesVO findById(@PathVariable Long id);

    @GetMapping("/feign/patient/archivesNo/epilcure")
    Integer findEpilcureNo();

    @GetMapping("/feign/patient/archives/patient/{id}")
    Boolean existedByPatientId(@PathVariable Long id);

    @PostMapping(value = "feign/patient/archives")
    ArchivesVO addArchivesAndPatient(@Validated({PatientDTO.EpilcureAddPatient.class}) @MultiRequestBody ArchivesDTO archivesDTO
            , @Validated(PatientDTO.EpilcureAddPatient.class) @MultiRequestBody PatientDTO patientDTO
            , @Validated(PatientDTO.EpilcureAddPatient.class) @MultiRequestBody PatientDeviceDTO patientDeviceDTO
            , @Validated(PatientDTO.EpilcureAddPatient.class) @MultiRequestBody PatientSurgerySchemeDTO patientSurgerySchemeDTO
            , @Validated(PatientDTO.EpilcureAddPatient.class) @MultiRequestBody MedicalRecordDTO medicalRecordDTO
            , @Validated(PatientDTO.EpilcureAddPatient.class) @MultiRequestBody ContractDTO contractDTO);
}
