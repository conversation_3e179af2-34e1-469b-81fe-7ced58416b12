package com.genlight.epilcure.api.patient.pojo.vo;

import com.genlight.epilcure.common.core.pojo.vo.BaseVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;

import java.util.List;

@Data
@SuperBuilder
@NoArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@Schema(name = "SurgeryElectrodeVO", description = "手术中触点检测信息View Object")
public class SurgeryTouchPointVO extends BaseVO {
    @Schema(description = "阻抗")
    private Long impedance;
    @Schema(description = "是否正常")
    private Boolean result;
    @Schema(description = "是否缝合前")
    private Boolean before;
    @Schema(description = "通道")
    private Integer position;
    @Schema(description = "极性")
    private List<Integer> polarity;
    @Schema(description = "术中脑电图片")
    private String eegFile;
}
