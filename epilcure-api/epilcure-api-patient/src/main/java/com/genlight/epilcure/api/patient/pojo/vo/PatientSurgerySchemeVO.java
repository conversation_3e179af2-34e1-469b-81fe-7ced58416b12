package com.genlight.epilcure.api.patient.pojo.vo;

import com.genlight.epilcure.common.core.pojo.vo.BaseVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;

import java.util.Date;

@Data
@SuperBuilder
@NoArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@Schema(description = "患者手术计划View Object")
public class PatientSurgerySchemeVO extends BaseVO {
    @Schema(description = "评估医生")
    private String schemeName;
    @Schema(description = "手术医生")
    private Long userId;
    @Schema(description = "手术医生名称")
    private String userName;
    @Schema(description = "手术医生号码")
    private String userPhone;

    @Schema(description = "医院Id")
    private Long orgId;
    @Schema(description = "医院名称")
    private String orgName;

    @Schema(description = "科室Id")
    private Long deptId;
    @Schema(description = "科室名称")
    private String deptName;

    @Schema(description = "手术时间")
    private Date surgeryDate;
    @Schema(description = "工程师Id")
    private Long adviserId;
    @Schema(description = "工程师名称")
    private String adviserName;
}
