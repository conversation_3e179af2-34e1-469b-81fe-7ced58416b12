package com.genlight.epilcure.api.patient.pojo.dto;

import com.fasterxml.jackson.annotation.JsonView;
import com.genlight.epilcure.api.patient.pojo.dto.PatientDTO.EpilcureAddPatient;
import com.genlight.epilcure.common.core.constants.RegexConstants;
import com.genlight.epilcure.common.core.dao.enums.Status;
import com.genlight.epilcure.common.core.pojo.dto.BaseDTO;
import com.genlight.epilcure.common.core.pojo.valid.Add;
import com.genlight.epilcure.common.core.pojo.valid.Find;
import com.genlight.epilcure.common.core.pojo.valid.Update;
import com.genlight.epilcure.common.core.pojo.view.SummaryView;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import jakarta.validation.groups.Default;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.Date;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
@SuperBuilder
@NoArgsConstructor
@Schema(description = "患者设备信息")
public class PatientDeviceDTO extends BaseDTO {
    @NotNull(message = "刺激器型号不能为空", groups = {Add.class, AddModel.class, AddBcm.class, EpilcureAddPatient.class})
    @Schema(description = "刺激器型号Id")
    @JsonView({Add.class, AddModel.class, AddBcm.class, EpilcureAddPatient.class, SummaryView.class})
    private Long bcmModelId;

    @NotNull(message = "程控仪型号不能为空", groups = {Add.class, AddModel.class, AddBpp.class, EpilcureAddPatient.class})
    @Schema(description = "程控仪型号id")
    @JsonView({Add.class, AddModel.class, AddBpp.class, EpilcureAddPatient.class, SummaryView.class})
    private Long bppModelId;

    @NotNull(message = "电极型号列表不能为空", groups = {Add.class, AddModel.class, AddEle.class, EpilcureAddPatient.class})
    @Size(min = 1, message = "至少分配一个电极设备型号", groups = {Add.class, AddModel.class, AddEle.class, EpilcureAddPatient.class})
    @Schema(description = "电极型号不能为空")
    @JsonView({Add.class, AddModel.class, AddEle.class, EpilcureAddPatient.class, SummaryView.class})
    @Valid
    private List<ContractElectrodeModelDTO> contractElectrodeModels;

    @Schema(description = "患者Id")
    @JsonView({Add.class, AddModel.class, AddDevice.class, AddBcm.class, AddBpp.class, AddEle.class, SummaryView.class})
    @NotNull(message = "患者Id不能为空", groups = {Add.class, AddModel.class, AddDevice.class, AddBcm.class, AddBpp.class, AddEle.class})
    private Long patientId;

    @Schema(description = "植入的刺激器Id")
    @JsonView({Add.class})
    @NotNull(message = "刺激器Id不能为空", groups = {Add.class})
    private Long bcmId;

    @Schema(description = "mac地址")
    private String bcmMac;

    // isbs电极录入使用签名的方式.
    @Schema(description = "刺激器签名")
    @JsonView({AddBcm.class, AddDevice.class, SummaryView.class})
    @NotNull(message = "刺激器签名不能为空", groups = {AddBcm.class, AddDevice.class})
    @Pattern(message = "刺激器签名不合法", groups = AddBcm.class, regexp = RegexConstants.EPILEPSY_BCM)
    private String bcmSn;

    @Schema(description = "使用的程控仪Id")
    private Long bppId;

    @Schema(description = "程控仪签名")
    private String bppSn;

    @Schema(description = "植入的电极Id")
    @NotNull(message = "电极设备不能为空", groups = {AddDevice.class, Add.class, AddEle.class, SummaryView.class, Update.class})
    @Valid
    private List<SurgeryElectrodeDTO> surgeryElectrodes;

    @Schema(description = "是否验证")
    private Boolean check;

    @Schema(description = "手术时间,如手术时间不为空则会添加一条手术记录")
    @JsonView({AddDevice.class, SummaryView.class})
    private Date surgeryDate;

    @Schema(description = "手术方案Id")
    @JsonView({AddDevice.class, SummaryView.class})
    private Long archivesId;

    @Schema(description = "状态")
    @JsonView(Find.class)
    private Status status;

    public interface AddBpp extends Default {
    }

    public interface AddBcm extends Default {
    }

    public interface AddEle extends Default {
    }

    public interface AddModel extends Default {
    }

    public interface AddDevice extends Default {
    }
}
