package com.genlight.epilcure.api.patient.pojo.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.genlight.epilcure.common.core.dao.enums.Status;
import com.genlight.epilcure.common.core.pojo.dto.BaseDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;
import org.hibernate.validator.constraints.Length;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/6/3 17:21
 * @Version 1.0.0
 **/
@Getter
@Setter
@SuperBuilder
@NoArgsConstructor
@Schema(name = "SurgeryDTO", description = "手术信息")
public class SurgeryDTO extends BaseDTO {

    @Schema(description = "患者Id")
    @NotNull(message = "患者Id不能为空")
    private Long patientId;

    @Schema(description = "档案Id")
    @NotNull(message = "档案Id不能为空")
    private Long archivesId;

    @Schema(description = "手术时间")
    @JsonIgnore
    private Date surgeryDate;

    @NotNull(message = "手术触点监控信息不能为空")
    @Schema(description = "手术触点检测信息")
    @Valid
    private List<SurgeryTouchPointDTO> surgeryTouchPoints;

    @Schema(description = "状态")
    @JsonIgnore
    private Status status;

    @Length(min = 0, max = 2048, message = "阻抗信息长度为{min}~{max}")
    @Schema(description = "阻抗数组")
    private String impedance;
}
