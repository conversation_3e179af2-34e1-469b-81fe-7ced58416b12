package com.genlight.epilcure.api.patient.pojo.dto;

import com.genlight.epilcure.common.core.pojo.dto.BaseDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @Date 2022/6/12 14:23
 * @Version 1.0.0
 **/
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "患者服药史")
public class PatientDrugsDTO extends BaseDTO {
    @Schema(description = "药物Id")
    private Long drugsId;

    @Schema(description = "用法")
    @NotNull(message = "用法不能为空")
    private String usage;

    @Schema(description = "用量")
    @NotNull(message = "用量不能为空")
    private String dosage;

//    @Schema(description = "用药时间段")
//    @NotNull(message = "用药时间段不能为空")
//    private String timePeriodOfAdministration;

    @Schema(description = "备注")
    private String drugDescription;

}
