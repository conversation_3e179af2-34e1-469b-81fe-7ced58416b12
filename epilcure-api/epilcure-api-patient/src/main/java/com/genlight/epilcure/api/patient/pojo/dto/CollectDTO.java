package com.genlight.epilcure.api.patient.pojo.dto;

import com.fasterxml.jackson.annotation.JsonView;
import com.genlight.epilcure.common.core.pojo.dto.BaseDTO;
import com.genlight.epilcure.common.core.pojo.valid.Add;
import com.genlight.epilcure.common.core.pojo.valid.Find;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;

import java.util.Date;

@Data
@SuperBuilder
@NoArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@Schema(description = "搜索,增加,更新收藏（报告）")
public class CollectDTO extends BaseDTO {

    @Schema(description = "收藏备注")
    private String remark;

    @NotNull(message = "患者id不能为空！", groups = Add.class)
    @Schema(description = "患者id")
    private Long patientId;

    @Schema(description = "开始时间")
    @JsonView(Find.class)
    private Date startTime;

    @Schema(description = "结束时间")
    @JsonView(Find.class)
    private Date endTime;

    @Schema(description = "查看类型")
    @JsonView(Find.class)
    private String caseType;

}
