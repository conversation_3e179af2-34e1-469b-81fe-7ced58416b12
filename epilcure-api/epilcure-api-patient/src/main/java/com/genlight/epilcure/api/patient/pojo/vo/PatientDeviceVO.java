package com.genlight.epilcure.api.patient.pojo.vo;

import com.genlight.epilcure.api.device.pojo.enums.ProtocolVersions;
import com.genlight.epilcure.common.core.dao.enums.Status;
import com.genlight.epilcure.common.core.dao.generator.annotation.SignOrder;
import com.genlight.epilcure.common.core.pojo.vo.BaseVO;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.Column;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;
import org.hibernate.annotations.Comment;

import java.util.Date;
import java.util.List;

@Data
@SuperBuilder
@NoArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@Schema(description = "患者设备View Object")
public class PatientDeviceVO extends BaseVO {
    @Schema(description = "刺激器型号Id")
    private Long bcmModelId;
    @Schema(description = "程控仪型号id")
    private Long bppModelId;
    @Schema(description = "电极型号不能为空")
    private List<ContractElectrodeModelVO> contractElectrodeModels;
    @Schema(description = "植入的刺激器Id")
    private Long bcmId;
    @Schema(description = "刺激器签名")
    private String bcmSn;
    @Enumerated(EnumType.ORDINAL)
    private ProtocolVersions protocolVersions;
    @Schema(description = "使用的程控仪Id")
    private Long bppId;
    @Schema(description = "程控仪签名")
    private String bppSn;

    @Schema(description = "是否验证")
    private Boolean check;
    @Schema(description = "当前设备状态")
    private Status status;
    @Schema(description = "植入的电极Id")
    private List<SurgeryElectrodeVO> surgeryElectrodes;

    @Schema(description = "硬件版本号")
    private String hardwareVersion;

    @Schema(description = "软件版本号")
    private String softwareVersion;

    @Schema(description = "刺激器mac")
    private String bcmMac;

    @Schema(description = "刺激器植入时间")
    private Date bcmImplantationDate;
}
