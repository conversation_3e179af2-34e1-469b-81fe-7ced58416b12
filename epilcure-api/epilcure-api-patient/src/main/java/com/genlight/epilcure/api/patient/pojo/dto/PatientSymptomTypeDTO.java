package com.genlight.epilcure.api.patient.pojo.dto;

import com.genlight.epilcure.common.core.pojo.dto.BaseDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR> maoyan
 * @Date : 2022/8/5 17:16
 * @Version :1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "患者症状类型")
public class PatientSymptomTypeDTO extends BaseDTO {
    @Schema(description = "症状类型Id")
    private Long symptomTypeId;
}
