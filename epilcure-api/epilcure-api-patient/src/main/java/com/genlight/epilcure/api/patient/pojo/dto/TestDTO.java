package com.genlight.epilcure.api.patient.pojo.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.genlight.epilcure.common.core.dao.enums.Status;
import com.genlight.epilcure.common.core.pojo.dto.BaseDTO;
import com.genlight.epilcure.common.core.pojo.valid.Add;
import com.genlight.epilcure.common.core.pojo.valid.TextLength;
import com.genlight.epilcure.common.core.pojo.valid.Update;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.Column;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
@SuperBuilder
@NoArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@Schema(description = "测试名单的传输对象")
public class TestDTO extends BaseDTO {

    @Schema(description = "测试名单名称", maxLength = 32)
    @NotBlank(groups = Add.class, message = "测试名单名称不能为空！")
    @TextLength(min = 1, max = 32, groups = {Add.class, Update.class}, message = "测试名单名称不能为空字符串且最大长度为32！")
    private String name;

    @Schema(description = "测试名单所属项目")
    @Column(nullable = false)
    @NotEmpty(groups = Add.class, message = "测试名单所属项目不能为空！")
    private List<Long> projects;

    @Schema(description = "测试名单所属项目，用于查询")
    private Long projectId;

    @Schema(description = "状态（0：禁用，1：启用，3：删除）")
    private Status status;

    @Schema(description = "描述信息", maxLength = 1024)
    @TextLength(max = 1024, groups = {Add.class, Update.class}, message = "描述信息的最大长度为1024！")
    private String description;

    @JsonIgnore
    @Schema(hidden = true)
    private Long patientId;

    @JsonIgnore
    @Schema(hidden = true)
    private boolean patientEquals;
}
