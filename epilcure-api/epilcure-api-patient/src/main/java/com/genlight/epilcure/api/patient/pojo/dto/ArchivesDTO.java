package com.genlight.epilcure.api.patient.pojo.dto;

import com.fasterxml.jackson.annotation.JsonView;
import com.genlight.epilcure.api.patient.enums.ArchivesStatus;
import com.genlight.epilcure.api.patient.enums.IdCardType;
import com.genlight.epilcure.api.patient.pojo.dto.PatientDTO.EpilcureAddPatient;
import com.genlight.epilcure.common.core.constants.RegexConstants;
import com.genlight.epilcure.common.core.dao.enums.Status;
import com.genlight.epilcure.common.core.pojo.dto.BaseDTO;
import com.genlight.epilcure.common.core.pojo.valid.Add;
import com.genlight.epilcure.common.core.pojo.valid.Find;
import com.genlight.epilcure.common.core.pojo.valid.Update;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;
import org.hibernate.validator.constraints.Length;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Getter
@Setter
@SuperBuilder
@NoArgsConstructor
@Schema(description = "档案信息")
public class ArchivesDTO extends BaseDTO {
    @Schema(description = "档案编号")
    @NotNull(message = "编号不允许为空", groups = {EpilcureAddPatient.class})
    @Pattern(regexp = RegexConstants.EPILEPSY_ARCHIVES, groups = EpilcureAddPatient.class)
    @JsonView({Find.class, EpilcureAddPatient.class})
    private String archivesNo;

    @Schema(description = "状态")
    @JsonView(Find.class)
    private ArchivesStatus status;

    @Schema(description = "编辑状态")
    @JsonView({Find.class, Update.class})
    private Status editStatus;

    @Schema(description = "不等于的状态")
    @JsonView(Find.class)
    private ArchivesStatus noStatus;

    @Schema(description = "疾病Id")
    @JsonView(Find.class)
    private Long diseaseId;

    @Schema(description = "患者名称")
    @JsonView(Find.class)
    private String patientName;

    @Schema(description = "患者名称缩写")
    @JsonView(Find.class)
    private String pyName;

    @Schema(description = "患者Id")
    @JsonView(Find.class)
    private Long patientId;

    @Schema(description = "证件类型")
    @JsonView(Find.class)
    private IdCardType idCardType;
    @Schema(description = "证件ID")
    @JsonView(Find.class)
    private String idCard;

    @NotNull(message = "医院Id不能为空", groups = {Add.class, EpilcureAddPatient.class})
    @Schema(description = "医院Id")
    @JsonView({Add.class, Find.class, EpilcureAddPatient.class})
    private Long orgId;

    @NotNull(message = "科室Id不能为空", groups = {Add.class, EpilcureAddPatient.class})
    @Schema(description = "科室Id")
    @JsonView({Add.class, Find.class, EpilcureAddPatient.class})
    private Long deptId;

    @Length(min = 1, max = 16, message = "病案号长度必须为{min}~{max}")
    @Schema(description = "病案号")
    private String medicalRecordNo;

    @Schema(description = "是否查询有程控记录的患者")
    @JsonView(Find.class)
    private Boolean findHasScheme;

    @Schema(description = "开始时间")
    @JsonView(Find.class)
    private Date startDate;

    @Schema(description = "结束时间")
    @JsonView(Find.class)
    private Date endDate;

    @Schema(description = "排序时间")
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME, pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonView(Find.class)
    private Date startSortTime;

    @Schema(description = "排序结束时间")
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME, pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonView(Find.class)
    private Date endSortTime;

    @Schema(description = "备注")
    private String remark;
}
