package com.genlight.epilcure.api.patient.feign;

import com.genlight.epilcure.api.patient.pojo.dto.PatientDeviceDTO;
import com.genlight.epilcure.api.patient.pojo.vo.PatientDeviceVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(value = "patient-service", contextId = "feignPatientDeviceController")
public interface IPatientDeviceController {
    @GetMapping("/feign/patientDevices/patients/{id}")
    PatientDeviceVO findByPatientId(@PathVariable Long id);

    @PutMapping("/feign/patientDevices/{id}")
    PatientDeviceVO updateBpp(@PathVariable Long id, @RequestBody PatientDeviceDTO patientDeviceDTO);
}
