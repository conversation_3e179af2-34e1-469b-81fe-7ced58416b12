package com.genlight.epilcure.api.patient.pojo.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.genlight.epilcure.common.core.pojo.dto.BaseDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

@Getter
@Setter
@SuperBuilder
@NoArgsConstructor
@Schema(description = "合同电极信息")
public class ContractElectrodeModelDTO extends BaseDTO {
    @NotNull(message = "型号Id不能为空")
    @Schema(description = "电极型号")
    private Long electrodeModelId;

    @NotNull(message = "通道不能为空")
    @Schema(description = "通道")
    private Integer position;

    @JsonIgnore
    private boolean used;
}
