package com.genlight.epilcure.api.patient.pojo.dto;

import com.fasterxml.jackson.annotation.JsonView;
import com.genlight.epilcure.api.patient.pojo.dto.PatientDTO.EpilcureAddPatient;
import com.genlight.epilcure.common.core.dao.enums.Status;
import com.genlight.epilcure.common.core.pojo.dto.BaseDTO;
import com.genlight.epilcure.common.core.pojo.valid.Add;
import com.genlight.epilcure.common.core.pojo.valid.Find;
import com.genlight.epilcure.common.core.pojo.valid.Update;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Null;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import org.hibernate.validator.constraints.Length;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/6/12 13:45
 * @Version 1.0.0
 **/
@Data
@EqualsAndHashCode(callSuper = true)
@SuperBuilder
@NoArgsConstructor
@Schema(description = "患者病历信息")
public class MedicalRecordDTO extends BaseDTO {

    @Schema(description = "患者Id")
    @JsonView({Add.class, Update.class})
    @NotNull(message = "患者Id不能为空", groups = {Add.class, Update.class})
    private Long patientId;

    @Schema(description = "档案Id")
    @JsonView({Add.class, Update.class})
    @NotNull(message = "档案id不能为空", groups = {Add.class, Update.class})
    private Long archivesId;

    @Schema(description = "病案号")
    private String medicalRecordNo;

    @Schema(description = "复诊时间")
    private Date scheduledDate;

    @Schema(description = "开机日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date powerUpDate;

    @Schema(description = "服药史备注")
    @Length(max = 2048, message = "服药史备注长度范围{min}~{max}")
    private String drugsRemark;

    @Schema(description = "病历录入者")
    @Null(message = "录入者由系统自动分配", groups = {Add.class, EpilcureAddPatient.class})
    @JsonView(Find.class)
    private Long userId;

    @Schema(description = "疾病Id")
    @NotNull(message = "疾病Id不能为空", groups = Add.class)
    private Long diseaseId;

    @Schema(description = "手术备注")
    private String surgeryRemark;

    @Schema(description = "疾病史备注")
    private String diseaseRemark;

    @Schema(description = "病程")
    private Integer durationDisease;


    @Schema(description = "状态")
    @JsonView(Find.class)
    private Status status;

    @Schema(description = "患者服药史")
    private List<PatientDrugsDTO> patientDrugs;

    @Schema(description = "患者病因")
    private List<PatientCauseDTO> patientCauses;

    @Schema(description = "患者症状")
    private List<PatientSymptomDTO> patientSymptoms;

    @Schema(description = "患者症状类型")
    private List<PatientSymptomTypeDTO> patientSymptomTypes;

    @Schema(description = "患者结论")
    private List<PatientDiagnosisDTO> patientDiagnosis;
}
