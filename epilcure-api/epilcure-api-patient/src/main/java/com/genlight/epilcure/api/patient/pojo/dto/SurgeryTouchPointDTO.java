package com.genlight.epilcure.api.patient.pojo.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.genlight.epilcure.common.core.pojo.dto.BaseDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Null;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

import java.util.List;

@Getter
@Setter
@SuperBuilder
@NoArgsConstructor
@Schema(name = "SurgeryTouchPointDTO", description = "手术触点检测")
public class SurgeryTouchPointDTO extends BaseDTO {
    @Schema(description = "阻抗")
    @NotNull(message = "阻抗值不能为空")
    private Long impedance;
    @Schema(description = "是否正常")
    @NotNull(message = "阻抗判定不能为空")
    private Boolean result;
    @Schema(description = "是否缝合前")
    @NotNull(message = "是否缝合信息不能为空")
    private Boolean before;
    @Schema(description = "通道")
    @NotNull(message = "通道不能为空")
    private Integer position;
    @Schema(description = "极性")
    @NotNull(message = "极性配置不能为空")
    @Size(min = 4, max = 5, message = "极性长度为{min}~{max}")
    private List<Integer> polarity;

    @Null(message = "请通过上传接口传递图像数据")
    @Schema(description = "脑电图片路径")
    @JsonIgnore
    private String eegFile;
}
