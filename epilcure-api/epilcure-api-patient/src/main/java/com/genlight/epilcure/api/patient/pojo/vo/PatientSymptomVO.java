package com.genlight.epilcure.api.patient.pojo.vo;

import com.genlight.epilcure.common.core.pojo.vo.BaseVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR>
 * @Date 2022/6/12 13:31
 * @Version 1.0.0
 **/
@Data
@SuperBuilder
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Schema(description = "患者症状View Object")
public class PatientSymptomVO extends BaseVO {
    @Schema(description = "症状Id")
    private Long symptomId;
}
