package com.genlight.epilcure.api.patient.pojo.vo;


import com.genlight.epilcure.common.core.pojo.vo.BaseVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/6/11 19:00
 * @Version 1.0.0
 **/
@Data
@SuperBuilder
@NoArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@Schema(description = "病历信息View Object")
public class MedicalRecordVO extends BaseVO {
    @Schema(description = "复诊时间")
    private Date scheduledDate;

    @Schema(description = "开机日期")
    @DateTimeFormat(style = "yyyy-MM-dd HH:mm:ss")
    private Date powerUpDate;

    @Schema(description = "服药史备注")
    private String drugsRemark;

    @Schema(description = "疾病Id")
    private Long diseaseId;

    @Schema(description = "手术备注")
    private String surgeryRemark;

    @Schema(description = "患者服药史")
    private List<PatientDrugsVO> patientDrugs;

    @Schema(description = "患者症状类型")
    private List<PatientSymptomTypeVO> patientSymptomTypes;

    @Schema(description = "患者症状")
    private List<PatientSymptomVO> patientSymptoms;

    @Schema(description = "患者结论")
    private List<PatientDiagnosisVO> patientDiagnosis;

    @Schema(description = "患者病因")
    private List<PatientCauseVO> patientCauses;

    @Schema(description = "疾病史备注")
    private String diseaseRemark;

    @Schema(description = "病程")
    private Integer durationDisease;
}
