package com.genlight.epilcure.api.patient.pojo.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.genlight.epilcure.common.core.dao.generator.annotation.SignOrder;
import com.genlight.epilcure.common.core.pojo.vo.BaseVO;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.Column;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;
import org.hibernate.annotations.Comment;

import java.util.Date;

@Data
@SuperBuilder
@NoArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@Schema(name = "SurgerElectrodeVO", description = "手术电极信息View Object")
public class SurgeryElectrodeVO extends BaseVO {
    @Schema(description = "刺激器Id")
    private Long electrodeId;

    @Schema(description = "电极签名")
    private String electrodeSn;

    @Schema(description = "通道")
    private Integer position;
    @Schema(description = "植入位置")
    private String location;
    @Schema(description = "靶点")
    private String target;

    @JsonIgnore
    private boolean isUse;

    @Schema(description = "植入时间")
    private Date implantationDate;
}
