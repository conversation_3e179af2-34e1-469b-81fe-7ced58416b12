package com.genlight.epilcure.api.patient.pojo.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonView;
import com.genlight.epilcure.common.core.pojo.dto.BaseDTO;
import com.genlight.epilcure.common.core.pojo.valid.Add;
import com.genlight.epilcure.common.core.pojo.valid.Find;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Null;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;
import org.hibernate.validator.constraints.Length;

import java.util.Date;

@Getter
@Setter
@SuperBuilder
@NoArgsConstructor
@Schema(name = "PatientSurgerySchemeDTO", description = "患者手术计划")
public class PatientSurgerySchemeDTO extends BaseDTO {
    @Schema(description = "评估医生")
    @Length(min = 0, max = 255, message = "评估医生名称长度范围{min}~{max}")
    private String schemeName;
    @Schema(description = "患者Id")
    @JsonView(Add.class)
    @NotNull(message = "患者Id不能为空", groups = Add.class)
    private Long patientId;

    @Schema(description = "档案Id")
    @NotNull(message = "档案Id不能为空")
    @JsonView(Add.class)
    private Long archivesId;
    @Schema(description = "手术医生")
    @NotNull(message = "手术医生不能为空")
    private Long userId;
    @Schema(description = "手术医生名称")
    @Null(message = "手术医生名称由系统分配")
    @JsonView(Find.class)
    private String userName;
    @JsonIgnore
    private String userPhone;
    @JsonIgnore
    private Long orgId;
    @JsonIgnore
    private String orgName;
    @JsonIgnore
    private Long deptId;
    @JsonIgnore
    private String deptName;
    @Schema(name = "工程师名称")
    @JsonView(Find.class)
    @Null(message = "工程师名称又系统分配")
    private String adviserName;
    @Schema(description = "手术时间")
    private Date surgeryDate;
    @Schema(description = "工程师Id")
    @JsonView(Find.class)
    @Null(message = "工程师Id由系统分配")
    private Long adviserId;
}
