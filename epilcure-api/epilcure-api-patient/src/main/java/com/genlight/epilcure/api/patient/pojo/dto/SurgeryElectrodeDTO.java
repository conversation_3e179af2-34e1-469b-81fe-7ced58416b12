package com.genlight.epilcure.api.patient.pojo.dto;

import com.fasterxml.jackson.annotation.JsonView;
import com.genlight.epilcure.api.patient.pojo.dto.PatientDeviceDTO.AddDevice;
import com.genlight.epilcure.common.core.pojo.dto.BaseDTO;
import com.genlight.epilcure.common.core.pojo.valid.Add;
import com.genlight.epilcure.common.core.pojo.valid.Find;
import com.genlight.epilcure.common.core.pojo.valid.NotNullIf;
import com.genlight.epilcure.common.core.pojo.valid.Update;
import com.genlight.epilcure.common.core.pojo.view.SummaryView;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Null;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;
import org.hibernate.validator.constraints.Length;

import static com.genlight.epilcure.api.patient.pojo.dto.PatientDeviceDTO.AddEle;

@Getter
@Setter
@SuperBuilder
@NoArgsConstructor
@NotNullIf(checkField = "target", dependField = "targetIndex", message = "靶点不能为空")
@NotNullIf(checkField = "targetIndex", dependField = "target", message = "靶点能为空")
@Schema(description = "手术信息电极参数")
public class SurgeryElectrodeDTO extends BaseDTO {

    @JsonView(Update.class)
    private Long id;

    @NotNull(message = "电极id不能为空", groups = {Add.class})
    @JsonView({Add.class, Update.class})
    @Schema(description = "刺激器Id")
    private Long electrodeId;

    @JsonView({Find.class, AddEle.class, AddDevice.class, SummaryView.class, Update.class})
    @Null(message = "电极签名由系统分配", groups = Add.class)
    @NotNull(message = "电极签名不能为空", groups = {AddEle.class, AddDevice.class, SummaryView.class})
    @Schema(description = "电极签名")
    private String electrodeSn;

    @Schema(description = "通道")
    @NotNull(message = "通道不能为空")
    private Integer position;
    
    @Schema(description = "植入位置")
    private String location;

    @Schema(description = "靶点")
    @Length(min = 1, max = 8, message = "靶点位置长度为{min}~{max}")
    private String target;

    @Schema(description = "植入位置")
    @NotNull(message = "植入位置不能为空")
    @Min(value = 0, message = "植入位置必须>=0")
    private Integer locationIndex;
    @Schema(description = "靶点")
    @Min(value = 0, message = "靶点必须>=0")
    private Integer targetIndex;
}
