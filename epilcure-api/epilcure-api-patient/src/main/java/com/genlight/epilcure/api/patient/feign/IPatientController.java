package com.genlight.epilcure.api.patient.feign;

import com.genlight.epilcure.api.patient.pojo.vo.PatientVO;
import io.swagger.v3.oas.annotations.Hidden;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

@FeignClient(value = "patient-service", contextId = "feignPatientController")
@Hidden
public interface IPatientController {
    @GetMapping("/feign/patient/patients/findByMobile/{mobile}")
    PatientVO findByMobile(@PathVariable String mobile);

    @GetMapping("/feign/patient/patients/{id}")
    PatientVO findById(@PathVariable Long id);

    @GetMapping("/feign/patient/patients/by/{cardId}")
    Long findIdByCardId(@PathVariable String cardId);
}
