package com.genlight.epilcure.api.patient.pojo.vo;

import com.genlight.epilcure.common.core.pojo.vo.BaseVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR> maoyan
 * @Date : 2022/8/5 16:59
 * @Version :1.0.0
 */
@Data
@SuperBuilder
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Schema(description = "患者症状类型View Object")
public class PatientSymptomTypeVO extends BaseVO {
    @Schema(description = "症状类型Id")
    private Long symptomTypeId;
}
