package com.genlight.epilcure.api.patient.pojo.dto;

import com.genlight.epilcure.common.core.pojo.dto.BaseDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @Date 2022/6/12 14:23
 * @Version 1.0.0
 **/
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "患者结论")
public class PatientDiagnosisDTO extends BaseDTO {
    @Schema(description = "结论Id")
    private Long diagnosisId;
}
