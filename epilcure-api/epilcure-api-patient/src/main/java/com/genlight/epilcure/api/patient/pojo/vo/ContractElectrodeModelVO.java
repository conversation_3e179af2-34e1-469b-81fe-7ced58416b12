package com.genlight.epilcure.api.patient.pojo.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.genlight.epilcure.common.core.pojo.vo.BaseVO;
import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;

@Data
@SuperBuilder
@NoArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@Schema(name = "ContractElectrodeModelVO", description = "合同电极型号View Object")
public class ContractElectrodeModelVO extends BaseVO {
    @Schema(description = "电极型号Id")
    private Long electrodeModelId;
    @Schema(description = "电极型号Id")
    private byte position;

    @Hidden
    @JsonIgnore
    private boolean isUsed;
}
