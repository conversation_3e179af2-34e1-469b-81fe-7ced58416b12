package com.genlight.epilcure.service.iScheme.pojo.convert;

import com.genlight.epilcure.common.core.pojo.convert.BaseConvert;
import com.genlight.epilcure.common.core.pojo.convert.qualified.Basic;
import com.genlight.epilcure.common.core.pojo.convert.qualified.Summary;
import com.genlight.epilcure.service.iScheme.dao.entity.InductionGroup;
import com.genlight.epilcure.service.iScheme.pojo.dto.InductionGroupDTO;
import com.genlight.epilcure.service.iScheme.pojo.vo.InductionGroupVO;
import org.mapstruct.BeanMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.NullValueCheckStrategy;

import java.util.List;

@Mapper(config = BaseConvert.class, uses = InductionPulseConvert.class)
public interface InductionGroupConvert extends BaseConvert<InductionGroup, InductionGroupVO, InductionGroupDTO> {

    @Mapping(target = "inductionPulses", ignore = true)
    @BeanMapping(nullValueCheckStrategy = NullValueCheckStrategy.ON_IMPLICIT_CONVERSION)
    @Override
    InductionGroup dto2po(InductionGroupDTO inductionGroupDTO);

    @Basic
    @Mapping(target = "inductionPulses", ignore = true)
    @BeanMapping(nullValueCheckStrategy = NullValueCheckStrategy.ON_IMPLICIT_CONVERSION)
    @Override
    InductionGroupVO po2vo(InductionGroup inductionGroup);

    @Summary
    @BeanMapping(nullValueCheckStrategy = NullValueCheckStrategy.ON_IMPLICIT_CONVERSION)
    InductionGroupVO po2voSummary(InductionGroup inductionGroup);

    @Basic
    @Override
    List<InductionGroupVO> po2vo(List<InductionGroup> inductionGroups);
}
