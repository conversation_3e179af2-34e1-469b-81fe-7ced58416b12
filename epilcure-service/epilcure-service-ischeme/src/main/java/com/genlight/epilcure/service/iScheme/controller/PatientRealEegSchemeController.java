package com.genlight.epilcure.service.iScheme.controller;

import com.fasterxml.jackson.annotation.JsonView;
import com.genlight.epilcure.common.core.mvc.response.JsonResult;
import com.genlight.epilcure.common.core.pojo.valid.Add;
import com.genlight.epilcure.service.iScheme.pojo.dto.PatientRealEegSchemeDTO;
import com.genlight.epilcure.service.iScheme.pojo.vo.PatientRealEegSchemeVO;
import com.genlight.epilcure.service.iScheme.service.PatientRealEegSchemeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/iScheme/patientRealEegSchemes")
@Tag(name = "ISBSPatientRealEegSchemeController", description = "患者实时采集方案相关接口")
public class PatientRealEegSchemeController {
    @Resource
    private PatientRealEegSchemeService patientRealEegSchemeService;

    @PostMapping
    @Operation(summary = "添加实时采集方案")
    @JsonView(Add.class)
    public JsonResult<PatientRealEegSchemeVO> add(@Valid @RequestBody PatientRealEegSchemeDTO patientRealEegSchemeDTO) {
        return JsonResult.ok(patientRealEegSchemeService.add(patientRealEegSchemeDTO));
    }

    @GetMapping("/patient/{id}")
    @Operation(summary = "根据患者查询当前最新的实时采集方案")
    public JsonResult<PatientRealEegSchemeVO> findByPatient(@PathVariable Long id) {
        return JsonResult.ok(patientRealEegSchemeService.findByPatientId(id));
    }

    @GetMapping("/{id}")
    @Operation(summary = "根据Id获取采集方案详细信息")
    public JsonResult<PatientRealEegSchemeVO> find(@PathVariable String id) {
        return JsonResult.ok(patientRealEegSchemeService.findById(id));
    }

    @GetMapping
    @Operation(summary = "根据条件查询患者实时采集方案")
    public JsonResult<Page<PatientRealEegSchemeVO>> finds(PatientRealEegSchemeDTO patientRealEegSchemeDTO, Pageable pageable) {
        return JsonResult.ok(patientRealEegSchemeService.finds(patientRealEegSchemeDTO, pageable));
    }
}
