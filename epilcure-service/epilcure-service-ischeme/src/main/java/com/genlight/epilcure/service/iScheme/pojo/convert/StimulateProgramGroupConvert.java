package com.genlight.epilcure.service.iScheme.pojo.convert;

import com.genlight.epilcure.common.core.pojo.convert.BaseConvert;
import com.genlight.epilcure.service.iScheme.dao.entity.StimulateProgramGroup;
import com.genlight.epilcure.service.iScheme.pojo.dto.StimulateProgramGroupDTO;
import com.genlight.epilcure.service.iScheme.pojo.vo.StimulateProgramGroupVO;
import org.mapstruct.BeanMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.NullValueCheckStrategy;

@Mapper(config = BaseConvert.class)
public interface StimulateProgramGroupConvert extends BaseConvert<StimulateProgramGroup, StimulateProgramGroupVO, StimulateProgramGroupDTO> {
    @Mapping(target = "active", expression = "java(java.util.Optional.ofNullable(stimulateProgramGroupDTO.getActive()).orElse(false))")
    @BeanMapping(nullValueCheckStrategy = NullValueCheckStrategy.ON_IMPLICIT_CONVERSION)
    @Override
    StimulateProgramGroup dto2po(StimulateProgramGroupDTO stimulateProgramGroupDTO);
}
