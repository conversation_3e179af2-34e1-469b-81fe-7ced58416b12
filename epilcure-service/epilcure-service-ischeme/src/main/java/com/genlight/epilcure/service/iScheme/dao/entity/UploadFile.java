package com.genlight.epilcure.service.iScheme.dao.entity;

import com.genlight.epilcure.common.core.dao.entity.TEntity;
import com.genlight.epilcure.common.core.dao.generator.annotation.SignOrder;
import com.genlight.epilcure.service.iScheme.constants.enums.FileStatus;
import com.genlight.epilcure.service.iScheme.constants.enums.FileType;
import jakarta.persistence.*;
import lombok.*;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;
import org.hibernate.annotations.Comment;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

/**
 * <AUTHOR>
 * @version 1.0.0
 */
@Setter
@Getter
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString
@Entity
@Cacheable
@DynamicInsert
@DynamicUpdate
@SignOrder(0)
@Table(name = "t_file")
public class UploadFile extends TEntity {

    @Comment("文件上传者")
    @Column
    @SignOrder(1)
    private Long userId;

    @Comment("文件名称")
    @Column(nullable = false)
    @SignOrder(2)
    private String name;

    @Comment("文件地址")
    @Column(nullable = false, unique = true)
    @SignOrder(3)
    private String url;

    @Comment("文件类型")
    @Column(nullable = false)
    @Enumerated(EnumType.ORDINAL)
    @SignOrder(4)
    private FileType type;

    @Comment("文件状态")
    @Column(nullable = false)
    @Enumerated(EnumType.ORDINAL)
    @SignOrder(5)
    private FileStatus status;

    @Comment("描述信息")
    @Column(columnDefinition = "TEXT")
    @SignOrder(9)
    private String description;

    @Column
    @SignOrder(11)
    @Comment("患者ID")
    private Long patientId;

}
