package com.genlight.epilcure.service.iScheme.dao.entity;


import com.genlight.epilcure.common.core.dao.generator.annotation.SignOrder;
import jakarta.persistence.*;
import lombok.*;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;
import org.hibernate.annotations.Comment;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

@Getter
@Setter
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString
@Entity
@Cacheable
@DynamicInsert
@DynamicUpdate
@SignOrder(0)
@Table(name = "t_induction_pulse")
public class InductionPulse extends StimulatePulseBase {

    @Comment("计算最小频段")
    private Integer calMinInduction;

    @Comment("计算最大频段")
    private Integer calMaxInduction;

    @OneToOne(cascade = CascadeType.ALL)
    private TouchPoint inductionTouchPoint;

    @Comment("最小频段")
    @Column(nullable = false)
    private Integer minInduction;

    @Comment("最大频段")
    @Column(nullable = false)
    private Integer maxInduction;

    @Comment("阈值")
    @Column
    private Float threshold;
}
