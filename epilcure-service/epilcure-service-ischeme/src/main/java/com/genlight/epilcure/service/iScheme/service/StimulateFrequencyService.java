package com.genlight.epilcure.service.iScheme.service;

import com.genlight.epilcure.api.patient.feign.IPatientDeviceController;
import com.genlight.epilcure.api.patient.pojo.vo.PatientDeviceVO;
import com.genlight.epilcure.common.core.service.BaseService;
import com.genlight.epilcure.common.core.service.exception.ArgsException;
import com.genlight.epilcure.service.iScheme.constants.CacheConstants;
import com.genlight.epilcure.service.iScheme.dao.entity.Scheme;
import com.genlight.epilcure.service.iScheme.dao.entity.StimulateFrequency;
import com.genlight.epilcure.service.iScheme.dao.repository.StimulateFrequencyRepository;
import com.genlight.epilcure.service.iScheme.pojo.convert.StimulateFrequencyConvert;
import com.genlight.epilcure.service.iScheme.pojo.dto.StimulateFrequencyDTO;
import com.genlight.epilcure.service.iScheme.pojo.vo.SchemeVO;
import com.genlight.epilcure.service.iScheme.pojo.vo.StimulateFrequencyVO;
import jakarta.annotation.Resource;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;

@Service
@CacheConfig(cacheNames = CacheConstants.CACHE_NAMES_STIMULATE_FREQUENCY)
public class StimulateFrequencyService extends BaseService<StimulateFrequency, Long, StimulateFrequencyRepository> {
    @Resource
    private StimulateFrequencyConvert stimulateFrequencyConvert;

    @Resource
    private SchemeService schemeService;
    @Resource
    private IPatientDeviceController iPatientDeviceController;

    @Transactional
    public StimulateFrequencyVO add(StimulateFrequencyDTO stimulateFrequencyDTO, SchemeVO schemeVO, PatientDeviceVO patientDeviceVO) {
        // 检测电极
        stimulateFrequencyDTO.getStimulatePulses().forEach(sps -> {
            if (Arrays.stream(sps.getTouchPoint().getPolarity()).filter(p -> p == 1).findAny().isEmpty()) {
                throw new ArgsException("通道[{0}]至少配置一个正极触点", sps.getTouchPoint().getPosition());
            }

            if (Arrays.stream(sps.getTouchPoint().getPolarity()).filter(p -> p == 2).findAny().isEmpty()) {
                throw new ArgsException("通道[{0}]至少配置一个负极极触点", sps.getTouchPoint().getPosition());
            }

            if (patientDeviceVO.getSurgeryElectrodes().stream().filter(s -> s.getPosition().equals(sps.getTouchPoint().getPosition())).findAny().isEmpty()) {
                throw new ArgsException("通道[{0}]没有设备", sps.getTouchPoint().getPosition());
            }
        });

        stimulateFrequencyDTO.setUserId(getUserId());
        stimulateFrequencyDTO.setUserName(getNikeName());
        stimulateFrequencyDTO.setUserPhone(getUserName());

        StimulateFrequency stimulateFrequency = stimulateFrequencyConvert.dto2po(stimulateFrequencyDTO);
        stimulateFrequency.setScheme(Scheme.builder().id(schemeVO.getId()).build());
        stimulateFrequency = repository.save(stimulateFrequency);
        return stimulateFrequencyConvert.po2vo(stimulateFrequency);
    }
}
