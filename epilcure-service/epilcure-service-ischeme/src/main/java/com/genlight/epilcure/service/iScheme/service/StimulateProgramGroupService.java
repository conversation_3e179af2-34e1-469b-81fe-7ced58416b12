package com.genlight.epilcure.service.iScheme.service;

import com.genlight.epilcure.api.device.feign.IElectrodeModelController;
import com.genlight.epilcure.api.patient.feign.IPatientDeviceController;
import com.genlight.epilcure.api.patient.pojo.vo.ContractElectrodeModelVO;
import com.genlight.epilcure.api.patient.pojo.vo.PatientDeviceVO;
import com.genlight.epilcure.api.patient.pojo.vo.SurgeryElectrodeVO;
import com.genlight.epilcure.common.core.service.BaseService;
import com.genlight.epilcure.common.core.service.exception.ArgsException;
import com.genlight.epilcure.service.iScheme.constants.CacheConstants;
import com.genlight.epilcure.service.iScheme.dao.entity.Scheme;
import com.genlight.epilcure.service.iScheme.dao.entity.StimulateProgramGroup;
import com.genlight.epilcure.service.iScheme.dao.repository.StimulateProgramGroupRepository;
import com.genlight.epilcure.service.iScheme.pojo.convert.StimulateProgramGroupConvert;
import com.genlight.epilcure.service.iScheme.pojo.dto.StimulateProgramGroupDTO;
import com.genlight.epilcure.service.iScheme.pojo.vo.SchemeVO;
import com.genlight.epilcure.service.iScheme.pojo.vo.StimulateProgramGroupVO;
import jakarta.annotation.Resource;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.annotation.Caching;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

@Service
@CacheConfig(cacheNames = CacheConstants.CACHE_NAMES_STIMULATE_GROUP)
public class StimulateProgramGroupService extends BaseService<StimulateProgramGroup, Long, StimulateProgramGroupRepository> {
    @Resource
    private StimulateTimeService stimulateTimeService;
    @Resource
    private StimulateProgramGroupConvert stimulateProgramGroupConvert;

    @Resource
    private IPatientDeviceController iPatientDeviceController;

    @Resource
    private IElectrodeModelController iElectrodeModelController;

    @Resource
    private SchemeService schemeService;

    @Transactional
    @Caching(evict = {
            @CacheEvict(cacheNames = CacheConstants.CACHE_NAMES_STIMULATE_GROUP_FINDS, allEntries = true)
    })
    public List<StimulateProgramGroupVO> addProgramGroup(List<StimulateProgramGroupDTO> stimulateProgramGroupDTOS, SchemeVO scheme, PatientDeviceVO patientDeviceVO) {
        Map<String, StimulateProgramGroupVO> resultMap = new HashMap<>();
        stimulateProgramGroupDTOS.forEach(stimulateProgramGroupDTO -> {
            if (resultMap.containsKey(stimulateProgramGroupDTO.getNo())) {
                throw new ArgsException("程序[{0}]已存在", stimulateProgramGroupDTO.getNo());
            }

            if (Objects.isNull(patientDeviceVO)) {
                throw new ArgsException("患者[{0}]设备信息为空", stimulateProgramGroupDTO.getPatientId());
            }
            // 检测电极
            stimulateProgramGroupDTO.getStimulatePulses().forEach(sps -> {

                if (Arrays.stream(sps.getTouchPoint().getPolarity()).filter(p -> p == 1).findAny().isEmpty()) {
                    throw new ArgsException("通道[{0}]至少配置一个正极触点", sps.getTouchPoint().getPosition());
                }

                if (Arrays.stream(sps.getTouchPoint().getPolarity()).filter(p -> p == 2).findAny().isEmpty()) {
                    throw new ArgsException("通道[{0}]至少配置一个负极触点", sps.getTouchPoint().getPosition());
                }

                Optional<SurgeryElectrodeVO> optional = patientDeviceVO.getSurgeryElectrodes().stream().filter(s -> s.getPosition().equals(sps.getTouchPoint().getPosition())).findAny();
                if (optional.isEmpty()) {
                    throw new ArgsException("通道[{0}]没有设备", sps.getTouchPoint().getPosition());
                }
                switch (sps.getElectricType()) {

                    case ConstantCurrent -> {

                        Optional<ContractElectrodeModelVO> modelVOOptional = patientDeviceVO.getContractElectrodeModels().stream().filter(s -> optional.get().getPosition().equals(sps.getTouchPoint().getPosition())).findFirst();
//                        ElectrodeModelVO electrodeModelVO = iElectrodeModelController.find(modelVOOptional.get().getElectrodeModelId());

                        if (Objects.isNull(sps.getElectric())) {
                            throw new ArgsException("恒流模式电流不能为空");
                        }

//                        float v = sps.getElectric() * sps.getPulseWidth() / (10 * electrodeModelVO.getTouchArea());
//                        if (v >= 30 && !isEngineer()) {
//                            throw new ArgsException("电荷密度[{0}]过高", v);
//                        }
//                        break;
                    }
                    case ConstantVoltage -> {
                        if (Objects.isNull(sps.getVoltage())) {
                            throw new ArgsException("恒压模式电压不能为空");
                        }
                    }
                }
            });

            stimulateProgramGroupDTO.setUserId(getUserId());
            stimulateProgramGroupDTO.setUserName(getNikeName());
            stimulateProgramGroupDTO.setUserPhone(getUserName());


            StimulateProgramGroup stimulateProgramGroup = stimulateProgramGroupConvert.dto2po(stimulateProgramGroupDTO);
            stimulateProgramGroup.setScheme(Scheme.builder().id(scheme.getId()).build());

            stimulateProgramGroup = repository.save(stimulateProgramGroup);


            StimulateProgramGroupVO stimulateProgramGroupVO = stimulateProgramGroupConvert.po2vo(stimulateProgramGroup);
            resultMap.put(stimulateProgramGroupVO.getNo(), stimulateProgramGroupVO);

        });

        return resultMap.values().stream().toList();
    }

    @Transactional(readOnly = true)
    @Cacheable(key = "#p0+#p1")
    public StimulateProgramGroupVO findById(Long id) {

        Optional<StimulateProgramGroup> optional = repository.findById(id);
        if (optional.isEmpty()) {
            throw new ArgsException("程序组[{0}]不存在", id);
        }
        return stimulateProgramGroupConvert.po2vo(optional.get());
    }
}
