package com.genlight.epilcure.service.iScheme.controller;


import com.genlight.epilcure.common.core.mvc.response.JsonResult;
import com.genlight.epilcure.service.iScheme.pojo.dto.SchemeLogDTO;
import com.genlight.epilcure.service.iScheme.pojo.vo.SchemeLogVO;
import com.genlight.epilcure.service.iScheme.service.SchemeLogService;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/iScheme/scheme_log")
@Tag(name = "ISBSSchemeLogController", description = "方案运行日志接口")
public class SchemeLogController {

    @Resource
    private SchemeLogService schemeLogService;

    @GetMapping("/finds")
    public JsonResult<Page<SchemeLogVO>> finds(SchemeLogDTO schemeLogDTO, Pageable pageable) {
        return JsonResult.ok(schemeLogService.finds(schemeLogDTO, pageable));
    }
}
