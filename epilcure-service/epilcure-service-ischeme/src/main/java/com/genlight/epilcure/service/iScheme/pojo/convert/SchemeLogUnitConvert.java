package com.genlight.epilcure.service.iScheme.pojo.convert;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.genlight.epilcure.common.core.pojo.convert.BaseConvert;
import com.genlight.epilcure.service.iScheme.dao.entity.SchemeLogUnit;
import com.genlight.epilcure.service.iScheme.pojo.dto.SchemeLogUnitDTO;
import com.genlight.epilcure.service.iScheme.pojo.vo.SchemeLogUnitVO;
import jakarta.annotation.Resource;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.List;

@Mapper(config = BaseConvert.class)
public abstract class SchemeLogUnitConvert implements BaseConvert<SchemeLogUnit, SchemeLogUnitVO, SchemeLogUnitDTO> {

    @Resource
    protected ObjectMapper objectMapper;

    protected List<Float> convertListLong(String s) {
        try {
            return objectMapper.readValue(s, objectMapper.getTypeFactory().constructCollectionType(List.class, Float.class));
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }

    protected String convertString(List<Float> l) {
        try {
            return objectMapper.writeValueAsString(l);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }


    @Mapping(target = "values", expression = "java(convertString(schemeLogUnitDTO.getValues()))")
    @Mapping(target = "electric", expression = "java(convertString(schemeLogUnitDTO.getElectric()))")
    @Override
    public abstract SchemeLogUnit dto2po(SchemeLogUnitDTO schemeLogUnitDTO);

    @Mapping(target = "values", expression = "java(convertListLong(schemeLogUnit.getValues()))")
    @Mapping(target = "electric", expression = "java(convertListLong(schemeLogUnit.getElectric()))")
    @Override
    public abstract SchemeLogUnitVO po2vo(SchemeLogUnit schemeLogUnit);
}
