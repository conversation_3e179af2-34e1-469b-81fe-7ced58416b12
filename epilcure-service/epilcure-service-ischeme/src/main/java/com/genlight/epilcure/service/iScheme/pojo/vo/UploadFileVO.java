package com.genlight.epilcure.service.iScheme.pojo.vo;

import com.genlight.epilcure.common.core.pojo.vo.BaseVO;
import com.genlight.epilcure.service.iScheme.constants.enums.FileStatus;
import com.genlight.epilcure.service.iScheme.constants.enums.FileType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
@SuperBuilder
@NoArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@Schema(description = "上传的文件视图对象")
public class UploadFileVO extends BaseVO {

    @Schema(description = "文件上传者")
    private Long userId;

    @Schema(description = "文件名称")
    private String name;

    @Schema(description = "患者Id", hidden = true)
    private Long patientId;

    @Schema(description = "文件地址")
    private String url;

    @Schema(description = "文件类型")
    private FileType type;

    @Schema(description = "文件状态（0：已上传，1：解析成功，2：解析失败）")
    private FileStatus status;

    @Schema(description = "文件版本号")
    private String fileVersion;

    @Schema(description = "描述信息")
    private String description;
}
