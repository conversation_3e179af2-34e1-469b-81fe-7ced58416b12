package com.genlight.epilcure.service.iScheme.controller;


import com.genlight.epilcure.common.core.mvc.response.JsonResult;
import com.genlight.epilcure.service.iScheme.pojo.dto.SchemeLogUnitDTO;
import com.genlight.epilcure.service.iScheme.pojo.vo.SchemeLogUnitVO;
import com.genlight.epilcure.service.iScheme.service.SchemeLogUnitService;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/api/iScheme/scheme_log_unit")
@Tag(name = "ISBSSchemeLogUnitController", description = "方案运行数据单元接口")
public class SchemeLogUnitController {

    @Resource
    private SchemeLogUnitService schemeLogUnitService;

    @GetMapping
    public JsonResult<List<SchemeLogUnitVO>> finds(SchemeLogUnitDTO schemeLogUnitDTO) {
        return JsonResult.ok(schemeLogUnitService.finds(schemeLogUnitDTO));
    }
}
