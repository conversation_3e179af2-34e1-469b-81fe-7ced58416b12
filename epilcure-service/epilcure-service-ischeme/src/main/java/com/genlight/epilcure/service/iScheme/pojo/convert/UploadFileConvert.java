package com.genlight.epilcure.service.iScheme.pojo.convert;

import com.genlight.epilcure.common.core.pojo.convert.BaseConvert;
import com.genlight.epilcure.service.iScheme.dao.entity.UploadFile;
import com.genlight.epilcure.service.iScheme.pojo.dto.UploadFileDTO;
import com.genlight.epilcure.service.iScheme.pojo.vo.UploadFileVO;
import org.mapstruct.Mapper;

@Mapper(config = BaseConvert.class)
public interface UploadFileConvert extends BaseConvert<UploadFile, UploadFileVO, UploadFileDTO> {
}
