package com.genlight.epilcure.service.iScheme.pojo.convert;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.genlight.epilcure.common.core.pojo.convert.BaseConvert;
import com.genlight.epilcure.service.iScheme.dao.entity.SchemeLog;
import com.genlight.epilcure.service.iScheme.pojo.dto.SchemeLogDTO;
import com.genlight.epilcure.service.iScheme.pojo.vo.SchemeLogVO;
import jakarta.annotation.Resource;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.List;

@Mapper(config = BaseConvert.class)
public abstract class SchemeLogConvert implements BaseConvert<SchemeLog, SchemeLogVO, SchemeLogDTO> {

    @Resource
    protected ObjectMapper objectMapper;

    protected List<Long> convertListLong(String s) {
        try {
            return objectMapper.readValue(s, objectMapper.getTypeFactory().constructCollectionType(List.class, Long.class));
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }

    protected String convertString(List<Long> l) {
        try {
            return objectMapper.writeValueAsString(l);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }

    @Mapping(target = "groupA", expression = "java(convertString(schemeLogDTO.getGroupA()))")
    @Mapping(target = "groupB", expression = "java(convertString(schemeLogDTO.getGroupB()))")
    @Mapping(target = "groupC", expression = "java(convertString(schemeLogDTO.getGroupC()))")
    @Mapping(target = "groupD", expression = "java(convertString(schemeLogDTO.getGroupD()))")
    @Override
    public abstract SchemeLog dto2po(SchemeLogDTO schemeLogDTO);

    @Mapping(target = "groupA", expression = "java(convertListLong(schemeLog.getGroupA()))")
    @Mapping(target = "groupB", expression = "java(convertListLong(schemeLog.getGroupB()))")
    @Mapping(target = "groupC", expression = "java(convertListLong(schemeLog.getGroupC()))")
    @Mapping(target = "groupD", expression = "java(convertListLong(schemeLog.getGroupD()))")
    @Override
    public abstract SchemeLogVO po2vo(SchemeLog schemeLog);

}
