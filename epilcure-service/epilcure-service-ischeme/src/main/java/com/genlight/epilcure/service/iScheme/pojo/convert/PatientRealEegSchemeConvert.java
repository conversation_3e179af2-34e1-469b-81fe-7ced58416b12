package com.genlight.epilcure.service.iScheme.pojo.convert;

import com.genlight.epilcure.common.core.pojo.convert.BaseConvert;
import com.genlight.epilcure.service.iScheme.dao.entity.PatientRealEegScheme;
import com.genlight.epilcure.service.iScheme.pojo.dto.PatientRealEegSchemeDTO;
import com.genlight.epilcure.service.iScheme.pojo.vo.PatientRealEegSchemeVO;
import org.mapstruct.BeanMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.NullValueCheckStrategy;

@Mapper(config = BaseConvert.class)
public interface PatientRealEegSchemeConvert extends BaseConvert<PatientRealEegScheme, PatientRealEegSchemeVO, PatientRealEegSchemeDTO> {
    @Mapping(target = "status", expression = "java(java.util.Optional.ofNullable(patientRealEegSchemeDTO.getStatus()).orElse(com.genlight.epilcure.common.core.dao.enums.Status.ENABLED))")
    @BeanMapping(nullValueCheckStrategy = NullValueCheckStrategy.ON_IMPLICIT_CONVERSION)
    @Override
    PatientRealEegScheme dto2po(PatientRealEegSchemeDTO patientRealEegSchemeDTO);
}
