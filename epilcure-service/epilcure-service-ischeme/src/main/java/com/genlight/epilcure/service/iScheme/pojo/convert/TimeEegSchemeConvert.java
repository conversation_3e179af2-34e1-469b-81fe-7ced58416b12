package com.genlight.epilcure.service.iScheme.pojo.convert;

import com.genlight.epilcure.common.core.pojo.convert.BaseConvert;
import com.genlight.epilcure.service.iScheme.dao.entity.TimeEegScheme;
import com.genlight.epilcure.service.iScheme.pojo.dto.TimeEegSchemeDTO;
import com.genlight.epilcure.service.iScheme.pojo.vo.TimeEegSchemeVO;
import org.mapstruct.Mapper;
import org.mapstruct.Named;

@Named("TimeEegSchemeConvert")
@Mapper(config = BaseConvert.class)
public interface TimeEegSchemeConvert extends BaseConvert<TimeEegScheme, TimeEegSchemeVO, TimeEegSchemeDTO> {
}
