package com.genlight.epilcure.service.iScheme.pojo.dto;

import com.genlight.epilcure.common.core.pojo.dto.BaseDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;


@Data
@SuperBuilder
@NoArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@Schema(description = "运行日志传输对象")
public class SchemeLogDTO extends BaseDTO {
    @Schema(description = "方案Id")
    private String schemeId;

    @Schema(description = "患者Id")
    private Long patientId;

    @Schema(description = "通道状态")
    private Byte channel;

    @Schema(description = "结束时间")
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME, pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    @Schema(description = "开始时间")
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME, pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    @Schema(description = "A组执行次数,[0,1]")
    private List<Long> groupA;

    @Schema(description = "B组执行次数,[0,1]")
    private List<Long> groupB;
    @Schema(description = "C组执行次数,[0,1]")
    private List<Long> groupC;
    @Schema(description = "D组执行次数,[0,1]")
    private List<Long> groupD;
}
