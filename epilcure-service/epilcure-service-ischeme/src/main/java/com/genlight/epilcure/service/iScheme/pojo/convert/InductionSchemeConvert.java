package com.genlight.epilcure.service.iScheme.pojo.convert;

import com.genlight.epilcure.common.core.pojo.convert.BaseConvert;
import com.genlight.epilcure.common.core.pojo.convert.qualified.Basic;
import com.genlight.epilcure.common.core.pojo.convert.qualified.Summary;
import com.genlight.epilcure.service.iScheme.dao.entity.InductionScheme;
import com.genlight.epilcure.service.iScheme.pojo.dto.InductionSchemeDTO;
import com.genlight.epilcure.service.iScheme.pojo.vo.InductionSchemeVO;
import org.mapstruct.BeanMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.NullValueCheckStrategy;

import java.util.List;


@Mapper(config = BaseConvert.class, uses = InductionGroupConvert.class)
public interface InductionSchemeConvert extends BaseConvert<InductionScheme, InductionSchemeVO, InductionSchemeDTO> {


    @Mapping(target = "inductionGroups", ignore = true)
    @Mapping(target = "status", expression = "java(java.util.Optional.ofNullable(inductionSchemeDTO.getStatus()).orElse(com.genlight.epilcure.common.core.dao.enums.Status.ENABLED))")
    @BeanMapping(nullValueCheckStrategy = NullValueCheckStrategy.ON_IMPLICIT_CONVERSION)
    @Override
    InductionScheme dto2po(InductionSchemeDTO inductionSchemeDTO);

    @Basic
    @Mapping(target = "inductionGroups", ignore = true)
    @BeanMapping(nullValueCheckStrategy = NullValueCheckStrategy.ON_IMPLICIT_CONVERSION)
    @Override
    InductionSchemeVO po2vo(InductionScheme inductionScheme);


    @Summary
    @BeanMapping(nullValueCheckStrategy = NullValueCheckStrategy.ON_IMPLICIT_CONVERSION)
    InductionSchemeVO po2voSummary(InductionScheme inductionScheme);

    @Basic
    @Override
    List<InductionSchemeVO> po2vo(List<InductionScheme> inductionSchemes);
}
