package com.genlight.epilcure.service.iScheme.service;

import com.genlight.epilcure.api.patient.pojo.vo.PatientDeviceVO;
import com.genlight.epilcure.common.core.service.BaseService;
import com.genlight.epilcure.common.core.service.exception.ServiceException;
import com.genlight.epilcure.service.iScheme.dao.entity.InductionGroup;
import com.genlight.epilcure.service.iScheme.dao.entity.InductionScheme;
import com.genlight.epilcure.service.iScheme.dao.repository.InductionGroupRepository;
import com.genlight.epilcure.service.iScheme.pojo.convert.InductionGroupConvert;
import com.genlight.epilcure.service.iScheme.pojo.dto.InductionGroupDTO;
import com.genlight.epilcure.service.iScheme.pojo.vo.InductionGroupVO;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Service
public class InductionGroupService extends BaseService<InductionGroup, Long, InductionGroupRepository> {

    @Resource
    private InductionGroupConvert inductionGroupConvert;

    @Resource
    private InductionPulseService inductionPulseService;

    @Transactional
    public List<InductionGroupVO> add(List<InductionGroupDTO> inductionGroupDTOS, InductionScheme inductionScheme, PatientDeviceVO patientDeviceVO) {
        List<InductionGroupVO> result = new ArrayList<>();
        inductionGroupDTOS.forEach(inductionGroupDTO -> {
            // 引用现有组
            if (Objects.nonNull(inductionGroupDTO.getId())) {
                if (!repository.existsById(inductionGroupDTO.getId())) {
                    throw new ServiceException("未找到闭环方案组[{0}]", inductionGroupDTO.getId());
                }

                inductionScheme.getInductionGroups().add(InductionGroup.builder().id(inductionGroupDTO.getId()).build());
            } else {// 新增组
                InductionGroup inductionGroup = inductionGroupConvert.dto2po(inductionGroupDTO);
                inductionGroup = repository.save(inductionGroup);
                InductionGroupVO inductionGroupVO = inductionGroupConvert.po2vo(inductionGroup);
                inductionGroupVO.getInductionPulses().addAll(inductionPulseService.add(inductionGroupDTO.getInductionPulses(), patientDeviceVO, inductionGroup));


                inductionScheme.getInductionGroups().add(inductionGroup);

                result.add(inductionGroupVO);

            }
        });

        return result;

    }
}
