package com.genlight.epilcure.service.iScheme.pojo.convert;

import com.genlight.epilcure.common.core.pojo.convert.BaseConvert;
import com.genlight.epilcure.service.iScheme.dao.entity.TimeEegGroupScheme;
import com.genlight.epilcure.service.iScheme.pojo.dto.TimeEegGroupSchemeDTO;
import com.genlight.epilcure.service.iScheme.pojo.vo.TimeEegGroupSchemeVO;
import org.mapstruct.*;

@Mapper(config = BaseConvert.class)
public interface TimeEegGroupSchemeConvert extends BaseConvert<TimeEegGroupScheme, TimeEegGroupSchemeVO, TimeEegGroupSchemeDTO> {

    @BeanMapping(nullValueCheckStrategy = NullValueCheckStrategy.ON_IMPLICIT_CONVERSION)
    @Override
    TimeEegGroupSchemeVO po2vo(TimeEegGroupScheme timeEegGroupScheme);


    @Mapping(target = "status", expression = "java(java.util.Optional.ofNullable(timeEegGroupSchemeDTO.getStatus()).orElse(com.genlight.epilcure.common.core.dao.enums.Status.ENABLED))")
    @BeanMapping(nullValueCheckStrategy = NullValueCheckStrategy.ON_IMPLICIT_CONVERSION)
    @Override
    TimeEegGroupScheme dto2po(TimeEegGroupSchemeDTO timeEegGroupSchemeDTO);

    @Mapping(target = "status", expression = "java(java.util.Optional.ofNullable(timeEegGroupSchemeDTO.getStatus()).orElse(com.genlight.epilcure.common.core.dao.enums.Status.ENABLED))")
    @BeanMapping(nullValueCheckStrategy = NullValueCheckStrategy.ON_IMPLICIT_CONVERSION)
    @Override
    TimeEegGroupScheme dto2po(TimeEegGroupSchemeDTO timeEegGroupSchemeDTO, @MappingTarget TimeEegGroupScheme timeEegGroupScheme);
}
