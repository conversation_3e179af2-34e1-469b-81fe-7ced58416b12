package com.genlight.epilcure.service.iScheme.service;

import com.genlight.epilcure.common.core.service.BaseService;
import com.genlight.epilcure.service.iScheme.dao.entity.PatientStimulateLog;
import com.genlight.epilcure.service.iScheme.dao.entity.PatientStimulateLog_;
import com.genlight.epilcure.service.iScheme.dao.repository.PatientStimulateLogRepository;
import com.genlight.epilcure.service.iScheme.pojo.convert.PatientStimulateLogConvert;
import com.genlight.epilcure.service.iScheme.pojo.dto.PatientStimulateLogDTO;
import com.genlight.epilcure.service.iScheme.pojo.vo.PatientStimulateLogVO;
import jakarta.annotation.Resource;
import jakarta.persistence.criteria.Predicate;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Objects;

@Service
public class PatientStimulateLogService extends BaseService<PatientStimulateLog, Long, PatientStimulateLogRepository> {

    @Resource
    private PatientStimulateLogConvert patientStimulateLogConvert;

    @Transactional
    public void add(List<PatientStimulateLogDTO> patientStimulateLogDTOS) {
        List<PatientStimulateLog> list = patientStimulateLogDTOS.stream().map(this::add).toList();
        repository.saveAll(list);
    }

    protected PatientStimulateLog add(PatientStimulateLogDTO patientStimulateLogDTO) {
        PatientStimulateLog patientStimulateLog = patientStimulateLogConvert.dto2po(patientStimulateLogDTO);

        patientStimulateLog.getPatientStimulatePulseLogs().forEach(p -> {
            p.setPatientStimulateLog(patientStimulateLog);
        });

        return patientStimulateLog;
    }

    @Transactional(readOnly = true)
    public Page<PatientStimulateLogVO> finds(PatientStimulateLogDTO patientStimulateLogDTO, Pageable pageable) {
        Specification specification = (root, query, criteriaBuilder) -> {
            Predicate predicate = criteriaBuilder.conjunction();
            if (Objects.nonNull(patientStimulateLogDTO.getStartDate())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.greaterThan(root.get(PatientStimulateLog_.SCHEME_DATE), patientStimulateLogDTO.getStartDate()));
            }
            if (Objects.nonNull(patientStimulateLogDTO.getEndDate())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.lessThan(root.get(PatientStimulateLog_.SCHEME_DATE), patientStimulateLogDTO.getEndDate()));
            }
            if (Objects.nonNull(patientStimulateLogDTO.getSchemeId())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get(PatientStimulateLog_.SCHEME_ID), patientStimulateLogDTO.getSchemeId()));
            }
            return predicate;
        };
        return patientStimulateLogConvert.po2vo(repository.findAll(specification, pageable));
    }

}
