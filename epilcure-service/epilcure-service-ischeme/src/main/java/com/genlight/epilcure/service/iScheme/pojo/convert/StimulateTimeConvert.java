package com.genlight.epilcure.service.iScheme.pojo.convert;

import com.genlight.epilcure.common.core.pojo.convert.BaseConvert;
import com.genlight.epilcure.service.iScheme.dao.entity.StimulateTime;
import com.genlight.epilcure.service.iScheme.pojo.dto.StimulateTimeDTO;
import com.genlight.epilcure.service.iScheme.pojo.vo.StimulateTimeVO;
import org.mapstruct.BeanMapping;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;

@Mapper(config = BaseConvert.class)
public interface StimulateTimeConvert extends BaseConvert<StimulateTime, StimulateTimeVO, StimulateTimeDTO> {
    @BeanMapping(nullValueCheckStrategy = NullValueCheckStrategy.ON_IMPLICIT_CONVERSION)
    @Override
    StimulateTime dto2po(StimulateTimeDTO stimulateTimeDTO);

    @BeanMapping(nullValueCheckStrategy = NullValueCheckStrategy.ON_IMPLICIT_CONVERSION)
    @Override
    StimulateTime dto2po(StimulateTimeDTO stimulateTimeDTO, @MappingTarget StimulateTime stimulateTime);
}
