package com.genlight.epilcure.service.iScheme.pojo.dto;

import com.genlight.epilcure.common.core.pojo.dto.BaseDTO;
import com.genlight.epilcure.common.core.pojo.valid.NotNullIf;
import com.genlight.epilcure.service.iScheme.constants.enums.ElectricType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import org.hibernate.validator.constraints.Range;

@Data
@SuperBuilder
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Schema(description = "刺激程序")
@NotNullIf(checkField = "voltage", dependField = "electric", message = "电流电压至少分配一组数据")
@NotNullIf(checkField = "electric", dependField = "voltage", message = "电流电压至少分配一组数据")
public class StimulatePulseDTO extends BaseDTO {
    @Schema(description = "模式")
    private ElectricType electricType;

    @Schema(description = "持续时间")
    @Range(min = 1, max = 1 * 60 * 60 * 24 * 10)
    private Integer duration;

    @Schema(description = "关断时间")
    @Range(min = 0, max = 1 * 60 * 60 * 24 * 10)
    private Integer offDuration;

    @Schema(description = "电压")
    private Float voltage;

    @Schema(description = "最小电压")
    private float minVoltage;

    @Schema(description = "最大电压")
    private float maxVoltage;

    @Schema(description = "电流")
    private Float electric;

    @Schema(description = "最小电流")
    private float minElectric;

    @Schema(description = "最大电流")
    private float maxElectric;

    @Schema(description = "频率")
    @Range(min = 2, max = 260, message = "频率范围为{min}~{max}")
    private Integer frequency;

    @Schema(description = "最小频率")
    private Integer minFrequency;

    @Schema(description = "最大频率")
    private Integer maxFrequency;

    @Schema(description = "脉冲宽度")
    @Range(min = 20, max = 450, message = "脉冲宽度范围为{min}~{max}")
    private Integer pulseWidth;

    @Schema(description = "最小脉冲宽度")
    private Integer minPulseWidth;

    @Schema(description = "最大脉冲宽度")
    private Integer maxPulseWidth;

    @Schema(description = "触点信息")
    private TouchPointDTO touchPoint;
}
