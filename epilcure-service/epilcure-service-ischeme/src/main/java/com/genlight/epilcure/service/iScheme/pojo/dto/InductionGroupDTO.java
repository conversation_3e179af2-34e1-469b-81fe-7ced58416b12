package com.genlight.epilcure.service.iScheme.pojo.dto;

import com.genlight.epilcure.common.core.pojo.dto.BaseDTO;
import com.genlight.epilcure.common.core.pojo.valid.Add;
import com.genlight.epilcure.common.core.pojo.valid.NotNullIf;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.List;


@Data
@SuperBuilder
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Schema(description = "闭环方案数据")
@NotNullIf(checkField = "id", dependField = "no", message = "序列不能为空")
@NotNullIf(checkField = "id", dependField = "name", message = "组名不能为空")
@NotNullIf(checkField = "id", dependField = "inductionPulses", message = "脉冲信息不能为空")
@NotNullIf(checkField = "id", dependField = "threshold", message = "阈值不能为空")
public class InductionGroupDTO extends BaseDTO {

    @Schema(description = "Id,新增方案时,如需保存原有方案则只需要传Id.")
    private Long id;

    @Schema(description = "序列")
    private String no;

    @Schema(description = "组名")
    private String name;

    @NotNull(message = "刺激时长不能为空", groups = Add.class)
    @Schema(description = "刺激时长")
    private Integer stimulate;

    @NotNull(message = "感应时长不能为空", groups = Add.class)
    @Schema(description = "感应时长")
    private Integer induction;

    @NotNull(message = "软启动时长不能为空", groups = Add.class)
    @Schema(description = "软启动")
    private Integer startup;

    @NotNull(message = "尾迹时长不能为空", groups = Add.class)
    @Schema(description = "尾迹时长")
    private Integer wake;

    @NotNull(message = "特征动时长不能为空", groups = Add.class)
    @Schema(description = "特征时长")
    private Integer characteristic;

    @NotNull(message = "覆盖率不能为空", groups = Add.class)
    @Schema(description = "覆盖率")
    private Integer overlap;

    @NotNull(message = "阈值类型不能为空", groups = Add.class)
    @Schema(description = "阈值类型")
    private Integer thresholdType;

    @Schema(description = "fft窗宽")
    private Integer fftWidth;

    @Schema(description = "单侧控制")
    private Integer single;


    @NotNull(message = "能量类型不能为空", groups = Add.class)
    @Schema(description = "能量类型")
    private Integer energyType;

    @NotNull(message = "算法类型不能为空", groups = Add.class)
    @Schema(description = "算法类型")
    private Integer algorithmType;

    @Size(min = 1, max = 4, message = "程控信息范围为{min}~{max}")
    @Schema(description = "程控信息")
    private List<InductionPulseDTO> inductionPulses;
}
