package com.genlight.epilcure.service.iScheme.pojo.dto;

import com.fasterxml.jackson.annotation.JsonView;
import com.genlight.epilcure.common.core.dao.enums.Status;
import com.genlight.epilcure.common.core.pojo.dto.BaseDTO;
import com.genlight.epilcure.common.core.pojo.valid.Add;
import com.genlight.epilcure.common.core.pojo.valid.Find;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

@Data
@SuperBuilder
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Schema(description = "患者采集方案数据")
public class PatientRealEegSchemeDTO extends BaseDTO {
    @Schema(description = "ID")
    @NotNull(message = "id不能为空", groups = Add.class)
    @JsonView(Add.class)
    private String id;
    @Schema(description = "触点配置")
    @NotNull(message = "触点信息不能为空")
    @Size(min = 1, max = 2, message = "至少分配一组触点信息")
    @Valid
    private List<TouchPointDTO> touchPoint;

    @Schema(description = "刺激开关")
    @NotNull(message = "是否开关刺激不能为空")
    private Boolean openStimulation;

    @Schema(description = "患者Id")
    @NotNull(message = "患者Id不能为空")
    private Long patientId;

    @Schema(description = "调参日期")
    @NotNull(message = "调参日期不能为空")
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
    private Date schemeDate;

    @Schema(description = "状态")
    private Status status;

    @Schema(description = "开始时间")
    @JsonView(Find.class)
    private Date startDate;
    @Schema(description = "结束时间")
    @JsonView(Find.class)
    private Date endDate;
}
