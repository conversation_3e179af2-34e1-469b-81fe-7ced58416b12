package com.genlight.epilcure.service.iScheme.pojo.dto;


import com.genlight.epilcure.common.core.pojo.valid.Add;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.Column;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@Data
@SuperBuilder
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Schema(description = "刺激脉冲")
public class InductionPulseDTO extends StimulatePulseDTO {

    @Schema(description = "阈值")
    private Float threshold;

    @NotNull(message = "感应触点不能为为空", groups = Add.class)
    @Schema(description = "感应触点")
    private TouchPointDTO inductionTouchPoint;

    @Schema(description = "计算最小频段")
    private Integer calMinInduction;

    @Schema(description = "计算最大频段")
    private Integer calMaxInduction;

    @Schema(description = "最小频段")
    @Column(nullable = false)
    private Integer minInduction;

    @Schema(description = "最大频段")
    @Column(nullable = false)
    private Integer maxInduction;
}
