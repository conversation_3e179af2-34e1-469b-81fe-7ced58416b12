package com.genlight.epilcure.service.iScheme.dao.entity;

import com.genlight.epilcure.common.core.dao.entity.TEntity;
import com.genlight.epilcure.common.core.dao.generator.annotation.SignOrder;
import jakarta.persistence.*;
import lombok.*;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;
import org.hibernate.annotations.Comment;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import java.util.Date;
import java.util.HashSet;
import java.util.Set;


@Getter
@Setter
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString
@Entity
@Cacheable
@DynamicInsert
@DynamicUpdate
@SignOrder(0)
@Table(name = "t_scheme_log")
public class SchemeLog extends TEntity {
    @Comment("方案Id")
    private String schemeId;

    @Comment("患者Id")
    private Long patientId;

    @Comment("通道状态")
    private Byte channel;

    @Comment("结束时间")
    private Date endTime;

    @Comment("A组执行次数,[0,1]")
    private String groupA;

    @Comment("B组执行次数,[0,1]")
    private String groupB;
    @Comment("C组执行次数,[0,1]")
    private String groupC;
    @Comment("D组执行次数,[0,1]")
    private String groupD;

    @ToString.Exclude
    @OneToMany(mappedBy = "schemeLog", cascade = CascadeType.PERSIST)
    @Builder.Default
    @Comment("数据单元")
    private Set<SchemeLogUnit> schemeLogUnits = new HashSet<>();
}
