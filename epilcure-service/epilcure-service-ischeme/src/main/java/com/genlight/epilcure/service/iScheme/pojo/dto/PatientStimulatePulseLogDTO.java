package com.genlight.epilcure.service.iScheme.pojo.dto;

import com.genlight.epilcure.common.core.pojo.dto.BaseDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@Data
@SuperBuilder
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Schema(description = "患者调参脉冲日志")
public class PatientStimulatePulseLogDTO extends BaseDTO {
    @Schema(description = "通道")
    private Integer position;

    @Schema(description = "设置前脉冲宽度")
    private Integer prePulseWidth;
    @Schema(description = "脉冲宽度")
    private Integer pulseWidth;
    @Schema(description = "设置前频率")
    private Integer preFrequency;
    @Schema(description = "频率")
    private Integer frequency;
    @Schema(description = "设置前电压")
    private Float preVoltage;
    @Schema(description = "电压")
    private Float voltage;
    @Schema(description = "设置前电流")
    private Float preElectric;
    @Schema(description = "电流")
    private Float electric;
}
