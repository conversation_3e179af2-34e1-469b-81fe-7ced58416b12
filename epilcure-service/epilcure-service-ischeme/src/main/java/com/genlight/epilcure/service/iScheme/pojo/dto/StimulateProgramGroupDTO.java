package com.genlight.epilcure.service.iScheme.pojo.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.genlight.epilcure.common.core.pojo.dto.BaseDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import org.hibernate.validator.constraints.Length;

import java.util.List;

@Data
@SuperBuilder
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Schema(description = "刺激程序组")
public class StimulateProgramGroupDTO extends BaseDTO {
    @Schema(description = "患者Id")
    @NotNull(message = "患者Id不能为空")
    private Long patientId;
    @Schema(description = "医生Id")
    @JsonIgnore
    private Long userId;

    @JsonIgnore
    private String userName;

    @JsonIgnore
    private String userPhone;
    @Schema(description = "序列")
    @NotNull(message = "序列不能为空")
    @Pattern(regexp = "^A$|^B$|^C$|^D$")
    private String no;
    @NotNull(message = "组名不能为空")
    @Length(min = 0, max = 255, message = "组名长度为{min}~{max}")
    @Schema(description = "组名")
    private String name;

    @NotNull(message = "当前是否激活状态不能为空")
    @Schema(description = "激活状态")
    private Boolean active;

    @Schema(description = "软启动")
    private Integer startup;

    @Schema(description = "脉冲信息")
    @NotNull(message = "脉冲信息不能为空")
    @Size(min = 1, max = 6, message = "程序组脉冲数量为{min}~{max}")
    @Valid
    private List<StimulatePulseDTO> stimulatePulses;
}
