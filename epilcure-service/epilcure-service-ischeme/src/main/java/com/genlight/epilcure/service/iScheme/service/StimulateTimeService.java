package com.genlight.epilcure.service.iScheme.service;

import com.genlight.epilcure.api.patient.feign.IPatientController;
import com.genlight.epilcure.common.core.service.BaseService;
import com.genlight.epilcure.common.core.service.exception.ArgsException;
import com.genlight.epilcure.service.iScheme.dao.entity.Scheme;
import com.genlight.epilcure.service.iScheme.dao.entity.StimulateProgramGroup;
import com.genlight.epilcure.service.iScheme.dao.entity.StimulateTime;
import com.genlight.epilcure.service.iScheme.dao.repository.StimulateTimeRepository;
import com.genlight.epilcure.service.iScheme.pojo.convert.StimulateTimeConvert;
import com.genlight.epilcure.service.iScheme.pojo.dto.StimulateTimeDTO;
import com.genlight.epilcure.service.iScheme.pojo.vo.SchemeVO;
import com.genlight.epilcure.service.iScheme.pojo.vo.StimulateProgramGroupVO;
import com.genlight.epilcure.service.iScheme.pojo.vo.StimulateTimeVO;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Service
public class StimulateTimeService extends BaseService<StimulateTime, Long, StimulateTimeRepository> {
    @Resource
    private StimulateTimeConvert stimulateTimeConvert;
    @Resource
    private IPatientController iPatientController;

    @Transactional
    public List<StimulateTimeVO> add(List<StimulateTimeDTO> stimulateTimeDTOS, List<StimulateProgramGroupVO> stimulateProgramGroups, SchemeVO schemeVO) {

        List<StimulateTimeVO> result = new ArrayList<>();
        stimulateTimeDTOS.forEach(stimulateTimeDTO -> {
            if (stimulateTimeDTO.getStartTime() >= stimulateTimeDTO.getEndTime()) {
                throw new ArgsException("开始时间[{0}]必须小于结束时间[{1}]", stimulateTimeDTO.getStartTime(), stimulateTimeDTO.getEndTime());
            }

            stimulateTimeDTOS.forEach(st -> {
                if (st != stimulateTimeDTO && ((stimulateTimeDTO.getStartTime() >= st.getStartTime() && stimulateTimeDTO.getStartTime() < st.getEndTime())
                        || (stimulateTimeDTO.getEndTime() > st.getEndTime() && stimulateTimeDTO.getEndTime() <= st.getEndTime())
                        || (stimulateTimeDTO.getStartTime() <= st.getStartTime() && stimulateTimeDTO.getEndTime() >= st.getEndTime()))) {
                    throw new ArgsException("当前时段[{0}]~[{1}]已存在程序组", st.getStartTime(), st.getEndTime());
                }
            });

            Optional<StimulateProgramGroupVO> optional = stimulateProgramGroups.stream().filter(spg -> spg.getNo().equals(stimulateTimeDTO.getStimulateProgramGroupNo())).findFirst();
            if (optional.isEmpty()) {
                throw new ArgsException("定时方案使用的程序组[{0}]不存在", stimulateTimeDTO.getStimulateProgramGroupNo());
            }

            stimulateTimeDTO.setUserId(getUserId());
            stimulateTimeDTO.setUserName(getNikeName());
            stimulateTimeDTO.setUserPhone(getUserName());


            StimulateTime stimulateTime = stimulateTimeConvert.dto2po(stimulateTimeDTO);
            stimulateTime.setScheme(Scheme.builder().id(schemeVO.getId()).build());
            stimulateTime.setStimulateProgramGroup(StimulateProgramGroup.builder().id(optional.get().getId()).build());
            iPatientController.findById(stimulateTimeDTO.getPatientId());

            stimulateTime = repository.save(stimulateTime);
            result.add(stimulateTimeConvert.po2vo(stimulateTime));
        });
        return result;
    }
}
