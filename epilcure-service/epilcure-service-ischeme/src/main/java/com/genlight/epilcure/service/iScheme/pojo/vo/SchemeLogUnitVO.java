package com.genlight.epilcure.service.iScheme.pojo.vo;

import com.genlight.epilcure.common.core.pojo.vo.BaseVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;

import java.util.List;


@Data
@SuperBuilder
@NoArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@Schema(description = "运行日志数据单元View Object")
public class SchemeLogUnitVO extends BaseVO {

    @Schema(description = "偏移量")
    private Integer offset;

    @Schema(description = "能量或能量比")
    private List<Float> values;

    @Schema(description = "电流")
    private List<Float> electric;

    private Integer order;
}
