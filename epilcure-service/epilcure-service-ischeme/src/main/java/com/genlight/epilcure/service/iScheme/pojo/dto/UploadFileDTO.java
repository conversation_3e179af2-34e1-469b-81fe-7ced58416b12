package com.genlight.epilcure.service.iScheme.pojo.dto;

import com.fasterxml.jackson.annotation.JsonView;
import com.genlight.epilcure.common.core.pojo.dto.BaseDTO;
import com.genlight.epilcure.common.core.pojo.valid.Find;
import com.genlight.epilcure.service.iScheme.constants.enums.FileStatus;
import com.genlight.epilcure.service.iScheme.constants.enums.FileType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
@SuperBuilder
@NoArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@Schema(description = "上传的文件传输对象")
public class UploadFileDTO extends BaseDTO {

    @Schema(description = "文件上传者")
    private Long userId;

    @Schema(description = "文件名称")
    private String name;

    @Schema(description = "文件地址")
    private String url;

    @JsonView(Find.class)
    @Schema(description = "文件类型")
    private FileType type;

    @Schema(description = "文件状态（0：已上传，1：解析成功，2：解析失败）")
    private FileStatus status;

    @Schema(description = "描述信息")
    private String description;

    @Schema(description = "患者Id")
    private Long patientId;

    @Schema(description = "开始时间")
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME, pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonView(Find.class)
    private Date startDate;
    @Schema(description = "结束时间")
    @JsonView(Find.class)
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME, pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endDate;
}
