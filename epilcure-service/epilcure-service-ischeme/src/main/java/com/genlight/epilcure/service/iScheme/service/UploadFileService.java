package com.genlight.epilcure.service.iScheme.service;

import com.genlight.epilcure.common.core.service.BaseService;
import com.genlight.epilcure.common.core.service.exception.ServiceException;
import com.genlight.epilcure.common.core.util.MinioUtils;
import com.genlight.epilcure.service.iScheme.constants.FileConstants;
import com.genlight.epilcure.service.iScheme.constants.enums.FileStatus;
import com.genlight.epilcure.service.iScheme.constants.enums.FileType;
import com.genlight.epilcure.service.iScheme.dao.entity.SchemeLog;
import com.genlight.epilcure.service.iScheme.dao.entity.UploadFile;
import com.genlight.epilcure.service.iScheme.dao.entity.UploadFile_;
import com.genlight.epilcure.service.iScheme.dao.repository.UploadFileRepository;
import com.genlight.epilcure.service.iScheme.pojo.convert.UploadFileConvert;
import com.genlight.epilcure.service.iScheme.pojo.dto.UploadFileDTO;
import com.genlight.epilcure.service.iScheme.pojo.vo.UploadFileVO;
import jakarta.annotation.Resource;
import jakarta.persistence.criteria.Predicate;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Objects;
import java.util.Optional;


@Service
@Slf4j
public class UploadFileService extends BaseService<UploadFile, Long, UploadFileRepository> {
    @Resource
    private MinioUtils minioUtils;

    @Resource
    private SchemeLogService schemeLogService;

    @Resource
    private UploadFileConvert convert;

    @Transactional
    public void uploadFile(Long patientId, MultipartFile file) {
        log.info("开始时间{}, 执行uploadFile(),线程：{}",System.currentTimeMillis() / 1000 ,Thread.currentThread().getName());
        //同步上传到 oss
        String filename = minioUtils.uploadCurrentUniqueName(FileConstants.SCHEME_LOG, file);
        UploadFile build = UploadFile.builder()
                .name(file.getOriginalFilename())
                .url(filename)
                .status(FileStatus.UPLOADED)
                .type(FileType.SCHEME_LOG)
                .userId(getUserId())
                .patientId(patientId)
                .build();
        //保存本地上传记录
        UploadFile save = repository.save(build);

        // schemeLogService.readLog(file, save); 如果不强制提交事物，readLog异步执行 会有几率 读取不到提交的文件
        // 强制提交事务
        TransactionSynchronizationManager.registerSynchronization(
                new TransactionSynchronization() {
                    @Override
                    public void afterCommit() {
                        schemeLogService.readLog(file, save); // 事务提交后执行
                    }
                }
        );
        log.info("结束时间{}, 执行uploadFile(),线程：{}",System.currentTimeMillis() / 1000 ,Thread.currentThread().getName());

    }

    @Transactional
    public void updateStatus(Long id, FileStatus fileStatus) {
        Optional<UploadFile> optional = repository.findById(id);
        if (optional.isEmpty()) {
            throw new ServiceException("文件不存在{}", String.valueOf(id));
        }

        UploadFile uploadFile = optional.get();
        uploadFile.setStatus(fileStatus);
        repository.save(uploadFile);
    }

    @Transactional(readOnly = true)
    public Page<UploadFileVO> finds(UploadFileDTO uploadFileDTO, Pageable pageable) {

        return convert.po2vo(repository.findAll(generateJpaSpecification(uploadFileDTO), pageable));
    }

    private Specification<UploadFile> generateJpaSpecification(UploadFileDTO uploadFileDTO) {
        return (root, query, criteriaBuilder) -> {
            Predicate predicate = criteriaBuilder.conjunction();
            if (Objects.nonNull(uploadFileDTO.getUserId())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get(UploadFile_.userId), uploadFileDTO.getUserId()));
            }
            if (Objects.nonNull(uploadFileDTO.getType())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get(UploadFile_.type), uploadFileDTO.getType()));
            }
            if (Objects.nonNull(uploadFileDTO.getStatus())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get(UploadFile_.status), uploadFileDTO.getStatus()));
            }
            if (StringUtils.hasText(uploadFileDTO.getName())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.like(root.get(UploadFile_.name), "%" + uploadFileDTO.getName() + "%"));
            }
            if (StringUtils.hasText(uploadFileDTO.getUrl())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.like(root.get(UploadFile_.url), "%" + uploadFileDTO.getUrl() + "%"));
            }
            return predicate;
        };
    }
    @Transactional
    public void saveSchemeLogs(List<SchemeLog> schemeLog) {
        try {
            schemeLogService.saveSchemeLogs(schemeLog);
        } catch (Exception e) {
            throw e;
        }
    }


}
