package com.genlight.epilcure.service.iScheme.controller;

import com.genlight.epilcure.common.core.mvc.response.JsonResult;
import com.genlight.epilcure.service.iScheme.pojo.dto.SchemeDTO;
import com.genlight.epilcure.service.iScheme.pojo.vo.SchemeVO;
import com.genlight.epilcure.service.iScheme.service.SchemeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/iScheme/schemes")
@Tag(name = "ISBSSchemeController", description = "方案相关接口")
public class SchemeController {
    @Resource
    private SchemeService schemeService;

    @GetMapping
    @Operation(summary = "根据条件搜索程控记录")
    public JsonResult<Page<SchemeVO>> finds(SchemeDTO schemeDTO, Pageable pageable) {
        return JsonResult.ok(schemeService.finds(schemeDTO, pageable));
    }

    @GetMapping("/patients/{id}")
    @Operation(summary = "根据患者Id获取方案详细信息")
    public JsonResult<SchemeVO> findByPatientId(@PathVariable Long id) {
        return JsonResult.ok(schemeService.findByPatientId(id));
    }

    @GetMapping("/{id}")
    @Operation(summary = "根据id获取方案详细信息")
    public JsonResult<SchemeVO> find(@PathVariable String id) {
        return JsonResult.ok(schemeService.findById(id));
    }

    @PostMapping
    @Operation(summary = "添加程控方案")
    public JsonResult<SchemeVO> add(@RequestBody @Valid SchemeDTO schemeDTO) {
        return JsonResult.ok(schemeService.add(schemeDTO));
    }
}
