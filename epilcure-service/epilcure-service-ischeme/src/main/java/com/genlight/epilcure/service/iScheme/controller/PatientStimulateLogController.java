package com.genlight.epilcure.service.iScheme.controller;

import com.genlight.epilcure.common.core.mvc.response.JsonResult;
import com.genlight.epilcure.service.iScheme.pojo.dto.PatientStimulateLogDTO;
import com.genlight.epilcure.service.iScheme.pojo.vo.PatientStimulateLogVO;
import com.genlight.epilcure.service.iScheme.service.PatientStimulateLogService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/iScheme/patientStimulateLogs")
@Tag(name = "ISBSPatientStimulateLogController", description = "患者刺激调参相关接口")
public class PatientStimulateLogController {
    @Resource
    private PatientStimulateLogService patientStimulateLogService;

    @PostMapping
    @Operation(summary = "上传调参日志")
    public JsonResult<Void> add(@Valid @RequestBody List<PatientStimulateLogDTO> patientStimulateLogDTO) {
        patientStimulateLogService.add(patientStimulateLogDTO);
        return JsonResult.ok();
    }

    @GetMapping
    @Operation(summary = "分页查询调参日志")
    public JsonResult<Page<PatientStimulateLogVO>> finds(PatientStimulateLogDTO patientStimulateLogDTO, Pageable pageable) {
        return JsonResult.ok(patientStimulateLogService.finds(patientStimulateLogDTO, pageable));
    }
}
