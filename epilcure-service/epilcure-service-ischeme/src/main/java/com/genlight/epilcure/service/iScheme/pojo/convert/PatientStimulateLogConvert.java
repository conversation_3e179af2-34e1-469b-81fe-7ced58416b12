package com.genlight.epilcure.service.iScheme.pojo.convert;

import com.genlight.epilcure.common.core.pojo.convert.BaseConvert;
import com.genlight.epilcure.service.iScheme.dao.entity.PatientStimulateLog;
import com.genlight.epilcure.service.iScheme.pojo.dto.PatientStimulateLogDTO;
import com.genlight.epilcure.service.iScheme.pojo.vo.PatientStimulateLogVO;
import org.mapstruct.BeanMapping;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;

@Mapper(config = BaseConvert.class)
public interface PatientStimulateLogConvert extends BaseConvert<PatientStimulateLog, PatientStimulateLogVO, PatientStimulateLogDTO> {

    @BeanMapping(nullValueCheckStrategy = NullValueCheckStrategy.ON_IMPLICIT_CONVERSION)
    @Override
    PatientStimulateLog dto2po(PatientStimulateLogDTO patientStimulateLogDTO);
}
