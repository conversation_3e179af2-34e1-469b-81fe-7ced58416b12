package com.genlight.epilcure.service.iScheme.pojo.dto;

import com.genlight.epilcure.common.core.pojo.dto.BaseDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;

import java.util.List;


@Data
@SuperBuilder
@NoArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@Schema(description = "运行日志数据单元View Object")
public class SchemeLogUnitDTO extends BaseDTO {

    @Schema(description = "偏移量")
    private Integer offset;

    @Schema(description = "能量或能量比")
    private List<Float> values;

    @Schema(description = "电流")
    private List<Float> electric;

    @Schema(description = "记录Id")
    private Long schemeLogId;
}
