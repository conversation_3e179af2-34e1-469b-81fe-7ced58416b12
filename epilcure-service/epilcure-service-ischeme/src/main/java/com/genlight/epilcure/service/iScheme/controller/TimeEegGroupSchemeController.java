package com.genlight.epilcure.service.iScheme.controller;

import com.genlight.epilcure.common.core.mvc.response.JsonResult;
import com.genlight.epilcure.service.iScheme.pojo.dto.TimeEegGroupSchemeDTO;
import com.genlight.epilcure.service.iScheme.pojo.vo.TimeEegGroupSchemeVO;
import com.genlight.epilcure.service.iScheme.service.TimeEegGroupSchemeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/iScheme/timeEegGroupSchemes")
@Tag(name = "ISBSTimeEegGroupSchemeController", description = "定时采集方案相关接口")
public class TimeEegGroupSchemeController {
    @Resource
    private TimeEegGroupSchemeService timeEegGroupSchemeService;

    @PostMapping
    @Operation(description = "添加定时采集方案")
    public JsonResult<TimeEegGroupSchemeVO> add(@Valid @RequestBody TimeEegGroupSchemeDTO timeEegGroupSchemeDTO) {
        return JsonResult.ok(timeEegGroupSchemeService.add(timeEegGroupSchemeDTO));
    }

    @GetMapping("/patient/{id}")
    @Operation(description = "根据患者查询当前最新的定时采集方案")
    public JsonResult<TimeEegGroupSchemeVO> findByPatient(@PathVariable Long id) {
        return JsonResult.ok(timeEegGroupSchemeService.findByPatientId(id));
    }

    @GetMapping("/{id}")
    @Operation(description = "根据Id获取采集方案详细信息")
    public JsonResult<TimeEegGroupSchemeVO> find(@PathVariable String id) {
        return JsonResult.ok(timeEegGroupSchemeService.findById(id));
    }

    @GetMapping
    @Operation(description = "根据条件查询定时采集方案")
    public JsonResult<Page<TimeEegGroupSchemeVO>> finds(TimeEegGroupSchemeDTO timeEegGroupSchemeDTO, Pageable pageable) {
        return JsonResult.ok(timeEegGroupSchemeService.finds(timeEegGroupSchemeDTO, pageable));
    }
}
