package com.genlight.epilcure.service.iScheme.pojo.convert;

import com.genlight.epilcure.common.core.pojo.convert.BaseConvert;
import com.genlight.epilcure.service.iScheme.dao.entity.InductionPulse;
import com.genlight.epilcure.service.iScheme.pojo.dto.InductionPulseDTO;
import com.genlight.epilcure.service.iScheme.pojo.vo.InductionPulseVO;
import org.mapstruct.Mapper;

@Mapper(config = BaseConvert.class)
public interface InductionPulseConvert extends BaseConvert<InductionPulse, InductionPulseVO, InductionPulseDTO> {
}
