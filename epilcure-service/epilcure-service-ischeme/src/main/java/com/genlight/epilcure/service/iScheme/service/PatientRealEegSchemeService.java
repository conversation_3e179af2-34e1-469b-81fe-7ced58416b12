package com.genlight.epilcure.service.iScheme.service;

import com.genlight.epilcure.api.patient.feign.IPatientController;
import com.genlight.epilcure.api.patient.pojo.vo.PatientVO;
import com.genlight.epilcure.common.core.dao.enums.Status;
import com.genlight.epilcure.common.core.service.BaseService;
import com.genlight.epilcure.common.core.service.exception.ArgsException;
import com.genlight.epilcure.service.iScheme.dao.entity.PatientRealEegScheme;
import com.genlight.epilcure.service.iScheme.dao.entity.PatientRealEegScheme_;
import com.genlight.epilcure.service.iScheme.dao.repository.PatientRealEegSchemeRepository;
import com.genlight.epilcure.service.iScheme.pojo.convert.PatientRealEegSchemeConvert;
import com.genlight.epilcure.service.iScheme.pojo.dto.PatientRealEegSchemeDTO;
import com.genlight.epilcure.service.iScheme.pojo.vo.PatientRealEegSchemeVO;
import jakarta.annotation.Resource;
import jakarta.persistence.criteria.Predicate;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Objects;
import java.util.Optional;

@Service
public class PatientRealEegSchemeService extends BaseService<PatientRealEegScheme, String, PatientRealEegSchemeRepository> {
    @Resource
    private IPatientController iPatientController;

    @Resource
    private PatientRealEegSchemeConvert patientRealEegSchemeConvert;

    @Transactional
    public PatientRealEegSchemeVO add(PatientRealEegSchemeDTO patientRealEegSchemeDTO) {

        Optional<PatientRealEegScheme> optional = repository.findById(patientRealEegSchemeDTO.getId());
        if (optional.isPresent()) {
            throw new ArgsException("患者采集方案Id[{0}]已存在", patientRealEegSchemeDTO.getId());
        }

        PatientVO patientVO = iPatientController.findById(patientRealEegSchemeDTO.getPatientId());
        patientRealEegSchemeDTO.getTouchPoint().forEach(tp -> {
            int positive = 0;
            int negative = 0;
            for (Integer integer : tp.getPolarity()) {
                if (integer.equals(1)) {
                    positive++;
                } else if (integer.equals(2)) {
                    negative++;
                }
            }

            if (positive != 1 || negative != 1) {
                throw new ArgsException("需要分配一对(+,-)极");
            }
        });
        PatientRealEegScheme patientRealEegScheme = patientRealEegSchemeConvert.dto2po(patientRealEegSchemeDTO);

        String today = DateFormatUtils.format(patientRealEegSchemeDTO.getSchemeDate(), "yyyy-MM-dd 00:00:00");
        String tomorrow = DateFormatUtils.format(new Date(patientRealEegSchemeDTO.getSchemeDate().getTime() + 1000 * 60 * 60 * 24), "yyyy-MM-dd 00:00:00");
        Date todayDate;
        Date tomorrowDate;
        try {
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd hh:mm:ss");
            todayDate = simpleDateFormat.parse(today);
            tomorrowDate = simpleDateFormat.parse(tomorrow);
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }

        optional = repository.findByPatientIdAndSchemeDateBetweenAndStatus(patientRealEegSchemeDTO.getPatientId(), todayDate, tomorrowDate, Status.ENABLED);
        if (optional.isPresent()) {
            PatientRealEegScheme oldScheme = optional.get();
            if (oldScheme.getSchemeDate().after(patientRealEegScheme.getSchemeDate())) {
                patientRealEegScheme.setStatus(Status.DISABLED);
            } else {
                oldScheme.setStatus(Status.DISABLED);
            }
        }

        return patientRealEegSchemeConvert.po2vo(repository.save(patientRealEegScheme));
    }

    @Transactional(readOnly = true)
    public PatientRealEegSchemeVO findByPatientId(Long patientId) {
        Optional<PatientRealEegScheme> optional = repository.findFirstByPatientIdAndStatusOrderBySchemeDateDesc(patientId, Status.ENABLED);
        if (optional.isEmpty()) {
            return null;
        }
        return patientRealEegSchemeConvert.po2vo(optional.get());
    }

    @Transactional(readOnly = true)
    public PatientRealEegSchemeVO findById(String id) {
        Optional<PatientRealEegScheme> optional = repository.findById(id);
        if (optional.isEmpty()) {
            throw new ArgsException("患者实时脑电采集方案[{0}]不存在");
        }
        return patientRealEegSchemeConvert.po2vo(optional.get());
    }

    @Transactional(readOnly = true)
    public Page<PatientRealEegSchemeVO> finds(PatientRealEegSchemeDTO patientRealEegSchemeDTO, Pageable pageable) {
        Specification<PatientRealEegScheme> specification = (root, query, criteriaBuilder) -> {
            Predicate predicate = criteriaBuilder.conjunction();
            if (Objects.nonNull(patientRealEegSchemeDTO.getStartDate())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.greaterThan(root.get(PatientRealEegScheme_.SCHEME_DATE), patientRealEegSchemeDTO.getStartDate()));
            }
            if (Objects.nonNull(patientRealEegSchemeDTO.getEndDate())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.lessThan(root.get(PatientRealEegScheme_.SCHEME_DATE), patientRealEegSchemeDTO.getEndDate()));
            }
            if (Objects.nonNull(patientRealEegSchemeDTO.getPatientId())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get(PatientRealEegScheme_.PATIENT_ID), patientRealEegSchemeDTO.getPatientId()));
            }
            if (Objects.nonNull(patientRealEegSchemeDTO.getStatus())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get(PatientRealEegScheme_.STATUS), patientRealEegSchemeDTO.getStatus()));
            }
            return predicate;
        };
        return patientRealEegSchemeConvert.po2vo(repository.findAll(specification, pageable));
    }
}
