package com.genlight.epilcure.service.iScheme.service;

import com.genlight.epilcure.api.patient.feign.IPatientDeviceController;
import com.genlight.epilcure.api.patient.pojo.vo.PatientDeviceVO;
import com.genlight.epilcure.common.core.constants.HttpCode;
import com.genlight.epilcure.common.core.dao.enums.Status;
import com.genlight.epilcure.common.core.service.BaseService;
import com.genlight.epilcure.common.core.service.exception.ArgsException;
import com.genlight.epilcure.common.core.service.exception.ServiceException;
import com.genlight.epilcure.common.rocketmq.pojo.bo.AddSchemeBO;
import com.genlight.epilcure.service.iScheme.constants.enums.ActiveType;
import com.genlight.epilcure.service.iScheme.dao.entity.Scheme;
import com.genlight.epilcure.service.iScheme.dao.entity.Scheme_;
import com.genlight.epilcure.service.iScheme.dao.repository.SchemeRepository;
import com.genlight.epilcure.service.iScheme.pojo.convert.SchemeConvert;
import com.genlight.epilcure.service.iScheme.pojo.dto.SchemeDTO;
import com.genlight.epilcure.service.iScheme.pojo.vo.SchemeVO;
import com.genlight.epilcure.service.iScheme.properties.SchemeProperties;
import jakarta.annotation.Resource;
import jakarta.persistence.criteria.Predicate;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

@Service
public class SchemeService extends BaseService<Scheme, String, SchemeRepository> {

    @Resource
    private StimulateProgramGroupService stimulateProgramGroupService;

    @Resource
    private StimulateTimeService stimulateTimeService;

    @Resource
    private StimulateFrequencyService stimulateFrequencyService;

    @Resource
    private SchemeConvert schemeConvert;

    @Resource
    private RocketMQTemplate rocketMQTemplate;

    @Resource
    private IPatientDeviceController iPatientDeviceController;

    @Resource
    private RedissonClient redissonClient;

    @Resource
    private SchemeProperties schemeProperties;

    @Transactional
    public SchemeVO add(SchemeDTO schemeDTO) {
        RLock lock = redissonClient.getLock("addISBSScheme%s".formatted(schemeDTO.getPatientId()));

        try {
            if (lock.tryLock(5, 5, TimeUnit.SECONDS)) {
                Optional<Scheme> optional = repository.findById(schemeDTO.getId());
                if (!optional.isEmpty()) {
                    throw new ArgsException("程控方案[{0}]已存在", HttpCode.I_SCHEME_ID_EXISTS, schemeDTO.getId());
                }

                Scheme scheme;
                String today = DateFormatUtils.format(schemeDTO.getSchemeDate(), "yyyy-MM-dd 00:00:00");
                String tomorrow = DateFormatUtils.format(new Date(schemeDTO.getSchemeDate().getTime() + 1000 * 60 * 60 * 24), "yyyy-MM-dd 00:00:00");

                Date todayDate;
                Date tomorrowDate;
                try {
                    SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd hh:mm:ss");
                    todayDate = simpleDateFormat.parse(today);
                    tomorrowDate = simpleDateFormat.parse(tomorrow);
                } catch (ParseException e) {
                    throw new RuntimeException(e);
                }


                optional = repository.findByPatientIdAndSchemeDateBetweenAndStatus(schemeDTO.getPatientId(), todayDate, tomorrowDate, Status.ENABLED);

                if (optional.isPresent()) {
                    scheme = optional.get();
                    if (schemeDTO.getSchemeDate().before(scheme.getSchemeDate())) {
                        schemeDTO.setStatus(Status.DISABLED);
                    } else {
                        scheme.setStatus(Status.DISABLED);
                    }
                }

                scheme = schemeConvert.dto2po(schemeDTO);

                scheme.setUserId(getUserId());
                scheme.setUserName(getNikeName());
                scheme.setUserPhone(getUserName());

                SchemeVO schemeVO = schemeConvert.po2vo(repository.saveAndFlush(scheme));

                PatientDeviceVO patientDeviceVO = iPatientDeviceController.findByPatientId(schemeVO.getPatientId());
                if (Objects.nonNull(schemeDTO.getStimulateProgramGroups())) {
                    schemeVO.setStimulateProgramGroups(stimulateProgramGroupService.addProgramGroup(schemeDTO.getStimulateProgramGroups(), schemeVO, patientDeviceVO));
                }

                if (Objects.nonNull(schemeDTO.getStimulateFrequency())) {
                    schemeVO.setStimulateFrequency(stimulateFrequencyService.add(schemeDTO.getStimulateFrequency(), schemeVO, patientDeviceVO));
                }

                if (Objects.nonNull(schemeDTO.getStimulateTimes())) {
                    schemeVO.setStimulateTimes(stimulateTimeService.add(schemeDTO.getStimulateTimes(), schemeVO.getStimulateProgramGroups(), schemeVO));
                }

                //todo:通知患者模块修改调参时间
                rocketMQTemplate.convertAndSend(schemeProperties.getUpdateSchemeTopic(), AddSchemeBO.builder()
                        .patientId(schemeDTO.getPatientId())
                        .schemeDate(new Date())
                        .scheduledDate(null)
                        .build());
                return schemeVO;
            }
            throw new ServiceException("服务器忙,请稍后再试");
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        } finally {
            this.unLock(lock);
        }
    }

    @Transactional(readOnly = true)
    public Page<SchemeVO> finds(SchemeDTO schemeDTO, Pageable pageable) {
        Specification<Scheme> specification = (root, query, criteriaBuilder) -> {
            Predicate predicate = criteriaBuilder.conjunction();

            predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get(Scheme_.PATIENT_ID), schemeDTO.getPatientId()));

            if (Objects.nonNull(schemeDTO.getUserId())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get(Scheme_.USER_ID), schemeDTO.getUserId()));
            }

            if (Objects.nonNull(schemeDTO.getStartDate())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.greaterThan(root.get(Scheme_.SCHEME_DATE), schemeDTO.getStartDate()));
            }
            if (Objects.nonNull(schemeDTO.getEndDate())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.lessThanOrEqualTo(root.get(Scheme_.SCHEME_DATE), schemeDTO.getEndDate()));
            }

            if (Objects.nonNull(schemeDTO.getStatus())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get(Scheme_.STATUS), schemeDTO.getStatus()));
            }

            return predicate;
        };
        return schemeConvert.po2vo(repository.findAll(specification, pageable));
    }

    @Transactional(readOnly = true)
    public SchemeVO findByPatientId(Long patientId) {
        Optional<Scheme> optional = repository.findFirstByPatientIdAndStatusOrderBySchemeDateDesc(patientId, Status.ENABLED);
        if (optional.isEmpty()) {
            throw new ArgsException("患者[{0}]不存在程控方案", patientId);
        }

        return schemeConvert.po2voBySummary(optional.get());
    }

    @Transactional(readOnly = true)
    public SchemeVO findById(String id) {
        Optional<Scheme> optional = repository.findById(id);
        if (optional.isEmpty()) {
            throw new ArgsException("方案[{0}]不存在", id);
        }

        return schemeConvert.po2voBySummary(optional.get());
    }

    @Transactional
    public SchemeVO changeActive(String schemeId, ActiveType activeType) {
        Optional<Scheme> optional = repository.findById(schemeId);
        if (optional.isEmpty()) {
            throw new ArgsException("程控方案[{0}]不存在", schemeId);
        }
        Scheme scheme = optional.get();
        scheme.setActiveType(activeType);

        return schemeConvert.po2vo(repository.save(scheme));
    }

}
