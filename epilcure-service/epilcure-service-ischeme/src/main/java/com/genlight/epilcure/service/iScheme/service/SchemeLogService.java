package com.genlight.epilcure.service.iScheme.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.genlight.epilcure.common.core.service.BaseService;
import com.genlight.epilcure.service.iScheme.constants.enums.FileStatus;
import com.genlight.epilcure.service.iScheme.dao.entity.SchemeLog;
import com.genlight.epilcure.service.iScheme.dao.entity.SchemeLogUnit;
import com.genlight.epilcure.service.iScheme.dao.entity.SchemeLog_;
import com.genlight.epilcure.service.iScheme.dao.entity.UploadFile;
import com.genlight.epilcure.service.iScheme.dao.repository.SchemeLogRepository;
import com.genlight.epilcure.service.iScheme.pojo.convert.SchemeLogConvert;
import com.genlight.epilcure.service.iScheme.pojo.dto.SchemeLogDTO;
import com.genlight.epilcure.service.iScheme.pojo.vo.SchemeLogVO;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import jakarta.annotation.Resource;
import jakarta.persistence.criteria.Predicate;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.time.Duration;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;

@Service
@Slf4j
public class SchemeLogService extends BaseService<SchemeLog, Long, SchemeLogRepository> {

    private static final DateTimeFormatter dateFormat = DateTimeFormatter.ofPattern("yyMMddHHmmss");

    @Resource
    private ObjectMapper objectMapper;

    @Resource
    @Lazy
    private UploadFileService uploadFileService;

    @Resource
    private SchemeLogConvert convert;



    @Async
    public void readLog(MultipartFile file, UploadFile uploadFile) {
        log.info("执行readLog(),线程：{}",Thread.currentThread().getName());
        analysis(file, uploadFile);
    }


    public Map<ZipEntry, byte[]> unCompress2(InputStream inputStream) {
        Instant start = Instant.now();
        try (ZipInputStream zipInputStream = new ZipInputStream(inputStream)) {
            ZipEntry zipEntry;
            Map<ZipEntry, byte[]> zipEntryMap = new LinkedHashMap<>();
            while (Objects.nonNull((zipEntry = zipInputStream.getNextEntry()))) {
                zipEntryMap.put(zipEntry, zipInputStream.readAllBytes());
            }
            return zipEntryMap;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public Map<ZipEntry, byte[]> unCompress(InputStream inputStream) {
        Instant start = Instant.now();
        try (ZipInputStream zipInputStream = new ZipInputStream(inputStream)) {
            ZipEntry zipEntry;
            Map<ZipEntry, byte[]> zipEntryMap = new LinkedHashMap<>();
            while ((zipEntry = zipInputStream.getNextEntry()) != null) {
                ByteArrayOutputStream baos = new ByteArrayOutputStream();
                byte[] buffer = new byte[8192];
                int len;
                while ((len = zipInputStream.read(buffer)) > 0) {
                    baos.write(buffer, 0, len);
                }
                zipEntryMap.put(zipEntry, baos.toByteArray());
            }
            log.info("解压耗时: {}ms", Duration.between(start, Instant.now()).toMillis());
            return zipEntryMap;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
    private static final char[] HEX_ARRAY = "0123456789abcdef".toCharArray();

    public String bytesToHex(byte[] bytes) {
        char[] hexChars = new char[bytes.length * 2];
        for (int i = 0; i < bytes.length; i++) {
            int v = bytes[i] & 0xFF;
            hexChars[i * 2] = HEX_ARRAY[v >>> 4];
            hexChars[i * 2 + 1] = HEX_ARRAY[v & 0x0F];
        }
        return new String(hexChars);
    }

    public void analysis(MultipartFile file, UploadFile uploadFile) {

        long startTime = System.currentTimeMillis();
        log.info("开始解析文件, fileId: {}, size: {}", uploadFile.getId(), file.getSize());
        try (InputStream fileInputStream = file.getInputStream()) {
            Map<ZipEntry, byte[]> zipEntryMap = unCompress(fileInputStream);
            log.info("解压完成, 条目数: {}, 耗时: {}ms", zipEntryMap.size(), System.currentTimeMillis() - startTime);
            ArrayList<SchemeLog> schemeLogList = new ArrayList<>();

            zipEntryMap.forEach((k, bytes) -> {
                ByteBuf buf = Unpooled.buffer(bytes.length);
                try {
                    buf.writeBytes(bytes);
                    parseSchemeLog(uploadFile, buf,schemeLogList);
                } catch (Exception e) {
                    log.error("解析 ZipEntry 失败, fileId: {}", uploadFile.getId(), e);
                    throw new RuntimeException(e);
                }finally {
                    if (buf != null){
                        buf.release();
                    }
                }
            });

            log.info("解析完成, SchemeLog 数量: {}, 耗时: {}ms", schemeLogList.size(), System.currentTimeMillis() - startTime);
            if(!schemeLogList.isEmpty()){
                uploadFileService.saveSchemeLogs(schemeLogList);
            }
            uploadFileService.updateStatus(uploadFile.getId(), FileStatus.PARSE_SUCCESS);
            log.info("数据库保存完成, 耗时: {}ms", System.currentTimeMillis() - startTime);
        } catch (Exception e) {
            log.error("文件解析失败, fileId: {}", uploadFile.getId(), e);
            uploadFileService.updateStatus(uploadFile.getId(), FileStatus.PARSE_FAIL);
        }
    }

    private void parseSchemeLog(UploadFile uploadFile, ByteBuf buf,ArrayList<SchemeLog> schemeLogList) throws JsonProcessingException {
        byte[] schemeIds = new byte[16];
        buf.readBytes(schemeIds);
        SchemeLog build = SchemeLog.builder()
                .schemeId(bytesToHex(schemeIds))
                .patientId(uploadFile.getPatientId())
                .channel(buf.readByte())
                .endTime(readDate(buf))
//                            .groupA()
//                            .groupB(objectMapper.writeValueAsString(List.of(buf.readUnsignedShortLE(), buf.readUnsignedShortLE())))
//                            .groupC(objectMapper.writeValueAsString(List.of(buf.readUnsignedShortLE(), buf.readUnsignedShortLE())))
//                            .groupD(objectMapper.writeValueAsString(List.of(buf.readUnsignedShortLE(), buf.readUnsignedShortLE())))
                .build();

        List<List<Integer>> counts = new ArrayList<>();
        for (int i = 0; i < 2; i++) {
            counts.add(new ArrayList<>());
            for (int j = 0; j < 4; j++) {
                counts.get(i).add(buf.readUnsignedShortLE());
            }
        }
        build.setGroupA(objectMapper.writeValueAsString(List.of(counts.get(0).get(0), counts.get(1).get(0))));
        build.setGroupB(objectMapper.writeValueAsString(List.of(counts.get(0).get(1), counts.get(1).get(1))));
        build.setGroupC(objectMapper.writeValueAsString(List.of(counts.get(0).get(2), counts.get(1).get(2))));
        build.setGroupD(objectMapper.writeValueAsString(List.of(counts.get(0).get(3), counts.get(1).get(3))));

        buf.readBytes(32);
        int order = 0;
        while (buf.readableBytes() >= 7) {
            order++;
            boolean b = buf.readBoolean();
            SchemeLogUnit schemeLogUnit = SchemeLogUnit.builder()
                    .offset(buf.readUnsignedShortLE())
                    .build();
            List<Float> values = new ArrayList<>();
            List<Integer> electrics = new ArrayList<>();
            // 双通道
            if (build.getChannel() > 2) {
                values.add(buf.readFloatLE());
                values.add(buf.readFloatLE());
                if (b) {
                    electrics.add(buf.readUnsignedShortLE());
                    electrics.add(buf.readUnsignedShortLE());
                }
            } else {
                values.add(buf.readFloatLE());
                if (b) {
                    electrics.add(buf.readUnsignedShortLE());
                }
            }
            schemeLogUnit.setValues(objectMapper.writeValueAsString(values));
            schemeLogUnit.setElectric(objectMapper.writeValueAsString(electrics));
            schemeLogUnit.setSchemeLog(build);
            schemeLogUnit.setOrder(order);
            build.getSchemeLogUnits().add(schemeLogUnit);
        }
        schemeLogList.add(build);
    }


    public void saveSchemeLogs(List<SchemeLog> schemeLog) {
        try {
            repository.saveAllAndFlush(schemeLog);
        } catch (Exception e) {
            throw e;
        }
    }

    public static Date readDate(ByteBuf buf) {
        return Date.from(LocalDateTime.parse(StringUtils.leftPad(String.valueOf(buf.readByte()), 2, "0")
                        + StringUtils.leftPad(String.valueOf(buf.readByte()), 2, "0")
                        + StringUtils.leftPad(String.valueOf(buf.readByte()), 2, "0")
                        + StringUtils.leftPad(String.valueOf(buf.readByte()), 2, "0")
                        + StringUtils.leftPad(String.valueOf(buf.readByte()), 2, "0")
                        + StringUtils.leftPad(String.valueOf(buf.readByte()), 2, "0"),
                dateFormat).atZone(ZoneId.systemDefault()).toInstant());
    }

    public Page<SchemeLogVO> finds(SchemeLogDTO schemeLogDTO, Pageable pageable) {
        return convert.po2vo(repository.findAll(generateJpaSpecification(schemeLogDTO), pageable));
    }

    private Specification<SchemeLog> generateJpaSpecification(SchemeLogDTO schemeLogDTO) {

        return (root, query, criteriaBuilder) -> {
            Predicate predicate = criteriaBuilder.conjunction();
            if (Objects.nonNull(schemeLogDTO.getSchemeId())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get(SchemeLog_.schemeId), schemeLogDTO.getSchemeId()));
            }

            if (Objects.nonNull(schemeLogDTO.getPatientId())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get(SchemeLog_.patientId), schemeLogDTO.getPatientId()));
            }


            if (Objects.nonNull(schemeLogDTO.getStartTime())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.greaterThanOrEqualTo(root.get(SchemeLog_.endTime), schemeLogDTO.getStartTime()));
            }

            if (Objects.nonNull(schemeLogDTO.getEndTime())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.lessThanOrEqualTo(root.get(SchemeLog_.endTime), schemeLogDTO.getEndTime()));
            }

            return predicate;
        };
    }
}
