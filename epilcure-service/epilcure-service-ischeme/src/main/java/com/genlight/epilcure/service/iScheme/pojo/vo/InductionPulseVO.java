package com.genlight.epilcure.service.iScheme.pojo.vo;


import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.Column;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;

@Data
@SuperBuilder
@NoArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@Schema(description = "刺激方案组View Object")
public class InductionPulseVO extends StimulatePulseVO {

    @Schema(description = "感应触点")
    private TouchPointVO inductionTouchPoint;

    @Schema(description = "阈值")
    private Float threshold;

    @Schema(description = "计算最小频段")
    private Integer calMinInduction;

    @Schema(description = "计算最大频段")
    private Integer calMaxInduction;

    @Schema(description = "最小频段")
    @Column(nullable = false)
    private Integer minInduction;

    @Schema(description = "最大频段")
    @Column(nullable = false)
    private Integer maxInduction;
}
