package com.genlight.epilcure.service.iScheme.service;

import com.genlight.epilcure.common.core.service.BaseService;
import com.genlight.epilcure.service.iScheme.dao.entity.SchemeLogUnit;
import com.genlight.epilcure.service.iScheme.dao.entity.SchemeLogUnit_;
import com.genlight.epilcure.service.iScheme.dao.entity.SchemeLog_;
import com.genlight.epilcure.service.iScheme.dao.repository.SchemeLogUnitRepository;
import com.genlight.epilcure.service.iScheme.pojo.convert.SchemeLogUnitConvert;
import com.genlight.epilcure.service.iScheme.pojo.dto.SchemeLogUnitDTO;
import com.genlight.epilcure.service.iScheme.pojo.vo.SchemeLogUnitVO;
import jakarta.annotation.Resource;
import jakarta.persistence.criteria.Predicate;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

@Service
public class SchemeLogUnitService extends BaseService<SchemeLogUnit, Long, SchemeLogUnitRepository> {

    @Resource
    private SchemeLogUnitConvert convert;

    public List<SchemeLogUnitVO> finds(SchemeLogUnitDTO schemeLogUnitDTO) {
        return convert.po2vo(repository.findAll(generateJpaSpecification(schemeLogUnitDTO)));
    }

    private Specification<SchemeLogUnit> generateJpaSpecification(SchemeLogUnitDTO schemeLogUnitDTO) {
        return (root, query, criteriaBuilder) -> {
            Predicate predicate = criteriaBuilder.conjunction();
            if (Objects.nonNull(schemeLogUnitDTO.getSchemeLogId())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get(SchemeLogUnit_.schemeLog).get(SchemeLog_.id), schemeLogUnitDTO.getSchemeLogId()));
            }
            return predicate;
        };
    }
}
