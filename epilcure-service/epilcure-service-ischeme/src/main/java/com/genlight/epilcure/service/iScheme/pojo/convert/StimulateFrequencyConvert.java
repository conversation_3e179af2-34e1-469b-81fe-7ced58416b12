package com.genlight.epilcure.service.iScheme.pojo.convert;

import com.genlight.epilcure.common.core.pojo.convert.BaseConvert;
import com.genlight.epilcure.service.iScheme.dao.entity.StimulateFrequency;
import com.genlight.epilcure.service.iScheme.pojo.dto.StimulateFrequencyDTO;
import com.genlight.epilcure.service.iScheme.pojo.vo.StimulateFrequencyVO;
import org.mapstruct.Mapper;

@Mapper(config = BaseConvert.class)
public interface StimulateFrequencyConvert extends BaseConvert<StimulateFrequency, StimulateFrequencyVO, StimulateFrequencyDTO> {
}
