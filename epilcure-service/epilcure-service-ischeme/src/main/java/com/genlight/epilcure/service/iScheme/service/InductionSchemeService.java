package com.genlight.epilcure.service.iScheme.service;

import com.genlight.epilcure.api.patient.feign.IPatientDeviceController;
import com.genlight.epilcure.api.patient.pojo.vo.PatientDeviceVO;
import com.genlight.epilcure.common.core.dao.enums.Status;
import com.genlight.epilcure.common.core.service.BaseService;
import com.genlight.epilcure.common.core.service.exception.ServiceException;
import com.genlight.epilcure.service.iScheme.dao.entity.InductionScheme;
import com.genlight.epilcure.service.iScheme.dao.entity.InductionScheme_;
import com.genlight.epilcure.service.iScheme.dao.repository.InductionSchemeRepository;
import com.genlight.epilcure.service.iScheme.pojo.convert.InductionSchemeConvert;
import com.genlight.epilcure.service.iScheme.pojo.dto.InductionSchemeDTO;
import com.genlight.epilcure.service.iScheme.pojo.vo.InductionGroupVO;
import com.genlight.epilcure.service.iScheme.pojo.vo.InductionSchemeVO;
import jakarta.annotation.Resource;
import jakarta.persistence.criteria.Predicate;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;


@Service
public class InductionSchemeService extends BaseService<InductionScheme, String, InductionSchemeRepository> {

    @Resource
    private InductionSchemeConvert inductionSchemeConvert;

    @Resource
    private IPatientDeviceController iPatientDeviceController;

    @Resource
    private InductionGroupService inductionGroupService;

    @Resource
    private RedissonClient redissonClient;

    @Transactional
    public InductionSchemeVO add(InductionSchemeDTO inductionSchemeDTO) {

        RLock lock = redissonClient.getLock("addInductionScheme%s".formatted(inductionSchemeDTO.getPatientId()));
        try {
            if (lock.tryLock(5, 5, TimeUnit.SECONDS)) {

                String today = DateFormatUtils.format(inductionSchemeDTO.getSchemeDate(), "yyyy-MM-dd 00:00:00");
                String tomorrow = DateFormatUtils.format(new Date(inductionSchemeDTO.getSchemeDate().getTime() + 1000 * 60 * 60 * 24), "yyyy-MM-dd 00:00:00");

                Date todayDate;
                Date tomorrowDate;
                try {
                    SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd hh:mm:ss");
                    todayDate = simpleDateFormat.parse(today);
                    tomorrowDate = simpleDateFormat.parse(tomorrow);
                } catch (ParseException e) {
                    throw new RuntimeException(e);
                }

                if (repository.existsById(inductionSchemeDTO.getId())) {
                    throw new ServiceException("方案Id[{0}]已存在", inductionSchemeDTO.getId());
                }

                Optional<InductionScheme> optional = repository.findByPatientIdAndSchemeDateBetweenAndStatus(inductionSchemeDTO.getPatientId(), todayDate, tomorrowDate, Status.ENABLED);

                if (optional.isPresent()) {
                    InductionScheme inductionScheme = optional.get();
                    if (inductionSchemeDTO.getSchemeDate().before(inductionScheme.getSchemeDate())) {
                        inductionSchemeDTO.setStatus(Status.DISABLED);
                    } else {
                        inductionScheme.setStatus(Status.DISABLED);
                    }
                }
                InductionScheme inductionScheme = inductionSchemeConvert.dto2po(inductionSchemeDTO);

                inductionScheme.setUserId(getUserId());
                inductionScheme.setUserName(getNikeName());
                inductionScheme.setUserPhone(getUserName());


                PatientDeviceVO patientDeviceVO = iPatientDeviceController.findByPatientId(inductionSchemeDTO.getPatientId());
                List<InductionGroupVO> groups = inductionGroupService.add(inductionSchemeDTO.getInductionGroups(), inductionScheme, patientDeviceVO);
                InductionSchemeVO inductionSchemeVO = inductionSchemeConvert.po2vo(repository.saveAndFlush(inductionScheme));
                inductionSchemeVO.getInductionGroups().addAll(groups);

                return inductionSchemeVO;
            }
            throw new ServiceException("服务器忙,请稍后再试");
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        } finally {
            unLock(lock);
        }
    }

    @Transactional(readOnly = true)
    public InductionSchemeVO findById(String id) {
        Optional<InductionScheme> optional = repository.findById(id);
        if (optional.isEmpty()) {
            throw new ServiceException("闭环方案[{0}]不存在", id);
        }

        return inductionSchemeConvert.po2voSummary(optional.get());
    }


    @Transactional(readOnly = true)
    public Page<InductionSchemeVO> finds(InductionSchemeDTO inductionSchemeDTO, Pageable pageable) {
        Specification<InductionScheme> specification = (root, query, criteriaBuilder) -> {
            Predicate predicate = criteriaBuilder.conjunction();
            if (Objects.nonNull(inductionSchemeDTO.getPatientId())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get(InductionScheme_.PATIENT_ID), inductionSchemeDTO.getPatientId()));
            }

            if (Objects.nonNull(inductionSchemeDTO.getStatus())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get(InductionScheme_.STATUS), inductionSchemeDTO.getStatus()));
            }

            if (Objects.nonNull(inductionSchemeDTO.getStartDate())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.greaterThanOrEqualTo(root.get(InductionScheme_.SCHEME_DATE), inductionSchemeDTO.getStartDate()));
            }

            if (Objects.nonNull(inductionSchemeDTO.getEndDate())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.lessThanOrEqualTo(root.get(InductionScheme_.SCHEME_DATE), inductionSchemeDTO.getEndDate()));
            }


            return predicate;
        };

        return inductionSchemeConvert.po2vo(repository.findAll(specification, pageable));
    }
}
