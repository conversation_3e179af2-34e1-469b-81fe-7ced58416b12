package com.genlight.epilcure.service.iScheme.dao.entity;

import com.genlight.epilcure.common.core.dao.entity.TEntity;
import com.genlight.epilcure.common.core.dao.generator.annotation.SignOrder;
import jakarta.persistence.*;
import lombok.*;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;
import org.hibernate.annotations.Comment;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import java.util.HashSet;
import java.util.Set;

@Getter
@Setter
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString
@Entity
@Cacheable
@DynamicInsert
@DynamicUpdate
@SignOrder(0)
@Table(name = "t_induction_group")
public class InductionGroup extends TEntity {
    @Comment("序列")
    @Column(nullable = false)
    @SignOrder(5)
    private String no;

    @Comment("组名")
    @Column(nullable = false)
    @SignOrder(6)
    private String name;

    @SignOrder(5)
    @Comment("刺激时长")
    private Integer stimulate;

    @SignOrder(6)
    @Comment("感应时长")
    private Integer induction;

    @SignOrder(7)
    @Comment("软启动")
    private Integer startup;

    @Comment("fft窗宽")
    private Integer fftWidth;

    @Comment("单侧控制")
    private Integer single;


    @SignOrder(8)
    @Comment("尾迹时长")
    private Integer wake;

    @SignOrder(9)
    @Comment("特征时长")
    private Integer characteristic;

    @SignOrder(10)
    @Comment("覆盖率")
    private Integer overlap;

    @SignOrder(11)
    @Comment("阈值类型")
    private Integer thresholdType;

    @SignOrder(12)
    @Comment("能量类型")
    private Integer energyType;

    @SignOrder(13)
    @Comment("算法类型")
    private Integer algorithmType;

    @Comment("程控信息")
    @ToString.Exclude
    @Builder.Default
    @ManyToMany(cascade = CascadeType.PERSIST)
    private Set<InductionPulse> inductionPulses = new HashSet<>();

    @ToString.Exclude
    @Comment("方案")
    @ManyToMany(mappedBy = "inductionGroups")
    private Set<InductionScheme> inductionSchemes = new HashSet<>();
}
