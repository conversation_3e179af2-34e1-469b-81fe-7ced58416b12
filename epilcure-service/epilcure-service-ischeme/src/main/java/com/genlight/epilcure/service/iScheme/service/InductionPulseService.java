package com.genlight.epilcure.service.iScheme.service;

import com.genlight.epilcure.api.patient.pojo.vo.PatientDeviceVO;
import com.genlight.epilcure.api.patient.pojo.vo.SurgeryElectrodeVO;
import com.genlight.epilcure.common.core.service.BaseService;
import com.genlight.epilcure.common.core.service.exception.ArgsException;
import com.genlight.epilcure.service.iScheme.dao.entity.InductionGroup;
import com.genlight.epilcure.service.iScheme.dao.entity.InductionPulse;
import com.genlight.epilcure.service.iScheme.dao.repository.InductionPulseRepository;
import com.genlight.epilcure.service.iScheme.pojo.convert.InductionPulseConvert;
import com.genlight.epilcure.service.iScheme.pojo.dto.InductionPulseDTO;
import com.genlight.epilcure.service.iScheme.pojo.dto.TouchPointDTO;
import com.genlight.epilcure.service.iScheme.pojo.vo.InductionPulseVO;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
public class InductionPulseService extends BaseService<InductionPulse, Long, InductionPulseRepository> {

    @Resource
    private InductionPulseConvert inductionPulseConvert;

    public void checkTouchPoint(TouchPointDTO touchPointDTO, PatientDeviceVO patientDeviceVO) {
        // 检测电极
        if (Arrays.stream(touchPointDTO.getPolarity()).filter(p -> p == 1).findAny().isEmpty()) {
            throw new ArgsException("通道[{0}]至少配置一个正极触点", touchPointDTO.getPosition());
        }

        if (Arrays.stream(touchPointDTO.getPolarity()).filter(p -> p == 2).findAny().isEmpty()) {
            throw new ArgsException("通道[{0}]至少配置一个负极触点", touchPointDTO.getPosition());
        }

        Optional<SurgeryElectrodeVO> optional = patientDeviceVO.getSurgeryElectrodes().stream().filter(s -> s.getPosition().equals(touchPointDTO.getPosition())).findAny();
        if (optional.isEmpty()) {
            throw new ArgsException("通道[{0}]没有设备", touchPointDTO.getPosition());
        }
    }

    public void checkInductionTouchPoint(TouchPointDTO touchPointDTO, PatientDeviceVO patientDeviceVO) {
        // 检测电极
        List<Integer> anodes = Arrays.stream(touchPointDTO.getPolarity()).filter(p -> p == 1).collect(Collectors.toList());
        List<Integer> cathodes = Arrays.stream(touchPointDTO.getPolarity()).filter(p -> p == 1).collect(Collectors.toList());
        if (anodes.isEmpty()) {
            throw new ArgsException("通道[{0}]至少配置一个正极触点", touchPointDTO.getPosition());
        }

        if (cathodes.isEmpty()) {
            throw new ArgsException("通道[{0}]至少配置一个负极触点", touchPointDTO.getPosition());
        }

        if (anodes.size() > 1 || cathodes.size() > 1) {
            throw new ArgsException("通道[{0}]只能有一组+,-极", touchPointDTO.getPosition());
        }

        Optional<SurgeryElectrodeVO> optional = patientDeviceVO.getSurgeryElectrodes().stream().filter(s -> s.getPosition().equals(touchPointDTO.getPosition())).findAny();
        if (optional.isEmpty()) {
            throw new ArgsException("通道[{0}]没有设备", touchPointDTO.getPosition());
        }
    }


    @Transactional
    public List<InductionPulseVO> add(List<InductionPulseDTO> inductionPulseDTOS, PatientDeviceVO patientDeviceVO, InductionGroup inductionGroup) {
        List<InductionPulseVO> resultMap = new ArrayList<>();
        inductionPulseDTOS.forEach(inductionPulseDTO -> {
            checkTouchPoint(inductionPulseDTO.getTouchPoint(), patientDeviceVO);
            checkInductionTouchPoint(inductionPulseDTO.getInductionTouchPoint(), patientDeviceVO);
            InductionPulse inductionPulse = inductionPulseConvert.dto2po(inductionPulseDTO);

            inductionGroup.getInductionPulses().add(inductionPulse);


            InductionPulseVO inductionPulseVO = inductionPulseConvert.po2vo(repository.save(inductionPulse));
            resultMap.add(inductionPulseVO);

        });

        return resultMap;
    }

}
