package com.genlight.epilcure.service.iScheme.controller;


import com.fasterxml.jackson.annotation.JsonView;
import com.genlight.epilcure.common.core.mvc.response.JsonResult;
import com.genlight.epilcure.common.core.pojo.valid.Find;
import com.genlight.epilcure.service.iScheme.pojo.dto.InductionSchemeDTO;
import com.genlight.epilcure.service.iScheme.pojo.vo.InductionSchemeVO;
import com.genlight.epilcure.service.iScheme.service.InductionSchemeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/iScheme/inductionSchemes")
@Tag(name = "InductionSchemeController", description = "闭环方案方案相关接口")
public class InductionSchemeController {

    @Resource
    private InductionSchemeService inductionSchemeService;

    @PostMapping
    @Operation(summary = "添加闭环方案")
    public JsonResult<InductionSchemeVO> add(@RequestBody InductionSchemeDTO inductionSchemeDTO) {
        return JsonResult.ok(inductionSchemeService.add(inductionSchemeDTO));
    }

    @GetMapping
    @Operation(summary = "根据条件查询方案列表")
    public JsonResult<Page<InductionSchemeVO>> finds(@JsonView(Find.class) InductionSchemeDTO inductionSchemeDTO, Pageable pageable) {
        return JsonResult.ok(inductionSchemeService.finds(inductionSchemeDTO, pageable));
    }

    @GetMapping("/{id}")
    @Operation(summary = "根据Id查询方案详细信息")
    public JsonResult<InductionSchemeVO> findById(@PathVariable String id) {
        return JsonResult.ok(inductionSchemeService.findById(id));
    }
}
