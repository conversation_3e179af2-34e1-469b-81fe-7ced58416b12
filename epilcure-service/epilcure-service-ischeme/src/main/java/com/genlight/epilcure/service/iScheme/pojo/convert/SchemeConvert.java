package com.genlight.epilcure.service.iScheme.pojo.convert;

import com.genlight.epilcure.common.core.pojo.convert.BaseConvert;
import com.genlight.epilcure.common.core.pojo.convert.qualified.Summary;
import com.genlight.epilcure.service.iScheme.dao.entity.Scheme;
import com.genlight.epilcure.service.iScheme.pojo.dto.SchemeDTO;
import com.genlight.epilcure.service.iScheme.pojo.vo.SchemeVO;
import org.mapstruct.BeanMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.NullValueCheckStrategy;

@Mapper(config = BaseConvert.class)
public interface SchemeConvert extends BaseConvert<Scheme, SchemeVO, SchemeDTO> {
    @Mapping(target = "stimulateProgramGroups", ignore = true)
    @Mapping(target = "stimulateTimes", ignore = true)
    @Mapping(target = "stimulateFrequency", ignore = true)
    @BeanMapping(nullValueCheckStrategy = NullValueCheckStrategy.ON_IMPLICIT_CONVERSION)
    @Override
    SchemeVO po2vo(Scheme scheme);

    @Summary
    @BeanMapping(nullValueCheckStrategy = NullValueCheckStrategy.ON_IMPLICIT_CONVERSION)
    SchemeVO po2voBySummary(Scheme scheme);

    @Mapping(target = "stimulateProgramGroups", ignore = true)
    @Mapping(target = "stimulateTimes", ignore = true)
    @Mapping(target = "stimulateFrequency", ignore = true)
    @Mapping(target = "status", expression = "java(java.util.Optional.ofNullable(schemeDTO.getStatus()).orElse(com.genlight.epilcure.common.core.dao.enums.Status.ENABLED))")
    @BeanMapping(nullValueCheckStrategy = NullValueCheckStrategy.ON_IMPLICIT_CONVERSION)
    @Override
    Scheme dto2po(SchemeDTO schemeDTO);
}
