package com.genlight.epilcure.service.iScheme.dao.entity;

import com.genlight.epilcure.common.core.dao.entity.TEntity;
import com.genlight.epilcure.common.core.dao.generator.annotation.SignOrder;
import jakarta.persistence.Cacheable;
import jakarta.persistence.Entity;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import lombok.*;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;
import org.hibernate.annotations.Comment;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;


@Getter
@Setter
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString
@Entity
@Cacheable
@DynamicInsert
@DynamicUpdate
@SignOrder(0)
@Table(name = "t_scheme_log_unit")
public class SchemeLogUnit extends TEntity {

    @Comment("关联日志")
    @ToString.Exclude
    @ManyToOne
    private SchemeLog schemeLog;

    @Comment("偏移量")
    private Integer offset;

    @Comment("能量或能量比")
    private String values;

    @Comment("电流")
    private String electric;

    @Comment("排序字段")
    private Integer order;
}
