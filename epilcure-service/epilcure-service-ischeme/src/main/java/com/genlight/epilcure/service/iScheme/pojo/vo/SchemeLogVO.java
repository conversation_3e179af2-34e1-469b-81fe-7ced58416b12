package com.genlight.epilcure.service.iScheme.pojo.vo;

import com.genlight.epilcure.common.core.pojo.vo.BaseVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;

import java.util.Date;
import java.util.List;


@Data
@SuperBuilder
@NoArgsConstructor
@Accessors(chain = true)
@Schema(description = "运行日志View Object")
public class SchemeLogVO extends BaseVO {
    @Schema(description = "方案Id")
    private String schemeId;

    @Schema(description = "患者Id")
    private Long patientId;

    @Schema(description = "通道状态")
    private Byte channel;

    @Schema(description = "结束时间")
    private Date endTime;

    @Schema(description = "A组执行次数,[0,1]")
    private List<Long> groupA;

    @Schema(description = "B组执行次数,[0,1]")
    private List<Long> groupB;
    @Schema(description = "C组执行次数,[0,1]")
    private List<Long> groupC;
    @Schema(description = "D组执行次数,[0,1]")
    private List<Long> groupD;
}
