package com.genlight.epilcure.service.iScheme.controller;


import com.genlight.epilcure.common.core.mvc.response.JsonResult;
import com.genlight.epilcure.service.iScheme.pojo.dto.UploadFileDTO;
import com.genlight.epilcure.service.iScheme.pojo.vo.UploadFileVO;
import com.genlight.epilcure.service.iScheme.service.UploadFileService;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

@RestController
@RequestMapping("/api/iScheme/upload_file")
@Tag(name = "ISBSUploadController", description = "方案运行日志文件接口")
public class UploadFileController {

    @Resource
    private UploadFileService uploadFileService;

    @PostMapping(value = "/{patientId}", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public JsonResult<Void> upload(@PathVariable Long patientId, @RequestPart MultipartFile file) {
        uploadFileService.uploadFile(patientId, file);
        return JsonResult.ok();
    }

    @GetMapping("/finds")
    public JsonResult<Page<UploadFileVO>> finds(UploadFileDTO uploadFileDTO, Pageable pageable) {
        return JsonResult.ok(uploadFileService.finds(uploadFileDTO, pageable));
    }
}
