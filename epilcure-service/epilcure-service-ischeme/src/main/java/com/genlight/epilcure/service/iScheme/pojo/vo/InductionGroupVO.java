package com.genlight.epilcure.service.iScheme.pojo.vo;

import com.genlight.epilcure.common.core.pojo.vo.BaseVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;

import java.util.ArrayList;
import java.util.List;


@Data
@SuperBuilder
@NoArgsConstructor
@Accessors(chain = true)
@Schema(description = "闭环方案组View Object")
public class InductionGroupVO extends BaseVO {

    @Schema(description = "序列")
    private String no;

    @Schema(description = "组名")
    private String name;

    @Schema(description = "刺激时长")
    private Integer stimulate;

    @Schema(description = "fft窗宽")
    private Integer fftWidth;

    @Schema(description = "单侧控制")
    private Integer single;

    @Schema(description = "感应时长")
    private Integer induction;

    @Schema(description = "软启动")
    private Integer startup;

    @Schema(description = "尾迹时长")
    private Integer wake;

    @Schema(description = "特征时长")
    private Integer characteristic;

    @Schema(description = "覆盖率")
    private Integer overlap;

    @Schema(description = "阈值类型")
    private Integer thresholdType;

    @Schema(description = "能量类型")
    private Integer energyType;

    @Schema(description = "算法类型")
    private Integer algorithmType;

    @Builder.Default
    @Schema(description = "程控信息")
    private List<InductionPulseVO> inductionPulses = new ArrayList<>();
}
