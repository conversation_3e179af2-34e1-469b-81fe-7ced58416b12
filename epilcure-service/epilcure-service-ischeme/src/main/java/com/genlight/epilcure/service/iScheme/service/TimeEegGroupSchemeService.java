package com.genlight.epilcure.service.iScheme.service;

import com.genlight.epilcure.api.patient.feign.IPatientController;
import com.genlight.epilcure.common.core.dao.enums.Status;
import com.genlight.epilcure.common.core.service.BaseService;
import com.genlight.epilcure.common.core.service.exception.ArgsException;
import com.genlight.epilcure.service.iScheme.dao.entity.TimeEegGroupScheme;
import com.genlight.epilcure.service.iScheme.dao.entity.TimeEegGroupScheme_;
import com.genlight.epilcure.service.iScheme.dao.repository.TimeEegGroupSchemeRepository;
import com.genlight.epilcure.service.iScheme.pojo.convert.TimeEegGroupSchemeConvert;
import com.genlight.epilcure.service.iScheme.pojo.dto.TimeEegGroupSchemeDTO;
import com.genlight.epilcure.service.iScheme.pojo.vo.TimeEegGroupSchemeVO;
import jakarta.annotation.Resource;
import jakarta.persistence.criteria.Predicate;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Objects;
import java.util.Optional;

@Service
public class TimeEegGroupSchemeService extends BaseService<TimeEegGroupScheme, String, TimeEegGroupSchemeRepository> {
    @Resource
    private TimeEegGroupSchemeConvert timeEegGroupSchemeConvert;
    @Resource
    private IPatientController iPatientController;

    @Transactional
    public TimeEegGroupSchemeVO add(TimeEegGroupSchemeDTO timeEegGroupSchemeDTO) {

        Optional<TimeEegGroupScheme> optional = repository.findById(timeEegGroupSchemeDTO.getId());
        if (optional.isPresent()) {
            throw new ArgsException("定时采集方案Id[{0}]已存在", timeEegGroupSchemeDTO.getId());
        }

        iPatientController.findById(timeEegGroupSchemeDTO.getPatientId());

        timeEegGroupSchemeDTO.getTimeEegSchemes().forEach(tes -> {
            tes.getTouchPoints().forEach(tp -> {
                int positive = 0;
                int negative = 0;
                for (Integer integer : tp.getPolarity()) {
                    if (integer.equals(1)) {
                        positive++;
                    } else if (integer.equals(2)) {
                        negative++;
                    }
                }

                if (positive != 1 || negative != 1) {
                    throw new ArgsException("需要分配一对(+,-)极");
                }
            });
        });

        String today = DateFormatUtils.format(timeEegGroupSchemeDTO.getSchemeDate(), "yyyy-MM-dd 00:00:00");
        String tomorrow = DateFormatUtils.format(new Date(timeEegGroupSchemeDTO.getSchemeDate().getTime() + 1000 * 60 * 60 * 24), "yyyy-MM-dd 00:00:00");
        Date todayDate;
        Date tomorrowDate;
        try {
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd hh:mm:ss");
            todayDate = simpleDateFormat.parse(today);
            tomorrowDate = simpleDateFormat.parse(tomorrow);
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }

        TimeEegGroupScheme timeEegGroupScheme = timeEegGroupSchemeConvert.dto2po(timeEegGroupSchemeDTO);

        optional = repository.findByPatientIdAndSchemeDateBetweenAndStatus(timeEegGroupSchemeDTO.getPatientId(), todayDate, tomorrowDate, Status.ENABLED);
        if (optional.isPresent()) {
            TimeEegGroupScheme oldScheme = optional.get();
            if (oldScheme.getSchemeDate().after(timeEegGroupScheme.getSchemeDate())) {
                timeEegGroupScheme.setStatus(Status.DISABLED);
            } else {
                oldScheme.setStatus(Status.DISABLED);
            }
        }

        timeEegGroupScheme.getTimeEegSchemes().forEach(tes -> {
            tes.setTimeEegGroupScheme(timeEegGroupScheme);
        });

        return timeEegGroupSchemeConvert.po2vo(repository.save(timeEegGroupScheme));
    }

    @Transactional(readOnly = true)
    public TimeEegGroupSchemeVO findByPatientId(Long patientId) {
        Optional<TimeEegGroupScheme> optional = repository.findFirstByPatientIdAndStatusOrderBySchemeDateDesc(patientId, Status.ENABLED);
        if (optional.isEmpty()) {
            return null;
        }
        return timeEegGroupSchemeConvert.po2vo(optional.get());
    }

    @Transactional(readOnly = true)
    public TimeEegGroupSchemeVO findById(String id) {
        Optional<TimeEegGroupScheme> optional = repository.findById(id);
        if (optional.isEmpty()) {
            throw new ArgsException("患者实时脑电采集方案[{0}]不存在");
        }
        return timeEegGroupSchemeConvert.po2vo(optional.get());
    }

    @Transactional(readOnly = true)
    public Page<TimeEegGroupSchemeVO> finds(TimeEegGroupSchemeDTO timeEegGroupSchemeDTO, Pageable pageable) {
        Specification<TimeEegGroupScheme> specification = (root, query, criteriaBuilder) -> {
            Predicate predicate = criteriaBuilder.conjunction();
            if (Objects.nonNull(timeEegGroupSchemeDTO.getStartDate())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.greaterThan(root.get(TimeEegGroupScheme_.SCHEME_DATE), timeEegGroupSchemeDTO.getStartDate()));
            }
            if (Objects.nonNull(timeEegGroupSchemeDTO.getEndDate())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.lessThan(root.get(TimeEegGroupScheme_.SCHEME_DATE), timeEegGroupSchemeDTO.getEndDate()));
            }
            if (Objects.nonNull(timeEegGroupSchemeDTO.getPatientId())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get(TimeEegGroupScheme_.PATIENT_ID), timeEegGroupSchemeDTO.getPatientId()));
            }
            if (Objects.nonNull(timeEegGroupSchemeDTO.getStatus())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get(TimeEegGroupScheme_.STATUS), timeEegGroupSchemeDTO.getStatus()));
            }
            return predicate;
        };
        return timeEegGroupSchemeConvert.po2vo(repository.findAll(specification, pageable));
    }
}
