package com.genlight.epilcure.service.iScheme;

import net.bytebuddy.agent.ByteBuddyAgent;
import org.aspectj.weaver.loadtime.Agent;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.instrument.InstrumentationSavingAgent;
import org.springframework.scheduling.annotation.EnableAsync;

import java.lang.instrument.Instrumentation;

@EnableDiscoveryClient
@SpringBootApplication(scanBasePackages = "com.genlight.epilcure")
@EnableAsync
public class ISchemeServiceApplication {

    public static void main(String[] args) {
        Instrumentation instrumentation = ByteBuddyAgent.install();
        Agent.agentmain("", instrumentation);
        InstrumentationSavingAgent.agentmain("", instrumentation);
        SpringApplication.run(ISchemeServiceApplication.class, args);
    }
}
