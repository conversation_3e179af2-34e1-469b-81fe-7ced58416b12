2025-06-17 15:09:00.461  INFO 133052 --- [main] c.g.e.s.i.ISchemeServiceApplication      : The following 1 profile is active: "test"
2025-06-17 15:09:01.094  INFO 133052 --- [main] Aspect<PERSON> <PERSON>                           : [AspectJ] Join point 'method-execution(com.genlight.epilcure.common.core.mvc.response.JsonResult com.genlight.epilcure.service.iScheme.controller.InductionSchemeController.add(com.genlight.epilcure.service.iScheme.pojo.dto.InductionSchemeDTO))' in Type 'com.genlight.epilcure.service.iScheme.controller.InductionSchemeController' (InductionSchemeController.java:28) advised by around advice from 'com.genlight.epilcure.common.core.mvc.aspectj.AccessAspect' (AccessAspect.java)
2025-06-17 15:09:01.096  INFO 133052 --- [main] <PERSON><PERSON><PERSON> <PERSON>                           : [AspectJ] Join point 'method-execution(com.genlight.epilcure.common.core.mvc.response.JsonResult com.genlight.epilcure.service.iScheme.controller.InductionSchemeController.add(com.genlight.epilcure.service.iScheme.pojo.dto.InductionSchemeDTO))' in Type 'com.genlight.epilcure.service.iScheme.controller.InductionSchemeController' (InductionSchemeController.java:28) advised by around advice from 'com.genlight.epilcure.common.core.mvc.aspectj.LogstashAspect' (LogstashAspect.java)
2025-06-17 15:09:01.097  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] Join point 'method-execution(com.genlight.epilcure.common.core.mvc.response.JsonResult com.genlight.epilcure.service.iScheme.controller.InductionSchemeController.finds(com.genlight.epilcure.service.iScheme.pojo.dto.InductionSchemeDTO, org.springframework.data.domain.Pageable))' in Type 'com.genlight.epilcure.service.iScheme.controller.InductionSchemeController' (InductionSchemeController.java:34) advised by around advice from 'com.genlight.epilcure.common.core.mvc.aspectj.AccessAspect' (AccessAspect.java)
2025-06-17 15:09:01.098  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] Join point 'method-execution(com.genlight.epilcure.common.core.mvc.response.JsonResult com.genlight.epilcure.service.iScheme.controller.InductionSchemeController.finds(com.genlight.epilcure.service.iScheme.pojo.dto.InductionSchemeDTO, org.springframework.data.domain.Pageable))' in Type 'com.genlight.epilcure.service.iScheme.controller.InductionSchemeController' (InductionSchemeController.java:34) advised by around advice from 'com.genlight.epilcure.common.core.mvc.aspectj.LogstashAspect' (LogstashAspect.java)
2025-06-17 15:09:01.098  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] Join point 'method-execution(com.genlight.epilcure.common.core.mvc.response.JsonResult com.genlight.epilcure.service.iScheme.controller.InductionSchemeController.findById(java.lang.String))' in Type 'com.genlight.epilcure.service.iScheme.controller.InductionSchemeController' (InductionSchemeController.java:40) advised by around advice from 'com.genlight.epilcure.common.core.mvc.aspectj.AccessAspect' (AccessAspect.java)
2025-06-17 15:09:01.099  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] Join point 'method-execution(com.genlight.epilcure.common.core.mvc.response.JsonResult com.genlight.epilcure.service.iScheme.controller.InductionSchemeController.findById(java.lang.String))' in Type 'com.genlight.epilcure.service.iScheme.controller.InductionSchemeController' (InductionSchemeController.java:40) advised by around advice from 'com.genlight.epilcure.common.core.mvc.aspectj.LogstashAspect' (LogstashAspect.java)
2025-06-17 15:09:01.124  WARN 133052 --- [main] AspectJ Weaver                           : [AspectJ] define generated class failed
2025-06-17 15:09:01.126  WARN 133052 --- [main] AspectJ Weaver                           : [AspectJ] define generated class failed
2025-06-17 15:09:01.127  WARN 133052 --- [main] AspectJ Weaver                           : [AspectJ] define generated class failed
2025-06-17 15:09:01.128  WARN 133052 --- [main] AspectJ Weaver                           : [AspectJ] define generated class failed
2025-06-17 15:09:01.128  WARN 133052 --- [main] AspectJ Weaver                           : [AspectJ] define generated class failed
2025-06-17 15:09:01.129  WARN 133052 --- [main] AspectJ Weaver                           : [AspectJ] define generated class failed
2025-06-17 15:09:01.133  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] Join point 'method-execution(com.genlight.epilcure.common.core.mvc.response.JsonResult com.genlight.epilcure.service.iScheme.controller.PatientRealEegSchemeController.add(com.genlight.epilcure.service.iScheme.pojo.dto.PatientRealEegSchemeDTO))' in Type 'com.genlight.epilcure.service.iScheme.controller.PatientRealEegSchemeController' (PatientRealEegSchemeController.java:28) advised by around advice from 'com.genlight.epilcure.common.core.mvc.aspectj.AccessAspect' (AccessAspect.java)
2025-06-17 15:09:01.134  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] Join point 'method-execution(com.genlight.epilcure.common.core.mvc.response.JsonResult com.genlight.epilcure.service.iScheme.controller.PatientRealEegSchemeController.add(com.genlight.epilcure.service.iScheme.pojo.dto.PatientRealEegSchemeDTO))' in Type 'com.genlight.epilcure.service.iScheme.controller.PatientRealEegSchemeController' (PatientRealEegSchemeController.java:28) advised by around advice from 'com.genlight.epilcure.common.core.mvc.aspectj.LogstashAspect' (LogstashAspect.java)
2025-06-17 15:09:01.135  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] Join point 'method-execution(com.genlight.epilcure.common.core.mvc.response.JsonResult com.genlight.epilcure.service.iScheme.controller.PatientRealEegSchemeController.findByPatient(java.lang.Long))' in Type 'com.genlight.epilcure.service.iScheme.controller.PatientRealEegSchemeController' (PatientRealEegSchemeController.java:34) advised by around advice from 'com.genlight.epilcure.common.core.mvc.aspectj.AccessAspect' (AccessAspect.java)
2025-06-17 15:09:01.135  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] Join point 'method-execution(com.genlight.epilcure.common.core.mvc.response.JsonResult com.genlight.epilcure.service.iScheme.controller.PatientRealEegSchemeController.findByPatient(java.lang.Long))' in Type 'com.genlight.epilcure.service.iScheme.controller.PatientRealEegSchemeController' (PatientRealEegSchemeController.java:34) advised by around advice from 'com.genlight.epilcure.common.core.mvc.aspectj.LogstashAspect' (LogstashAspect.java)
2025-06-17 15:09:01.136  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] Join point 'method-execution(com.genlight.epilcure.common.core.mvc.response.JsonResult com.genlight.epilcure.service.iScheme.controller.PatientRealEegSchemeController.find(java.lang.String))' in Type 'com.genlight.epilcure.service.iScheme.controller.PatientRealEegSchemeController' (PatientRealEegSchemeController.java:40) advised by around advice from 'com.genlight.epilcure.common.core.mvc.aspectj.AccessAspect' (AccessAspect.java)
2025-06-17 15:09:01.136  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] Join point 'method-execution(com.genlight.epilcure.common.core.mvc.response.JsonResult com.genlight.epilcure.service.iScheme.controller.PatientRealEegSchemeController.find(java.lang.String))' in Type 'com.genlight.epilcure.service.iScheme.controller.PatientRealEegSchemeController' (PatientRealEegSchemeController.java:40) advised by around advice from 'com.genlight.epilcure.common.core.mvc.aspectj.LogstashAspect' (LogstashAspect.java)
2025-06-17 15:09:01.137  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] Join point 'method-execution(com.genlight.epilcure.common.core.mvc.response.JsonResult com.genlight.epilcure.service.iScheme.controller.PatientRealEegSchemeController.finds(com.genlight.epilcure.service.iScheme.pojo.dto.PatientRealEegSchemeDTO, org.springframework.data.domain.Pageable))' in Type 'com.genlight.epilcure.service.iScheme.controller.PatientRealEegSchemeController' (PatientRealEegSchemeController.java:46) advised by around advice from 'com.genlight.epilcure.common.core.mvc.aspectj.AccessAspect' (AccessAspect.java)
2025-06-17 15:09:01.137  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] Join point 'method-execution(com.genlight.epilcure.common.core.mvc.response.JsonResult com.genlight.epilcure.service.iScheme.controller.PatientRealEegSchemeController.finds(com.genlight.epilcure.service.iScheme.pojo.dto.PatientRealEegSchemeDTO, org.springframework.data.domain.Pageable))' in Type 'com.genlight.epilcure.service.iScheme.controller.PatientRealEegSchemeController' (PatientRealEegSchemeController.java:46) advised by around advice from 'com.genlight.epilcure.common.core.mvc.aspectj.LogstashAspect' (LogstashAspect.java)
2025-06-17 15:09:01.143  WARN 133052 --- [main] AspectJ Weaver                           : [AspectJ] define generated class failed
2025-06-17 15:09:01.143  WARN 133052 --- [main] AspectJ Weaver                           : [AspectJ] define generated class failed
2025-06-17 15:09:01.144  WARN 133052 --- [main] AspectJ Weaver                           : [AspectJ] define generated class failed
2025-06-17 15:09:01.145  WARN 133052 --- [main] AspectJ Weaver                           : [AspectJ] define generated class failed
2025-06-17 15:09:01.146  WARN 133052 --- [main] AspectJ Weaver                           : [AspectJ] define generated class failed
2025-06-17 15:09:01.146  WARN 133052 --- [main] AspectJ Weaver                           : [AspectJ] define generated class failed
2025-06-17 15:09:01.147  WARN 133052 --- [main] AspectJ Weaver                           : [AspectJ] define generated class failed
2025-06-17 15:09:01.148  WARN 133052 --- [main] AspectJ Weaver                           : [AspectJ] define generated class failed
2025-06-17 15:09:01.151  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] Join point 'method-execution(com.genlight.epilcure.common.core.mvc.response.JsonResult com.genlight.epilcure.service.iScheme.controller.PatientStimulateLogController.add(java.util.List))' in Type 'com.genlight.epilcure.service.iScheme.controller.PatientStimulateLogController' (PatientStimulateLogController.java:27) advised by around advice from 'com.genlight.epilcure.common.core.mvc.aspectj.AccessAspect' (AccessAspect.java)
2025-06-17 15:09:01.152  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] Join point 'method-execution(com.genlight.epilcure.common.core.mvc.response.JsonResult com.genlight.epilcure.service.iScheme.controller.PatientStimulateLogController.add(java.util.List))' in Type 'com.genlight.epilcure.service.iScheme.controller.PatientStimulateLogController' (PatientStimulateLogController.java:27) advised by around advice from 'com.genlight.epilcure.common.core.mvc.aspectj.LogstashAspect' (LogstashAspect.java)
2025-06-17 15:09:01.153  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] Join point 'method-execution(com.genlight.epilcure.common.core.mvc.response.JsonResult com.genlight.epilcure.service.iScheme.controller.PatientStimulateLogController.finds(com.genlight.epilcure.service.iScheme.pojo.dto.PatientStimulateLogDTO, org.springframework.data.domain.Pageable))' in Type 'com.genlight.epilcure.service.iScheme.controller.PatientStimulateLogController' (PatientStimulateLogController.java:34) advised by around advice from 'com.genlight.epilcure.common.core.mvc.aspectj.AccessAspect' (AccessAspect.java)
2025-06-17 15:09:01.153  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] Join point 'method-execution(com.genlight.epilcure.common.core.mvc.response.JsonResult com.genlight.epilcure.service.iScheme.controller.PatientStimulateLogController.finds(com.genlight.epilcure.service.iScheme.pojo.dto.PatientStimulateLogDTO, org.springframework.data.domain.Pageable))' in Type 'com.genlight.epilcure.service.iScheme.controller.PatientStimulateLogController' (PatientStimulateLogController.java:34) advised by around advice from 'com.genlight.epilcure.common.core.mvc.aspectj.LogstashAspect' (LogstashAspect.java)
2025-06-17 15:09:01.156  WARN 133052 --- [main] AspectJ Weaver                           : [AspectJ] define generated class failed
2025-06-17 15:09:01.157  WARN 133052 --- [main] AspectJ Weaver                           : [AspectJ] define generated class failed
2025-06-17 15:09:01.158  WARN 133052 --- [main] AspectJ Weaver                           : [AspectJ] define generated class failed
2025-06-17 15:09:01.158  WARN 133052 --- [main] AspectJ Weaver                           : [AspectJ] define generated class failed
2025-06-17 15:09:01.162  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] Join point 'method-execution(com.genlight.epilcure.common.core.mvc.response.JsonResult com.genlight.epilcure.service.iScheme.controller.SchemeController.finds(com.genlight.epilcure.service.iScheme.pojo.dto.SchemeDTO, org.springframework.data.domain.Pageable))' in Type 'com.genlight.epilcure.service.iScheme.controller.SchemeController' (SchemeController.java:25) advised by around advice from 'com.genlight.epilcure.common.core.mvc.aspectj.AccessAspect' (AccessAspect.java)
2025-06-17 15:09:01.162  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] Join point 'method-execution(com.genlight.epilcure.common.core.mvc.response.JsonResult com.genlight.epilcure.service.iScheme.controller.SchemeController.finds(com.genlight.epilcure.service.iScheme.pojo.dto.SchemeDTO, org.springframework.data.domain.Pageable))' in Type 'com.genlight.epilcure.service.iScheme.controller.SchemeController' (SchemeController.java:25) advised by around advice from 'com.genlight.epilcure.common.core.mvc.aspectj.LogstashAspect' (LogstashAspect.java)
2025-06-17 15:09:01.163  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] Join point 'method-execution(com.genlight.epilcure.common.core.mvc.response.JsonResult com.genlight.epilcure.service.iScheme.controller.SchemeController.findByPatientId(java.lang.Long))' in Type 'com.genlight.epilcure.service.iScheme.controller.SchemeController' (SchemeController.java:31) advised by around advice from 'com.genlight.epilcure.common.core.mvc.aspectj.AccessAspect' (AccessAspect.java)
2025-06-17 15:09:01.164  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] Join point 'method-execution(com.genlight.epilcure.common.core.mvc.response.JsonResult com.genlight.epilcure.service.iScheme.controller.SchemeController.findByPatientId(java.lang.Long))' in Type 'com.genlight.epilcure.service.iScheme.controller.SchemeController' (SchemeController.java:31) advised by around advice from 'com.genlight.epilcure.common.core.mvc.aspectj.LogstashAspect' (LogstashAspect.java)
2025-06-17 15:09:01.164  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] Join point 'method-execution(com.genlight.epilcure.common.core.mvc.response.JsonResult com.genlight.epilcure.service.iScheme.controller.SchemeController.find(java.lang.String))' in Type 'com.genlight.epilcure.service.iScheme.controller.SchemeController' (SchemeController.java:37) advised by around advice from 'com.genlight.epilcure.common.core.mvc.aspectj.AccessAspect' (AccessAspect.java)
2025-06-17 15:09:01.164  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] Join point 'method-execution(com.genlight.epilcure.common.core.mvc.response.JsonResult com.genlight.epilcure.service.iScheme.controller.SchemeController.find(java.lang.String))' in Type 'com.genlight.epilcure.service.iScheme.controller.SchemeController' (SchemeController.java:37) advised by around advice from 'com.genlight.epilcure.common.core.mvc.aspectj.LogstashAspect' (LogstashAspect.java)
2025-06-17 15:09:01.165  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] Join point 'method-execution(com.genlight.epilcure.common.core.mvc.response.JsonResult com.genlight.epilcure.service.iScheme.controller.SchemeController.add(com.genlight.epilcure.service.iScheme.pojo.dto.SchemeDTO))' in Type 'com.genlight.epilcure.service.iScheme.controller.SchemeController' (SchemeController.java:43) advised by around advice from 'com.genlight.epilcure.common.core.mvc.aspectj.AccessAspect' (AccessAspect.java)
2025-06-17 15:09:01.165  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] Join point 'method-execution(com.genlight.epilcure.common.core.mvc.response.JsonResult com.genlight.epilcure.service.iScheme.controller.SchemeController.add(com.genlight.epilcure.service.iScheme.pojo.dto.SchemeDTO))' in Type 'com.genlight.epilcure.service.iScheme.controller.SchemeController' (SchemeController.java:43) advised by around advice from 'com.genlight.epilcure.common.core.mvc.aspectj.LogstashAspect' (LogstashAspect.java)
2025-06-17 15:09:01.170  WARN 133052 --- [main] AspectJ Weaver                           : [AspectJ] define generated class failed
2025-06-17 15:09:01.171  WARN 133052 --- [main] AspectJ Weaver                           : [AspectJ] define generated class failed
2025-06-17 15:09:01.171  WARN 133052 --- [main] AspectJ Weaver                           : [AspectJ] define generated class failed
2025-06-17 15:09:01.172  WARN 133052 --- [main] AspectJ Weaver                           : [AspectJ] define generated class failed
2025-06-17 15:09:01.173  WARN 133052 --- [main] AspectJ Weaver                           : [AspectJ] define generated class failed
2025-06-17 15:09:01.174  WARN 133052 --- [main] AspectJ Weaver                           : [AspectJ] define generated class failed
2025-06-17 15:09:01.175  WARN 133052 --- [main] AspectJ Weaver                           : [AspectJ] define generated class failed
2025-06-17 15:09:01.175  WARN 133052 --- [main] AspectJ Weaver                           : [AspectJ] define generated class failed
2025-06-17 15:09:01.178  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] Join point 'method-execution(com.genlight.epilcure.common.core.mvc.response.JsonResult com.genlight.epilcure.service.iScheme.controller.SchemeLogController.finds(com.genlight.epilcure.service.iScheme.pojo.dto.SchemeLogDTO, org.springframework.data.domain.Pageable))' in Type 'com.genlight.epilcure.service.iScheme.controller.SchemeLogController' (SchemeLogController.java:26) advised by around advice from 'com.genlight.epilcure.common.core.mvc.aspectj.AccessAspect' (AccessAspect.java)
2025-06-17 15:09:01.179  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] Join point 'method-execution(com.genlight.epilcure.common.core.mvc.response.JsonResult com.genlight.epilcure.service.iScheme.controller.SchemeLogController.finds(com.genlight.epilcure.service.iScheme.pojo.dto.SchemeLogDTO, org.springframework.data.domain.Pageable))' in Type 'com.genlight.epilcure.service.iScheme.controller.SchemeLogController' (SchemeLogController.java:26) advised by around advice from 'com.genlight.epilcure.common.core.mvc.aspectj.LogstashAspect' (LogstashAspect.java)
2025-06-17 15:09:01.182  WARN 133052 --- [main] AspectJ Weaver                           : [AspectJ] define generated class failed
2025-06-17 15:09:01.182  WARN 133052 --- [main] AspectJ Weaver                           : [AspectJ] define generated class failed
2025-06-17 15:09:01.185  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] Join point 'method-execution(com.genlight.epilcure.common.core.mvc.response.JsonResult com.genlight.epilcure.service.iScheme.controller.SchemeLogUnitController.finds(com.genlight.epilcure.service.iScheme.pojo.dto.SchemeLogUnitDTO))' in Type 'com.genlight.epilcure.service.iScheme.controller.SchemeLogUnitController' (SchemeLogUnitController.java:26) advised by around advice from 'com.genlight.epilcure.common.core.mvc.aspectj.AccessAspect' (AccessAspect.java)
2025-06-17 15:09:01.185  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] Join point 'method-execution(com.genlight.epilcure.common.core.mvc.response.JsonResult com.genlight.epilcure.service.iScheme.controller.SchemeLogUnitController.finds(com.genlight.epilcure.service.iScheme.pojo.dto.SchemeLogUnitDTO))' in Type 'com.genlight.epilcure.service.iScheme.controller.SchemeLogUnitController' (SchemeLogUnitController.java:26) advised by around advice from 'com.genlight.epilcure.common.core.mvc.aspectj.LogstashAspect' (LogstashAspect.java)
2025-06-17 15:09:01.187  WARN 133052 --- [main] AspectJ Weaver                           : [AspectJ] define generated class failed
2025-06-17 15:09:01.188  WARN 133052 --- [main] AspectJ Weaver                           : [AspectJ] define generated class failed
2025-06-17 15:09:01.191  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] Join point 'method-execution(com.genlight.epilcure.common.core.mvc.response.JsonResult com.genlight.epilcure.service.iScheme.controller.TimeEegGroupSchemeController.add(com.genlight.epilcure.service.iScheme.pojo.dto.TimeEegGroupSchemeDTO))' in Type 'com.genlight.epilcure.service.iScheme.controller.TimeEegGroupSchemeController' (TimeEegGroupSchemeController.java:25) advised by around advice from 'com.genlight.epilcure.common.core.mvc.aspectj.AccessAspect' (AccessAspect.java)
2025-06-17 15:09:01.191  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] Join point 'method-execution(com.genlight.epilcure.common.core.mvc.response.JsonResult com.genlight.epilcure.service.iScheme.controller.TimeEegGroupSchemeController.add(com.genlight.epilcure.service.iScheme.pojo.dto.TimeEegGroupSchemeDTO))' in Type 'com.genlight.epilcure.service.iScheme.controller.TimeEegGroupSchemeController' (TimeEegGroupSchemeController.java:25) advised by around advice from 'com.genlight.epilcure.common.core.mvc.aspectj.LogstashAspect' (LogstashAspect.java)
2025-06-17 15:09:01.192  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] Join point 'method-execution(com.genlight.epilcure.common.core.mvc.response.JsonResult com.genlight.epilcure.service.iScheme.controller.TimeEegGroupSchemeController.findByPatient(java.lang.Long))' in Type 'com.genlight.epilcure.service.iScheme.controller.TimeEegGroupSchemeController' (TimeEegGroupSchemeController.java:31) advised by around advice from 'com.genlight.epilcure.common.core.mvc.aspectj.AccessAspect' (AccessAspect.java)
2025-06-17 15:09:01.192  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] Join point 'method-execution(com.genlight.epilcure.common.core.mvc.response.JsonResult com.genlight.epilcure.service.iScheme.controller.TimeEegGroupSchemeController.findByPatient(java.lang.Long))' in Type 'com.genlight.epilcure.service.iScheme.controller.TimeEegGroupSchemeController' (TimeEegGroupSchemeController.java:31) advised by around advice from 'com.genlight.epilcure.common.core.mvc.aspectj.LogstashAspect' (LogstashAspect.java)
2025-06-17 15:09:01.192  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] Join point 'method-execution(com.genlight.epilcure.common.core.mvc.response.JsonResult com.genlight.epilcure.service.iScheme.controller.TimeEegGroupSchemeController.find(java.lang.String))' in Type 'com.genlight.epilcure.service.iScheme.controller.TimeEegGroupSchemeController' (TimeEegGroupSchemeController.java:37) advised by around advice from 'com.genlight.epilcure.common.core.mvc.aspectj.AccessAspect' (AccessAspect.java)
2025-06-17 15:09:01.193  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] Join point 'method-execution(com.genlight.epilcure.common.core.mvc.response.JsonResult com.genlight.epilcure.service.iScheme.controller.TimeEegGroupSchemeController.find(java.lang.String))' in Type 'com.genlight.epilcure.service.iScheme.controller.TimeEegGroupSchemeController' (TimeEegGroupSchemeController.java:37) advised by around advice from 'com.genlight.epilcure.common.core.mvc.aspectj.LogstashAspect' (LogstashAspect.java)
2025-06-17 15:09:01.194  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] Join point 'method-execution(com.genlight.epilcure.common.core.mvc.response.JsonResult com.genlight.epilcure.service.iScheme.controller.TimeEegGroupSchemeController.finds(com.genlight.epilcure.service.iScheme.pojo.dto.TimeEegGroupSchemeDTO, org.springframework.data.domain.Pageable))' in Type 'com.genlight.epilcure.service.iScheme.controller.TimeEegGroupSchemeController' (TimeEegGroupSchemeController.java:43) advised by around advice from 'com.genlight.epilcure.common.core.mvc.aspectj.AccessAspect' (AccessAspect.java)
2025-06-17 15:09:01.194  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] Join point 'method-execution(com.genlight.epilcure.common.core.mvc.response.JsonResult com.genlight.epilcure.service.iScheme.controller.TimeEegGroupSchemeController.finds(com.genlight.epilcure.service.iScheme.pojo.dto.TimeEegGroupSchemeDTO, org.springframework.data.domain.Pageable))' in Type 'com.genlight.epilcure.service.iScheme.controller.TimeEegGroupSchemeController' (TimeEegGroupSchemeController.java:43) advised by around advice from 'com.genlight.epilcure.common.core.mvc.aspectj.LogstashAspect' (LogstashAspect.java)
2025-06-17 15:09:01.198  WARN 133052 --- [main] AspectJ Weaver                           : [AspectJ] define generated class failed
2025-06-17 15:09:01.199  WARN 133052 --- [main] AspectJ Weaver                           : [AspectJ] define generated class failed
2025-06-17 15:09:01.199  WARN 133052 --- [main] AspectJ Weaver                           : [AspectJ] define generated class failed
2025-06-17 15:09:01.200  WARN 133052 --- [main] AspectJ Weaver                           : [AspectJ] define generated class failed
2025-06-17 15:09:01.203  WARN 133052 --- [main] AspectJ Weaver                           : [AspectJ] define generated class failed
2025-06-17 15:09:01.203  WARN 133052 --- [main] AspectJ Weaver                           : [AspectJ] define generated class failed
2025-06-17 15:09:01.204  WARN 133052 --- [main] AspectJ Weaver                           : [AspectJ] define generated class failed
2025-06-17 15:09:01.205  WARN 133052 --- [main] AspectJ Weaver                           : [AspectJ] define generated class failed
2025-06-17 15:09:01.208  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] Join point 'method-execution(com.genlight.epilcure.common.core.mvc.response.JsonResult com.genlight.epilcure.service.iScheme.controller.UploadFileController.upload(java.lang.Long, org.springframework.web.multipart.MultipartFile))' in Type 'com.genlight.epilcure.service.iScheme.controller.UploadFileController' (UploadFileController.java:26) advised by around advice from 'com.genlight.epilcure.common.core.mvc.aspectj.AccessAspect' (AccessAspect.java)
2025-06-17 15:09:01.209  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] Join point 'method-execution(com.genlight.epilcure.common.core.mvc.response.JsonResult com.genlight.epilcure.service.iScheme.controller.UploadFileController.upload(java.lang.Long, org.springframework.web.multipart.MultipartFile))' in Type 'com.genlight.epilcure.service.iScheme.controller.UploadFileController' (UploadFileController.java:26) advised by around advice from 'com.genlight.epilcure.common.core.mvc.aspectj.LogstashAspect' (LogstashAspect.java)
2025-06-17 15:09:01.209  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] Join point 'method-execution(com.genlight.epilcure.common.core.mvc.response.JsonResult com.genlight.epilcure.service.iScheme.controller.UploadFileController.finds(com.genlight.epilcure.service.iScheme.pojo.dto.UploadFileDTO, org.springframework.data.domain.Pageable))' in Type 'com.genlight.epilcure.service.iScheme.controller.UploadFileController' (UploadFileController.java:32) advised by around advice from 'com.genlight.epilcure.common.core.mvc.aspectj.AccessAspect' (AccessAspect.java)
2025-06-17 15:09:01.210  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] Join point 'method-execution(com.genlight.epilcure.common.core.mvc.response.JsonResult com.genlight.epilcure.service.iScheme.controller.UploadFileController.finds(com.genlight.epilcure.service.iScheme.pojo.dto.UploadFileDTO, org.springframework.data.domain.Pageable))' in Type 'com.genlight.epilcure.service.iScheme.controller.UploadFileController' (UploadFileController.java:32) advised by around advice from 'com.genlight.epilcure.common.core.mvc.aspectj.LogstashAspect' (LogstashAspect.java)
2025-06-17 15:09:01.213  WARN 133052 --- [main] AspectJ Weaver                           : [AspectJ] define generated class failed
2025-06-17 15:09:01.213  WARN 133052 --- [main] AspectJ Weaver                           : [AspectJ] define generated class failed
2025-06-17 15:09:01.214  WARN 133052 --- [main] AspectJ Weaver                           : [AspectJ] define generated class failed
2025-06-17 15:09:01.215  WARN 133052 --- [main] AspectJ Weaver                           : [AspectJ] define generated class failed
2025-06-17 15:09:01.285  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] Join point 'method-execution(java.util.List com.genlight.epilcure.service.iScheme.service.InductionGroupService.add(java.util.List, com.genlight.epilcure.service.iScheme.dao.entity.InductionScheme, com.genlight.epilcure.api.patient.pojo.vo.PatientDeviceVO))' in Type 'com.genlight.epilcure.service.iScheme.service.InductionGroupService' (InductionGroupService.java:31) advised by around advice from 'org.springframework.transaction.aspectj.AnnotationTransactionAspect' (AbstractTransactionAspect.aj:67)
2025-06-17 15:09:01.287  WARN 133052 --- [main] AspectJ Weaver                           : [AspectJ] define generated class failed
2025-06-17 15:09:01.291  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] Join point 'method-execution(java.util.List com.genlight.epilcure.service.iScheme.service.InductionPulseService.add(java.util.List, com.genlight.epilcure.api.patient.pojo.vo.PatientDeviceVO, com.genlight.epilcure.service.iScheme.dao.entity.InductionGroup))' in Type 'com.genlight.epilcure.service.iScheme.service.InductionPulseService' (InductionPulseService.java:71) advised by around advice from 'org.springframework.transaction.aspectj.AnnotationTransactionAspect' (AbstractTransactionAspect.aj:67)
2025-06-17 15:09:01.294  WARN 133052 --- [main] AspectJ Weaver                           : [AspectJ] define generated class failed
2025-06-17 15:09:01.302  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] Join point 'method-execution(com.genlight.epilcure.service.iScheme.pojo.vo.InductionSchemeVO com.genlight.epilcure.service.iScheme.service.InductionSchemeService.add(com.genlight.epilcure.service.iScheme.pojo.dto.InductionSchemeDTO))' in Type 'com.genlight.epilcure.service.iScheme.service.InductionSchemeService' (InductionSchemeService.java:53) advised by around advice from 'org.springframework.transaction.aspectj.AnnotationTransactionAspect' (AbstractTransactionAspect.aj:67)
2025-06-17 15:09:01.302  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] Join point 'method-execution(com.genlight.epilcure.service.iScheme.pojo.vo.InductionSchemeVO com.genlight.epilcure.service.iScheme.service.InductionSchemeService.findById(java.lang.String))' in Type 'com.genlight.epilcure.service.iScheme.service.InductionSchemeService' (InductionSchemeService.java:108) advised by around advice from 'org.springframework.transaction.aspectj.AnnotationTransactionAspect' (AbstractTransactionAspect.aj:67)
2025-06-17 15:09:01.303  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] Join point 'method-execution(org.springframework.data.domain.Page com.genlight.epilcure.service.iScheme.service.InductionSchemeService.finds(com.genlight.epilcure.service.iScheme.pojo.dto.InductionSchemeDTO, org.springframework.data.domain.Pageable))' in Type 'com.genlight.epilcure.service.iScheme.service.InductionSchemeService' (InductionSchemeService.java:119) advised by around advice from 'org.springframework.transaction.aspectj.AnnotationTransactionAspect' (AbstractTransactionAspect.aj:67)
2025-06-17 15:09:01.306  WARN 133052 --- [main] AspectJ Weaver                           : [AspectJ] define generated class failed
2025-06-17 15:09:01.307  WARN 133052 --- [main] AspectJ Weaver                           : [AspectJ] define generated class failed
2025-06-17 15:09:01.308  WARN 133052 --- [main] AspectJ Weaver                           : [AspectJ] define generated class failed
2025-06-17 15:09:01.312  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] Join point 'method-execution(com.genlight.epilcure.service.iScheme.pojo.vo.PatientRealEegSchemeVO com.genlight.epilcure.service.iScheme.service.PatientRealEegSchemeService.add(com.genlight.epilcure.service.iScheme.pojo.dto.PatientRealEegSchemeDTO))' in Type 'com.genlight.epilcure.service.iScheme.service.PatientRealEegSchemeService' (PatientRealEegSchemeService.java:40) advised by around advice from 'org.springframework.transaction.aspectj.AnnotationTransactionAspect' (AbstractTransactionAspect.aj:67)
2025-06-17 15:09:01.312  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] Join point 'method-execution(com.genlight.epilcure.service.iScheme.pojo.vo.PatientRealEegSchemeVO com.genlight.epilcure.service.iScheme.service.PatientRealEegSchemeService.findByPatientId(java.lang.Long))' in Type 'com.genlight.epilcure.service.iScheme.service.PatientRealEegSchemeService' (PatientRealEegSchemeService.java:90) advised by around advice from 'org.springframework.transaction.aspectj.AnnotationTransactionAspect' (AbstractTransactionAspect.aj:67)
2025-06-17 15:09:01.313  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] Join point 'method-execution(com.genlight.epilcure.service.iScheme.pojo.vo.PatientRealEegSchemeVO com.genlight.epilcure.service.iScheme.service.PatientRealEegSchemeService.findById(java.lang.String))' in Type 'com.genlight.epilcure.service.iScheme.service.PatientRealEegSchemeService' (PatientRealEegSchemeService.java:99) advised by around advice from 'org.springframework.transaction.aspectj.AnnotationTransactionAspect' (AbstractTransactionAspect.aj:67)
2025-06-17 15:09:01.313  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] Join point 'method-execution(org.springframework.data.domain.Page com.genlight.epilcure.service.iScheme.service.PatientRealEegSchemeService.finds(com.genlight.epilcure.service.iScheme.pojo.dto.PatientRealEegSchemeDTO, org.springframework.data.domain.Pageable))' in Type 'com.genlight.epilcure.service.iScheme.service.PatientRealEegSchemeService' (PatientRealEegSchemeService.java:108) advised by around advice from 'org.springframework.transaction.aspectj.AnnotationTransactionAspect' (AbstractTransactionAspect.aj:67)
2025-06-17 15:09:01.317  WARN 133052 --- [main] AspectJ Weaver                           : [AspectJ] define generated class failed
2025-06-17 15:09:01.319  WARN 133052 --- [main] AspectJ Weaver                           : [AspectJ] define generated class failed
2025-06-17 15:09:01.320  WARN 133052 --- [main] AspectJ Weaver                           : [AspectJ] define generated class failed
2025-06-17 15:09:01.321  WARN 133052 --- [main] AspectJ Weaver                           : [AspectJ] define generated class failed
2025-06-17 15:09:01.323  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] Join point 'method-execution(void com.genlight.epilcure.service.iScheme.service.PatientStimulateLogService.add(java.util.List))' in Type 'com.genlight.epilcure.service.iScheme.service.PatientStimulateLogService' (PatientStimulateLogService.java:29) advised by around advice from 'org.springframework.transaction.aspectj.AnnotationTransactionAspect' (AbstractTransactionAspect.aj:67)
2025-06-17 15:09:01.323  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] Join point 'method-execution(org.springframework.data.domain.Page com.genlight.epilcure.service.iScheme.service.PatientStimulateLogService.finds(com.genlight.epilcure.service.iScheme.pojo.dto.PatientStimulateLogDTO, org.springframework.data.domain.Pageable))' in Type 'com.genlight.epilcure.service.iScheme.service.PatientStimulateLogService' (PatientStimulateLogService.java:45) advised by around advice from 'org.springframework.transaction.aspectj.AnnotationTransactionAspect' (AbstractTransactionAspect.aj:67)
2025-06-17 15:09:01.326  WARN 133052 --- [main] AspectJ Weaver                           : [AspectJ] define generated class failed
2025-06-17 15:09:01.327  WARN 133052 --- [main] AspectJ Weaver                           : [AspectJ] define generated class failed
2025-06-17 15:09:01.331  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] Join point 'method-execution(void com.genlight.epilcure.service.iScheme.service.SchemeLogService.readLog(org.springframework.web.multipart.MultipartFile, com.genlight.epilcure.service.iScheme.dao.entity.UploadFile))' in Type 'com.genlight.epilcure.service.iScheme.service.SchemeLogService' (SchemeLogService.java:58) advised by around advice from 'org.springframework.scheduling.aspectj.AnnotationAsyncExecutionAspect' (AbstractAsyncExecutionAspect.aj:65)
2025-06-17 15:09:01.332  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] Join point 'method-execution(void com.genlight.epilcure.service.iScheme.service.SchemeLogService.analysis(org.springframework.web.multipart.MultipartFile, com.genlight.epilcure.service.iScheme.dao.entity.UploadFile))' in Type 'com.genlight.epilcure.service.iScheme.service.SchemeLogService' (SchemeLogService.java:93) advised by around advice from 'org.springframework.transaction.aspectj.AnnotationTransactionAspect' (AbstractTransactionAspect.aj:67)
2025-06-17 15:09:01.336  WARN 133052 --- [main] AspectJ Weaver                           : [AspectJ] define generated class failed
2025-06-17 15:09:01.337  WARN 133052 --- [main] AspectJ Weaver                           : [AspectJ] define generated class failed
2025-06-17 15:09:01.345  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] Join point 'method-execution(com.genlight.epilcure.service.iScheme.pojo.vo.SchemeVO com.genlight.epilcure.service.iScheme.service.SchemeService.add(com.genlight.epilcure.service.iScheme.pojo.dto.SchemeDTO))' in Type 'com.genlight.epilcure.service.iScheme.service.SchemeService' (SchemeService.java:67) advised by around advice from 'org.springframework.transaction.aspectj.AnnotationTransactionAspect' (AbstractTransactionAspect.aj:67)
2025-06-17 15:09:01.345  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] Join point 'method-execution(org.springframework.data.domain.Page com.genlight.epilcure.service.iScheme.service.SchemeService.finds(com.genlight.epilcure.service.iScheme.pojo.dto.SchemeDTO, org.springframework.data.domain.Pageable))' in Type 'com.genlight.epilcure.service.iScheme.service.SchemeService' (SchemeService.java:141) advised by around advice from 'org.springframework.transaction.aspectj.AnnotationTransactionAspect' (AbstractTransactionAspect.aj:67)
2025-06-17 15:09:01.346  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] Join point 'method-execution(com.genlight.epilcure.service.iScheme.pojo.vo.SchemeVO com.genlight.epilcure.service.iScheme.service.SchemeService.findByPatientId(java.lang.Long))' in Type 'com.genlight.epilcure.service.iScheme.service.SchemeService' (SchemeService.java:168) advised by around advice from 'org.springframework.transaction.aspectj.AnnotationTransactionAspect' (AbstractTransactionAspect.aj:67)
2025-06-17 15:09:01.346  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] Join point 'method-execution(com.genlight.epilcure.service.iScheme.pojo.vo.SchemeVO com.genlight.epilcure.service.iScheme.service.SchemeService.findById(java.lang.String))' in Type 'com.genlight.epilcure.service.iScheme.service.SchemeService' (SchemeService.java:178) advised by around advice from 'org.springframework.transaction.aspectj.AnnotationTransactionAspect' (AbstractTransactionAspect.aj:67)
2025-06-17 15:09:01.347  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] Join point 'method-execution(com.genlight.epilcure.service.iScheme.pojo.vo.SchemeVO com.genlight.epilcure.service.iScheme.service.SchemeService.changeActive(java.lang.String, com.genlight.epilcure.service.iScheme.constants.enums.ActiveType))' in Type 'com.genlight.epilcure.service.iScheme.service.SchemeService' (SchemeService.java:188) advised by around advice from 'org.springframework.transaction.aspectj.AnnotationTransactionAspect' (AbstractTransactionAspect.aj:67)
2025-06-17 15:09:01.351  WARN 133052 --- [main] AspectJ Weaver                           : [AspectJ] define generated class failed
2025-06-17 15:09:01.352  WARN 133052 --- [main] AspectJ Weaver                           : [AspectJ] define generated class failed
2025-06-17 15:09:01.353  WARN 133052 --- [main] AspectJ Weaver                           : [AspectJ] define generated class failed
2025-06-17 15:09:01.354  WARN 133052 --- [main] AspectJ Weaver                           : [AspectJ] define generated class failed
2025-06-17 15:09:01.355  WARN 133052 --- [main] AspectJ Weaver                           : [AspectJ] define generated class failed
2025-06-17 15:09:01.358  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] Join point 'method-execution(com.genlight.epilcure.service.iScheme.pojo.vo.StimulateFrequencyVO com.genlight.epilcure.service.iScheme.service.StimulateFrequencyService.add(com.genlight.epilcure.service.iScheme.pojo.dto.StimulateFrequencyDTO, com.genlight.epilcure.service.iScheme.pojo.vo.SchemeVO, com.genlight.epilcure.api.patient.pojo.vo.PatientDeviceVO))' in Type 'com.genlight.epilcure.service.iScheme.service.StimulateFrequencyService' (StimulateFrequencyService.java:36) advised by around advice from 'org.springframework.transaction.aspectj.AnnotationTransactionAspect' (AbstractTransactionAspect.aj:67)
2025-06-17 15:09:01.360  WARN 133052 --- [main] AspectJ Weaver                           : [AspectJ] define generated class failed
2025-06-17 15:09:01.363  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] Join point 'method-execution(java.util.List com.genlight.epilcure.service.iScheme.service.StimulateProgramGroupService.addProgramGroup(java.util.List, com.genlight.epilcure.service.iScheme.pojo.vo.SchemeVO, com.genlight.epilcure.api.patient.pojo.vo.PatientDeviceVO))' in Type 'com.genlight.epilcure.service.iScheme.service.StimulateProgramGroupService' (StimulateProgramGroupService.java:50) advised by around advice from 'org.springframework.transaction.aspectj.AnnotationTransactionAspect' (AbstractTransactionAspect.aj:67)
2025-06-17 15:09:01.363  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] Join point 'method-execution(java.util.List com.genlight.epilcure.service.iScheme.service.StimulateProgramGroupService.addProgramGroup(java.util.List, com.genlight.epilcure.service.iScheme.pojo.vo.SchemeVO, com.genlight.epilcure.api.patient.pojo.vo.PatientDeviceVO))' in Type 'com.genlight.epilcure.service.iScheme.service.StimulateProgramGroupService' (StimulateProgramGroupService.java:50) advised by around advice from 'org.springframework.cache.aspectj.AnnotationCacheAspect' (AbstractCacheAspect.aj:64)
2025-06-17 15:09:01.364  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] Join point 'method-execution(com.genlight.epilcure.service.iScheme.pojo.vo.StimulateProgramGroupVO com.genlight.epilcure.service.iScheme.service.StimulateProgramGroupService.findById(java.lang.Long))' in Type 'com.genlight.epilcure.service.iScheme.service.StimulateProgramGroupService' (StimulateProgramGroupService.java:122) advised by around advice from 'org.springframework.transaction.aspectj.AnnotationTransactionAspect' (AbstractTransactionAspect.aj:67)
2025-06-17 15:09:01.365  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] Join point 'method-execution(com.genlight.epilcure.service.iScheme.pojo.vo.StimulateProgramGroupVO com.genlight.epilcure.service.iScheme.service.StimulateProgramGroupService.findById(java.lang.Long))' in Type 'com.genlight.epilcure.service.iScheme.service.StimulateProgramGroupService' (StimulateProgramGroupService.java:122) advised by around advice from 'org.springframework.cache.aspectj.AnnotationCacheAspect' (AbstractCacheAspect.aj:64)
2025-06-17 15:09:01.367  WARN 133052 --- [main] AspectJ Weaver                           : [AspectJ] define generated class failed
2025-06-17 15:09:01.368  WARN 133052 --- [main] AspectJ Weaver                           : [AspectJ] define generated class failed
2025-06-17 15:09:01.369  WARN 133052 --- [main] AspectJ Weaver                           : [AspectJ] define generated class failed
2025-06-17 15:09:01.371  WARN 133052 --- [main] AspectJ Weaver                           : [AspectJ] define generated class failed
2025-06-17 15:09:01.373  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] Join point 'method-execution(java.util.List com.genlight.epilcure.service.iScheme.service.StimulateTimeService.add(java.util.List, java.util.List, com.genlight.epilcure.service.iScheme.pojo.vo.SchemeVO))' in Type 'com.genlight.epilcure.service.iScheme.service.StimulateTimeService' (StimulateTimeService.java:33) advised by around advice from 'org.springframework.transaction.aspectj.AnnotationTransactionAspect' (AbstractTransactionAspect.aj:67)
2025-06-17 15:09:01.374  WARN 133052 --- [main] AspectJ Weaver                           : [AspectJ] define generated class failed
2025-06-17 15:09:01.378  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] Join point 'method-execution(com.genlight.epilcure.service.iScheme.pojo.vo.TimeEegGroupSchemeVO com.genlight.epilcure.service.iScheme.service.TimeEegGroupSchemeService.add(com.genlight.epilcure.service.iScheme.pojo.dto.TimeEegGroupSchemeDTO))' in Type 'com.genlight.epilcure.service.iScheme.service.TimeEegGroupSchemeService' (TimeEegGroupSchemeService.java:38) advised by around advice from 'org.springframework.transaction.aspectj.AnnotationTransactionAspect' (AbstractTransactionAspect.aj:67)
2025-06-17 15:09:01.378  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] Join point 'method-execution(com.genlight.epilcure.service.iScheme.pojo.vo.TimeEegGroupSchemeVO com.genlight.epilcure.service.iScheme.service.TimeEegGroupSchemeService.findByPatientId(java.lang.Long))' in Type 'com.genlight.epilcure.service.iScheme.service.TimeEegGroupSchemeService' (TimeEegGroupSchemeService.java:96) advised by around advice from 'org.springframework.transaction.aspectj.AnnotationTransactionAspect' (AbstractTransactionAspect.aj:67)
2025-06-17 15:09:01.379  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] Join point 'method-execution(com.genlight.epilcure.service.iScheme.pojo.vo.TimeEegGroupSchemeVO com.genlight.epilcure.service.iScheme.service.TimeEegGroupSchemeService.findById(java.lang.String))' in Type 'com.genlight.epilcure.service.iScheme.service.TimeEegGroupSchemeService' (TimeEegGroupSchemeService.java:105) advised by around advice from 'org.springframework.transaction.aspectj.AnnotationTransactionAspect' (AbstractTransactionAspect.aj:67)
2025-06-17 15:09:01.379  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] Join point 'method-execution(org.springframework.data.domain.Page com.genlight.epilcure.service.iScheme.service.TimeEegGroupSchemeService.finds(com.genlight.epilcure.service.iScheme.pojo.dto.TimeEegGroupSchemeDTO, org.springframework.data.domain.Pageable))' in Type 'com.genlight.epilcure.service.iScheme.service.TimeEegGroupSchemeService' (TimeEegGroupSchemeService.java:114) advised by around advice from 'org.springframework.transaction.aspectj.AnnotationTransactionAspect' (AbstractTransactionAspect.aj:67)
2025-06-17 15:09:01.381  WARN 133052 --- [main] AspectJ Weaver                           : [AspectJ] define generated class failed
2025-06-17 15:09:01.382  WARN 133052 --- [main] AspectJ Weaver                           : [AspectJ] define generated class failed
2025-06-17 15:09:01.383  WARN 133052 --- [main] AspectJ Weaver                           : [AspectJ] define generated class failed
2025-06-17 15:09:01.384  WARN 133052 --- [main] AspectJ Weaver                           : [AspectJ] define generated class failed
2025-06-17 15:09:01.387  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] Join point 'method-execution(void com.genlight.epilcure.service.iScheme.service.UploadFileService.uploadFile(java.lang.Long, org.springframework.web.multipart.MultipartFile))' in Type 'com.genlight.epilcure.service.iScheme.service.UploadFileService' (UploadFileService.java:44) advised by around advice from 'org.springframework.transaction.aspectj.AnnotationTransactionAspect' (AbstractTransactionAspect.aj:67)
2025-06-17 15:09:01.388  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] Join point 'method-execution(void com.genlight.epilcure.service.iScheme.service.UploadFileService.updateStatus(java.lang.Long, com.genlight.epilcure.service.iScheme.constants.enums.FileStatus))' in Type 'com.genlight.epilcure.service.iScheme.service.UploadFileService' (UploadFileService.java:62) advised by around advice from 'org.springframework.transaction.aspectj.AnnotationTransactionAspect' (AbstractTransactionAspect.aj:67)
2025-06-17 15:09:01.389  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] Join point 'method-execution(org.springframework.data.domain.Page com.genlight.epilcure.service.iScheme.service.UploadFileService.finds(com.genlight.epilcure.service.iScheme.pojo.dto.UploadFileDTO, org.springframework.data.domain.Pageable))' in Type 'com.genlight.epilcure.service.iScheme.service.UploadFileService' (UploadFileService.java:75) advised by around advice from 'org.springframework.transaction.aspectj.AnnotationTransactionAspect' (AbstractTransactionAspect.aj:67)
2025-06-17 15:09:01.391  WARN 133052 --- [main] AspectJ Weaver                           : [AspectJ] define generated class failed
2025-06-17 15:09:01.392  WARN 133052 --- [main] AspectJ Weaver                           : [AspectJ] define generated class failed
2025-06-17 15:09:01.393  WARN 133052 --- [main] AspectJ Weaver                           : [AspectJ] define generated class failed
2025-06-17 15:09:01.433  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] Join point 'method-execution(void com.genlight.epilcure.common.core.util.InitUtils.repairSign(java.util.List, org.springframework.data.jpa.repository.JpaRepository, java.util.List, java.util.List))' in Type 'com.genlight.epilcure.common.core.util.InitUtils' (InitUtils.java:188) advised by around advice from 'org.springframework.transaction.aspectj.AnnotationTransactionAspect' (AbstractTransactionAspect.aj:67)
2025-06-17 15:09:01.436  WARN 133052 --- [main] AspectJ Weaver                           : [AspectJ] define generated class failed
2025-06-17 15:09:02.178  INFO 133052 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-17 15:09:02.180  INFO 133052 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-17 15:09:02.199  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] AspectJ Weaver Version 1.9.19 built on Wednesday Dec 21, 2022 at 06:57:22 PST
2025-06-17 15:09:02.199  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] register classloader org.springframework.data.jpa.repository.config.InspectionClassLoader@49bc1f6c
2025-06-17 15:09:02.199  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] using configuration file:/E:/Maven/MavenRepository/com/genlight/epilcure-common-core/1.0.1-SNAPSHOT/epilcure-common-core-1.0.1-SNAPSHOT.jar!/META-INF/aop.xml
2025-06-17 15:09:02.199  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] using configuration file:/E:/Maven/MavenRepository/org/springframework/spring-aspects/6.0.10/spring-aspects-6.0.10.jar!/META-INF/aop.xml
2025-06-17 15:09:02.199  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] register aspect com.genlight.epilcure.common.core.mvc.aspectj.AccessAspect
2025-06-17 15:09:02.200  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] register aspect com.genlight.epilcure.common.core.mvc.aspectj.LogstashAspect
2025-06-17 15:09:02.201  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] register aspect com.genlight.epilcure.common.core.mvc.aspectj.FileValidAspect
2025-06-17 15:09:02.201  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] register aspect org.springframework.beans.factory.aspectj.AnnotationBeanConfigurerAspect
2025-06-17 15:09:02.204  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] register aspect org.springframework.scheduling.aspectj.AnnotationAsyncExecutionAspect
2025-06-17 15:09:02.205  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] register aspect org.springframework.transaction.aspectj.AnnotationTransactionAspect
2025-06-17 15:09:02.205  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] register aspect org.springframework.transaction.aspectj.JtaAnnotationTransactionAspect
2025-06-17 15:09:02.206  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] register aspect org.springframework.cache.aspectj.AnnotationCacheAspect
2025-06-17 15:09:02.207  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] register aspect org.springframework.cache.aspectj.JCacheCacheAspect
2025-06-17 15:09:02.208  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] weaver operating in reweavable mode.  Need to verify any required types exist.
2025-06-17 15:09:02.393  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] AspectJ Weaver Version 1.9.19 built on Wednesday Dec 21, 2022 at 06:57:22 PST
2025-06-17 15:09:02.393  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] register classloader org.springframework.data.jpa.repository.config.InspectionClassLoader@7daf167
2025-06-17 15:09:02.393  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] using configuration file:/E:/Maven/MavenRepository/com/genlight/epilcure-common-core/1.0.1-SNAPSHOT/epilcure-common-core-1.0.1-SNAPSHOT.jar!/META-INF/aop.xml
2025-06-17 15:09:02.393  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] using configuration file:/E:/Maven/MavenRepository/org/springframework/spring-aspects/6.0.10/spring-aspects-6.0.10.jar!/META-INF/aop.xml
2025-06-17 15:09:02.393  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] register aspect com.genlight.epilcure.common.core.mvc.aspectj.AccessAspect
2025-06-17 15:09:02.394  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] register aspect com.genlight.epilcure.common.core.mvc.aspectj.LogstashAspect
2025-06-17 15:09:02.394  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] register aspect com.genlight.epilcure.common.core.mvc.aspectj.FileValidAspect
2025-06-17 15:09:02.395  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] register aspect org.springframework.beans.factory.aspectj.AnnotationBeanConfigurerAspect
2025-06-17 15:09:02.398  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] register aspect org.springframework.scheduling.aspectj.AnnotationAsyncExecutionAspect
2025-06-17 15:09:02.399  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] register aspect org.springframework.transaction.aspectj.AnnotationTransactionAspect
2025-06-17 15:09:02.400  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] register aspect org.springframework.transaction.aspectj.JtaAnnotationTransactionAspect
2025-06-17 15:09:02.401  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] register aspect org.springframework.cache.aspectj.AnnotationCacheAspect
2025-06-17 15:09:02.402  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] register aspect org.springframework.cache.aspectj.JCacheCacheAspect
2025-06-17 15:09:02.403  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] weaver operating in reweavable mode.  Need to verify any required types exist.
2025-06-17 15:09:02.413  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] AspectJ Weaver Version 1.9.19 built on Wednesday Dec 21, 2022 at 06:57:22 PST
2025-06-17 15:09:02.413  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] register classloader org.springframework.data.jpa.repository.config.InspectionClassLoader@4b99f2ed
2025-06-17 15:09:02.413  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] using configuration file:/E:/Maven/MavenRepository/com/genlight/epilcure-common-core/1.0.1-SNAPSHOT/epilcure-common-core-1.0.1-SNAPSHOT.jar!/META-INF/aop.xml
2025-06-17 15:09:02.413  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] using configuration file:/E:/Maven/MavenRepository/org/springframework/spring-aspects/6.0.10/spring-aspects-6.0.10.jar!/META-INF/aop.xml
2025-06-17 15:09:02.414  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] register aspect com.genlight.epilcure.common.core.mvc.aspectj.AccessAspect
2025-06-17 15:09:02.414  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] register aspect com.genlight.epilcure.common.core.mvc.aspectj.LogstashAspect
2025-06-17 15:09:02.415  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] register aspect com.genlight.epilcure.common.core.mvc.aspectj.FileValidAspect
2025-06-17 15:09:02.415  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] register aspect org.springframework.beans.factory.aspectj.AnnotationBeanConfigurerAspect
2025-06-17 15:09:02.417  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] register aspect org.springframework.scheduling.aspectj.AnnotationAsyncExecutionAspect
2025-06-17 15:09:02.418  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] register aspect org.springframework.transaction.aspectj.AnnotationTransactionAspect
2025-06-17 15:09:02.419  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] register aspect org.springframework.transaction.aspectj.JtaAnnotationTransactionAspect
2025-06-17 15:09:02.419  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] register aspect org.springframework.cache.aspectj.AnnotationCacheAspect
2025-06-17 15:09:02.420  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] register aspect org.springframework.cache.aspectj.JCacheCacheAspect
2025-06-17 15:09:02.421  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] weaver operating in reweavable mode.  Need to verify any required types exist.
2025-06-17 15:09:02.432  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] AspectJ Weaver Version 1.9.19 built on Wednesday Dec 21, 2022 at 06:57:22 PST
2025-06-17 15:09:02.432  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] register classloader org.springframework.data.jpa.repository.config.InspectionClassLoader@158cb50a
2025-06-17 15:09:02.432  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] using configuration file:/E:/Maven/MavenRepository/com/genlight/epilcure-common-core/1.0.1-SNAPSHOT/epilcure-common-core-1.0.1-SNAPSHOT.jar!/META-INF/aop.xml
2025-06-17 15:09:02.432  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] using configuration file:/E:/Maven/MavenRepository/org/springframework/spring-aspects/6.0.10/spring-aspects-6.0.10.jar!/META-INF/aop.xml
2025-06-17 15:09:02.432  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] register aspect com.genlight.epilcure.common.core.mvc.aspectj.AccessAspect
2025-06-17 15:09:02.433  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] register aspect com.genlight.epilcure.common.core.mvc.aspectj.LogstashAspect
2025-06-17 15:09:02.433  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] register aspect com.genlight.epilcure.common.core.mvc.aspectj.FileValidAspect
2025-06-17 15:09:02.434  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] register aspect org.springframework.beans.factory.aspectj.AnnotationBeanConfigurerAspect
2025-06-17 15:09:02.436  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] register aspect org.springframework.scheduling.aspectj.AnnotationAsyncExecutionAspect
2025-06-17 15:09:02.437  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] register aspect org.springframework.transaction.aspectj.AnnotationTransactionAspect
2025-06-17 15:09:02.438  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] register aspect org.springframework.transaction.aspectj.JtaAnnotationTransactionAspect
2025-06-17 15:09:02.439  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] register aspect org.springframework.cache.aspectj.AnnotationCacheAspect
2025-06-17 15:09:02.440  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] register aspect org.springframework.cache.aspectj.JCacheCacheAspect
2025-06-17 15:09:02.440  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] weaver operating in reweavable mode.  Need to verify any required types exist.
2025-06-17 15:09:02.451  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] AspectJ Weaver Version 1.9.19 built on Wednesday Dec 21, 2022 at 06:57:22 PST
2025-06-17 15:09:02.451  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] register classloader org.springframework.data.jpa.repository.config.InspectionClassLoader@68423388
2025-06-17 15:09:02.451  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] using configuration file:/E:/Maven/MavenRepository/com/genlight/epilcure-common-core/1.0.1-SNAPSHOT/epilcure-common-core-1.0.1-SNAPSHOT.jar!/META-INF/aop.xml
2025-06-17 15:09:02.451  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] using configuration file:/E:/Maven/MavenRepository/org/springframework/spring-aspects/6.0.10/spring-aspects-6.0.10.jar!/META-INF/aop.xml
2025-06-17 15:09:02.451  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] register aspect com.genlight.epilcure.common.core.mvc.aspectj.AccessAspect
2025-06-17 15:09:02.452  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] register aspect com.genlight.epilcure.common.core.mvc.aspectj.LogstashAspect
2025-06-17 15:09:02.452  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] register aspect com.genlight.epilcure.common.core.mvc.aspectj.FileValidAspect
2025-06-17 15:09:02.453  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] register aspect org.springframework.beans.factory.aspectj.AnnotationBeanConfigurerAspect
2025-06-17 15:09:02.455  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] register aspect org.springframework.scheduling.aspectj.AnnotationAsyncExecutionAspect
2025-06-17 15:09:02.456  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] register aspect org.springframework.transaction.aspectj.AnnotationTransactionAspect
2025-06-17 15:09:02.456  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] register aspect org.springframework.transaction.aspectj.JtaAnnotationTransactionAspect
2025-06-17 15:09:02.457  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] register aspect org.springframework.cache.aspectj.AnnotationCacheAspect
2025-06-17 15:09:02.458  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] register aspect org.springframework.cache.aspectj.JCacheCacheAspect
2025-06-17 15:09:02.459  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] weaver operating in reweavable mode.  Need to verify any required types exist.
2025-06-17 15:09:02.468  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] AspectJ Weaver Version 1.9.19 built on Wednesday Dec 21, 2022 at 06:57:22 PST
2025-06-17 15:09:02.468  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] register classloader org.springframework.data.jpa.repository.config.InspectionClassLoader@4199761d
2025-06-17 15:09:02.468  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] using configuration file:/E:/Maven/MavenRepository/com/genlight/epilcure-common-core/1.0.1-SNAPSHOT/epilcure-common-core-1.0.1-SNAPSHOT.jar!/META-INF/aop.xml
2025-06-17 15:09:02.468  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] using configuration file:/E:/Maven/MavenRepository/org/springframework/spring-aspects/6.0.10/spring-aspects-6.0.10.jar!/META-INF/aop.xml
2025-06-17 15:09:02.468  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] register aspect com.genlight.epilcure.common.core.mvc.aspectj.AccessAspect
2025-06-17 15:09:02.469  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] register aspect com.genlight.epilcure.common.core.mvc.aspectj.LogstashAspect
2025-06-17 15:09:02.469  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] register aspect com.genlight.epilcure.common.core.mvc.aspectj.FileValidAspect
2025-06-17 15:09:02.470  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] register aspect org.springframework.beans.factory.aspectj.AnnotationBeanConfigurerAspect
2025-06-17 15:09:02.472  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] register aspect org.springframework.scheduling.aspectj.AnnotationAsyncExecutionAspect
2025-06-17 15:09:02.472  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] register aspect org.springframework.transaction.aspectj.AnnotationTransactionAspect
2025-06-17 15:09:02.473  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] register aspect org.springframework.transaction.aspectj.JtaAnnotationTransactionAspect
2025-06-17 15:09:02.474  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] register aspect org.springframework.cache.aspectj.AnnotationCacheAspect
2025-06-17 15:09:02.474  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] register aspect org.springframework.cache.aspectj.JCacheCacheAspect
2025-06-17 15:09:02.475  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] weaver operating in reweavable mode.  Need to verify any required types exist.
2025-06-17 15:09:02.484  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] AspectJ Weaver Version 1.9.19 built on Wednesday Dec 21, 2022 at 06:57:22 PST
2025-06-17 15:09:02.484  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] register classloader org.springframework.data.jpa.repository.config.InspectionClassLoader@7f36cd8f
2025-06-17 15:09:02.484  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] using configuration file:/E:/Maven/MavenRepository/com/genlight/epilcure-common-core/1.0.1-SNAPSHOT/epilcure-common-core-1.0.1-SNAPSHOT.jar!/META-INF/aop.xml
2025-06-17 15:09:02.484  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] using configuration file:/E:/Maven/MavenRepository/org/springframework/spring-aspects/6.0.10/spring-aspects-6.0.10.jar!/META-INF/aop.xml
2025-06-17 15:09:02.484  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] register aspect com.genlight.epilcure.common.core.mvc.aspectj.AccessAspect
2025-06-17 15:09:02.485  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] register aspect com.genlight.epilcure.common.core.mvc.aspectj.LogstashAspect
2025-06-17 15:09:02.485  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] register aspect com.genlight.epilcure.common.core.mvc.aspectj.FileValidAspect
2025-06-17 15:09:02.486  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] register aspect org.springframework.beans.factory.aspectj.AnnotationBeanConfigurerAspect
2025-06-17 15:09:02.487  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] register aspect org.springframework.scheduling.aspectj.AnnotationAsyncExecutionAspect
2025-06-17 15:09:02.488  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] register aspect org.springframework.transaction.aspectj.AnnotationTransactionAspect
2025-06-17 15:09:02.489  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] register aspect org.springframework.transaction.aspectj.JtaAnnotationTransactionAspect
2025-06-17 15:09:02.490  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] register aspect org.springframework.cache.aspectj.AnnotationCacheAspect
2025-06-17 15:09:02.491  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] register aspect org.springframework.cache.aspectj.JCacheCacheAspect
2025-06-17 15:09:02.491  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] weaver operating in reweavable mode.  Need to verify any required types exist.
2025-06-17 15:09:02.500  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] AspectJ Weaver Version 1.9.19 built on Wednesday Dec 21, 2022 at 06:57:22 PST
2025-06-17 15:09:02.500  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] register classloader org.springframework.data.jpa.repository.config.InspectionClassLoader@39d37da8
2025-06-17 15:09:02.500  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] using configuration file:/E:/Maven/MavenRepository/com/genlight/epilcure-common-core/1.0.1-SNAPSHOT/epilcure-common-core-1.0.1-SNAPSHOT.jar!/META-INF/aop.xml
2025-06-17 15:09:02.500  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] using configuration file:/E:/Maven/MavenRepository/org/springframework/spring-aspects/6.0.10/spring-aspects-6.0.10.jar!/META-INF/aop.xml
2025-06-17 15:09:02.501  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] register aspect com.genlight.epilcure.common.core.mvc.aspectj.AccessAspect
2025-06-17 15:09:02.501  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] register aspect com.genlight.epilcure.common.core.mvc.aspectj.LogstashAspect
2025-06-17 15:09:02.501  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] register aspect com.genlight.epilcure.common.core.mvc.aspectj.FileValidAspect
2025-06-17 15:09:02.502  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] register aspect org.springframework.beans.factory.aspectj.AnnotationBeanConfigurerAspect
2025-06-17 15:09:02.504  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] register aspect org.springframework.scheduling.aspectj.AnnotationAsyncExecutionAspect
2025-06-17 15:09:02.505  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] register aspect org.springframework.transaction.aspectj.AnnotationTransactionAspect
2025-06-17 15:09:02.505  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] register aspect org.springframework.transaction.aspectj.JtaAnnotationTransactionAspect
2025-06-17 15:09:02.506  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] register aspect org.springframework.cache.aspectj.AnnotationCacheAspect
2025-06-17 15:09:02.507  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] register aspect org.springframework.cache.aspectj.JCacheCacheAspect
2025-06-17 15:09:02.507  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] weaver operating in reweavable mode.  Need to verify any required types exist.
2025-06-17 15:09:02.515  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] AspectJ Weaver Version 1.9.19 built on Wednesday Dec 21, 2022 at 06:57:22 PST
2025-06-17 15:09:02.515  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] register classloader org.springframework.data.jpa.repository.config.InspectionClassLoader@52df6d0f
2025-06-17 15:09:02.515  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] using configuration file:/E:/Maven/MavenRepository/com/genlight/epilcure-common-core/1.0.1-SNAPSHOT/epilcure-common-core-1.0.1-SNAPSHOT.jar!/META-INF/aop.xml
2025-06-17 15:09:02.515  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] using configuration file:/E:/Maven/MavenRepository/org/springframework/spring-aspects/6.0.10/spring-aspects-6.0.10.jar!/META-INF/aop.xml
2025-06-17 15:09:02.515  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] register aspect com.genlight.epilcure.common.core.mvc.aspectj.AccessAspect
2025-06-17 15:09:02.516  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] register aspect com.genlight.epilcure.common.core.mvc.aspectj.LogstashAspect
2025-06-17 15:09:02.516  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] register aspect com.genlight.epilcure.common.core.mvc.aspectj.FileValidAspect
2025-06-17 15:09:02.516  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] register aspect org.springframework.beans.factory.aspectj.AnnotationBeanConfigurerAspect
2025-06-17 15:09:02.518  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] register aspect org.springframework.scheduling.aspectj.AnnotationAsyncExecutionAspect
2025-06-17 15:09:02.518  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] register aspect org.springframework.transaction.aspectj.AnnotationTransactionAspect
2025-06-17 15:09:02.519  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] register aspect org.springframework.transaction.aspectj.JtaAnnotationTransactionAspect
2025-06-17 15:09:02.519  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] register aspect org.springframework.cache.aspectj.AnnotationCacheAspect
2025-06-17 15:09:02.520  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] register aspect org.springframework.cache.aspectj.JCacheCacheAspect
2025-06-17 15:09:02.521  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] weaver operating in reweavable mode.  Need to verify any required types exist.
2025-06-17 15:09:02.530  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] AspectJ Weaver Version 1.9.19 built on Wednesday Dec 21, 2022 at 06:57:22 PST
2025-06-17 15:09:02.530  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] register classloader org.springframework.data.jpa.repository.config.InspectionClassLoader@69374e28
2025-06-17 15:09:02.530  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] using configuration file:/E:/Maven/MavenRepository/com/genlight/epilcure-common-core/1.0.1-SNAPSHOT/epilcure-common-core-1.0.1-SNAPSHOT.jar!/META-INF/aop.xml
2025-06-17 15:09:02.530  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] using configuration file:/E:/Maven/MavenRepository/org/springframework/spring-aspects/6.0.10/spring-aspects-6.0.10.jar!/META-INF/aop.xml
2025-06-17 15:09:02.530  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] register aspect com.genlight.epilcure.common.core.mvc.aspectj.AccessAspect
2025-06-17 15:09:02.530  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] register aspect com.genlight.epilcure.common.core.mvc.aspectj.LogstashAspect
2025-06-17 15:09:02.531  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] register aspect com.genlight.epilcure.common.core.mvc.aspectj.FileValidAspect
2025-06-17 15:09:02.531  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] register aspect org.springframework.beans.factory.aspectj.AnnotationBeanConfigurerAspect
2025-06-17 15:09:02.533  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] register aspect org.springframework.scheduling.aspectj.AnnotationAsyncExecutionAspect
2025-06-17 15:09:02.533  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] register aspect org.springframework.transaction.aspectj.AnnotationTransactionAspect
2025-06-17 15:09:02.534  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] register aspect org.springframework.transaction.aspectj.JtaAnnotationTransactionAspect
2025-06-17 15:09:02.534  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] register aspect org.springframework.cache.aspectj.AnnotationCacheAspect
2025-06-17 15:09:02.535  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] register aspect org.springframework.cache.aspectj.JCacheCacheAspect
2025-06-17 15:09:02.536  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] weaver operating in reweavable mode.  Need to verify any required types exist.
2025-06-17 15:09:02.544  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] AspectJ Weaver Version 1.9.19 built on Wednesday Dec 21, 2022 at 06:57:22 PST
2025-06-17 15:09:02.544  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] register classloader org.springframework.data.jpa.repository.config.InspectionClassLoader@230dd372
2025-06-17 15:09:02.544  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] using configuration file:/E:/Maven/MavenRepository/com/genlight/epilcure-common-core/1.0.1-SNAPSHOT/epilcure-common-core-1.0.1-SNAPSHOT.jar!/META-INF/aop.xml
2025-06-17 15:09:02.544  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] using configuration file:/E:/Maven/MavenRepository/org/springframework/spring-aspects/6.0.10/spring-aspects-6.0.10.jar!/META-INF/aop.xml
2025-06-17 15:09:02.544  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] register aspect com.genlight.epilcure.common.core.mvc.aspectj.AccessAspect
2025-06-17 15:09:02.544  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] register aspect com.genlight.epilcure.common.core.mvc.aspectj.LogstashAspect
2025-06-17 15:09:02.545  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] register aspect com.genlight.epilcure.common.core.mvc.aspectj.FileValidAspect
2025-06-17 15:09:02.546  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] register aspect org.springframework.beans.factory.aspectj.AnnotationBeanConfigurerAspect
2025-06-17 15:09:02.547  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] register aspect org.springframework.scheduling.aspectj.AnnotationAsyncExecutionAspect
2025-06-17 15:09:02.547  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] register aspect org.springframework.transaction.aspectj.AnnotationTransactionAspect
2025-06-17 15:09:02.548  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] register aspect org.springframework.transaction.aspectj.JtaAnnotationTransactionAspect
2025-06-17 15:09:02.548  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] register aspect org.springframework.cache.aspectj.AnnotationCacheAspect
2025-06-17 15:09:02.549  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] register aspect org.springframework.cache.aspectj.JCacheCacheAspect
2025-06-17 15:09:02.549  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] weaver operating in reweavable mode.  Need to verify any required types exist.
2025-06-17 15:09:02.557  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] AspectJ Weaver Version 1.9.19 built on Wednesday Dec 21, 2022 at 06:57:22 PST
2025-06-17 15:09:02.557  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] register classloader org.springframework.data.jpa.repository.config.InspectionClassLoader@949165f
2025-06-17 15:09:02.557  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] using configuration file:/E:/Maven/MavenRepository/com/genlight/epilcure-common-core/1.0.1-SNAPSHOT/epilcure-common-core-1.0.1-SNAPSHOT.jar!/META-INF/aop.xml
2025-06-17 15:09:02.557  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] using configuration file:/E:/Maven/MavenRepository/org/springframework/spring-aspects/6.0.10/spring-aspects-6.0.10.jar!/META-INF/aop.xml
2025-06-17 15:09:02.558  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] register aspect com.genlight.epilcure.common.core.mvc.aspectj.AccessAspect
2025-06-17 15:09:02.558  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] register aspect com.genlight.epilcure.common.core.mvc.aspectj.LogstashAspect
2025-06-17 15:09:02.558  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] register aspect com.genlight.epilcure.common.core.mvc.aspectj.FileValidAspect
2025-06-17 15:09:02.559  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] register aspect org.springframework.beans.factory.aspectj.AnnotationBeanConfigurerAspect
2025-06-17 15:09:02.560  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] register aspect org.springframework.scheduling.aspectj.AnnotationAsyncExecutionAspect
2025-06-17 15:09:02.561  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] register aspect org.springframework.transaction.aspectj.AnnotationTransactionAspect
2025-06-17 15:09:02.561  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] register aspect org.springframework.transaction.aspectj.JtaAnnotationTransactionAspect
2025-06-17 15:09:02.562  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] register aspect org.springframework.cache.aspectj.AnnotationCacheAspect
2025-06-17 15:09:02.563  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] register aspect org.springframework.cache.aspectj.JCacheCacheAspect
2025-06-17 15:09:02.563  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] weaver operating in reweavable mode.  Need to verify any required types exist.
2025-06-17 15:09:02.571  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] AspectJ Weaver Version 1.9.19 built on Wednesday Dec 21, 2022 at 06:57:22 PST
2025-06-17 15:09:02.571  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] register classloader org.springframework.data.jpa.repository.config.InspectionClassLoader@32d3b965
2025-06-17 15:09:02.571  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] using configuration file:/E:/Maven/MavenRepository/com/genlight/epilcure-common-core/1.0.1-SNAPSHOT/epilcure-common-core-1.0.1-SNAPSHOT.jar!/META-INF/aop.xml
2025-06-17 15:09:02.571  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] using configuration file:/E:/Maven/MavenRepository/org/springframework/spring-aspects/6.0.10/spring-aspects-6.0.10.jar!/META-INF/aop.xml
2025-06-17 15:09:02.571  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] register aspect com.genlight.epilcure.common.core.mvc.aspectj.AccessAspect
2025-06-17 15:09:02.572  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] register aspect com.genlight.epilcure.common.core.mvc.aspectj.LogstashAspect
2025-06-17 15:09:02.572  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] register aspect com.genlight.epilcure.common.core.mvc.aspectj.FileValidAspect
2025-06-17 15:09:02.573  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] register aspect org.springframework.beans.factory.aspectj.AnnotationBeanConfigurerAspect
2025-06-17 15:09:02.574  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] register aspect org.springframework.scheduling.aspectj.AnnotationAsyncExecutionAspect
2025-06-17 15:09:02.574  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] register aspect org.springframework.transaction.aspectj.AnnotationTransactionAspect
2025-06-17 15:09:02.575  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] register aspect org.springframework.transaction.aspectj.JtaAnnotationTransactionAspect
2025-06-17 15:09:02.575  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] register aspect org.springframework.cache.aspectj.AnnotationCacheAspect
2025-06-17 15:09:02.576  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] register aspect org.springframework.cache.aspectj.JCacheCacheAspect
2025-06-17 15:09:02.576  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] weaver operating in reweavable mode.  Need to verify any required types exist.
2025-06-17 15:09:02.583  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] AspectJ Weaver Version 1.9.19 built on Wednesday Dec 21, 2022 at 06:57:22 PST
2025-06-17 15:09:02.583  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] register classloader org.springframework.data.jpa.repository.config.InspectionClassLoader@21ebf9be
2025-06-17 15:09:02.583  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] using configuration file:/E:/Maven/MavenRepository/com/genlight/epilcure-common-core/1.0.1-SNAPSHOT/epilcure-common-core-1.0.1-SNAPSHOT.jar!/META-INF/aop.xml
2025-06-17 15:09:02.583  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] using configuration file:/E:/Maven/MavenRepository/org/springframework/spring-aspects/6.0.10/spring-aspects-6.0.10.jar!/META-INF/aop.xml
2025-06-17 15:09:02.583  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] register aspect com.genlight.epilcure.common.core.mvc.aspectj.AccessAspect
2025-06-17 15:09:02.584  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] register aspect com.genlight.epilcure.common.core.mvc.aspectj.LogstashAspect
2025-06-17 15:09:02.584  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] register aspect com.genlight.epilcure.common.core.mvc.aspectj.FileValidAspect
2025-06-17 15:09:02.585  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] register aspect org.springframework.beans.factory.aspectj.AnnotationBeanConfigurerAspect
2025-06-17 15:09:02.586  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] register aspect org.springframework.scheduling.aspectj.AnnotationAsyncExecutionAspect
2025-06-17 15:09:02.586  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] register aspect org.springframework.transaction.aspectj.AnnotationTransactionAspect
2025-06-17 15:09:02.587  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] register aspect org.springframework.transaction.aspectj.JtaAnnotationTransactionAspect
2025-06-17 15:09:02.587  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] register aspect org.springframework.cache.aspectj.AnnotationCacheAspect
2025-06-17 15:09:02.588  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] register aspect org.springframework.cache.aspectj.JCacheCacheAspect
2025-06-17 15:09:02.588  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] weaver operating in reweavable mode.  Need to verify any required types exist.
2025-06-17 15:09:02.595  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] AspectJ Weaver Version 1.9.19 built on Wednesday Dec 21, 2022 at 06:57:22 PST
2025-06-17 15:09:02.595  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] register classloader org.springframework.data.jpa.repository.config.InspectionClassLoader@34907a49
2025-06-17 15:09:02.595  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] using configuration file:/E:/Maven/MavenRepository/com/genlight/epilcure-common-core/1.0.1-SNAPSHOT/epilcure-common-core-1.0.1-SNAPSHOT.jar!/META-INF/aop.xml
2025-06-17 15:09:02.595  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] using configuration file:/E:/Maven/MavenRepository/org/springframework/spring-aspects/6.0.10/spring-aspects-6.0.10.jar!/META-INF/aop.xml
2025-06-17 15:09:02.596  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] register aspect com.genlight.epilcure.common.core.mvc.aspectj.AccessAspect
2025-06-17 15:09:02.596  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] register aspect com.genlight.epilcure.common.core.mvc.aspectj.LogstashAspect
2025-06-17 15:09:02.596  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] register aspect com.genlight.epilcure.common.core.mvc.aspectj.FileValidAspect
2025-06-17 15:09:02.597  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] register aspect org.springframework.beans.factory.aspectj.AnnotationBeanConfigurerAspect
2025-06-17 15:09:02.598  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] register aspect org.springframework.scheduling.aspectj.AnnotationAsyncExecutionAspect
2025-06-17 15:09:02.599  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] register aspect org.springframework.transaction.aspectj.AnnotationTransactionAspect
2025-06-17 15:09:02.599  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] register aspect org.springframework.transaction.aspectj.JtaAnnotationTransactionAspect
2025-06-17 15:09:02.600  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] register aspect org.springframework.cache.aspectj.AnnotationCacheAspect
2025-06-17 15:09:02.601  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] register aspect org.springframework.cache.aspectj.JCacheCacheAspect
2025-06-17 15:09:02.601  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] weaver operating in reweavable mode.  Need to verify any required types exist.
2025-06-17 15:09:02.609  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] AspectJ Weaver Version 1.9.19 built on Wednesday Dec 21, 2022 at 06:57:22 PST
2025-06-17 15:09:02.609  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] register classloader org.springframework.data.jpa.repository.config.InspectionClassLoader@627cb3ed
2025-06-17 15:09:02.609  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] using configuration file:/E:/Maven/MavenRepository/com/genlight/epilcure-common-core/1.0.1-SNAPSHOT/epilcure-common-core-1.0.1-SNAPSHOT.jar!/META-INF/aop.xml
2025-06-17 15:09:02.609  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] using configuration file:/E:/Maven/MavenRepository/org/springframework/spring-aspects/6.0.10/spring-aspects-6.0.10.jar!/META-INF/aop.xml
2025-06-17 15:09:02.609  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] register aspect com.genlight.epilcure.common.core.mvc.aspectj.AccessAspect
2025-06-17 15:09:02.609  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] register aspect com.genlight.epilcure.common.core.mvc.aspectj.LogstashAspect
2025-06-17 15:09:02.610  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] register aspect com.genlight.epilcure.common.core.mvc.aspectj.FileValidAspect
2025-06-17 15:09:02.610  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] register aspect org.springframework.beans.factory.aspectj.AnnotationBeanConfigurerAspect
2025-06-17 15:09:02.611  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] register aspect org.springframework.scheduling.aspectj.AnnotationAsyncExecutionAspect
2025-06-17 15:09:02.612  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] register aspect org.springframework.transaction.aspectj.AnnotationTransactionAspect
2025-06-17 15:09:02.612  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] register aspect org.springframework.transaction.aspectj.JtaAnnotationTransactionAspect
2025-06-17 15:09:02.613  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] register aspect org.springframework.cache.aspectj.AnnotationCacheAspect
2025-06-17 15:09:02.613  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] register aspect org.springframework.cache.aspectj.JCacheCacheAspect
2025-06-17 15:09:02.614  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] weaver operating in reweavable mode.  Need to verify any required types exist.
2025-06-17 15:09:02.622  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] AspectJ Weaver Version 1.9.19 built on Wednesday Dec 21, 2022 at 06:57:22 PST
2025-06-17 15:09:02.622  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] register classloader org.springframework.data.jpa.repository.config.InspectionClassLoader@5319e4fc
2025-06-17 15:09:02.622  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] using configuration file:/E:/Maven/MavenRepository/com/genlight/epilcure-common-core/1.0.1-SNAPSHOT/epilcure-common-core-1.0.1-SNAPSHOT.jar!/META-INF/aop.xml
2025-06-17 15:09:02.622  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] using configuration file:/E:/Maven/MavenRepository/org/springframework/spring-aspects/6.0.10/spring-aspects-6.0.10.jar!/META-INF/aop.xml
2025-06-17 15:09:02.622  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] register aspect com.genlight.epilcure.common.core.mvc.aspectj.AccessAspect
2025-06-17 15:09:02.623  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] register aspect com.genlight.epilcure.common.core.mvc.aspectj.LogstashAspect
2025-06-17 15:09:02.623  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] register aspect com.genlight.epilcure.common.core.mvc.aspectj.FileValidAspect
2025-06-17 15:09:02.624  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] register aspect org.springframework.beans.factory.aspectj.AnnotationBeanConfigurerAspect
2025-06-17 15:09:02.625  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] register aspect org.springframework.scheduling.aspectj.AnnotationAsyncExecutionAspect
2025-06-17 15:09:02.625  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] register aspect org.springframework.transaction.aspectj.AnnotationTransactionAspect
2025-06-17 15:09:02.626  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] register aspect org.springframework.transaction.aspectj.JtaAnnotationTransactionAspect
2025-06-17 15:09:02.626  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] register aspect org.springframework.cache.aspectj.AnnotationCacheAspect
2025-06-17 15:09:02.627  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] register aspect org.springframework.cache.aspectj.JCacheCacheAspect
2025-06-17 15:09:02.627  INFO 133052 --- [main] AspectJ Weaver                           : [AspectJ] weaver operating in reweavable mode.  Need to verify any required types exist.
2025-06-17 15:09:02.698  INFO 133052 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 512 ms. Found 17 JPA repository interfaces.
2025-06-17 15:09:02.710  INFO 133052 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Multiple Spring Data modules found, entering strict repository configuration mode
2025-06-17 15:09:02.711  INFO 133052 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-17 15:09:02.729  INFO 133052 --- [main] .RepositoryConfigurationExtensionSupport : Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.genlight.epilcure.service.iScheme.dao.repository.InductionGroupRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-17 15:09:02.742  INFO 133052 --- [main] .RepositoryConfigurationExtensionSupport : Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.genlight.epilcure.service.iScheme.dao.repository.InductionPulseRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-17 15:09:02.745  INFO 133052 --- [main] .RepositoryConfigurationExtensionSupport : Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.genlight.epilcure.service.iScheme.dao.repository.InductionSchemeRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-17 15:09:02.746  INFO 133052 --- [main] .RepositoryConfigurationExtensionSupport : Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.genlight.epilcure.service.iScheme.dao.repository.PatientRealEegSchemeRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-17 15:09:02.747  INFO 133052 --- [main] .RepositoryConfigurationExtensionSupport : Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.genlight.epilcure.service.iScheme.dao.repository.PatientStimulateLogRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-17 15:09:02.749  INFO 133052 --- [main] .RepositoryConfigurationExtensionSupport : Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.genlight.epilcure.service.iScheme.dao.repository.PatientStimulatePulseLogRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-17 15:09:02.750  INFO 133052 --- [main] .RepositoryConfigurationExtensionSupport : Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.genlight.epilcure.service.iScheme.dao.repository.SchemeLogRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-17 15:09:02.751  INFO 133052 --- [main] .RepositoryConfigurationExtensionSupport : Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.genlight.epilcure.service.iScheme.dao.repository.SchemeLogUnitRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-17 15:09:02.753  INFO 133052 --- [main] .RepositoryConfigurationExtensionSupport : Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.genlight.epilcure.service.iScheme.dao.repository.SchemeRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-17 15:09:02.754  INFO 133052 --- [main] .RepositoryConfigurationExtensionSupport : Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.genlight.epilcure.service.iScheme.dao.repository.StimulateFrequencyRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-17 15:09:02.756  INFO 133052 --- [main] .RepositoryConfigurationExtensionSupport : Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.genlight.epilcure.service.iScheme.dao.repository.StimulateProgramGroupRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-17 15:09:02.756  INFO 133052 --- [main] .RepositoryConfigurationExtensionSupport : Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.genlight.epilcure.service.iScheme.dao.repository.StimulatePulseRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-17 15:09:02.758  INFO 133052 --- [main] .RepositoryConfigurationExtensionSupport : Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.genlight.epilcure.service.iScheme.dao.repository.StimulateTimeRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-17 15:09:02.759  INFO 133052 --- [main] .RepositoryConfigurationExtensionSupport : Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.genlight.epilcure.service.iScheme.dao.repository.TimeEegGroupSchemeRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-17 15:09:02.760  INFO 133052 --- [main] .RepositoryConfigurationExtensionSupport : Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.genlight.epilcure.service.iScheme.dao.repository.TimeEegSchemeRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-17 15:09:02.760  INFO 133052 --- [main] .RepositoryConfigurationExtensionSupport : Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.genlight.epilcure.service.iScheme.dao.repository.TouchPointRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-17 15:09:02.762  INFO 133052 --- [main] .RepositoryConfigurationExtensionSupport : Spring Data Redis - Could not safely identify store assignment for repository candidate interface com.genlight.epilcure.service.iScheme.dao.repository.UploadFileRepository; If you want this repository to be a Redis repository, consider annotating your entities with one of these annotations: org.springframework.data.redis.core.RedisHash (preferred), or consider extending one of the following types with your repository: org.springframework.data.keyvalue.repository.KeyValueRepository
2025-06-17 15:09:02.762  INFO 133052 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 45 ms. Found 0 Redis repository interfaces.
2025-06-17 15:09:03.060 ERROR 133052 --- [main] o.s.boot.SpringApplication               : Application run failed

java.lang.IllegalStateException: Unable to load cache item
	at org.springframework.cglib.core.internal.LoadingCache.createEntry(LoadingCache.java:75) ~[spring-core-6.0.10.jar:6.0.10]
	at org.springframework.cglib.core.internal.LoadingCache.get(LoadingCache.java:34) ~[spring-core-6.0.10.jar:6.0.10]
	at org.springframework.cglib.core.AbstractClassGenerator$ClassLoaderData.get(AbstractClassGenerator.java:130) ~[spring-core-6.0.10.jar:6.0.10]
	at org.springframework.cglib.core.AbstractClassGenerator.create(AbstractClassGenerator.java:317) ~[spring-core-6.0.10.jar:6.0.10]
	at org.springframework.cglib.proxy.Enhancer.createHelper(Enhancer.java:562) ~[spring-core-6.0.10.jar:6.0.10]
	at org.springframework.cglib.proxy.Enhancer.createClass(Enhancer.java:407) ~[spring-core-6.0.10.jar:6.0.10]
	at org.springframework.context.annotation.ConfigurationClassEnhancer.createClass(ConfigurationClassEnhancer.java:138) ~[spring-context-6.0.10.jar:6.0.10]
	at org.springframework.context.annotation.ConfigurationClassEnhancer.enhance(ConfigurationClassEnhancer.java:109) ~[spring-context-6.0.10.jar:6.0.10]
	at org.springframework.context.annotation.ConfigurationClassPostProcessor.enhanceConfigurationClasses(ConfigurationClassPostProcessor.java:531) ~[spring-context-6.0.10.jar:6.0.10]
	at org.springframework.context.annotation.ConfigurationClassPostProcessor.postProcessBeanFactory(ConfigurationClassPostProcessor.java:308) ~[spring-context-6.0.10.jar:6.0.10]
	at org.springframework.context.support.PostProcessorRegistrationDelegate.invokeBeanFactoryPostProcessors(PostProcessorRegistrationDelegate.java:358) ~[spring-context-6.0.10.jar:6.0.10]
	at org.springframework.context.support.PostProcessorRegistrationDelegate.invokeBeanFactoryPostProcessors(PostProcessorRegistrationDelegate.java:150) ~[spring-context-6.0.10.jar:6.0.10]
	at org.springframework.context.support.AbstractApplicationContext.invokeBeanFactoryPostProcessors(AbstractApplicationContext.java:771) ~[spring-context-6.0.10.jar:6.0.10]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:589) ~[spring-context-6.0.10.jar:6.0.10]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:146) ~[spring-boot-3.1.1.jar:3.1.1]
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:734) ~[spring-boot-3.1.1.jar:3.1.1]
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:436) ~[spring-boot-3.1.1.jar:3.1.1]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:312) ~[spring-boot-3.1.1.jar:3.1.1]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1306) ~[spring-boot-3.1.1.jar:3.1.1]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1295) ~[spring-boot-3.1.1.jar:3.1.1]
	at com.genlight.epilcure.service.iScheme.ISchemeServiceApplication.main(ISchemeServiceApplication.java:22) ~[classes/:na]
Caused by: java.lang.NoClassDefFoundError: com/genlight/epilcure/common/core/util/InitUtils$AjcClosure1
	at java.base/java.lang.Class.getDeclaredConstructors0(Native Method) ~[na:na]
	at java.base/java.lang.Class.privateGetDeclaredConstructors(Class.java:3405) ~[na:na]
	at java.base/java.lang.Class.getDeclaredConstructors(Class.java:2587) ~[na:na]
	at org.springframework.cglib.proxy.Enhancer.generateClass(Enhancer.java:655) ~[spring-core-6.0.10.jar:6.0.10]
	at org.springframework.cglib.transform.TransformingClassGenerator.generateClass(TransformingClassGenerator.java:35) ~[spring-core-6.0.10.jar:6.0.10]
	at org.springframework.cglib.core.DefaultGeneratorStrategy.generate(DefaultGeneratorStrategy.java:26) ~[spring-core-6.0.10.jar:6.0.10]
	at org.springframework.cglib.core.ClassLoaderAwareGeneratorStrategy.generate(ClassLoaderAwareGeneratorStrategy.java:57) ~[spring-core-6.0.10.jar:6.0.10]
	at org.springframework.cglib.core.AbstractClassGenerator.generate(AbstractClassGenerator.java:366) ~[spring-core-6.0.10.jar:6.0.10]
	at org.springframework.cglib.proxy.Enhancer.generate(Enhancer.java:575) ~[spring-core-6.0.10.jar:6.0.10]
	at org.springframework.cglib.core.AbstractClassGenerator$ClassLoaderData.lambda$new$1(AbstractClassGenerator.java:107) ~[spring-core-6.0.10.jar:6.0.10]
	at org.springframework.cglib.core.internal.LoadingCache.lambda$createEntry$1(LoadingCache.java:52) ~[spring-core-6.0.10.jar:6.0.10]
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264) ~[na:na]
	at org.springframework.cglib.core.internal.LoadingCache.createEntry(LoadingCache.java:57) ~[spring-core-6.0.10.jar:6.0.10]
	... 20 common frames omitted
Caused by: java.lang.ClassNotFoundException: com.genlight.epilcure.common.core.util.InitUtils$AjcClosure1
	at java.base/jdk.internal.loader.BuiltinClassLoader.loadClass(BuiltinClassLoader.java:641) ~[na:na]
	at java.base/jdk.internal.loader.ClassLoaders$AppClassLoader.loadClass(ClassLoaders.java:188) ~[na:na]
	at java.base/java.lang.ClassLoader.loadClass(ClassLoader.java:521) ~[na:na]
	... 33 common frames omitted

2025-06-17 15:09:03.070  WARN 133052 --- [Thread-10] c.a.n.common.http.HttpClientBeanHolder   : [HttpClientBeanHolder] Start destroying common HttpClient
2025-06-17 15:09:03.070  WARN 133052 --- [Thread-19] c.a.nacos.common.notify.NotifyCenter     : [NotifyCenter] Start destroying Publisher
2025-06-17 15:09:03.070  WARN 133052 --- [Thread-19] c.a.nacos.common.notify.NotifyCenter     : [NotifyCenter] Destruction of the end
2025-06-17 15:09:03.070  WARN 133052 --- [Thread-10] c.a.n.common.http.HttpClientBeanHolder   : [HttpClientBeanHolder] Destruction of the end
