package com.genlight.epilcure.service.config.dao.entity;

import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;
import org.springframework.cache.annotation.Cacheable;

/**
 * <AUTHOR> maoyan
 * @Date : 2022/8/5 14:07
 * @Version :1.0.0
 */
@Getter
@Setter
@Entity
@Accessors(chain = true)
@SuperBuilder
@Cacheable
@NoArgsConstructor
@DynamicInsert
@DynamicUpdate
@Table(name = "t_symptom_type")
public class SymptomType extends MedicalRecordBase {

}
