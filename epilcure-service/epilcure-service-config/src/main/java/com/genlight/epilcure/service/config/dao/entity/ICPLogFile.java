package com.genlight.epilcure.service.config.dao.entity;

import com.genlight.epilcure.common.core.dao.entity.TBase;
import com.genlight.epilcure.common.core.dao.enums.Status;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;
import org.hibernate.annotations.Comment;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

@Getter
@Setter
@Accessors(chain = true)
@SuperBuilder
@NoArgsConstructor
@DynamicUpdate
@DynamicInsert
@Entity
@Table(name = "t_icp_log_file")
public class ICPLogFile extends TBase {
    @Column(nullable = false)
    @Comment("路径")
    private String path;

    @Column
    @Comment("解析状态")
    private Status status;

    @Column(length = 2048)
    private String remark;
}
