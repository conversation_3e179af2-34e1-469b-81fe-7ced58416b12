package com.genlight.epilcure.service.config.pojo.vo;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;

@Data
@SuperBuilder
@NoArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@Schema(description = "蓝牙状态")
public class ICPBleStatus extends LogBaseVO {
    private Byte freeTimeOut;

    private Byte confirmTimeOut;
}
