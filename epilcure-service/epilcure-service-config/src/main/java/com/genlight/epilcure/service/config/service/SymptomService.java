package com.genlight.epilcure.service.config.service;

import com.genlight.epilcure.api.config.pojo.dto.SymptomDTO;
import com.genlight.epilcure.api.config.pojo.vo.SymptomVO;
import com.genlight.epilcure.service.config.constants.CacheConstants;
import com.genlight.epilcure.service.config.dao.entity.Symptom;
import com.genlight.epilcure.service.config.dao.repository.SymptomRepository;
import com.genlight.epilcure.service.config.pojo.convert.SymptomConvert;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/6/11 12:06
 * @Version 1.0.0
 **/
@Service
@CacheConfig(cacheNames = CacheConstants.CACHE_NAMES_SYMPTOM)
public class SymptomService extends MedicalRecordBaseService<Symptom, SymptomVO, SymptomDTO, SymptomRepository, SymptomConvert> {
    @Override
    @Cacheable(cacheNames = CacheConstants.CACHE_NAMES_SYMPTOM_FINDS)
    public List<SymptomVO> finds(SymptomDTO dto) {
        return super.finds(dto);
    }

    @Override
    @CacheEvict(cacheNames = CacheConstants.CACHE_NAMES_SYMPTOM_FINDS, allEntries = true)
    public SymptomVO update(Long aLong, SymptomDTO dto) {
        return super.update(aLong, dto);
    }

    @Override
    @CacheEvict(cacheNames = CacheConstants.CACHE_NAMES_SYMPTOM_FINDS, allEntries = true)
    public SymptomVO add(SymptomDTO dto) {
        return super.add(dto);
    }
}
