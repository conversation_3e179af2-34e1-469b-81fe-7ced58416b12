package com.genlight.epilcure.service.config.feign;

import com.genlight.epilcure.api.config.feign.IDiagnosisController;
import com.genlight.epilcure.api.config.pojo.vo.DiagnosisVO;
import com.genlight.epilcure.service.config.service.DiagnosisService;
import io.swagger.v3.oas.annotations.Hidden;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/6/12 15:16
 * @Version 1.0.0
 **/
@RestController("feignDiagnosisController")
@RequestMapping
@Hidden
public class DiagnosisController implements IDiagnosisController {

    @Resource
    private DiagnosisService diagnosisService;

    @Override
    public Boolean existsById(Long id) {
        return diagnosisService.existsById(id);
    }

    @Override
    public Boolean existsByIds(List<Long> ids) {
        return diagnosisService.existsByIds(ids);
    }

    @Override
    public DiagnosisVO findById(Long id) {
        return diagnosisService.findById(id);
    }
}
