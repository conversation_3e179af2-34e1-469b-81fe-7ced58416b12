package com.genlight.epilcure.service.config.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.genlight.epilcure.api.config.pojo.dto.ICPLogDetailDTO;
import com.genlight.epilcure.api.config.pojo.vo.ICPLogDetailVO;
import com.genlight.epilcure.api.config.pojo.vo.ICPLogFileVO;
import com.genlight.epilcure.common.core.dao.enums.Status;
import com.genlight.epilcure.common.core.service.BaseService;
import com.genlight.epilcure.service.config.dao.entity.ICPLogDetails;
import com.genlight.epilcure.service.config.dao.entity.ICPLogFile;
import com.genlight.epilcure.service.config.dao.repository.ICPLogDetailRepository;
import com.genlight.epilcure.service.config.pojo.convert.ICPLogDetailConvert;
import com.genlight.epilcure.service.config.pojo.enums.ICPLogType;
import com.genlight.epilcure.service.config.pojo.vo.*;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import jakarta.annotation.Resource;
import jakarta.persistence.criteria.Predicate;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.nio.charset.Charset;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.zip.ZipInputStream;

@Service
@Slf4j
public class ICPLogDetailService extends BaseService<ICPLogDetails, Long, ICPLogDetailRepository> {
    @Resource
    private ObjectMapper objectMapper;

    @Resource
    private ICPLogDetailConvert icpLogDetailConvert;

    @Resource
    private ICPLogService icpLogService;

    private static final DateTimeFormatter dateFormat = DateTimeFormatter.ofPattern("yyMMddHHmmss");

    @Transactional
    @Async
    public CompletableFuture<List<ICPLogDetailVO>> analysis(MultipartFile multipartFile, ICPLogFileVO icpLogFileVO) {
        List<LogBaseVO> logDetails = new ArrayList<>();
        Long patientId = Long.parseLong(Objects.requireNonNull(multipartFile.getOriginalFilename()).split("-")[2]);
        try (ZipInputStream zipInputStream = new ZipInputStream(multipartFile.getInputStream())) {
            while (Objects.nonNull(zipInputStream.getNextEntry())) {
                ByteBuf byteBuffer = Unpooled.wrappedBuffer(zipInputStream.readAllBytes());
                while (byteBuffer.readableBytes() > 3) {
                    ICPLogType type = ICPLogType.valueof(byteBuffer.readByte());

                    byteBuffer.readByte(); // 跳过索引

                    switch (type) {
                        case BLE_STATUS -> {
                            byteBuffer.readBytes(3);
                            for (int i = 0; i < 3; i++) {
                                ICPBleStatus logBaseVO = new ICPBleStatus();
                                try {
                                    Date date = readDate(byteBuffer);
                                    logBaseVO.setDate(date);
                                    logBaseVO.setFreeTimeOut(byteBuffer.readByte());
                                    logBaseVO.setConfirmTimeOut(byteBuffer.readByte());

                                    logDetails.add(logBaseVO);
                                } catch (Exception ex) {
                                    log.error(ex.getMessage());
                                    byteBuffer.readBytes(2);
                                    break;
                                }


                            }
                        }
                        case STIMULATION -> {
                            byteBuffer.readBytes(3);
                            for (int i = 0; i < 3; i++) {
                                ICPStimulation logBaseVO = new ICPStimulation();
                                try {
                                    Date date = readDate(byteBuffer);
                                    logBaseVO.setDate(date);
                                    logBaseVO.setStimulationType(byteBuffer.readByte());
                                    logBaseVO.setState(byteBuffer.readByte());
                                    logBaseVO.setSchemeIndex(byteBuffer.readByte());

                                    logDetails.add(logBaseVO);
                                } catch (Exception ex) {
                                    log.error(ex.getMessage());
                                    byteBuffer.readBytes(3);
                                    break;
                                }
                            }
                        }
                        case RESPONSE -> {
                            byteBuffer.readBytes(3);
                            for (int i = 0; i < 3; i++) {
                                ICPResponseStatus logBaseVO = new ICPResponseStatus();
                                try {
                                    Date date = readDate(byteBuffer);
                                    logBaseVO.setDate(date);
                                    logBaseVO.setResponseStatus(byteBuffer.readByte());
                                    logDetails.add(logBaseVO);
                                } catch (Exception ex) {
                                    log.error(ex.getMessage());
                                    byteBuffer.readBytes(1);
                                    break;
                                }
                            }
                        }
                        case SAMPLE -> {
                            byteBuffer.readBytes(3);
                            for (int i = 0; i < 3; i++) {
                                ICPSampleConfig logBaseVO = new ICPSampleConfig();
                                try {
                                    Date date = readDate(byteBuffer);
                                    logBaseVO.setDate(date);
                                    logBaseVO.setChannelOne(byteBuffer.readByte());
                                    logBaseVO.setTouchOne(byteBuffer.readBytes(5).array());
                                    logBaseVO.setChannelTwo(byteBuffer.readByte());
                                    logBaseVO.setTouchTwo(byteBuffer.readBytes(5).array());

                                    logBaseVO.setUpload(byteBuffer.readByte());
                                    logBaseVO.setStimulation(byteBuffer.readByte());
                                    logDetails.add(logBaseVO);
                                } catch (Exception ex) {
                                    log.error(ex.getMessage());
                                    byteBuffer.readBytes(14);
                                    break;
                                }
                            }
                        }
                        case DETECTION -> {
                            byteBuffer.readBytes(3);
                            for (int i = 0; i < 3; i++) {
                                ICPDetection logBaseVO = new ICPDetection();
                                try {
                                    Date date = readDate(byteBuffer);
                                    logBaseVO.setDate(date);
                                    logBaseVO.setChannelOne(byteBuffer.readByte());
                                    logBaseVO.setTouchOne(byteBuffer.readBytes(5).array());
                                    logBaseVO.setChannelTwo(byteBuffer.readByte());
                                    logBaseVO.setTouchTwo(byteBuffer.readBytes(5).array());

                                    byteBuffer.readBytes(6);
                                    logDetails.add(logBaseVO);
                                } catch (Exception ex) {
                                    log.error(ex.getMessage());
                                    byteBuffer.readBytes(18);
                                    break;
                                }
                            }
                        }
                        case OFF -> {
                            byteBuffer.readBytes(3);
                            for (int i = 0; i < 3; i++) {
                                ICPOff logBaseVO = new ICPOff();
                                try {
                                    Date date = readDate(byteBuffer);
                                    logBaseVO.setDate(date);
                                    logBaseVO.setOffType(byteBuffer.readByte());
                                    logBaseVO.setResponseStatus(byteBuffer.readByte());
                                    logBaseVO.setStimulationType(byteBuffer.readByte());
                                    logBaseVO.setStimulationStatus(byteBuffer.readByte());
                                    logBaseVO.setSchemeIndex(byteBuffer.readByte());

                                    logDetails.add(logBaseVO);
                                } catch (Exception ex) {
                                    log.error(ex.getMessage());
                                    byteBuffer.readBytes(5);
                                    break;
                                }
                            }
                        }
                        case RESET -> {
                            byteBuffer.readBytes(3);
                            for (int i = 0; i < 3; i++) {
                                ICPReset logBaseVO = new ICPReset();
                                try {
                                    Date date = readDate(byteBuffer);
                                    logBaseVO.setDate(date);
                                    logBaseVO.setResponseStatus(byteBuffer.readByte());
                                    logBaseVO.setResetType(byteBuffer.readByte());

                                    logBaseVO.setStimulationType(byteBuffer.readByte());
                                    logBaseVO.setStimulationStatus(byteBuffer.readByte());
                                    logBaseVO.setSchemeIndex(byteBuffer.readByte());

                                    logDetails.add(logBaseVO);
                                } catch (Exception ex) {
                                    log.error(ex.getMessage());
                                    byteBuffer.readBytes(5);
                                    break;
                                }
                            }
                        }
                        case RECHARGE -> {
                            byteBuffer.readBytes(3);

                            for (int i = 0; i < 3; i++) {
                                ICPRecharge logBaseVO = new ICPRecharge();
                                try {
                                    Date date = readDate(byteBuffer);
                                    logBaseVO.setDate(date);
                                    logBaseVO.setStartDate(readDate(byteBuffer));
                                    logBaseVO.setEndDate(readDate(byteBuffer));

                                    logBaseVO.setStartVoltage(byteBuffer.readUnsignedShort());
                                    logBaseVO.setEndVoltage(byteBuffer.readUnsignedShort());
                                    logBaseVO.setStartStatus(byteBuffer.readByte());

                                    logDetails.add(logBaseVO);
                                } catch (Exception ex) {
                                    log.error(ex.getMessage());
                                    byteBuffer.readBytes(21);
                                    break;
                                }
                            }
                        }
                        case LIFE -> {
                            byteBuffer.readBytes(3);

                            for (int i = 0; i < 3; i++) {
                                ICPLife logBaseVO = new ICPLife();
                                try {
                                    Date date = readDate(byteBuffer);
                                    logBaseVO.setDate(date);
                                    logBaseVO.setLife(byteBuffer.readInt());
                                    logBaseVO.setInfo(byteBuffer.readBytes(7).toString(Charset.forName("utf8")));
                                    logBaseVO.setSn(byteBuffer.readBytes(4).toString());
                                    byteBuffer.readBytes(19);

                                    logDetails.add(logBaseVO);
                                } catch (Exception ex) {
                                    log.error(ex.getMessage());
                                    byteBuffer.readBytes(44);
                                    break;
                                }
                            }
                        }
                        case STIMULATION_PARAM -> {
                            byteBuffer.readBytes(3);
                            for (int i = 0; i < 3; i++) {
                                ICPStimulationParam logBaseVO = new ICPStimulationParam();

                                try {
                                    Date date = readDate(byteBuffer);
                                    logBaseVO.setDate(date);
                                    logBaseVO.setInfo(byteBuffer.readBytes(549).array());

                                    logDetails.add(logBaseVO);
                                } catch (Exception ex) {
                                    log.error(ex.getMessage());
                                    byteBuffer.readBytes(549);
                                    break;
                                }
                            }
                        }
                        default -> {
                            log.error("日志类型不存在");
                            continue;
                        }
                    }
                }
            }
            icpLogService.updateFile(icpLogFileVO.getId(), Status.ENABLED, null);
            List<ICPLogDetails> entities = new ArrayList<>();
            logDetails.forEach(l -> {
                try {
                    entities.add(ICPLogDetails.builder()
                            .icpLogFile(ICPLogFile.builder().id(icpLogFileVO.getId()).build())
                            .data(objectMapper.writeValueAsString(l))
                            .type(l.getType().intValue())
                            .patientId(patientId)
                            .createTime(l.getDate())
                            .build());
                } catch (JsonProcessingException e) {
                    throw new RuntimeException(e);
                }
            });
            return CompletableFuture.completedFuture(icpLogDetailConvert.po2vo(repository.saveAll(entities)));
        } catch (Exception e) {
            icpLogService.updateFile(icpLogFileVO.getId(), Status.DELETED, e.getMessage());
            throw new RuntimeException(e);
        }


    }

    private Date readDate(ByteBuf buf) {
        // 解析刺激数据时，数据量大时， 性能有问题
        // String date = String.format("%02d%02d%02d%02d%02d%02d", buf.readByte(), buf.readByte(), buf.readByte(), buf.readByte(), buf.readByte(), buf.readByte());
        return Date.from(LocalDateTime.parse(StringUtils.leftPad(String.valueOf(buf.readByte()), 2, "0")
                        + StringUtils.leftPad(String.valueOf(buf.readByte()), 2, "0")
                        + StringUtils.leftPad(String.valueOf(buf.readByte()), 2, "0")
                        + StringUtils.leftPad(String.valueOf(buf.readByte()), 2, "0")
                        + StringUtils.leftPad(String.valueOf(buf.readByte()), 2, "0")
                        + StringUtils.leftPad(String.valueOf(buf.readByte()), 2, "0"),
                dateFormat).atZone(ZoneId.systemDefault()).toInstant());
    }

    @Transactional(readOnly = true)
    public Page<ICPLogDetailVO> findBppLogs(ICPLogDetailDTO bppLogDetailDTO, Pageable pageable) {
        Specification<ICPLogDetails> specification = (root, query, criteriaBuilder) -> {

            Predicate predicate = criteriaBuilder.conjunction();
            if (Objects.nonNull(bppLogDetailDTO.getStartDate())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.greaterThanOrEqualTo(root.get("createTime"), bppLogDetailDTO.getStartDate()));
            }
            if (Objects.nonNull(bppLogDetailDTO.getEndDate())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.lessThanOrEqualTo(root.get("createTime"), bppLogDetailDTO.getEndDate()));
            }
            if (Objects.nonNull(bppLogDetailDTO.getType())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("type"), bppLogDetailDTO.getType()));
            }
            if (Objects.nonNull(bppLogDetailDTO.getPatientId())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("patientId"), bppLogDetailDTO.getPatientId()));
            }
            return predicate;

        };
        return icpLogDetailConvert.po2vo(repository.findAll(specification, pageable));
    }
}
