package com.genlight.epilcure.service.config.controller;

import com.genlight.epilcure.api.config.pojo.dto.SymptomDTO;
import com.genlight.epilcure.api.config.pojo.vo.SymptomVO;
import com.genlight.epilcure.common.core.mvc.response.JsonResult;
import com.genlight.epilcure.service.config.service.SymptomService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/6/11 12:29
 * @Version 1.0.0
 **/
@RestController
@RequestMapping("/api/config/symptoms")
@Tag(name = "SymptomController", description = "症状相关接口")
public class SymptomController {
    @Resource
    private SymptomService symptomService;

    @PostMapping
    @Operation(summary = "新增症状")
    public JsonResult<SymptomVO> add(@Valid @RequestBody SymptomDTO symptomDTO) {
        return JsonResult.ok(symptomService.add(symptomDTO));
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "删除症状")
    public JsonResult<String> delete(@PathVariable Long id) {
        symptomService.delete(id);
        return JsonResult.ok("删除成功");
    }

    @GetMapping("/{id}")
    @Operation(summary = "根据id获取症状信息")
    public JsonResult<SymptomVO> find(@PathVariable Long id) {
        return JsonResult.ok(symptomService.findById(id));
    }

    @PutMapping("/{id}")
    @Operation(summary = "修改症状信息")
    public JsonResult<SymptomVO> update(@PathVariable Long id, @RequestBody SymptomDTO symptomDTO) {
        return JsonResult.ok(symptomService.update(id, symptomDTO));
    }

    @GetMapping("/finds")
    @Operation(summary = "根据条件查询症状列表")
    public JsonResult<List<SymptomVO>> finds(SymptomDTO symptomDTO) {
        return JsonResult.ok(symptomService.finds(symptomDTO));
    }

}
