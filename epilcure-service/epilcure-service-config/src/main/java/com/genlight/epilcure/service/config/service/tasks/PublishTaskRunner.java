package com.genlight.epilcure.service.config.service.tasks;

import com.genlight.epilcure.api.config.pojo.vo.ClientPublishTaskVO;
import com.genlight.epilcure.service.config.service.ClientPublishTaskService;
import com.genlight.epilcure.service.config.service.ClientVersionService;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;

import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

public class PublishTaskRunner implements Runnable {
    private final ClientVersionService clientVersionService;

    private final ClientPublishTaskService clientPublishTaskService;

    private final RedissonClient redissonClient;

    public PublishTaskRunner(ClientVersionService clientVersionService, ClientPublishTaskService clientPublishTaskService, RedissonClient redissonClient) {
        this.clientVersionService = clientVersionService;
        this.clientPublishTaskService = clientPublishTaskService;
        this.redissonClient = redissonClient;
    }

    @Override
    public void run() {
        while (true) {
            RLock obtain = redissonClient.getLock("findClientPublishTask");
            boolean b = false;
            try {

                b = obtain.tryLock(5, 5, TimeUnit.SECONDS);

                List<ClientPublishTaskVO> all = clientPublishTaskService.findsAll();
                Date now = new Date();
                all.forEach(cpt -> {
                    if (cpt.getPublishDate().before(now)) {
                        clientVersionService.publishVersion(cpt);
                    }
                });
            } catch (Exception e) {
                e.printStackTrace();
            } finally {

                try {
                    if (b)
                        obtain.unlock();
                } catch (Exception e) {
                    e.printStackTrace();
                } finally {
                    try {
                        Thread.sleep(5000);
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                    }
                }
            }
        }
    }
}
