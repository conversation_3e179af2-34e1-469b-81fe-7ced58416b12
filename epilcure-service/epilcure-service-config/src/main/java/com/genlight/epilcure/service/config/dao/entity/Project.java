package com.genlight.epilcure.service.config.dao.entity;

import com.genlight.epilcure.api.config.enums.ProjectType;
import com.genlight.epilcure.common.core.dao.entity.TBase;
import com.genlight.epilcure.common.core.dao.enums.Status;
import jakarta.persistence.*;
import lombok.*;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;
import org.hibernate.annotations.Comment;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

/**
 * <AUTHOR>
 * @Date 2022/6/26 12:26
 * @Version 1.0.0
 **/
@Setter
@Getter
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString
@Entity
@Cacheable
@DynamicInsert
@DynamicUpdate
@Table(name = "t_project")
public class Project extends TBase {

    @Comment("项目名称")
    @Column(nullable = false, unique = true)
    private String name;

    @Comment("项目唯一标识")
    @Column(nullable = false, unique = true)
    private String code;

    @Comment("项目类型")
    @Enumerated(EnumType.ORDINAL)
    @Column(nullable = false)
    private ProjectType type;

    @Comment("项目包名")
    @Column
    private String packageName;

    @Comment("状态")
    private Status status;

    @Comment("硬件版本号")
    @Column
    private String hardwareVersion;
}
