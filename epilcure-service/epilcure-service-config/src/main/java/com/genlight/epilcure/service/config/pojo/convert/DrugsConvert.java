package com.genlight.epilcure.service.config.pojo.convert;

import com.genlight.epilcure.api.config.pojo.dto.DrugsDTO;
import com.genlight.epilcure.api.config.pojo.vo.DrugsVO;
import com.genlight.epilcure.common.core.pojo.convert.qualified.All;
import com.genlight.epilcure.common.core.pojo.convert.qualified.Basic;
import com.genlight.epilcure.service.config.dao.entity.Drugs;
import org.mapstruct.*;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/6/8 23:29
 * @Version 1.0.0
 **/
@Named("DrugsConvert")
@Mapper(config = MedicalRecordBaseConvert.class, uses = DiseaseConvert.class)
public interface DrugsConvert extends MedicalRecordBaseConvert<Drugs, DrugsVO, DrugsDTO> {
    @Basic
    @Mapping(target = "diseases", ignore = true)
    @BeanMapping(nullValueCheckStrategy = NullValueCheckStrategy.ON_IMPLICIT_CONVERSION)
    @Override
    DrugsVO po2vo(Drugs po);

    @IterableMapping(qualifiedBy = Basic.class)
    List<DrugsVO> po2vo(List<Drugs> pos);

    @All
    @Mapping(target = "diseases", qualifiedByName = "DiseaseConvert", qualifiedBy = Basic.class)
    @BeanMapping(nullValueCheckStrategy = NullValueCheckStrategy.ON_IMPLICIT_CONVERSION)
    @Override
    DrugsVO po2voBySummay(Drugs po);
}
