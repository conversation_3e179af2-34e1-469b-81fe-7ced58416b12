package com.genlight.epilcure.service.config.pojo.convert;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.genlight.epilcure.api.config.pojo.dto.ClientPublishTaskDTO;
import com.genlight.epilcure.api.config.pojo.vo.ClientPublishTaskVO;
import com.genlight.epilcure.common.core.pojo.convert.BaseConvert;
import com.genlight.epilcure.service.config.dao.entity.ClientPublishTask;
import org.mapstruct.*;
import org.springframework.beans.factory.annotation.Autowired;

@Mapper(config = BaseConvert.class, uses = ClientVersionConvert.class)
public abstract class ClientPublishTaskConvert implements BaseConvert<ClientPublishTask, ClientPublishTaskVO, ClientPublishTaskDTO> {

    @Autowired
    protected ObjectMapper objectMapper;

    @Mapping(target = "status", expression = "java(java.util.Optional.ofNullable(clientPublishTaskDTO.getStatus()).orElse(com.genlight.epilcure.common.core.dao.enums.Status.DISABLED))")
    @Mapping(target = "tests", expression = "java(java.util.Objects.nonNull(clientPublishTaskDTO.getTests()) ? com.genlight.epilcure.common.core.util.FunctionUtils.ThrowingFunction.<List<Long>, String>sneaky(objectMapper::writeValueAsString).apply(clientPublishTaskDTO.getTests()): null)")
    @Mapping(target = "patientTests", expression = "java(java.util.Objects.nonNull(clientPublishTaskDTO.getPatientTests()) ? com.genlight.epilcure.common.core.util.FunctionUtils.ThrowingFunction.<List<Long>, String>sneaky(objectMapper::writeValueAsString).apply(clientPublishTaskDTO.getPatientTests()): null)")
    public abstract ClientPublishTask dto2po(ClientPublishTaskDTO clientPublishTaskDTO);

    @Mapping(target = "status", expression = "java(java.util.Optional.ofNullable(clientPublishTaskDTO.getStatus()).orElse(com.genlight.epilcure.common.core.dao.enums.Status.DISABLED))")
    @Mapping(target = "tests", expression = "java(java.util.Objects.nonNull(clientPublishTaskDTO.getTests()) ? com.genlight.epilcure.common.core.util.FunctionUtils.ThrowingFunction.<List<Long>, String>sneaky(objectMapper::writeValueAsString).apply(clientPublishTaskDTO.getTests()): null)")
    @Mapping(target = "patientTests", expression = "java(java.util.Objects.nonNull(clientPublishTaskDTO.getPatientTests()) ? com.genlight.epilcure.common.core.util.FunctionUtils.ThrowingFunction.<List<Long>, String>sneaky(objectMapper::writeValueAsString).apply(clientPublishTaskDTO.getPatientTests()): null)")
    public abstract ClientPublishTask dto2po(ClientPublishTaskDTO clientPublishTaskDTO, @MappingTarget ClientPublishTask clientPublishTask);

    @Mapping(target = "tests", expression = "java(java.util.Objects.nonNull(po.getTests()) ? com.genlight.epilcure.common.core.util.FunctionUtils.ThrowingFunctionTowParams.<String, com.fasterxml.jackson.databind.JavaType, List<Long>>sneaky(objectMapper::readValue).apply(po.getTests(), com.fasterxml.jackson.databind.type.TypeFactory.defaultInstance().constructCollectionType(List.class, Long.class)): null)")
    @Mapping(target = "patientTests", expression = "java(java.util.Objects.nonNull(po.getPatientTests()) ? com.genlight.epilcure.common.core.util.FunctionUtils.ThrowingFunctionTowParams.<String, com.fasterxml.jackson.databind.JavaType, List<Long>>sneaky(objectMapper::readValue).apply(po.getPatientTests(), com.fasterxml.jackson.databind.type.TypeFactory.defaultInstance().constructCollectionType(List.class, Long.class)): null)")
    @BeanMapping(nullValueCheckStrategy = NullValueCheckStrategy.ON_IMPLICIT_CONVERSION)
    public abstract ClientPublishTaskVO po2vo(ClientPublishTask po);
}
