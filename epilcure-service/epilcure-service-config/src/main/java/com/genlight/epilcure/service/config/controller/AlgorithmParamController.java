package com.genlight.epilcure.service.config.controller;

import com.genlight.epilcure.api.config.pojo.dto.AlgorithmParamDTO;
import com.genlight.epilcure.api.config.pojo.vo.AlgorithmParamVO;
import com.genlight.epilcure.common.core.mvc.response.JsonResult;
import com.genlight.epilcure.service.config.service.AlgorithmParamService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/6/26 21:31
 * @Version 1.0.0
 **/
@RestController
@RequestMapping("/api/config/algorithmParams")
@Tag(name = "AlgorithmParamController", description = "算法参数相关接口")
public class AlgorithmParamController {
    @Resource
    private AlgorithmParamService algorithmParamService;

    @PostMapping
    @Operation(summary = "新增算法算法")
    public JsonResult<AlgorithmParamVO> add(@Valid @RequestBody AlgorithmParamDTO algorithmParamDTO) {
        return JsonResult.ok(algorithmParamService.addAlgorithmParam(algorithmParamDTO));
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "删除算法参数")
    public JsonResult<String> delete(@PathVariable Long id) {
        algorithmParamService.deleteAlgorithmParam(id);
        return JsonResult.ok("删除成功");
    }

    @GetMapping("/algorithms/{id}")
    @Operation(summary = "根据算法id获取算法参数列表")
    public JsonResult<List<AlgorithmParamVO>> findByAlgorithm(@PathVariable Long id, @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date updateTime) {
        return JsonResult.ok(algorithmParamService.findsByAlgorithm(id, updateTime));
    }

    @PutMapping("/{id}")
    @Operation(summary = "修改算法参数")
    public JsonResult<AlgorithmParamVO> update(@PathVariable Long id, @Valid @RequestBody AlgorithmParamDTO algorithmParamDTO) {
        return JsonResult.ok(algorithmParamService.updateAlgorithmParam(id, algorithmParamDTO));
    }

}
