package com.genlight.epilcure.service.config.controller;

import com.genlight.epilcure.api.config.pojo.dto.DiagnosisDTO;
import com.genlight.epilcure.api.config.pojo.vo.DiagnosisVO;
import com.genlight.epilcure.common.core.mvc.response.JsonResult;
import com.genlight.epilcure.common.core.pojo.valid.Add;
import com.genlight.epilcure.service.config.service.DiagnosisService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/config/diagnosis")
@Tag(name = "DiagnosisController", description = "结论相关接口")
public class DiagnosisController {
    @Autowired
    private DiagnosisService diagnosisService;

    @Operation(summary = "根据id获取结论")
    @GetMapping("/{id}")
    public JsonResult<DiagnosisVO> find(@PathVariable Long id) {
        return JsonResult.ok(diagnosisService.findById(id));
    }

    @Operation(summary = "添加结论类型")
    @PostMapping
    public JsonResult<String> add(@Validated(Add.class) @RequestBody DiagnosisDTO diagnosisDTO) {
        diagnosisService.add(diagnosisDTO);
        return JsonResult.ok("新增成功");
    }

    @Operation(summary = "修改结论信息")
    @PutMapping("/{id}")
    public JsonResult<String> update(@PathVariable Long id, @Valid @RequestBody DiagnosisDTO diagnosisDTO) {
        diagnosisService.update(id, diagnosisDTO);
        return JsonResult.ok("修改成功");
    }

    @GetMapping("/findAll")
    @Operation(summary = "获取结论列表")
    public JsonResult<List<DiagnosisVO>> findAll(DiagnosisDTO diagnosisDTO) {
        return JsonResult.ok(diagnosisService.finds(diagnosisDTO));
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "删除结论")
    public JsonResult<String> delete(@PathVariable Long id) {
        diagnosisService.delete(id);
        return JsonResult.ok("删除成功");
    }
}
