package com.genlight.epilcure.service.config.service;

import com.genlight.epilcure.api.config.pojo.dto.ClientPublishTaskDTO;
import com.genlight.epilcure.api.config.pojo.dto.ClientVersionDTO;
import com.genlight.epilcure.api.config.pojo.vo.ClientPublishTaskVO;
import com.genlight.epilcure.api.config.pojo.vo.ClientVersionVO;
import com.genlight.epilcure.api.patient.feign.IPatientTestController;
import com.genlight.epilcure.api.user.feign.ITestController;
import com.genlight.epilcure.common.core.dao.enums.Status;
import com.genlight.epilcure.common.core.service.BaseService;
import com.genlight.epilcure.common.core.service.exception.ArgsException;
import com.genlight.epilcure.common.core.service.exception.ServiceException;
import com.genlight.epilcure.service.config.constants.CacheConstants;
import com.genlight.epilcure.service.config.dao.entity.*;
import com.genlight.epilcure.service.config.dao.repository.ClientPublishTaskRepository;
import com.genlight.epilcure.service.config.pojo.convert.ClientPublishTaskConvert;
import jakarta.annotation.Resource;
import jakarta.persistence.criteria.Predicate;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

@Service
@CacheConfig(cacheNames = CacheConstants.CACHE_NAMES_CLIENT_PUBLISH)
public class ClientPublishTaskService extends BaseService<ClientPublishTask, Long, ClientPublishTaskRepository> {

    @Resource
    private ITestController iTestController;

    @Resource
    private IPatientTestController iPatientTestController;

    @Resource
    private ClientVersionService clientVersionService;

    @Resource
    private ClientPublishTaskConvert clientPublishTaskConvert;

    @Resource
    private RedissonClient redissonClient;

    @Resource
    private ProjectService projectService;

    @Transactional
    @CacheEvict(allEntries = true)
    public ClientPublishTaskVO add(ClientPublishTaskDTO clientPublishTaskDTO) {
        if (!clientVersionService.existById(clientPublishTaskDTO.getClientVersionId())) {
            throw new ArgsException("版本Id[{0}]不存在");
        }

        if (Objects.nonNull(clientPublishTaskDTO.getTests())) {
            for (Long testId : clientPublishTaskDTO.getTests()) {
                if (iTestController.existsTestById(testId) == 0) {
                    throw new ServiceException("用户测试名单[{0}]不存在！", testId);
                }
            }
        }

        if (Objects.nonNull(clientPublishTaskDTO.getPatientTests())) {
            for (Long testId : clientPublishTaskDTO.getPatientTests()) {
                if (iPatientTestController.existsTestById(testId) == 0) {
                    throw new ServiceException("患者测试名单[{0}]不存在！", testId);
                }
            }
        }

        ClientVersionVO clientVersionVO = clientVersionService.findById(clientPublishTaskDTO.getClientVersionId());
        ClientVersionVO lowVersion = clientVersionService.findLowVersion(clientVersionVO.getProject().getId());
        if (Objects.nonNull(lowVersion)) {
            if (clientVersionVO.getSerialNumber() < lowVersion.getSerialNumber()) {
                throw new ArgsException("发布版本不能小于最低兼容版本");
            }
        }

        RLock obtain = redissonClient.getLock("addClientPublishTask");
        try {
            if (obtain.tryLock(5, 5, TimeUnit.SECONDS)) {
                Optional<ClientPublishTask> optional = repository.findByProjectIdAndClientStatusAndStatus(clientVersionVO.getProject().getId(), clientPublishTaskDTO.getClientStatus(), Status.ENABLED);
                Optional<ClientPublishTask> optionalDisable = repository.findByProjectIdAndClientStatusAndStatus(clientVersionVO.getProject().getId(), clientPublishTaskDTO.getClientStatus(), Status.DISABLED);
                optional.ifPresent(clientPublishTask -> updateStatus(clientPublishTask.getId(), Status.DELETED));
                optionalDisable.ifPresent(clientPublishTask -> updateStatus(clientPublishTask.getId(), Status.DELETED));
                ClientPublishTask clientPublishTask = clientPublishTaskConvert.dto2po(clientPublishTaskDTO);
                clientPublishTask.setClientVersion(ClientVersion.builder().id(clientPublishTaskDTO.getClientVersionId()).build());
                clientPublishTask.setProject(Project.builder().id(clientVersionVO.getProject().getId()).build());
                clientPublishTask.setUserId(getUserId());
                clientPublishTask.setUserName(getNikeName());
                return clientPublishTaskConvert.po2vo(repository.save(clientPublishTask));
            }
            throw new ServiceException("操作太频繁，请稍后再试！");
        } catch (Exception ex) {
            throw new RuntimeException(ex);
        } finally {
            this.unLock(obtain);
        }
    }

    @Transactional(readOnly = true)
    public ClientPublishTask findByProjectIdAndClientStatusAndStatus(ClientVersionDTO clientVersionDTO) {
        Optional<ClientPublishTask> optional = repository.findByProjectIdAndClientStatusAndStatus(clientVersionDTO.getProjectId(), clientVersionDTO.getClientStatus(), Status.ENABLED);
        return optional.orElse(null);
    }

    @Cacheable
    @Transactional(readOnly = true)
    public List<ClientPublishTaskVO> findsAll() {
        Specification<ClientPublishTask> specification = (root, query, criteriaBuilder) -> {
            Predicate predicate = criteriaBuilder.conjunction();
            predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get(ClientPublishTask_.STATUS), Status.DISABLED));
            return predicate;
        };
        return clientPublishTaskConvert.po2vo(repository.findAll(specification));
    }

    @Cacheable
    @Transactional(readOnly = true)
    public Page<ClientPublishTaskVO> finds(ClientPublishTaskDTO clientPublishTaskDTO, Pageable pageable) {
        Specification<ClientPublishTask> specification = (root, query, criteriaBuilder) -> {
            Predicate predicate = criteriaBuilder.conjunction();
            if (Objects.nonNull(clientPublishTaskDTO.getClientVersionId())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get(ClientPublishTask_.CLIENT_VERSION).get(ClientVersion_.ID), clientPublishTaskDTO.getClientVersionId()));
            }
            if (Objects.nonNull(clientPublishTaskDTO.getStatus())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get(ClientPublishTask_.STATUS), clientPublishTaskDTO.getStatus()));
            }
            return predicate;
        };
        return clientPublishTaskConvert.po2vo(repository.findAll(specification, pageable));
    }

    @CacheEvict(allEntries = true)
    @Transactional
    public void updateStatus(Long id, Status status) {
        Optional<ClientPublishTask> optional = repository.findById(id);
        if (optional.isEmpty()) {
            throw new ArgsException("任务[{0}]不存在", id);
        }

        optional.get().setStatus(status);
        repository.save(optional.get());
    }

    @CacheEvict(allEntries = true)
    @Transactional
    public ClientPublishTaskVO update(Long id, ClientPublishTaskDTO clientPublishTaskDTO) {

        if (Objects.nonNull(clientPublishTaskDTO.getTests())) {
            for (Long testId : clientPublishTaskDTO.getTests()) {
                if (iTestController.existsTestById(testId) == 0) {
                    throw new ServiceException("测试名单[{0}]不存在！", testId);
                }
            }
        }

        RLock obtain = redissonClient.getLock("findClientPublishTask");
        try {
            if (obtain.tryLock(5, 5, TimeUnit.SECONDS)) {
                Optional<ClientPublishTask> optional = repository.findById(id);
                if (optional.isEmpty()) {
                    throw new ServiceException("任务[{0}]不存在", id);
                }

                ClientPublishTask clientPublishTask = optional.get();
                if (clientPublishTask.getStatus().equals(Status.DISABLED)) {
                    throw new ServiceException("任务已发布不允许修改");
                }
                clientPublishTaskConvert.dto2po(clientPublishTaskDTO, clientPublishTask);
                return clientPublishTaskConvert.po2vo(repository.save(clientPublishTask));
            }
            throw new ServiceException("操作太频繁，请稍后再试！");
        } catch (Exception ex) {
            throw new RuntimeException(ex);
        } finally {
            this.unLock(obtain);
        }
    }
}
