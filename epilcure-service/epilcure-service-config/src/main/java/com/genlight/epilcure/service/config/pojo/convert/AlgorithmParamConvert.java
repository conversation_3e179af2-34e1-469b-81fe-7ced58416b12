package com.genlight.epilcure.service.config.pojo.convert;

import com.genlight.epilcure.api.config.pojo.dto.AlgorithmParamDTO;
import com.genlight.epilcure.api.config.pojo.vo.AlgorithmParamVO;
import com.genlight.epilcure.common.core.pojo.convert.BaseConvert;
import com.genlight.epilcure.service.config.dao.entity.AlgorithmParam;
import org.mapstruct.Mapper;
import org.mapstruct.Named;

@Named("AlgorithmParamConvert")
@Mapper(config = BaseConvert.class)
public interface AlgorithmParamConvert extends BaseConvert<AlgorithmParam, AlgorithmParamVO, AlgorithmParamDTO> {
}
