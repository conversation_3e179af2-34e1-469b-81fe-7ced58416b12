package com.genlight.epilcure.service.config.service;

import com.genlight.epilcure.api.config.pojo.vo.BppLogFileVO;
import com.genlight.epilcure.common.core.dao.enums.Status;
import com.genlight.epilcure.common.core.service.BaseService;
import com.genlight.epilcure.common.core.util.MinioUtils;
import com.genlight.epilcure.service.config.constants.FileConstants;
import com.genlight.epilcure.service.config.dao.entity.BppLogFile;
import com.genlight.epilcure.service.config.dao.repository.BppLogRepository;
import com.genlight.epilcure.service.config.pojo.convert.BppLogFileConvert;
import jakarta.annotation.Nullable;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.util.Optional;

@Service
public class BppLogService extends BaseService<BppLogFile, Long, BppLogRepository> {
    @Resource
    private MinioUtils minioUtils;

    @Resource
    private BppLogFileConvert bppLogFileConvert;

    public BppLogFileVO add(MultipartFile multipartFile) {
        return saveFile(multipartFile);
    }

    @Transactional
    private BppLogFileVO saveFile(MultipartFile multipartFile) {
        String path = minioUtils.upload(FileConstants.BPP_Log_DIR, multipartFile);
        BppLogFile bppLogFile = BppLogFile.builder()
                .path(path)
                .status(Status.ADD).build();
        return bppLogFileConvert.po2vo(repository.save(bppLogFile));
    }

    @Transactional
    public void updateFile(Long id, Status status, @Nullable String remark) {
        Optional<BppLogFile> optional = repository.findById(id);
        if (optional.isEmpty()) {
            throw new RuntimeException("日子文件不存在");
        }
        BppLogFile bppLogFile = optional.get();
        bppLogFile.setStatus(status);
        bppLogFile.setRemark(remark);
        repository.save(bppLogFile);
    }
}
