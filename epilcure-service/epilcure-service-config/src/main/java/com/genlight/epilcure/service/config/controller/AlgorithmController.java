package com.genlight.epilcure.service.config.controller;

import com.genlight.epilcure.api.config.pojo.dto.AlgorithmDTO;
import com.genlight.epilcure.api.config.pojo.vo.AlgorithmVO;
import com.genlight.epilcure.common.core.mvc.response.JsonResult;
import com.genlight.epilcure.service.config.service.AlgorithmService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/6/26 20:45
 * @Version 1.0.0
 **/
@RestController
@RequestMapping("/api/config/algorithms")
@Tag(name = "AlgorithmController", description = "算法配置相关接口")
public class AlgorithmController {
    @Resource
    private AlgorithmService algorithmService;

    @PostMapping
    @Operation(summary = "添加算法")
    public JsonResult<AlgorithmVO> add(@Valid @RequestBody AlgorithmDTO algorithmDTO) {
        return JsonResult.ok(algorithmService.addAlgorithm(algorithmDTO));
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "删除算法")
    public JsonResult<String> delete(@PathVariable Long id) {
        algorithmService.deleteAlgorithm(id);
        return JsonResult.ok("删除成功");
    }

    @GetMapping("/{id}")
    @Operation(summary = "根据Id获得算法")
    public JsonResult<AlgorithmVO> find(@PathVariable Long id) {
        return JsonResult.ok(algorithmService.find(id));
    }

    @GetMapping("/finds")
    @Operation(summary = "根据条件查询算法列表")
    public JsonResult<List<AlgorithmVO>> finds(AlgorithmDTO algorithmDTO) {
        return JsonResult.ok(algorithmService.findsAlgorithm(algorithmDTO));
    }

    @PutMapping("/{id}")
    @Operation(summary = "修改算法信息")
    public JsonResult<AlgorithmVO> update(@PathVariable Long id, @Valid @RequestBody AlgorithmDTO algorithmDTO) {
        return JsonResult.ok(algorithmService.updateAlgorithm(id, algorithmDTO));
    }


}
