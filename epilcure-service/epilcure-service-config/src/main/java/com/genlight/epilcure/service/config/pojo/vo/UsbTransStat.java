package com.genlight.epilcure.service.config.pojo.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;

import java.util.Date;

@Data
@SuperBuilder
@NoArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class UsbTransStat extends LogBaseVO {
    private byte inoutStat;
    private byte usb3303Flag;
    private byte usbStackFlag;
    private byte usbStatFlag;
    private Date usbPutout;
}
