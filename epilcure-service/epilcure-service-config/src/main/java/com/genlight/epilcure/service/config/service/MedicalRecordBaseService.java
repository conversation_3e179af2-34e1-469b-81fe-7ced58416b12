package com.genlight.epilcure.service.config.service;

import com.genlight.epilcure.api.config.pojo.dto.MedicalRecordBaseDTO;
import com.genlight.epilcure.api.config.pojo.vo.MedicalRecordBaseVO;
import com.genlight.epilcure.common.core.service.exception.ArgsException;
import com.genlight.epilcure.service.config.dao.entity.MedicalRecordBase;
import com.genlight.epilcure.service.config.dao.repository.MedicalRecordBaseRepository;
import com.genlight.epilcure.service.config.pojo.convert.MedicalRecordBaseConvert;
import jakarta.annotation.Resource;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @Date 2022/6/11 14:52
 * @Version 1.0.0
 **/
public class MedicalRecordBaseService<T extends MedicalRecordBase,
        VO extends MedicalRecordBaseVO,
        DTO extends MedicalRecordBaseDTO,
        Repository extends MedicalRecordBaseRepository<T, Long> & JpaSpecificationExecutor<T>,
        Convert extends MedicalRecordBaseConvert<T, VO, DTO>> {
    @Autowired
    private Repository repository;
    @Autowired
    private Convert convert;


    @Resource
    private DiseaseService diseaseService;

    /**
     * 添加病历配置
     *
     * @param dto
     * @return
     */
    @Transactional
    public VO add(DTO dto) {
        if (!diseaseService.existsById(dto.getDiseaseId())) {
            throw new ArgsException("疾病Id[{0}]不存在", dto.getDiseaseId());
        }

        Optional<T> optional = repository.findByName(dto.getName());
        T t;
        if (!optional.isEmpty()) {
            t = optional.get();
        } else {
            t = convert.dto2po(dto);
        }

        t.getDiseases().add(diseaseService.getDiseaseById(dto.getDiseaseId()));

        return convert.po2vo(repository.save(t));
    }


    @Transactional(readOnly = true)
    public VO findById(Long id) {
        Optional<T> optional = repository.findById(id);
        if (optional.isEmpty()) {
            throw new ArgsException("Id[{0}]不存在", id);
        }
        return convert.po2voBySummay(optional.get());
    }

    /**
     * 查询所有病历配置
     *
     * @return
     */
    @Transactional(readOnly = true)
    public List<VO> finds(DTO dto) {
        Specification<T> drugsSpecification = new Specification<T>() {
            @Override
            public Predicate toPredicate(Root<T> root, CriteriaQuery<?> query, CriteriaBuilder criteriaBuilder) {
//                Root<Disease> diseaseRoot = query.from(Disease.class);
                List<Predicate> predicates = new ArrayList<>();
                if (Objects.nonNull(dto.getName())) {
                    predicates.add(criteriaBuilder.like(root.get("name"), "%" + dto.getName() + "%"));
                }
                if (Objects.nonNull(dto.getDiseaseId())) {
                    predicates.add(criteriaBuilder.equal(root.join("diseases").get("id"), dto.getDiseaseId()));
                }
                return criteriaBuilder.and(predicates.toArray(new Predicate[predicates.size()]));
            }
        };

        return convert.po2vo(repository.findAll(drugsSpecification));
    }

    /**
     * 根据Id删除病历配置
     *
     * @param id
     * @return
     */
    public boolean delete(Long id) {
        repository.deleteById(id);

        //todo:派发消息同步患者信息
        //
        return true;
    }

    /**
     * 修改病历配置
     *
     * @param id
     * @param dto
     * @return
     */
    @Transactional
    public VO update(Long id, DTO dto) {
        Optional<T> optional = repository.findById(id);
        if (optional.isEmpty()) {
            throw new ArgsException("修改的Id[{0}]不存在", id);
        }

        T t = optional.get();
        convert.dto2po(dto, t);
        return convert.po2vo(repository.save(t));
    }

    public Boolean existsById(Long id) {
        return repository.existsById(id);
    }

    public Boolean existsByIds(List<Long> ids) {
        for (Long id : ids) {
            if (!repository.existsById(id)) {
                throw new ArgsException("id[{0}]不存在", id);
            }
        }
        return true;
    }
}
