package com.genlight.epilcure.service.config.controller;

import com.genlight.epilcure.api.config.pojo.dto.AreaDTO;
import com.genlight.epilcure.api.config.pojo.vo.AreaVO;
import com.genlight.epilcure.common.core.mvc.response.JsonResult;
import com.genlight.epilcure.service.config.service.AreaService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/config/areas")
@Tag(name = "AreaController", description = "地址相关接口")
public class AreaController {
    @Resource
    private AreaService areaService;

    @GetMapping("/{id}")
    @Operation(summary = "根据Id获取地址信息")
    public JsonResult<AreaVO> find(@PathVariable Long id) {
        return JsonResult.ok(areaService.getArea(id));
    }

    @GetMapping
    @Operation(summary = "根据条件动态查询地址信息")
    public JsonResult<List<AreaVO>> finds(AreaDTO areaDTO) {
        return JsonResult.ok(areaService.finds(areaDTO));
    }

    @PostMapping
    @Operation(summary = "添加地区")
    public JsonResult<AreaVO> add(@Valid @RequestBody AreaDTO areaDTO) {
        return JsonResult.ok(areaService.addArea(areaDTO));
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "根据id删除地区")
    public JsonResult<String> delete(@PathVariable Long id) {
        areaService.deleteArea(id);
        return JsonResult.ok("删除成功");
    }

    @PutMapping("/{id}")
    @Operation(summary = "修改地区信息")
    public JsonResult<AreaVO> update(@PathVariable Long id, @Valid @RequestBody AreaDTO areaDTO) {
        return JsonResult.ok(areaService.updateArea(id, areaDTO));
    }
}
