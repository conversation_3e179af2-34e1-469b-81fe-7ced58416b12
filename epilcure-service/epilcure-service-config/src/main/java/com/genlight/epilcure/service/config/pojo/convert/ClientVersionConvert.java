package com.genlight.epilcure.service.config.pojo.convert;

import com.genlight.epilcure.api.config.pojo.dto.ClientVersionDTO;
import com.genlight.epilcure.api.config.pojo.vo.ClientVersionVO;
import com.genlight.epilcure.common.core.pojo.convert.BaseConvert;
import com.genlight.epilcure.service.config.dao.entity.ClientVersion;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

@Mapper(config = BaseConvert.class)
public interface ClientVersionConvert extends BaseConvert<ClientVersion, ClientVersionVO, ClientVersionDTO> {

    @Mapping(target = "clientStatus", expression = "java(java.util.Optional.ofNullable(clientVersionDTO.getClientStatus()).orElse(com.genlight.epilcure.api.config.enums.ClientStatus.None))")
    @Mapping(target = "editStatus", expression = "java(java.util.Optional.ofNullable(clientVersionDTO.getEditStatus()).orElse(com.genlight.epilcure.common.core.dao.enums.Status.ENABLED))")
    ClientVersion dto2po(ClientVersionDTO clientVersionDTO, @MappingTarget ClientVersion clientVersion);

    @Mapping(target = "isLow", expression = "java(java.util.Optional.ofNullable(clientVersionDTO.getIsLow()).orElse(false))")
    @Mapping(target = "clientStatus", expression = "java(java.util.Optional.ofNullable(clientVersionDTO.getClientStatus()).orElse(com.genlight.epilcure.api.config.enums.ClientStatus.None))")
    @Mapping(target = "editStatus", expression = "java(java.util.Optional.ofNullable(clientVersionDTO.getEditStatus()).orElse(com.genlight.epilcure.common.core.dao.enums.Status.ENABLED))")
    ClientVersion dto2po(ClientVersionDTO clientVersionDTO);
}
