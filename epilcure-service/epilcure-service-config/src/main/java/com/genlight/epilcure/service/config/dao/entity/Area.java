package com.genlight.epilcure.service.config.dao.entity;

import com.genlight.epilcure.common.core.dao.entity.TEntity;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;
import org.hibernate.annotations.Comment;
import org.hibernate.annotations.Immutable;

/**
 * <AUTHOR>
 * @Date 2022/5/15 21:13
 * @Version 1.0.0
 **/
@Setter
@Getter
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@Entity
@Cacheable
@Immutable
@Table(name = "t_area", indexes = {
        @Index(name = "areaName_index", columnList = "areaName")
})
public class Area extends TEntity {

    @Comment("名称")
    @Column(nullable = false)
    private String areaName;

    @Comment("地区编码")
    @Column(nullable = false)
    private String areaCode;

    @Comment("地区级别（1:省份province,2:市city,3:区县district,4:街道street）")
    @Column(nullable = false)
    private byte level;

    @Comment("城市编码")
    @Column(nullable = false)
    private String cityCode;

    @Comment("经纬度")
    @Column(nullable = false)
    private String center;

    @Comment("上级Id")
    @Column(nullable = false)
    private Long parentId;
}
