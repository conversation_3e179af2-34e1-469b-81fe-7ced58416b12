package com.genlight.epilcure.service.config.pojo.convert;

import com.genlight.epilcure.api.config.pojo.dto.ProjectDTO;
import com.genlight.epilcure.api.config.pojo.vo.ProjectVO;
import com.genlight.epilcure.common.core.pojo.convert.BaseConvert;
import com.genlight.epilcure.service.config.dao.entity.Project;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(config = BaseConvert.class)
public interface ProjectConvert extends BaseConvert<Project, ProjectVO, ProjectDTO> {

    @Mapping(target = "status", expression = "java(java.util.Optional.ofNullable(projectDTO.getStatus()).orElse(com.genlight.epilcure.common.core.dao.enums.Status.ENABLED))")
    @Override
    Project dto2po(ProjectDTO projectDTO);
}
