package com.genlight.epilcure.service.config.controller;

import com.genlight.epilcure.api.config.pojo.dto.SymptomTypeDTO;
import com.genlight.epilcure.api.config.pojo.vo.SymptomTypeVO;
import com.genlight.epilcure.common.core.mvc.response.JsonResult;
import com.genlight.epilcure.service.config.service.SymptomTypeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR> maoyan
 * @Date : 2022/8/5 17:43
 * @Version :1.0.0
 */
@RestController
@RequestMapping("/api/config/symptomTypes")
@Tag(name = "SymptomTypeController", description = "症状类型相关接口")
public class SymptomTypeController {
    @Resource
    private SymptomTypeService symptomTypeService;

    @PostMapping
    @Operation(summary = "新增症状")
    public JsonResult<SymptomTypeVO> add(@Valid @RequestBody SymptomTypeDTO symptomTypeDTO) {
        return JsonResult.ok(symptomTypeService.add(symptomTypeDTO));
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "删除症状")
    public JsonResult<String> delete(@PathVariable Long id) {
        symptomTypeService.delete(id);
        return JsonResult.ok("删除成功");
    }

    @GetMapping("/{id}")
    @Operation(summary = "根据id获取症状信息")
    public JsonResult<SymptomTypeVO> find(@PathVariable Long id) {
        return JsonResult.ok(symptomTypeService.findById(id));
    }

    @PutMapping("/{id}")
    @Operation(summary = "修改症状信息")
    public JsonResult<SymptomTypeVO> update(@PathVariable Long id, @RequestBody SymptomTypeDTO symptomTypeDTO) {
        return JsonResult.ok(symptomTypeService.update(id, symptomTypeDTO));
    }

    @GetMapping("/finds")
    @Operation(summary = "根据条件查询症状列表")
    public JsonResult<List<SymptomTypeVO>> finds(SymptomTypeDTO symptomTypeDTO) {
        return JsonResult.ok(symptomTypeService.finds(symptomTypeDTO));
    }
}
