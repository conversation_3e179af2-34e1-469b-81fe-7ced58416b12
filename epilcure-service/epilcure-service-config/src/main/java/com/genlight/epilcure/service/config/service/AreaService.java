package com.genlight.epilcure.service.config.service;

import com.genlight.epilcure.api.config.pojo.dto.AreaDTO;
import com.genlight.epilcure.api.config.pojo.vo.AreaVO;
import com.genlight.epilcure.common.core.service.BaseService;
import com.genlight.epilcure.common.core.service.exception.ArgsException;
import com.genlight.epilcure.service.config.constants.CacheConstants;
import com.genlight.epilcure.service.config.dao.entity.Area;
import com.genlight.epilcure.service.config.dao.entity.Area_;
import com.genlight.epilcure.service.config.dao.repository.AreaRepository;
import com.genlight.epilcure.service.config.pojo.convert.AreaConvert;
import jakarta.annotation.Resource;
import jakarta.persistence.criteria.Predicate;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.annotation.Caching;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Service
@CacheConfig(cacheNames = CacheConstants.CACHE_NAMES_AREA)
public class AreaService extends BaseService<Area, Long, AreaRepository> {
    @Resource
    private AreaConvert areaConvert;

    @Cacheable(key = "#p0")
    @Transactional(readOnly = true)
    public AreaVO getArea(Long id) {
        Optional<Area> optional = repository.findById(id);
        if (optional.isEmpty()) {
            throw new ArgsException("地址Id[{0}]不存在", id);
        }
        return areaConvert.po2vo(optional.get());
    }

    @Cacheable(cacheNames = CacheConstants.CACHE_NAMES_AREA_FINDS)
    @Transactional(readOnly = true)
    public List<AreaVO> finds(AreaDTO areaDTO) {
        Specification<Area> specification = (root, query, criteriaBuilder) -> {
            Predicate predicate = criteriaBuilder.conjunction();
            if (Objects.nonNull(areaDTO.getLevel())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("level"), areaDTO.getLevel()));
            }
            if (Objects.nonNull(areaDTO.getParentId())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("parentId"), areaDTO.getParentId()));
            }
            if (Objects.nonNull(areaDTO.getUpdateTime())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.greaterThanOrEqualTo(root.get(Area_.UPDATE_TIME), areaDTO.getUpdateTime()));
            }

            return predicate;
        };
        return areaConvert.po2vo(repository.findAll(specification));
    }

    @Transactional
    @CacheEvict(cacheNames = CacheConstants.CACHE_NAMES_AREA_FINDS, allEntries = true)
    public AreaVO addArea(AreaDTO areaDTO) {
        Area area = areaConvert.dto2po(areaDTO);
        return areaConvert.po2vo(repository.save(area));
    }

    public Boolean deleteArea(Long id) {
        Optional<Area> optional = repository.findById(id);
        if (optional.isEmpty()) {
            throw new ArgsException("删除的地区Id[{0}]不存在");
        }
        repository.deleteById(id);
        return true;
    }

    @Transactional
    @Caching(evict = {
            @CacheEvict(cacheNames = CacheConstants.CACHE_NAMES_AREA_FINDS, allEntries = true),
            @CacheEvict(key = "#p0")
    })
    public AreaVO updateArea(Long id, AreaDTO areaDTO) {
        Optional<Area> optional = repository.findById(id);
        if (optional.isEmpty()) {
            throw new ArgsException("修改的地区Id[{0}]不存在");
        }
        Area area = optional.get();
        areaConvert.dto2po(areaDTO, area);
        return areaConvert.po2vo(repository.save(area));
    }


}
