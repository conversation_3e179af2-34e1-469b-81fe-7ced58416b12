package com.genlight.epilcure.service.config.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.genlight.epilcure.api.config.pojo.dto.BppLogDetailDTO;
import com.genlight.epilcure.api.config.pojo.vo.BppLogDetailVO;
import com.genlight.epilcure.api.config.pojo.vo.BppLogFileVO;
import com.genlight.epilcure.api.device.feign.IBcmController;
import com.genlight.epilcure.api.device.pojo.vo.BcmVO;
import com.genlight.epilcure.common.core.dao.enums.Status;
import com.genlight.epilcure.common.core.service.BaseService;
import com.genlight.epilcure.service.config.dao.entity.BppLogDetails;
import com.genlight.epilcure.service.config.dao.entity.BppLogFile;
import com.genlight.epilcure.service.config.dao.repository.BppLogDetailRepository;
import com.genlight.epilcure.service.config.pojo.convert.BppLogDetailConvert;
import com.genlight.epilcure.service.config.pojo.enums.BcmRecordType;
import com.genlight.epilcure.service.config.pojo.enums.BppLogType;
import com.genlight.epilcure.service.config.pojo.vo.*;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import jakarta.annotation.Resource;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.zip.ZipInputStream;

@Service
@Slf4j
public class BppLogDetailService extends BaseService<BppLogDetails, Long, BppLogDetailRepository> {
    @Resource
    private ObjectMapper objectMapper;

    @Resource
    private BppLogDetailConvert bppLogDetailConvert;

    @Resource
    private BppLogService bppLogService;

    @Resource
    private IBcmController bcmController;

    @Transactional
    @Async
    public CompletableFuture<List<BppLogDetailVO>> analysis(MultipartFile multipartFile, BppLogFileVO bppLogFileVO) {
        List<BppLogDetails> logDetails = new ArrayList<>();
        Long patientId = 0l;
        try{
            patientId = Long.parseLong(Objects.requireNonNull(multipartFile.getOriginalFilename()).split("-")[2]);
        }catch (Exception ex){
            BcmVO byMac = bcmController.findByMac(multipartFile.getOriginalFilename().split("-")[5]);
            patientId = byMac.getPatientId();
        }
        try (ZipInputStream zipInputStream = new ZipInputStream(multipartFile.getInputStream())) {
            while (Objects.nonNull(zipInputStream.getNextEntry())) {
                ByteBuf byteBuffer = Unpooled.wrappedBuffer(zipInputStream.readAllBytes());
                while (byteBuffer.readableBytes() > 5) {
                    int endIndex = byteBuffer.readerIndex() + 16;
                    BppLogType type = BppLogType.valueof(byteBuffer.readByte());

                    long times = byteBuffer.readUnsignedIntLE();
                    LogBaseVO logBaseVO = null;
                    switch (type) {

                        case BPP_POWER_OFF -> {
                            logBaseVO = new BppPowerOffVO();
                            break;
                        }
                        case BPP_TIME_SET -> {
                            logBaseVO = BppTimeSet.builder()
                                    .afterTime(new Date(byteBuffer.readIntLE() * 1000L))
                                    .build();
                            break;
                        }
                        case BPP_EEG_REC -> {
                            logBaseVO = EegRecVO.builder()
                                    .eegDataNum(byteBuffer.readUnsignedShortLE())
                                    .stimulationNum(byteBuffer.readUnsignedShortLE())
                                    .eegDataNum0(byteBuffer.readUnsignedShortLE())
                                    .stimulationNum0(byteBuffer.readUnsignedShortLE())
                                    .transFlag(byteBuffer.readByte())
                                    .build();
                            break;
                        }
                        case BPP_SCALP_THICK -> {
                            logBaseVO = BppScalpThick.builder()
                                    .scalpThickness(byteBuffer.readByte())
                                    .build();
                            break;
                        }
                        case BPP_UPGRADE_VER -> {
                            logBaseVO = BppUpgradeVer.builder()
                                    .upgradeVer(new Integer[]{(int) byteBuffer.readByte(), (int) byteBuffer.readByte(), (int) byteBuffer.readByte()})
                                    .build();
                            break;
                        }
                        case BPP_STORE_COVER -> {
                            logBaseVO = StorConver.builder().build();
                            break;
                        }
                        case BPP_USB_OPT -> {
                            logBaseVO = UsbTransStat.builder()
                                    .inoutStat(byteBuffer.readByte())
                                    .usb3303Flag(byteBuffer.readByte())
                                    .usbStackFlag(byteBuffer.readByte())
                                    .usbStatFlag(byteBuffer.readByte())
                                    .usbPutout(new Date(byteBuffer.readIntLE() * 1000L))
                                    .build();
                            break;
                        }
                        case BPP_TREAT_OPT -> {
                            logBaseVO = BppTreat.builder().build();
                        }
                        case BPP_HARD_FAULT -> {
                            logBaseVO = BppHardFault.builder()
                                    .num(byteBuffer.readChar())
                                    .reg1(byteBuffer.readIntLE())
                                    .reg2(byteBuffer.readIntLE())
                                    .build();
                            break;
                        }
                        case BCM_COMM_TIME -> {
                            logBaseVO = BcmCommTime.builder()
                                    .commType(byteBuffer.readUnsignedByte())
                                    .treatStats(byteBuffer.readUnsignedByte())
                                    .build();
                            break;
                        }
                        case BCM_EEG_REC -> {
                            logBaseVO = BcmEegRec.builder()
                                    .algorTrigNum((short) byteBuffer.readByte())
                                    .magnetTrigNum((short) byteBuffer.readByte())
                                    .fixtimeTrigNum((short) byteBuffer.readByte())
                                    .stimulationNum(byteBuffer.readIntLE())
                                    .readStat(byteBuffer.readByte())
                                    .build();
                            break;
                        }
                        case BCM_ON_OFF_STAT -> {
                            logBaseVO = BcmOnoffStatus.builder()
                                    .bcmOnoffStatus(byteBuffer.readByte())
                                    .build();
                            break;
                        }
                        case BCM_RESP_TREAT -> {
                            logBaseVO = BcmRespTreat.builder()
                                    .respTreatStatus(byteBuffer.readByte())
                                    .build();
                            break;
                        }
                        case BCM_FORCE_OFF -> {
                            logBaseVO = BcmForceOff.builder().build();
                            break;
                        }
                        case BCM_ABNORMAL_RST -> {
                            logBaseVO = BcmAbnorRst.builder()
                                    .wdgFlag(byteBuffer.readByte())
                                    .softwareFlag(byteBuffer.readByte())
                                    .lowvoltaFlag(byteBuffer.readByte())
                                    .lowpowFlag(byteBuffer.readByte())
                                    .iwdgFlag(byteBuffer.readByte())
                                    .rstpinFlag(byteBuffer.readByte())
                                    .optFlag(byteBuffer.readByte())
                                    .firewallFlag(byteBuffer.readByte())
                                    .build();
                            break;
                        }
                        case BCM_CHARGER -> {
                            logBaseVO = BcmRecharger.builder()
                                    .chargeType(byteBuffer.readIntLE())
                                    .bcmPower(byteBuffer.readUnsignedShortLE())
                                    .bcmCouple(byteBuffer.readUnsignedShortLE())
                                    .build();
                            break;
                        }
                        case BCM_RECORD -> {
                            logBaseVO = BcmRecord.builder()
                                    .recordType(BcmRecordType.valueOf(byteBuffer.readByte()))
                                    .data(byteBuffer.readByte())
                                    .build();
                            break;
                        }
                        case ACTION_RUN_TIME -> {
                            logBaseVO = ActionRunTime.builder()
                                    .actNum(byteBuffer.readUnsignedByte())
                                    .diffTime(byteBuffer.readUnsignedShortLE())
                                    .build();
                            break;
                        }
                        case SYS_START_TIME -> {
                            logBaseVO = SystemStartTime.builder()
                                    .year(byteBuffer.readUnsignedByte())
                                    .month(byteBuffer.readUnsignedByte())
                                    .day(byteBuffer.readUnsignedByte())
                                    .hour(byteBuffer.readUnsignedByte())
                                    .min(byteBuffer.readUnsignedByte())
                                    .sec(byteBuffer.readUnsignedByte())
                                    .build();
                        }
                        case None1 -> {
                            byteBuffer.skipBytes(11);
                        }
                        default -> {
                            log.error("日志类型不存在");
                            continue;
                        }
                    }
                    if (logBaseVO != null) {
                        logBaseVO.setDate(new Date(times * 1000L));
                        logBaseVO.setType((byte) type.ordinal());
                        logDetails.add(BppLogDetails.builder()
                                .patientId(patientId)
                                .createTime(logBaseVO.getDate())
                                .data(objectMapper.writeValueAsString(logBaseVO))
                                .type(type.ordinal())
                                .bppLog(BppLogFile.builder().id(bppLogFileVO.getId()).build())
                                .build());
                        if (byteBuffer.readerIndex() != endIndex) {
                            byteBuffer.readBytes(endIndex - byteBuffer.readerIndex());
                        }
                    }


                }
            }
            bppLogService.updateFile(bppLogFileVO.getId(), Status.ENABLED, null);
            return CompletableFuture.completedFuture(bppLogDetailConvert.po2vo(repository.saveAll(logDetails)));
        } catch (Exception e) {
            bppLogService.updateFile(bppLogFileVO.getId(), Status.DELETED, e.getMessage());
            throw new RuntimeException(e);
        }


    }

    @Transactional(readOnly = true)
    public Page<BppLogDetailVO> findBppLogs(BppLogDetailDTO bppLogDetailDTO, Pageable pageable) {
        Specification<BppLogDetails> specification = new Specification<>() {
            @Override
            public Predicate toPredicate(@NotNull Root<BppLogDetails> root, CriteriaQuery<?> query, CriteriaBuilder criteriaBuilder) {

                Predicate predicate = criteriaBuilder.conjunction();
                if (Objects.nonNull(bppLogDetailDTO.getStartDate())) {
                    predicate = criteriaBuilder.and(predicate, criteriaBuilder.greaterThanOrEqualTo(root.get("createTime"), bppLogDetailDTO.getStartDate()));
                }
                if (Objects.nonNull(bppLogDetailDTO.getEndDate())) {
                    predicate = criteriaBuilder.and(predicate, criteriaBuilder.lessThanOrEqualTo(root.get("createTime"), bppLogDetailDTO.getEndDate()));
                }
                if (Objects.nonNull(bppLogDetailDTO.getType())) {
                    predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("type"), bppLogDetailDTO.getType()));
                }
                if (Objects.nonNull(bppLogDetailDTO.getPatientId())) {
                    predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("patientId"), bppLogDetailDTO.getPatientId()));
                }
                return predicate;

            }
        };
        return bppLogDetailConvert.po2vo(repository.findAll(specification, pageable));
    }
}
