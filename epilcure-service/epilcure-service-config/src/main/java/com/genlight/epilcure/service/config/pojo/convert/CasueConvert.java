package com.genlight.epilcure.service.config.pojo.convert;

import com.genlight.epilcure.api.config.pojo.dto.CasueDTO;
import com.genlight.epilcure.api.config.pojo.vo.CasueVO;
import com.genlight.epilcure.common.core.pojo.convert.qualified.All;
import com.genlight.epilcure.common.core.pojo.convert.qualified.Basic;
import com.genlight.epilcure.service.config.dao.entity.Casue;
import org.mapstruct.*;

import java.util.List;

@Mapper(config = MedicalRecordBaseConvert.class, uses = DiseaseConvert.class)
public interface CasueConvert extends MedicalRecordBaseConvert<Casue, CasueVO, CasueDTO> {
    @Basic
    @Mapping(target = "diseases", ignore = true)
    @BeanMapping(nullValueCheckStrategy = NullValueCheckStrategy.ON_IMPLICIT_CONVERSION)
    @Override
    CasueVO po2vo(Casue po);

    @IterableMapping(qualifiedBy = Basic.class)
    List<CasueVO> po2vo(List<Casue> pos);

    @All
    @Mapping(target = "diseases", qualifiedByName = "DiseaseConvert", qualifiedBy = Basic.class)
    @BeanMapping(nullValueCheckStrategy = NullValueCheckStrategy.ON_IMPLICIT_CONVERSION)
    CasueVO po2voBySummay(Casue po);
}
