package com.genlight.epilcure.service.config.dao.repository;

import com.genlight.epilcure.service.config.dao.entity.AlgorithmParam;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import java.util.Date;
import java.util.List;

public interface AlgorithmParamRepository extends JpaRepository<AlgorithmParam, Long>, JpaSpecificationExecutor<AlgorithmParam> {
    boolean existsByAlgorithmsIdAndId(Long algorithmsId, Long id);

    List<AlgorithmParam> findAllByUpdateTimeGreaterThan(Date updateTime);
}
