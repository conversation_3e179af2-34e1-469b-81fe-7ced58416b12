package com.genlight.epilcure.service.config.dao.entity;

import com.genlight.epilcure.common.core.dao.entity.TBase;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;
import org.hibernate.annotations.Comment;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import java.util.Date;

@Getter
@Setter
@Accessors(chain = true)
@SuperBuilder
@NoArgsConstructor
@DynamicUpdate
@DynamicInsert
@Entity
@Table(name = "t_bpp_log_detail")
public class BppLogDetails extends TBase {

    @Column(nullable = false)
    private String data;

    @Column
    private Integer type;

    @Column
    private Long patientId;

    @Comment("创建时间")
    @Column(nullable = false, updatable = false)
    @Temporal(TemporalType.TIMESTAMP)
    private Date createTime;

    @ToString.Exclude
    @ManyToOne
    private BppLogFile bppLog;
}
