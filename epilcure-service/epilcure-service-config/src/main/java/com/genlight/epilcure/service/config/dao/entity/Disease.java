package com.genlight.epilcure.service.config.dao.entity;

import com.genlight.epilcure.common.core.dao.entity.TBase;
import jakarta.persistence.*;
import lombok.*;
import lombok.experimental.SuperBuilder;
import org.hibernate.annotations.Comment;

import java.util.HashSet;
import java.util.Set;


@Getter
@Setter
@Entity
@SuperBuilder
@NoArgsConstructor
@Table(name = "t_disease")
public class Disease extends TBase {
    @Comment("疾病名称")
    @Column(nullable = false, unique = true)
    private String name;

    @Builder.Default
    @ToString.Exclude
    @ManyToMany(mappedBy = "diseases", fetch = FetchType.LAZY)
    private Set<Drugs> drugs = new HashSet<>();

    @Builder.Default
    @ToString.Exclude
    @ManyToMany(mappedBy = "diseases", fetch = FetchType.LAZY)
    private Set<Symptom> symptoms = new HashSet<>();
}
