package com.genlight.epilcure.service.config.controller;

import com.genlight.epilcure.api.config.pojo.dto.ConfigsDTO;
import com.genlight.epilcure.api.config.pojo.vo.ConfigsVO;
import com.genlight.epilcure.common.core.mvc.response.JsonResult;
import com.genlight.epilcure.service.config.service.ConfigsService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/config/configs")
@Tag(name = "ConfigsController", description = "系统配置相关接口")
public class ConfigsController {
    @Resource
    private ConfigsService configsService;

    @PostMapping
    @Operation(summary = "新增系统配置")
    public JsonResult<ConfigsVO> add(@Valid @RequestBody ConfigsDTO configsDTO) {
        return JsonResult.ok(configsService.addConfig(configsDTO));
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "删除系统配置")
    public JsonResult<String> delete(@PathVariable Long id) {
        configsService.deleteConfig(id);
        return JsonResult.ok("删除成功");
    }

    @GetMapping
    @Operation(summary = "根据条件查询配置信息")
    public JsonResult<Page<ConfigsVO>> finds(ConfigsDTO configsDTO, Pageable pageable) {
        return JsonResult.ok(configsService.finds(configsDTO, pageable));
    }

    @GetMapping("/keys/{key}")
    @Operation(summary = "根据Key值查找配置")
    public JsonResult<ConfigsVO> findByKey(@PathVariable String key) {
        return JsonResult.ok(configsService.findByKey(key));
    }

    @GetMapping("/{id}")
    @Operation(summary = "根据id获取系统配置")
    public JsonResult<ConfigsVO> find(@PathVariable Long id) {
        return JsonResult.ok(configsService.find(id));
    }

    @PutMapping("/{id}")
    @Operation(summary = "修改系统配置")
    public JsonResult<ConfigsVO> update(@PathVariable Long id, @Valid @RequestBody ConfigsDTO configsDTO) {
        return JsonResult.ok(configsService.update(id, configsDTO));
    }
}
