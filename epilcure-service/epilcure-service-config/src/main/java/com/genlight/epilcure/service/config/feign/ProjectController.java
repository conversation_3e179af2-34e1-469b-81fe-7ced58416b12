package com.genlight.epilcure.service.config.feign;

import com.genlight.epilcure.api.config.feign.IProjectController;
import com.genlight.epilcure.service.config.service.ProjectService;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RequestMapping
@RestController("feignProjectController")
public class ProjectController implements IProjectController {

    @Resource
    private ProjectService projectService;

    @Override
    public boolean existsById(Long id) {
        return projectService.existsProjectById(id);
    }
}
