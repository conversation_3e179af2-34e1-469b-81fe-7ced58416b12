package com.genlight.epilcure.service.config.pojo.convert;

import com.genlight.epilcure.api.config.pojo.dto.DiagnosisDTO;
import com.genlight.epilcure.api.config.pojo.vo.DiagnosisVO;
import com.genlight.epilcure.common.core.pojo.convert.qualified.All;
import com.genlight.epilcure.common.core.pojo.convert.qualified.Basic;
import com.genlight.epilcure.service.config.dao.entity.Diagnosis;
import org.mapstruct.*;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/6/11 15:49
 * @Version 1.0.0
 **/
@Mapper(config = MedicalRecordBaseConvert.class, uses = DiseaseConvert.class)
public interface DiagnosisConvert extends MedicalRecordBaseConvert<Diagnosis, DiagnosisVO, DiagnosisDTO> {
    @Basic
    @Mapping(target = "diseases", ignore = true)
    @BeanMapping(nullValueCheckStrategy = NullValueCheckStrategy.ON_IMPLICIT_CONVERSION)
    @Override
    DiagnosisVO po2vo(Diagnosis po);

    @IterableMapping(qualifiedBy = Basic.class)
    List<DiagnosisVO> po2vo(List<Diagnosis> pos);

    @All
    @Mapping(target = "diseases", qualifiedByName = "DiseaseConvert", qualifiedBy = Basic.class)
    @BeanMapping(nullValueCheckStrategy = NullValueCheckStrategy.ON_IMPLICIT_CONVERSION)
    DiagnosisVO po2voBySummay(Diagnosis po);
}
