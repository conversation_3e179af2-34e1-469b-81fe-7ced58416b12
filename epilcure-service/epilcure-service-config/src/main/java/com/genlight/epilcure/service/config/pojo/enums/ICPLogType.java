package com.genlight.epilcure.service.config.pojo.enums;

public enum ICPLogType {
    NONE, BLE_STATUS, STIMULATION, RESPONSE, SAMPLE, DETECTION, OFF, RESET, RECHARGE, LIFE, STIMULATION_PARAM;


    public static ICPLogType valueof(byte val) {
        if (val == 0) {
            return NONE;
        } else if (val == 1) {
            return BLE_STATUS;
        } else if (val == 2) {
            return STIMULATION;
        } else if (val == 3) {
            return RESPONSE;
        } else if (val == 4) {
            return SAMPLE;
        } else if (val == 5) {
            return DETECTION;
        } else if (val == 6) {
            return OFF;
        } else if (val == 7) {
            return RESET;
        } else if (val == 8) {
            return RECHARGE;
        } else if (val == 9) {
            return LIFE;
        } else if (val == 10) {
            return STIMULATION_PARAM;
        }

        return NONE;
    }
}
