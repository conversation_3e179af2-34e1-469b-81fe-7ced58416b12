package com.genlight.epilcure.service.config.dao.repository;

import com.genlight.epilcure.service.config.dao.entity.MedicalRecordBase;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.repository.NoRepositoryBean;

import java.util.Optional;

/**
 * <AUTHOR>
 * @Date 2022/6/12 16:39
 * @Version 1.0.0
 **/
@NoRepositoryBean
public interface MedicalRecordBaseRepository<T extends MedicalRecordBase, ID> extends JpaRepository<T, ID>, JpaSpecificationExecutor<T> {
    Optional<T> findByName(String name);
}
