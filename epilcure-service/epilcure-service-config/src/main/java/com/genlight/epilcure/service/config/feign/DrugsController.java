package com.genlight.epilcure.service.config.feign;

import com.genlight.epilcure.api.config.feign.IDrugsController;
import com.genlight.epilcure.api.config.pojo.vo.DrugsVO;
import com.genlight.epilcure.service.config.service.DrugsService;
import io.swagger.v3.oas.annotations.Hidden;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/6/10 22:35
 * @Version 1.0.0
 **/
@Hidden
@RestController("feignDrugsController")
@RequestMapping
public class DrugsController implements IDrugsController {
    @Resource
    private DrugsService drugsService;

    @Override
    public Boolean existsById(Long id) {
        return drugsService.existsById(id);
    }

    @Override
    public Boolean existsByIds(List<Long> ids) {
        return drugsService.existsByIds(ids);
    }

    @Override
    public DrugsVO find(Long id) {
        return drugsService.findById(id);
    }
}
