package com.genlight.epilcure.service.config.pojo.vo;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;

@Data
@SuperBuilder
@NoArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@Schema(description = "充电")
public class ICPLife extends LogBaseVO {
    private Integer life;

    private String info;

    private String sn;

    private Byte hardwareType;

    private String hardVersion;
}
