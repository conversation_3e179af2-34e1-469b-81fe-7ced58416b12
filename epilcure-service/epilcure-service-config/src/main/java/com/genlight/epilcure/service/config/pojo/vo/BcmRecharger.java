package com.genlight.epilcure.service.config.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;

@Data
@SuperBuilder
@NoArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@Schema(description = "刺激器充电记录")
public class BcmRecharger extends LogBaseVO {
    Integer chargeType;
    Integer bcmPower;
    Integer bcmCouple;
}
