package com.genlight.epilcure.service.config.pojo.convert;

import com.genlight.epilcure.api.config.pojo.dto.SymptomTypeDTO;
import com.genlight.epilcure.api.config.pojo.vo.SymptomTypeVO;
import com.genlight.epilcure.common.core.pojo.convert.qualified.All;
import com.genlight.epilcure.common.core.pojo.convert.qualified.Basic;
import com.genlight.epilcure.service.config.dao.entity.SymptomType;
import org.mapstruct.*;

import java.util.List;

/**
 * <AUTHOR> maoyan
 * @Date : 2022/8/5 17:50
 * @Version :1.0.0
 */
@Named("SymptomTypeConvert")
@Mapper(config = MedicalRecordBaseConvert.class)
public interface SymptomTypeConvert extends MedicalRecordBaseConvert<SymptomType, SymptomTypeVO, SymptomTypeDTO> {
    @Basic
    @Mapping(target = "diseases", ignore = true)
    @BeanMapping(nullValueCheckStrategy = NullValueCheckStrategy.ON_IMPLICIT_CONVERSION)
    @Override
    SymptomTypeVO po2vo(SymptomType po);

    @IterableMapping(qualifiedBy = Basic.class)
    List<SymptomTypeVO> po2vo(List<SymptomType> pos);

    @All
    @Mapping(target = "diseases", qualifiedByName = "DiseaseConvert", qualifiedBy = Basic.class)
    @BeanMapping(nullValueCheckStrategy = NullValueCheckStrategy.ON_IMPLICIT_CONVERSION)
    SymptomTypeVO po2voBySummay(SymptomType po);
}
