package com.genlight.epilcure.service.config.dao.entity;

import com.genlight.epilcure.api.config.enums.ClientPublishType;
import com.genlight.epilcure.api.config.enums.ClientStatus;
import com.genlight.epilcure.common.core.dao.entity.TEntity;
import com.genlight.epilcure.common.core.dao.enums.Status;
import com.genlight.epilcure.common.core.dao.generator.annotation.SignOrder;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import org.hibernate.annotations.Comment;

import java.util.Date;

@Getter
@Setter
@Entity
@Cacheable
@SuperBuilder
@NoArgsConstructor
@SignOrder(0)
@Table(name = "t_client_publish_task", indexes = {
        @Index(name = "project_publishType_idx", columnList = "project_id,clientStatus")
})
public class ClientPublishTask extends TEntity {

    @ToString.Exclude
    @Comment("客户端信息")
    @ManyToOne(optional = false, fetch = FetchType.LAZY)
    private ClientVersion clientVersion;

    @Comment("发布时间")
    @Temporal(TemporalType.TIMESTAMP)
    @Column(nullable = false)
    @SignOrder(1)
    private Date publishDate;

    @Comment("发布类型")
    @Enumerated(EnumType.ORDINAL)
    @Column(nullable = false)
    @SignOrder(2)
    private ClientStatus clientStatus;

    @Comment("发布的项目")
    @ManyToOne(optional = false, fetch = FetchType.LAZY)
    private Project project;

    @Schema(description = "用户测试名单")
    @SignOrder(3)
    private String tests;

    @Schema(description = "患者测试名单")
    @SignOrder(4)
    private String patientTests;

    @Comment("更新类型")
    @Enumerated(EnumType.ORDINAL)
    @Column(nullable = false)
    @SignOrder(5)
    private ClientPublishType clientPublishType;

    @Comment("任务状态")
    @Column(nullable = false)
    @SignOrder(6)
    private Status status;

    @Comment("操作者Id")
    @SignOrder(7)
    private Long userId;

    @Comment("操作者")
    @SignOrder(8)
    private String userName;
}
