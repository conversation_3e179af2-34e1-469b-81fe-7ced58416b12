package com.genlight.epilcure.service.config.controller;

import com.genlight.epilcure.api.config.pojo.vo.ICPLogFileVO;
import com.genlight.epilcure.common.core.mvc.response.JsonResult;
import com.genlight.epilcure.service.config.service.ICPLogDetailService;
import com.genlight.epilcure.service.config.service.ICPLogService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

@RestController
@RequestMapping("/api/config/icpLogFiles")
@Tag(name = "ICPLogFileController", description = "嵌软日志文件相关接口")
public class ICPLogFileController {
    @Resource
    private ICPLogService icpLogService;
    @Resource
    private ICPLogDetailService icpLogDetailService;

    @Operation(summary = "上传嵌软日志文件（Zip文件）")
    @PostMapping(value = "/upload", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public JsonResult<ICPLogFileVO> uploadICPLogFile(@RequestPart MultipartFile multipartFile) {
        return JsonResult.ok(icpLogService.add(multipartFile));
    }
}
