package com.genlight.epilcure.service.config.dao.entity;

import com.genlight.epilcure.common.core.dao.entity.TBase;
import jakarta.persistence.Column;
import jakarta.persistence.ManyToMany;
import jakarta.persistence.MappedSuperclass;
import lombok.*;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;
import org.hibernate.annotations.Comment;

import java.util.HashSet;
import java.util.Set;

/**
 * <AUTHOR>
 * @Date 2022/6/11 13:55
 * @Version 1.0.0
 * @Descript 病历配置抽象信息
 **/
@Setter
@Getter
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@MappedSuperclass
public class MedicalRecordBase extends TBase {
    @Comment("名称")
    @Column(nullable = false, unique = true)
    private String name;

    @Comment("所属疾病")
    @Builder.Default
    @ToString.Exclude
    @ManyToMany
    private Set<Disease> diseases = new HashSet<>();
}
