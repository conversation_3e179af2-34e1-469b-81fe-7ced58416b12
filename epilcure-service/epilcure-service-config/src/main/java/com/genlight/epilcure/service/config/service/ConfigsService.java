package com.genlight.epilcure.service.config.service;

import com.genlight.epilcure.api.config.pojo.dto.ConfigsDTO;
import com.genlight.epilcure.api.config.pojo.vo.ConfigsVO;
import com.genlight.epilcure.common.core.service.BaseService;
import com.genlight.epilcure.common.core.service.exception.ArgsException;
import com.genlight.epilcure.common.core.service.exception.ServiceException;
import com.genlight.epilcure.service.config.constants.CacheConstants;
import com.genlight.epilcure.service.config.dao.entity.Configs;
import com.genlight.epilcure.service.config.dao.repository.ConfigsRepository;
import com.genlight.epilcure.service.config.pojo.convert.ConfigsConvert;
import jakarta.annotation.Resource;
import jakarta.persistence.criteria.Predicate;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.annotation.Caching;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

@Cacheable(cacheNames = CacheConstants.CACHE_NAMES_CONFIGS)
@Service
public class ConfigsService extends BaseService<Configs, Long, ConfigsRepository> {
    @Resource
    private ConfigsConvert configsConvert;

    @Resource
    private RedissonClient redissonClient;

    @Transactional
    @Caching(evict = {
            @CacheEvict(cacheNames = CacheConstants.CACHE_NAMES_CONFIGS_FINDS, allEntries = true)
    })
    public ConfigsVO addConfig(ConfigsDTO configsDTO) {
        RLock lock = redissonClient.getLock("addConfig-%s".formatted(configsDTO.getKey()));
        try {
            if (lock.tryLock(2, 2, TimeUnit.SECONDS)) {
                if (repository.existsByKey(configsDTO.getKey())) {
                    throw new ArgsException("配置Key[{0}]已存在", configsDTO.getKey());
                }
                return configsConvert.po2vo(repository.saveAndFlush(configsConvert.dto2po(configsDTO)));
            }
            throw new ServiceException("操作太频繁，请稍后再试！");
        } catch (Exception e) {
            throw new RuntimeException(e);
        } finally {
            this.unLock(lock);
        }
    }

    @Transactional
    @Caching(evict = {
            @CacheEvict(cacheNames = CacheConstants.CACHE_NAMES_CONFIGS_FINDS, allEntries = true),
            @CacheEvict(key = "#p0")
    })
    public Boolean deleteConfig(Long id) {
        if (!repository.existsById(id)) {
            throw new ArgsException("删除的配置Id[{0}]不存在", id);
        }
        repository.deleteById(id);
        return true;
    }

    @Transactional(readOnly = true)
    @Cacheable(key = "#p0")
    public ConfigsVO find(Long id) {
        Optional<Configs> optional = repository.findById(id);
        if (optional.isEmpty()) {
            throw new ArgsException("配置Id[{0}]不存在", id);
        }
        return configsConvert.po2vo(optional.get());
    }

    @Transactional(readOnly = true)
    @Cacheable(cacheNames = CacheConstants.CACHE_NAMES_CONFIGS_FINDS)
    public ConfigsVO findByKey(String key) {
        Optional<Configs> optional = repository.findByKey(key);
        if (optional.isEmpty()) {
            throw new ArgsException("配置Key[{0}]不存在", key);
        }
        return configsConvert.po2vo(optional.get());
    }

    @Transactional(readOnly = true)
    @Cacheable(cacheNames = CacheConstants.CACHE_NAMES_CONFIGS_FINDS)
    public Optional<ConfigsVO> findByNearsVersion(String key, String version) {
        Optional<Configs> optional = repository.findFirstByKeyAndVersionLessThanEqualOrderByVersionDesc(key, version);
        return optional.map(configs -> configsConvert.po2vo(configs));
    }

    @Transactional(readOnly = true)
    @Cacheable(cacheNames = CacheConstants.CACHE_NAMES_CONFIGS_FINDS)
    public Page<ConfigsVO> finds(ConfigsDTO configsDTO, Pageable pageable) {
        Specification<Configs> specification = (root, query, criteriaBuilder) -> {
            Predicate predicate = criteriaBuilder.conjunction();
            if (Objects.nonNull(configsDTO.getKey())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.like(root.get("key"), "%" + configsDTO.getKey() + "%"));
            }
            if (Objects.nonNull(configsDTO.getRemark())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.like(root.get("remark"), "%" + configsDTO.getRemark() + "%"));
            }

            return predicate;
        };
        return configsConvert.po2vo(repository.findAll(specification, pageable));
    }

    @Transactional
    @Caching(evict = {
            @CacheEvict(cacheNames = CacheConstants.CACHE_NAMES_CONFIGS_FINDS, allEntries = true),
            @CacheEvict(cacheNames = CacheConstants.CACHE_NAMES_CONFIGS, key = "#p0")
    })
    public ConfigsVO update(Long id, ConfigsDTO configsDTO) {
        Optional<Configs> optional = repository.findById(id);
        if (optional.isEmpty()) {
            throw new ArgsException("配置Id[{0}]不存在", id);
        }
        Configs configs = optional.get();

        configsConvert.dto2po(configsDTO, configs);
        return configsConvert.po2vo(repository.save(configs));
    }


}
