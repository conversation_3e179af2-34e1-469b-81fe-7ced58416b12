package com.genlight.epilcure.service.config.pojo.vo;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;

@Data
@SuperBuilder
@NoArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@Schema(description = "复位")
public class ICPReset extends LogBaseVO {
    private byte resetType;

    private byte responseStatus;

    private byte stimulationType;

    private byte stimulationStatus;

    private byte schemeIndex;
}
