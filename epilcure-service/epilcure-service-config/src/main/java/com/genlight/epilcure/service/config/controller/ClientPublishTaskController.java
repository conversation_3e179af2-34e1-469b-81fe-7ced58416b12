package com.genlight.epilcure.service.config.controller;

import com.genlight.epilcure.api.config.pojo.dto.ClientPublishTaskDTO;
import com.genlight.epilcure.api.config.pojo.vo.ClientPublishTaskVO;
import com.genlight.epilcure.common.core.mvc.response.JsonResult;
import com.genlight.epilcure.service.config.service.ClientPublishTaskService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/config/clientPublish")
@Tag(name = "ClientPublishTaskController", description = "发布任务相关接口")
public class ClientPublishTaskController {
    @Resource
    private ClientPublishTaskService clientPublishTaskService;

    @Operation(summary = "新增发布任务")
    @PostMapping
    public JsonResult<ClientPublishTaskVO> add(@Valid @RequestBody ClientPublishTaskDTO clientPublishTaskDTO) {
        return JsonResult.ok(clientPublishTaskService.add(clientPublishTaskDTO));
    }

    @Operation(summary = "根据条件查询发布任务")
    @GetMapping
    public JsonResult<Page<ClientPublishTaskVO>> finds(ClientPublishTaskDTO clientPublishTaskDTO, Pageable pageable) {
        return JsonResult.ok(clientPublishTaskService.finds(clientPublishTaskDTO, pageable));
    }

    @PutMapping("/{id}")
    @Operation(summary = "修改发布任务")
    public JsonResult<ClientPublishTaskVO> update(@PathVariable Long id, @RequestBody ClientPublishTaskDTO clientPublishTaskDTO) {
        return JsonResult.ok(clientPublishTaskService.update(id, clientPublishTaskDTO));
    }
}
