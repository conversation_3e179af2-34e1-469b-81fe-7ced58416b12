package com.genlight.epilcure.service.config.dao.repository;

import com.genlight.epilcure.service.config.dao.entity.Algorithm;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import java.util.Optional;

public interface AlgorithmRepository extends JpaRepository<Algorithm, Long>, JpaSpecificationExecutor<Algorithm> {
    Optional<Algorithm> findByCode(String code);
}
