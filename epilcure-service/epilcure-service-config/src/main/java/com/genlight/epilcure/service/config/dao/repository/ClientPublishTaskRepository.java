package com.genlight.epilcure.service.config.dao.repository;

import com.genlight.epilcure.api.config.enums.ClientStatus;
import com.genlight.epilcure.common.core.dao.enums.Status;
import com.genlight.epilcure.service.config.dao.entity.ClientPublishTask;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import java.util.Optional;

public interface ClientPublishTaskRepository extends JpaRepository<ClientPublishTask, Long>, JpaSpecificationExecutor<ClientPublishTask> {
    Optional<ClientPublishTask> findByProjectIdAndClientStatusAndStatus(Long projectId, ClientStatus clientStatus, Status status);
}
