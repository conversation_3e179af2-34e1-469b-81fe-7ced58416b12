package com.genlight.epilcure.service.config.dao.entity;

import jakarta.persistence.Cacheable;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

/**
 * <AUTHOR>
 * @Date 2022/6/8 23:13
 * @Version 1.0.0
 **/
@Getter
@Setter
@Entity
@Accessors(chain = true)
@SuperBuilder
@Cacheable
@NoArgsConstructor
@DynamicInsert
@DynamicUpdate
@Table(name = "t_drugs")
public class Drugs extends MedicalRecordBase {
}
