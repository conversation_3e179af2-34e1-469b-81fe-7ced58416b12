package com.genlight.epilcure.service.config.controller;

import com.genlight.epilcure.api.config.pojo.dto.ProjectDTO;
import com.genlight.epilcure.api.config.pojo.vo.ProjectVO;
import com.genlight.epilcure.common.core.mvc.response.JsonResult;
import com.genlight.epilcure.common.core.pojo.valid.Add;
import com.genlight.epilcure.common.core.pojo.valid.Update;
import com.genlight.epilcure.service.config.service.ProjectService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @Date 2022/6/26 20:45
 * @Version 1.0.0
 **/
@RestController
@RequestMapping("/api/config/projects")
@Tag(name = "ProjectController", description = "项目配置相关接口")
public class ProjectController {

    @Resource
    private ProjectService projectService;

    @GetMapping("/{id}")
    @Operation(summary = "根据ID查询动作")
    public JsonResult<ProjectVO> findProjectById(@PathVariable Long id) {
        return JsonResult.ok(projectService.findProjectById(id));
    }

    @GetMapping
    @Operation(summary = "根据动态条件查询动作")
    public JsonResult<Page<ProjectVO>> findProjectsByPageable(ProjectDTO projectDTO, Pageable pageable) {
        return JsonResult.ok(projectService.findProjectsByPageable(projectDTO, pageable));
    }

    @PostMapping
    @Operation(summary = "新增项目")
    public JsonResult<ProjectVO> addProject(@Validated(Add.class) @RequestBody ProjectDTO projectDTO) {
        return JsonResult.ok(projectService.addProject(projectDTO));
    }

    @PutMapping("/{id}")
    @Operation(summary = "修改项目")
    public JsonResult<ProjectVO> updateProject(@PathVariable Long id, @Validated(Update.class) @RequestBody ProjectDTO projectDTO) {
        return JsonResult.ok(projectService.updateProject(id, projectDTO));
    }
}
