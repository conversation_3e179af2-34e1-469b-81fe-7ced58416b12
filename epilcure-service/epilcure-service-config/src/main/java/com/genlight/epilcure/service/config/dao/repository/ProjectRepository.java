package com.genlight.epilcure.service.config.dao.repository;

import com.genlight.epilcure.service.config.dao.entity.Project;
import org.jetbrains.annotations.NotNull;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

public interface ProjectRepository extends JpaRepository<Project, Long>, JpaSpecificationExecutor<Project> {

    boolean existsById(@NotNull Long id);

    boolean existsByName(String name);

    boolean existsByCode(String code);
}
