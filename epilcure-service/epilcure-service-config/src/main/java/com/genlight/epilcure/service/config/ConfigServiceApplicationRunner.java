package com.genlight.epilcure.service.config;

import com.genlight.epilcure.service.config.service.ClientPublishTaskService;
import com.genlight.epilcure.service.config.service.ClientVersionService;
import com.genlight.epilcure.service.config.service.tasks.PublishTaskRunner;
import jakarta.annotation.Resource;
import org.redisson.api.RedissonClient;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

@Component
public class ConfigServiceApplicationRunner implements ApplicationRunner {
    @Resource
    private ClientVersionService clientVersionService;
    @Resource
    private ClientPublishTaskService clientPublishTaskService;

    @Resource
    private RedissonClient redissonClient;

    @Override
    public void run(ApplicationArguments args) {
        new Thread(new PublishTaskRunner(clientVersionService, clientPublishTaskService, redissonClient)).start();
    }
}
