package com.genlight.epilcure.service.config.feign;

import com.genlight.epilcure.api.config.feign.IAlgorithmParamController;
import com.genlight.epilcure.service.config.service.AlgorithmParamService;
import io.swagger.v3.oas.annotations.Hidden;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController("feignAlgorithmParamController")
@RequestMapping
@Hidden
public class AlgorithmParamController implements IAlgorithmParamController {
    @Resource
    private AlgorithmParamService algorithmParamService;

//    @Override
//    public AlgorithmParamVO find(Long id, Long algorithmId) {
//        return algorithmParamService.find(id);
//    }
}
