package com.genlight.epilcure.service.config.controller;

import com.genlight.epilcure.api.config.pojo.dto.DiseaseDTO;
import com.genlight.epilcure.api.config.pojo.vo.DiseaseVO;
import com.genlight.epilcure.common.core.mvc.response.JsonResult;
import com.genlight.epilcure.service.config.service.DiseaseService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/config/diseases")
@Tag(name = "DiseaseController", description = "疾病类型相关接口")
public class DiseaseController {
    @Autowired
    private DiseaseService diseaseService;

    @Operation(summary = "根据id获取疾病类型")
    @GetMapping("/{id}")
    public JsonResult<DiseaseVO> find(@PathVariable Long id) {
        return JsonResult.ok(diseaseService.findDiseaseById(id));
    }

    @Operation(summary = "添加疾病类型")
    @PostMapping
    public JsonResult<String> add(@RequestBody DiseaseDTO diseaseDTO) {
        diseaseService.addDisease(diseaseDTO);
        return JsonResult.ok("新增成功");
    }

    @Operation(summary = "修改疾病信息")
    @PutMapping("/{id}")
    public JsonResult<String> edit(@PathVariable Long id, @Valid @RequestBody DiseaseDTO diseaseDTO) {
        diseaseService.editDisease(id, diseaseDTO);
        return JsonResult.ok("修改成功");
    }

    @Operation(summary = "删除疾病信息")
    @DeleteMapping("/{id}")
    public JsonResult<String> delete(@PathVariable Long id) {
        diseaseService.deleteDisease(id);
        return JsonResult.ok("删除成功");
    }

    @GetMapping("/findAll")
    @Operation(summary = "获取所有疾病类型")
    public JsonResult<List<DiseaseVO>> findAll() {
        return JsonResult.ok(diseaseService.findAllDiseases());
    }
}
