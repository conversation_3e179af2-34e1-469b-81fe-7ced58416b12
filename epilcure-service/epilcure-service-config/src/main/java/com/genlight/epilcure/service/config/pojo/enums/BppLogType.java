package com.genlight.epilcure.service.config.pojo.enums;

public enum BppLogType {
    BPP_POWER_OFF,
    BPP_TIME_SET,
    BPP_EEG_REC,
    BPP_SCALP_THICK,
    BPP_UPGRADE_VER,
    BPP_STORE_COVER,
    BPP_USB_OPT,
    BPP_TREAT_OPT,
    BPP_HARD_FAULT,
    None1,
    BCM_COMM_TIME,
    BCM_EEG_REC,
    BCM_ON_OFF_STAT,
    BCM_RESP_TREAT,
    BCM_FORCE_OFF,
    BCM_ABNORMAL_RST,
    BCM_CHARGER,

    BCM_RECORD,

    ACTION_RUN_TIME,

    SYS_START_TIME;

    public static BppLogType valueof(byte val) {
        if (val == 0) {
            return BPP_POWER_OFF;
        } else if (val == 1) {
            return BPP_TIME_SET;
        } else if (val == 2) {
            return BPP_EEG_REC;
        } else if (val == 3) {
            return BPP_SCALP_THICK;
        } else if (val == 4) {
            return BPP_UPGRADE_VER;
        } else if (val == 5) {
            return BPP_STORE_COVER;
        } else if (val == 6) {
            return BPP_USB_OPT;
        } else if (val == 7) {
            return BPP_TREAT_OPT;
        } else if (val == 8) {
            return BPP_HARD_FAULT;
        } else if (val == 10) {
            return BCM_COMM_TIME;
        } else if (val == 11) {
            return BCM_EEG_REC;
        } else if (val == 12) {
            return BCM_ON_OFF_STAT;
        } else if (val == 13) {
            return BCM_RESP_TREAT;
        } else if (val == 14) {
            return BCM_FORCE_OFF;
        } else if (val == 15) {
            return BCM_ABNORMAL_RST;
        } else if (val == 16) {
            return BCM_CHARGER;
        } else if (val == 17) {
            return BCM_RECORD;
        } else if (val == 18) {
            return ACTION_RUN_TIME;
        } else if (val == 19) {
            return SYS_START_TIME;
        } else {
            return None1;
        }
    }
}
