package com.genlight.epilcure.service.config.dao.repository;

import com.genlight.epilcure.api.config.enums.ClientStatus;
import com.genlight.epilcure.service.config.dao.entity.ClientVersion;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import java.util.Optional;

public interface ClientVersionRepository extends JpaRepository<ClientVersion, Long>, JpaSpecificationExecutor<ClientVersion> {

    boolean existsByVersionCodeAndPackageName(String versionCode, String packageName);

    Optional<ClientVersion> findFirstByProjectIdAndVersionOrderBySerialNumberDesc(Long projectId, String version);

    Optional<ClientVersion> findFirstByProjectIdAndAndClientStatus(Long projectId, ClientStatus clientStatus);

    Optional<ClientVersion> findFirstByIsLowAndProjectId(<PERSON><PERSON><PERSON> isLow, Long projectId);
}
