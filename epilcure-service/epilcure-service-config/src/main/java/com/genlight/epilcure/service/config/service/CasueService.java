package com.genlight.epilcure.service.config.service;

import com.genlight.epilcure.api.config.pojo.dto.CasueDTO;
import com.genlight.epilcure.api.config.pojo.vo.CasueVO;
import com.genlight.epilcure.service.config.constants.CacheConstants;
import com.genlight.epilcure.service.config.dao.entity.Casue;
import com.genlight.epilcure.service.config.dao.repository.CasueRepository;
import com.genlight.epilcure.service.config.pojo.convert.CasueConvert;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/6/11 15:46
 * @Version 1.0.0
 **/
@Service
@CacheConfig(cacheNames = CacheConstants.CACHE_NAMES_CASUE)
public class CasueService extends MedicalRecordBaseService<Casue, CasueVO, CasueDTO, CasueRepository, CasueConvert> {
    @Cacheable(CacheConstants.CACHE_NAMES_CASUE_FINDS)
    @Override
    public List<CasueVO> finds(CasueDTO dto) {
        return super.finds(dto);
    }

    @CacheEvict(cacheNames = CacheConstants.CACHE_NAMES_CASUE_FINDS, allEntries = true)
    @Override
    public CasueVO update(Long aLong, CasueDTO dto) {
        return super.update(aLong, dto);
    }

    @CacheEvict(cacheNames = CacheConstants.CACHE_NAMES_CASUE_FINDS, allEntries = true)
    @Override
    public CasueVO add(CasueDTO dto) {
        return super.add(dto);
    }
}
