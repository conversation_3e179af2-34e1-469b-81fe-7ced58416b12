package com.genlight.epilcure.service.config.pojo.vo;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;

import java.util.Date;

@Data
@SuperBuilder
@NoArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@Schema(description = "充电")
public class ICPRecharge extends LogBaseVO {
    private Date startDate;

    private Date endDate;

    private Integer startVoltage;

    private Integer endVoltage;

    private Byte startStatus;
}
