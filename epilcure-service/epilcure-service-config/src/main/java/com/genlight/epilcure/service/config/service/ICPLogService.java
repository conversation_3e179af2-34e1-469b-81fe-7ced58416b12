package com.genlight.epilcure.service.config.service;

import com.genlight.epilcure.api.config.pojo.vo.ICPLogFileVO;
import com.genlight.epilcure.common.core.dao.enums.Status;
import com.genlight.epilcure.common.core.service.BaseService;
import com.genlight.epilcure.common.core.util.MinioUtils;
import com.genlight.epilcure.service.config.constants.FileConstants;
import com.genlight.epilcure.service.config.dao.entity.ICPLogFile;
import com.genlight.epilcure.service.config.dao.repository.ICPLogRepository;
import com.genlight.epilcure.service.config.pojo.convert.ICPLogFileConvert;
import jakarta.annotation.Nullable;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.util.Optional;

@Service
public class ICPLogService extends BaseService<ICPLogFile, Long, ICPLogRepository> {
    @Resource
    private MinioUtils minioUtils;

    @Resource
    private ICPLogFileConvert icpLogFileConvert;

    @Resource
    private ICPLogDetailService icpLogDetailService;

    public ICPLogFileVO add(MultipartFile multipartFile) {
        ICPLogFileVO savedFile = saveFile(multipartFile);
        return savedFile;
    }

    @Transactional
    private ICPLogFileVO saveFile(MultipartFile multipartFile) {
        String path = minioUtils.upload(FileConstants.BPP_Log_DIR, multipartFile);
        ICPLogFile bppLogFile = ICPLogFile.builder()
                .path(path)
                .status(Status.ADD).build();
        return icpLogFileConvert.po2vo(repository.save(bppLogFile));
    }

    @Transactional
    public void updateFile(Long id, Status status, @Nullable String remark) {
        Optional<ICPLogFile> optional = repository.findById(id);
        if (optional.isEmpty()) {
            throw new RuntimeException("日子文件不存在");
        }
        ICPLogFile bppLogFile = optional.get();
        bppLogFile.setStatus(status);
        bppLogFile.setRemark(remark);
        repository.save(bppLogFile);
    }
}
