package com.genlight.epilcure.service.config.pojo.enums;

public enum BcmRecordType {
    None,
    Runtime,
    BleDisconnect,
    BleConnected,
    Reset,
    ResponseClose;

    public static BcmRecordType valueOf(byte val) {
        return switch (val) {
            case 1 -> Runtime;
            case 2 -> BleDisconnect;
            case 3 -> BleConnected;
            case 4 -> Reset;
            case 5 -> ResponseClose;
            default -> None;
        };
    }
}
