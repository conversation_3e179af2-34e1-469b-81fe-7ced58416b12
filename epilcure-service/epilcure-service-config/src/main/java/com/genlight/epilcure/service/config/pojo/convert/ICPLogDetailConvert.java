package com.genlight.epilcure.service.config.pojo.convert;

import com.genlight.epilcure.api.config.pojo.dto.ICPLogDetailDTO;
import com.genlight.epilcure.api.config.pojo.vo.ICPLogDetailVO;
import com.genlight.epilcure.common.core.pojo.convert.BaseConvert;
import com.genlight.epilcure.service.config.dao.entity.ICPLogDetails;
import org.mapstruct.Mapper;

@Mapper(config = BaseConvert.class)
public interface ICPLogDetailConvert extends BaseConvert<ICPLogDetails, ICPLogDetailVO, ICPLogDetailDTO> {
}
