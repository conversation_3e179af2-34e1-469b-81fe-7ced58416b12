package com.genlight.epilcure.service.config.pojo.convert;

import com.genlight.epilcure.api.config.pojo.dto.BppLogDetailDTO;
import com.genlight.epilcure.api.config.pojo.vo.BppLogDetailVO;
import com.genlight.epilcure.common.core.pojo.convert.BaseConvert;
import com.genlight.epilcure.service.config.dao.entity.BppLogDetails;
import org.mapstruct.Mapper;

@Mapper(config = BaseConvert.class)
public interface BppLogDetailConvert extends BaseConvert<BppLogDetails, BppLogDetailVO, BppLogDetailDTO> {
}
