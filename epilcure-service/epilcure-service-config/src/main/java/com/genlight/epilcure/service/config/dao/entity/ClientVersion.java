package com.genlight.epilcure.service.config.dao.entity;

import com.genlight.epilcure.api.config.enums.ClientPublishType;
import com.genlight.epilcure.api.config.enums.ClientStatus;
import com.genlight.epilcure.common.core.dao.entity.TEntity;
import com.genlight.epilcure.common.core.dao.enums.Status;
import com.genlight.epilcure.common.core.dao.generator.annotation.SignOrder;
import jakarta.persistence.*;
import lombok.*;
import lombok.experimental.SuperBuilder;
import org.hibernate.annotations.Comment;

import java.util.Date;
import java.util.HashSet;
import java.util.Set;

@Getter
@Setter
@Entity
@Cacheable
@SuperBuilder
@NoArgsConstructor
@SignOrder(0)
@Table(name = "t_client_version", indexes = {
        @Index(name = "packageName_versionCode_u_idx", columnList = "versionCode,packageName", unique = true),
        @Index(name = "projectId_version_idx", columnList = "project_id,version"),
        @Index(name = "clientPublishType_idx", columnList = "clientPublishType")
})
public class ClientVersion extends TEntity {


    @Comment("包名")
    @Column(nullable = false)
    @SignOrder(1)
    private String packageName;

    @Comment("版本名")
    @Column(nullable = false)
    @SignOrder(2)
    private String version;

    @Comment("版本号")
    @Column(nullable = false)
    @SignOrder(3)
    private String versionCode;

    @Comment("流水号")
    @Column(nullable = false)
    @SignOrder(4)
    private Integer serialNumber;

    @Comment("hash")
    @Column
    @SignOrder(5)
    private String hash;

    @Comment("编译时间")
    @Column
    @SignOrder(6)
    private String compilerTime;

    @Comment("硬件版本")
    @Column
    @SignOrder(7)
    private String hardwareVersion;

    @Comment("路径")
    @Column(nullable = false, length = 2048)
    @SignOrder(8)
    private String url;

    @Comment("包大小")
    @Column(nullable = false)
    @SignOrder(9)
    private Integer size;

    @Comment("版本状态")
    @Enumerated(EnumType.ORDINAL)
    @Column
    @SignOrder(10)
    private ClientStatus clientStatus;

    @Comment("是否可修改")
    @Column(nullable = false)
    @SignOrder(11)
    private Status editStatus;

    @Comment("版本更新类型")
    @Column
    @SignOrder(12)
    private ClientPublishType clientPublishType;

    @Comment("发布日期")
    @Temporal(TemporalType.TIMESTAMP)
    @Column
    @SignOrder(13)
    private Date publishDate;

    @Comment("是否是最低版本")
    @Column(nullable = false, columnDefinition = "bit default 0")
    @SignOrder(14)
    private Boolean isLow;

    @Comment("上传用户Id")
    @SignOrder(15)
    private Long userId;

    @Comment("上传用户名")
    @SignOrder(16)
    private String userName;

    @Comment("对应工程")
    @ToString.Exclude
    @ManyToOne(fetch = FetchType.LAZY)
    private Project project;

    @Comment("描述信息")
    @Column(columnDefinition = "text")
    @SignOrder(15)
    private String note;

    @Comment("发布任务")
    @ToString.Exclude
    @Builder.Default
    @OneToMany(mappedBy = "clientVersion")
    private Set<ClientPublishTask> clientPublishTasks = new HashSet<>();
}
