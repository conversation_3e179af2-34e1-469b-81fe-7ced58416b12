package com.genlight.epilcure.service.config.feign;

import com.genlight.epilcure.api.config.feign.IAlgorithmController;
import com.genlight.epilcure.api.config.pojo.vo.AlgorithmVO;
import com.genlight.epilcure.service.config.service.AlgorithmService;
import io.swagger.v3.oas.annotations.Hidden;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController("feignAlgorithmController")
@RequestMapping
@Hidden
public class AlgorithmController implements IAlgorithmController {
    @Resource
    private AlgorithmService algorithmService;

    @Override
    public boolean existsById(Long id) {

        return algorithmService.existsById(id);
    }

    @Override
    public AlgorithmVO find(Long id) {
        return algorithmService.find(id);
    }

    @Override
    public AlgorithmVO findByCode(String code) {
        return algorithmService.findByCode(code);
    }
}
