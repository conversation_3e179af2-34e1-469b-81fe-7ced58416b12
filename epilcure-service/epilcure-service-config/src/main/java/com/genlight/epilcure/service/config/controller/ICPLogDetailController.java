package com.genlight.epilcure.service.config.controller;

import com.genlight.epilcure.api.config.pojo.dto.ICPLogDetailDTO;
import com.genlight.epilcure.api.config.pojo.vo.ICPLogDetailVO;
import com.genlight.epilcure.common.core.mvc.response.JsonResult;
import com.genlight.epilcure.service.config.service.ICPLogDetailService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/config/icpLogDetails")
@Tag(name = "ICPLogDetailController", description = "嵌软详细日志相关接口")
public class ICPLogDetailController {
    @Resource
    private ICPLogDetailService icpLogDetailService;

    @GetMapping("/finds")
    @Operation(summary = "获取日志列表")
    public JsonResult<Page<ICPLogDetailVO>> findsBppLog(ICPLogDetailDTO icpLogDetailDTO, Pageable pageable) {
        return JsonResult.ok(icpLogDetailService.findBppLogs(icpLogDetailDTO, pageable));
    }
}
