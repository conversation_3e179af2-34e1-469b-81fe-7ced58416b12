package com.genlight.epilcure.service.config.pojo.convert;

import com.genlight.epilcure.api.config.pojo.dto.DiseaseDTO;
import com.genlight.epilcure.api.config.pojo.vo.DiseaseVO;
import com.genlight.epilcure.common.core.pojo.convert.BaseConvert;
import com.genlight.epilcure.common.core.pojo.convert.qualified.Basic;
import com.genlight.epilcure.service.config.dao.entity.Disease;
import org.mapstruct.*;

import java.util.List;

@Named("DiseaseConvert")
@Mapper(config = BaseConvert.class, uses = {DrugsConvert.class, SymptomConvert.class})
public interface DiseaseConvert extends BaseConvert<Disease, DiseaseVO, DiseaseDTO> {
    @Basic
    @Mapping(target = "drugs", ignore = true)
    @Mapping(target = "symptoms", ignore = true)
    @BeanMapping(nullValueCheckStrategy = NullValueCheckStrategy.ON_IMPLICIT_CONVERSION)
    @Override
    DiseaseVO po2vo(Disease disease);

    @IterableMapping(qualifiedBy = Basic.class)
    @BeanMapping(nullValueCheckStrategy = NullValueCheckStrategy.ON_IMPLICIT_CONVERSION)
    @Override
    List<DiseaseVO> po2vo(List<Disease> depts);
}
