package com.genlight.epilcure.service.config.pojo.convert;

import com.genlight.epilcure.api.config.pojo.dto.BppLogFileDTO;
import com.genlight.epilcure.api.config.pojo.vo.BppLogFileVO;
import com.genlight.epilcure.common.core.pojo.convert.BaseConvert;
import com.genlight.epilcure.service.config.dao.entity.BppLogFile;
import org.mapstruct.Mapper;

@Mapper(config = BaseConvert.class)
public interface BppLogFileConvert extends BaseConvert<BppLogFile, BppLogFileVO, BppLogFileDTO> {
}
