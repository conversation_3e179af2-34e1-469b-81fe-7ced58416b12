package com.genlight.epilcure.service.config.dao.entity;

import com.genlight.epilcure.common.core.dao.entity.TEntity;
import jakarta.persistence.*;
import lombok.*;
import lombok.experimental.SuperBuilder;
import org.hibernate.annotations.Comment;

import java.util.HashSet;
import java.util.Set;

/**
 * <AUTHOR>
 * @Date 2022/6/26 12:26
 * @Version 1.0.0
 **/
@Getter
@Setter
@Entity
@Cacheable
@SuperBuilder
@NoArgsConstructor
@Table(name = "t_algorithm")
public class Algorithm extends TEntity {

    @Comment("算法名称")
    @Column(nullable = false)
    private String name;

    @Comment("疾病Id")
    @ManyToOne
    private Disease disease;

    @Comment("icon路径")
    private String icon;

    @Comment("算法编码")
    @Column
    private String code;

    @Comment("算法支持的版本编号")
    @Column
    private Integer version;

    @Builder.Default
    @ToString.Exclude
    @ManyToMany(cascade = CascadeType.ALL)
    @Comment("参数集合")
    private Set<AlgorithmParam> algorithmParams = new HashSet<>();
}
