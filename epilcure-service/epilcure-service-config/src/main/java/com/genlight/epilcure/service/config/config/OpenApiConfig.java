package com.genlight.epilcure.service.config.config;

import io.swagger.v3.oas.models.Components;
import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.media.ArraySchema;
import io.swagger.v3.oas.models.media.IntegerSchema;
import io.swagger.v3.oas.models.media.Schema;
import io.swagger.v3.oas.models.security.SecurityRequirement;
import io.swagger.v3.oas.models.security.SecurityScheme;
import io.swagger.v3.oas.models.servers.Server;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.domain.Pageable;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0.0
 */
@Configuration(proxyBeanMethods = false)
public class OpenApiConfig {

    @Bean
    public OpenAPI openAPI() {
        return new OpenAPI()
                .info(new Info()
                        .version("v1.0.0")
                        .contact(new Contact().name("唐臻").email("<EMAIL>"))
                        .title("Epilcure-Patient-Service - API Interface Documentation"))
                .addServersItem(new Server().url("http://localhost:1010").description("DEV Server"))
                .addServersItem(new Server().url("http://epilcure.cloud.com/").description("TEST Server"))
                .addServersItem(new Server().url("http://localhost:8002").description("Gateway Server"))
                .addSecurityItem(new SecurityRequirement().addList("apiKey"))
                .components(new Components()
                        .addSecuritySchemes("apiKey", new SecurityScheme()
                                .name("token")
                                .in(SecurityScheme.In.HEADER)
                                .type(SecurityScheme.Type.APIKEY))
                        .addSchemas("Pageable", new Schema<Pageable>()
                                .type("object")
                                .addProperty("page", new IntegerSchema().minimum(BigDecimal.valueOf(0)).example("0").description("page number, default is 0. example: page=0"))
                                .addProperty("size", new IntegerSchema().minimum(BigDecimal.valueOf(1)).example("20").description("page size, default is 20. example: size=20"))
                                .addProperty("sort", new ArraySchema().type("string").example("").description("property or property,asc multiple allowed, default is null. example: sort=time,asc&sort=id,desc"))));
    }
}
