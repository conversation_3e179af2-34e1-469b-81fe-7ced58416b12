package com.genlight.epilcure.service.config.controller;

import com.genlight.epilcure.api.config.pojo.dto.DrugsDTO;
import com.genlight.epilcure.api.config.pojo.vo.DrugsVO;
import com.genlight.epilcure.common.core.mvc.response.JsonResult;
import com.genlight.epilcure.common.core.pojo.valid.Add;
import com.genlight.epilcure.common.core.pojo.valid.Update;
import com.genlight.epilcure.service.config.service.DrugsService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/6/8 23:47
 * @Version 1.0.0
 **/
@RestController
@RequestMapping("/api/config/drugs")
@Tag(name = "DrugsController", description = "药品相关接口")
public class DrugsController {
    @Resource
    private DrugsService drugsService;

    @Operation(summary = "根据Id获取药品信息")
    @GetMapping("/{id}")
    public JsonResult<DrugsVO> find(@PathVariable Long id) {
        return JsonResult.ok(drugsService.findById(id));
    }

    @Operation(summary = "根据条件查询药品列表")
    @GetMapping("/finds")
    public JsonResult<List<DrugsVO>> finds(DrugsDTO drugsDTO) {
        return JsonResult.ok(drugsService.finds(drugsDTO));
    }

    @Operation(summary = "新增药品")
    @PostMapping
    public JsonResult<DrugsVO> add(@Validated(Add.class) @RequestBody DrugsDTO drugsDTO) {
        return JsonResult.ok(drugsService.add(drugsDTO));
    }

    @Operation(summary = "修改药品信息")
    @PutMapping("/{id}")
    public JsonResult<DrugsVO> update(@PathVariable Long id, @Validated(Update.class) @RequestBody DrugsDTO drugsDTO) {
        return JsonResult.ok(drugsService.update(id, drugsDTO));
    }

    @Operation(summary = "根据Id删除药品")
    @DeleteMapping("/{id}")
    public JsonResult<String> delete(@PathVariable Long id) {
        drugsService.delete(id);
        return JsonResult.ok("删除成功");
    }
}
