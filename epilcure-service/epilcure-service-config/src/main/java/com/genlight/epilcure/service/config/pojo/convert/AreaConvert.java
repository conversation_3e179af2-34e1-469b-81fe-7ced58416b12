package com.genlight.epilcure.service.config.pojo.convert;

import com.genlight.epilcure.api.config.pojo.dto.AreaDTO;
import com.genlight.epilcure.api.config.pojo.vo.AreaVO;
import com.genlight.epilcure.common.core.pojo.convert.BaseConvert;
import com.genlight.epilcure.service.config.dao.entity.Area;
import org.mapstruct.Mapper;

@Mapper(config = BaseConvert.class)
public interface AreaConvert extends BaseConvert<Area, AreaVO, AreaDTO> {
}
