package com.genlight.epilcure.service.config.pojo.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;

@Data
@SuperBuilder
@NoArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class EegRecVO extends LogBaseVO {
    private Integer eegDataNum;
    private Integer stimulationNum;
    private Integer eegDataNum0;
    private Integer stimulationNum0;
    private byte transFlag;
}
