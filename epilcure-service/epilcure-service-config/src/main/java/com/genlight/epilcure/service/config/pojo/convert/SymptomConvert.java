package com.genlight.epilcure.service.config.pojo.convert;

import com.genlight.epilcure.api.config.pojo.dto.SymptomDTO;
import com.genlight.epilcure.api.config.pojo.vo.SymptomVO;
import com.genlight.epilcure.common.core.pojo.convert.qualified.All;
import com.genlight.epilcure.common.core.pojo.convert.qualified.Basic;
import com.genlight.epilcure.service.config.dao.entity.Symptom;
import org.mapstruct.*;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/6/11 12:10
 * @Version 1.0.0
 **/
@Named("SymptomConvert")
@Mapper(config = MedicalRecordBaseConvert.class)
public interface SymptomConvert extends MedicalRecordBaseConvert<Symptom, SymptomVO, SymptomDTO> {
    @Basic
    @Mapping(target = "diseases", ignore = true)
    @BeanMapping(nullValueCheckStrategy = NullValueCheckStrategy.ON_IMPLICIT_CONVERSION)
    @Override
    SymptomVO po2vo(Symptom po);

    @IterableMapping(qualifiedBy = Basic.class)
    List<SymptomVO> po2vo(List<Symptom> pos);

    @All
    @Mapping(target = "diseases", qualifiedByName = "DiseaseConvert", qualifiedBy = Basic.class)
    @BeanMapping(nullValueCheckStrategy = NullValueCheckStrategy.ON_IMPLICIT_CONVERSION)
    SymptomVO po2voBySummay(Symptom po);
}
