package com.genlight.epilcure.service.config.pojo.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;

@Data
@SuperBuilder
@NoArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class BcmAbnorRst extends LogBaseVO {
    private byte wdgFlag;
    private byte softwareFlag;
    private byte lowvoltaFlag;
    private byte lowpowFlag;
    private byte iwdgFlag;
    private byte rstpinFlag;
    private byte optFlag;
    private byte firewallFlag;
}
