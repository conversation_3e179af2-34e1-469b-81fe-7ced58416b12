package com.genlight.epilcure.service.config;

import net.bytebuddy.agent.ByteBuddyAgent;
import org.aspectj.weaver.loadtime.Agent;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.instrument.InstrumentationSavingAgent;

import java.lang.instrument.Instrumentation;

@EnableDiscoveryClient
@SpringBootApplication(scanBasePackages = "com.genlight.epilcure")
public class ConfigServiceApplication {

    public static void main(String[] args) {
        Instrumentation instrumentation = ByteBuddyAgent.install();
        Agent.agentmain("", instrumentation);
        InstrumentationSavingAgent.agentmain("", instrumentation);
        SpringApplication.run(ConfigServiceApplication.class, args);
    }

}
