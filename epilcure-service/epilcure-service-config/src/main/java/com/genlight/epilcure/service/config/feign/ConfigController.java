package com.genlight.epilcure.service.config.feign;

import com.genlight.epilcure.api.config.feign.IConfigController;
import com.genlight.epilcure.api.config.pojo.vo.ConfigsVO;
import com.genlight.epilcure.service.config.service.ConfigsService;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Optional;

@RestController
@RequestMapping
public class ConfigController implements IConfigController {
    @Resource
    private ConfigsService configsService;

    @Override
    public ConfigsVO findByKey(@PathVariable String key) {
        return configsService.findByKey(key);
    }

    @Override
    public Optional<ConfigsVO> findByKeyNearVersion(String key, String version) {
        return configsService.findByNearsVersion(key, version);
    }
}
