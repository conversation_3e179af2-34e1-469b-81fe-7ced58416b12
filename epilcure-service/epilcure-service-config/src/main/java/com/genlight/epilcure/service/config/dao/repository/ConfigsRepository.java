package com.genlight.epilcure.service.config.dao.repository;

import com.genlight.epilcure.service.config.dao.entity.Configs;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import java.util.Optional;

public interface ConfigsRepository extends JpaRepository<Configs, Long>, JpaSpecificationExecutor<Configs> {
    Boolean existsByKey(String key);

    Optional<Configs> findByKey(String key);

    Optional<Configs> findFirstByKeyAndVersionLessThanEqualOrderByVersionDesc(String key, String version);
}
