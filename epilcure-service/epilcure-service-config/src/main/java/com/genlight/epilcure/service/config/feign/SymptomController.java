package com.genlight.epilcure.service.config.feign;

import com.genlight.epilcure.api.config.feign.ISymptomController;
import com.genlight.epilcure.api.config.pojo.vo.SymptomVO;
import com.genlight.epilcure.service.config.service.SymptomService;
import io.swagger.v3.oas.annotations.Hidden;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/6/12 15:24
 * @Version 1.0.0
 **/
@RestController("feignSymptomController")
@RequestMapping
@Hidden
public class SymptomController implements ISymptomController {
    @Resource
    private SymptomService symptomService;

    @Override
    public Boolean existsById(Long id) {
        return symptomService.existsById(id);
    }

    @Override
    public Boolean existsByIds(List<Long> ids) {
        return symptomService.existsByIds(ids);
    }

    @Override
    public SymptomVO findById(Long id) {
        return symptomService.findById(id);
    }
}
