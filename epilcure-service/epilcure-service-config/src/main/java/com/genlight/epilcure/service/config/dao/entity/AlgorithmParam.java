package com.genlight.epilcure.service.config.dao.entity;

import com.genlight.epilcure.common.core.dao.entity.TEntity;
import jakarta.persistence.*;
import lombok.*;
import lombok.experimental.SuperBuilder;
import org.hibernate.annotations.Comment;

import java.util.HashSet;
import java.util.Set;

/**
 * <AUTHOR>
 * @Date 2022/6/26 12:34
 * @Version 1.0.0
 **/
@Getter
@Setter
@Entity
@Cacheable
@SuperBuilder
@NoArgsConstructor
@ToString(callSuper = true)
@Table(name = "t_algorithm_param")
public class AlgorithmParam extends TEntity {

    @Comment("参数名")
    @Column(nullable = false)
    private String name;

    @Comment("参数单位")
    @Column(nullable = false)
    private String unit;

    @Comment("最小值")
    @Column(nullable = false)
    private Double min;

    @Comment("最大值")
    @Column(nullable = false)
    private Double max;

    @Comment("参数选项")
    @Column(nullable = false)
    private String data;

    @Comment("参数编码")
    @Column(nullable = false)
    private String code;

    @Comment("排除参数")
    @Column
    private String excludeData;

    @ToString.Exclude
    @Builder.Default
    @ManyToMany(mappedBy = "algorithmParams")
    private Set<Algorithm> algorithms = new HashSet<>();
}
