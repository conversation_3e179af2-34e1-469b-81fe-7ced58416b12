package com.genlight.epilcure.service.config.service;

import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.type.TypeFactory;
import com.genlight.epilcure.api.config.enums.ClientStatus;
import com.genlight.epilcure.api.config.pojo.dto.ClientVersionDTO;
import com.genlight.epilcure.api.config.pojo.vo.ClientPublishTaskVO;
import com.genlight.epilcure.api.config.pojo.vo.ClientVersionVO;
import com.genlight.epilcure.api.patient.feign.IPatientTestController;
import com.genlight.epilcure.api.user.feign.ITestController;
import com.genlight.epilcure.common.core.dao.enums.Status;
import com.genlight.epilcure.common.core.service.BaseService;
import com.genlight.epilcure.common.core.service.exception.ArgsException;
import com.genlight.epilcure.common.core.service.exception.ServiceException;
import com.genlight.epilcure.common.core.util.FunctionUtils;
import com.genlight.epilcure.common.core.util.MinioUtils;
import com.genlight.epilcure.service.config.constants.CacheConstants;
import com.genlight.epilcure.service.config.dao.entity.ClientPublishTask;
import com.genlight.epilcure.service.config.dao.entity.ClientVersion;
import com.genlight.epilcure.service.config.dao.entity.ClientVersion_;
import com.genlight.epilcure.service.config.dao.entity.Project_;
import com.genlight.epilcure.service.config.dao.repository.ClientVersionRepository;
import com.genlight.epilcure.service.config.pojo.convert.ClientVersionConvert;
import jakarta.annotation.Nullable;
import jakarta.annotation.Resource;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.Predicate;
import net.dongliu.apk.parser.ApkFile;
import net.dongliu.apk.parser.bean.ApkMeta;
import org.apache.commons.io.FileUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.annotation.Caching;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

@Service
@CacheConfig(cacheNames = CacheConstants.CACHE_NAMES_CLIENT_VERSION)
public class ClientVersionService extends BaseService<ClientVersion, Long, ClientVersionRepository> {

    @Resource
    private MinioUtils minioUtils;

    @Autowired
    protected ObjectMapper objectMapper;

    @Resource
    private RedissonClient redissonClient;

    @Resource
    private ProjectService projectService;

    @Resource
    private ITestController iTestController;

    @Resource
    private IPatientTestController iPatientTestController;

    @Resource
    private ClientVersionConvert clientVersionConvert;

    @Resource
    private ClientPublishTaskService clientPublishTaskService;

    @Transactional
    @Caching(evict = {@CacheEvict(cacheNames = CacheConstants.CACHE_NAMES_CLIENT_VERSION_FINDS, allEntries = true)})
    public ClientVersionVO add(ClientVersionDTO clientVersionDTO, MultipartFile apk) {

        if (!projectService.existsProjectById(clientVersionDTO.getProjectId())) {
            throw new ArgsException("工程Id[{0}]不存在");
        }

        String filename = Objects.requireNonNull(apk.getOriginalFilename()).toLowerCase();
        if (!(filename.endsWith(".apk") || filename.endsWith(".bin"))) {
            throw new ArgsException("文件名后缀必须是apk或bin");
        }

        ClientVersion clientVersion = clientVersionConvert.dto2po(clientVersionDTO);
        clientVersion.setProject(projectService.getProjectById(clientVersionDTO.getProjectId()));
        if (!apk.getOriginalFilename().startsWith(clientVersion.getProject().getPackageName())) {
            throw new ArgsException("文件名前缀必须以[" + clientVersion.getProject().getPackageName() + "]开头");
        }

        boolean isApk = filename.endsWith(".apk");
        if (isApk) {
            String[] names = apk.getOriginalFilename().replace(clientVersion.getProject().getPackageName(), "").replace(".apk", "").split("_");
            if (names.length != 4) {
                // com.glightmed.epilcure.patient.bcp_202302031757_1.0.0_release.apk
                throw new ArgsException("文件名规则：前缀_编译时间_version_buildType.apk");
            }
            if (!names[2].equals(clientVersionDTO.getVersion())) {
                throw new ArgsException("文件名上的软件版本号和version不一致");
            }
            clientVersion.setCompilerTime(names[1]);
        } else {
            String[] names = apk.getOriginalFilename().replace(clientVersion.getProject().getPackageName(), "").replace(".bin", "").split("_");
            if (names.length != 10) {
                // com.neuhill.isbs_ipg_a80fbd52_202301311741_hw_0_1_sw_1_0_0.bin
                // com.neuhill.isbs_ipg_[哈希]_[编译时间]_[硬件版本标识]_[硬件版本号]_[软件版本标识]_[软件版本号].bin
                throw new ArgsException("文件名规则：前缀_哈希值_编译时间_硬件版本_软件版本.bin");
            }
            if (!(names[7] + "." + names[8] + "." + names[9]).equals(clientVersionDTO.getVersion())) {
                throw new ArgsException("文件名上的软件版本号和version不一致");
            }
            clientVersion.setHash(names[1]);
            clientVersion.setCompilerTime(names[2]);
            clientVersion.setHardwareVersion(names[4] + "." + names[5]);
        }

        RLock lock = redissonClient.getLock("ClientSerialNumber");
        try {
            if (lock.tryLock(1, 1, TimeUnit.MINUTES)) {
                clientVersion.setSize((int) apk.getSize());
                clientVersion.setUserId(getUserId());
                clientVersion.setUserName(getNikeName());
                if (isApk) {
                    getApkMeta(apk, clientVersion);
                } else {
                    String versionCode = clientVersion.getCompilerTime();
                    String packageName = apk.getOriginalFilename();
                    clientVersion.setVersionCode(versionCode);
                    clientVersion.setPackageName(packageName);
                    if (repository.existsByVersionCodeAndPackageName(versionCode, packageName)) {
                        throw new ServiceException("包名版本已存在");
                    }
                }
                String installDIR = "/install/";
                String filePath = minioUtils.upload(installDIR, apk);
                clientVersion.setUrl(filePath);
                Optional<ClientVersion> optional = repository.findFirstByProjectIdAndVersionOrderBySerialNumberDesc(clientVersionDTO.getProjectId(), clientVersionDTO.getVersion());
                if (optional.isEmpty()) {
                    clientVersion.setSerialNumber(1);
                } else {
                    clientVersion.setSerialNumber(optional.get().getSerialNumber() + 1);
                }
                return clientVersionConvert.po2vo(repository.saveAndFlush(clientVersion));
            } else {
                throw new RuntimeException("服务忙,请稍后再试");
            }
        } catch (Exception ex) {
            throw new RuntimeException(ex);
        } finally {
            this.unLock(lock);
        }
    }

    private void getApkMeta(MultipartFile apk, ClientVersion clientVersion) {
        try {
            String[] split = Objects.requireNonNull(apk.getOriginalFilename()).split("\\.");
            File file = File.createTempFile(split[0], split[1]);
            FileUtils.copyInputStreamToFile(apk.getInputStream(), file);
            try (ApkFile apkFile = new ApkFile(file)) {
                ApkMeta apkMeta = apkFile.getApkMeta();
                if (repository.existsByVersionCodeAndPackageName(apkMeta.getVersionCode().toString(), apk.getOriginalFilename())) {
                    throw new ServiceException("Apk版本重复");
                }
                clientVersion.setVersionCode(apkMeta.getVersionCode().toString());
                clientVersion.setPackageName(apk.getOriginalFilename());
            }
            file.deleteOnExit();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    @Transactional(readOnly = true)
    @Cacheable(cacheNames = CacheConstants.CACHE_NAMES_CLIENT_VERSION_FINDS)
    public Page<ClientVersionVO> finds(ClientVersionDTO clientVersionDTO, Pageable pageable) {
        Specification<ClientVersion> specification = (root, query, criteriaBuilder) -> {
            Predicate predicate = criteriaBuilder.conjunction();
            if (Objects.nonNull(clientVersionDTO.getProjectId())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get(ClientVersion_.PROJECT).get(Project_.ID), clientVersionDTO.getProjectId()));
            }
            if (Objects.nonNull(clientVersionDTO.getStartDate())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.greaterThan(root.get(ClientVersion_.CREATE_TIME), clientVersionDTO.getStartDate()));
            }
            if (Objects.nonNull(clientVersionDTO.getEndDate())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.lessThan(root.get(ClientVersion_.CREATE_TIME), clientVersionDTO.getEndDate()));
            }
            if (Objects.nonNull((clientVersionDTO.getClientStatus()))) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get(ClientVersion_.CLIENT_STATUS), clientVersionDTO.getClientStatus()));
            }
            if (Objects.nonNull(clientVersionDTO.getClientStatusList())) {
                CriteriaBuilder.In<ClientStatus> in = criteriaBuilder.in(root.get(ClientVersion_.CLIENT_STATUS));
                clientVersionDTO.getClientStatusList().forEach(in::value);
                predicate = criteriaBuilder.and(predicate, in);
            }
            if (StringUtils.hasText(clientVersionDTO.getPackageName())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.like(root.get(ClientVersion_.packageName), "%" + clientVersionDTO.getPackageName() + "%"));
            }
            if (StringUtils.hasText(clientVersionDTO.getVersion())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.like(root.get(ClientVersion_.version), "%" + clientVersionDTO.getVersion() + "%"));
            }
            if (StringUtils.hasText(clientVersionDTO.getVersionCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.like(root.get(ClientVersion_.versionCode), "%" + clientVersionDTO.getVersionCode() + "%"));
            }
            if (StringUtils.hasText(clientVersionDTO.getHash())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.like(root.get(ClientVersion_.hash), "%" + clientVersionDTO.getHash() + "%"));
            }
            if (StringUtils.hasText(clientVersionDTO.getCompilerTime())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.like(root.get(ClientVersion_.compilerTime), "%" + clientVersionDTO.getCompilerTime() + "%"));
            }
            if (StringUtils.hasText(clientVersionDTO.getProjectCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.like(root.get(ClientVersion_.project).get(Project_.code), "%" + clientVersionDTO.getProjectCode() + "%"));
            }

            if (Objects.nonNull(clientVersionDTO.getNotClientStatus())) {
                Predicate notEqual = criteriaBuilder.notEqual(root.get(ClientVersion_.CLIENT_STATUS), clientVersionDTO.getNotClientStatus());
                if (Objects.nonNull(clientVersionDTO.getIsLow())) {
                    notEqual = criteriaBuilder.or(notEqual, criteriaBuilder.equal(root.get(ClientVersion_.IS_LOW), clientVersionDTO.getIsLow()));
                }
                predicate = criteriaBuilder.and(predicate, notEqual);
            } else {
                if (Objects.nonNull(clientVersionDTO.getIsLow())) {
                    predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get(ClientVersion_.IS_LOW), clientVersionDTO.getIsLow()));
                }
            }
            return predicate;
        };

        return clientVersionConvert.po2vo(repository.findAll(specification, pageable));
    }

    @Transactional
    @Caching(evict = {@CacheEvict(cacheNames = CacheConstants.CACHE_NAMES_CLIENT_VERSION_FINDS, allEntries = true), @CacheEvict(key = "#p0.clientVersion.id")})
    public void publishVersion(ClientPublishTaskVO clientPublishTaskVO) {
        Optional<ClientVersion> optional = repository.findById(clientPublishTaskVO.getClientVersion().getId());
        if (optional.isEmpty()) {
            throw new ArgsException("版本Id[{0}]不存在");
        }

        ClientVersion clientVersion = optional.get();
        Optional<ClientVersion> versionOptional = repository.findFirstByProjectIdAndAndClientStatus(clientPublishTaskVO.getProject().getId(), clientPublishTaskVO.getClientStatus());
        if (versionOptional.isPresent()) {
            ClientVersion oldClient = versionOptional.get();
            oldClient.setClientStatus(ClientStatus.Expire);
            oldClient.setClientPublishType(null);
        }
        clientVersion.setClientStatus(clientPublishTaskVO.getClientStatus());
        clientVersion.setClientPublishType(clientPublishTaskVO.getClientPublishType());
        clientVersion.setEditStatus(Status.DISABLED);
        repository.save(clientVersion);
        ClientPublishTask clientPublishTask = clientPublishTaskService.findByProjectIdAndClientStatusAndStatus(ClientVersionDTO.builder()
                .projectId(clientVersion.getProject().getId())
                .clientStatus(clientVersion.getClientStatus())
                .build());
        if (Objects.nonNull(clientPublishTask)) {
            clientPublishTaskService.updateStatus(clientPublishTask.getId(), Status.DELETED);
        }
        clientPublishTaskService.updateStatus(clientPublishTaskVO.getId(), Status.ENABLED);
    }

    @Transactional(readOnly = true)
    @Cacheable(cacheNames = CacheConstants.CACHE_NAMES_CLIENT_VERSION_FINDS, key = "'projectId::'+#p0")
    public ClientVersionVO findLowVersion(Long projectId) {
        Optional<ClientVersion> optional = repository.findFirstByIsLowAndProjectId(true, projectId);
        return optional.map(clientVersion -> clientVersionConvert.po2vo(clientVersion)).orElse(null);
    }

    @Caching(evict = {@CacheEvict(cacheNames = CacheConstants.CACHE_NAMES_CLIENT_VERSION_FINDS, allEntries = true), @CacheEvict(key = "#id")})
    @Transactional
    public void updateLow(Long id) {
        Optional<ClientVersion> optional = repository.findById(id);
        if (optional.isEmpty()) {
            throw new ArgsException("版本Id[{0}]不存在");
        }
        ClientVersion clientVersion = optional.get();
        Optional<ClientVersion> testOptional = repository.findFirstByProjectIdAndAndClientStatus(clientVersion.getProject().getId(), ClientStatus.Test);
        Optional<ClientVersion> releaseOptional = repository.findFirstByProjectIdAndAndClientStatus(clientVersion.getProject().getId(), ClientStatus.Release);
        if (testOptional.isPresent()) {
            if (testOptional.get().getSerialNumber() < clientVersion.getSerialNumber()) {
                throw new ArgsException("最低兼容版本不能高于灰测版本");
            }
        }
        if (releaseOptional.isPresent()) {
            if (releaseOptional.get().getSerialNumber() < clientVersion.getSerialNumber()) {
                throw new ArgsException("最低兼容版本不能高于发布版本");
            }
        }

        Optional<ClientVersion> lowOptional = repository.findFirstByIsLowAndProjectId(true, clientVersion.getProject().getId());
        lowOptional.ifPresent(version -> version.setIsLow(false));
        clientVersion.setIsLow(true);
        repository.save(clientVersion);
    }

    @Transactional
    public ClientVersionVO findClientByUserAndProjectId(Long projectId) {
        boolean isTest = false;
        Optional<ClientVersion> testOptional = repository.findFirstByProjectIdAndAndClientStatus(projectId, ClientStatus.Test);
        if (testOptional.isPresent()) {
            for (ClientPublishTask task : testOptional.get().getClientPublishTasks()) {
                if (task.getClientStatus().equals(ClientStatus.Test) && Objects.nonNull(task.getTests())) {
                    List<Long> testIds = FunctionUtils.ThrowingFunctionTowParams.<String, JavaType, List<Long>>sneaky(objectMapper::readValue).apply(task.getTests(), TypeFactory.defaultInstance().constructCollectionType(List.class, Long.class));
                    for (Long testId : testIds) {
                        if (iTestController.existsUserByTestId(testId, getUserId()) == 1) {
                            isTest = true;
                            break;
                        }
                    }
                }
                if (task.getClientStatus().equals(ClientStatus.Test) && Objects.nonNull(task.getPatientTests())) {
                    List<Long> patientTestIds = FunctionUtils.ThrowingFunctionTowParams.<String, JavaType, List<Long>>sneaky(objectMapper::readValue).apply(task.getPatientTests(), TypeFactory.defaultInstance().constructCollectionType(List.class, Long.class));
                    for (Long testId : patientTestIds) {
                        if (iPatientTestController.existsPatientByTestId(testId, getUserId()) == 1) {
                            isTest = true;
                            break;
                        }
                    }
                }
                if (isTest) break;
            }
        }
        ClientVersion clientVersion;
        Optional<ClientVersion> releaseOptional = repository.findFirstByProjectIdAndAndClientStatus(projectId, ClientStatus.Release);
        if (isTest) {
            clientVersion = testOptional.get();
            if (releaseOptional.isPresent()) {
                int releaseVersion = Integer.parseInt(releaseOptional.get().getVersion().replace(".", ""));
                int testVersion = Integer.parseInt(testOptional.get().getVersion().replace(".", ""));
                if (releaseVersion > testVersion
                        || (releaseVersion == testVersion && releaseOptional.get().getSerialNumber() > testOptional.get().getSerialNumber())) {
                    clientVersion = releaseOptional.get();
                }
            } else {
                clientVersion = testOptional.get();
            }

        } else {
            if (releaseOptional.isPresent()) {
                clientVersion = releaseOptional.get();
            } else {
                return null;
            }
        }
        // 非白名单获取当前发布版本
        ClientVersionVO clientVersionVO = clientVersionConvert.po2vo(clientVersion);
        ClientVersionVO lowVersion = findLowVersion(projectId);
        if (Objects.nonNull(lowVersion))
            clientVersionVO.setLowVersion(lowVersion.getVersion() + '.' + String.format("%0" + 4 + "d", lowVersion.getSerialNumber()));
        else
            clientVersionVO.setLowVersion(clientVersionVO.getVersion() + '.' + String.format("%0" + 4 + "d", clientVersionVO.getSerialNumber()));
        return clientVersionVO;
    }

    @Transactional
    @Caching(evict = {@CacheEvict(cacheNames = CacheConstants.CACHE_NAMES_CLIENT_VERSION_FINDS, allEntries = true), @CacheEvict(key = "#id")})
    public ClientVersionVO update(Long id, ClientVersionDTO clientVersionDTO, @Nullable MultipartFile apk) {
        File file;
        ApkMeta apkMeta;

        RLock lock = redissonClient.getLock("ClientSerialNumber");
        try {
            if (lock.tryLock(3, 3, TimeUnit.SECONDS)) {
                Optional<ClientVersion> optional = repository.findById(id);
                if (optional.isEmpty()) {
                    throw new ArgsException("版本Id[{0}]不存在", id);
                }
                ClientVersion clientVersion = optional.get();
                if ((clientVersion.getClientStatus() != ClientStatus.None) || clientVersion.getIsLow()) {
                    throw new ArgsException("当前版本已发布,不允许修改");
                }
                clientVersionConvert.dto2po(clientVersionDTO, clientVersion);
                if (Objects.nonNull(apk)) {
                    String[] split = Objects.requireNonNull(apk.getOriginalFilename()).split("\\.");
                    file = File.createTempFile(split[0], split[1]);
                    apk.transferTo(file);
                    try (ApkFile apkFile = new ApkFile(file)) {
                        apkMeta = apkFile.getApkMeta();
                        if (repository.existsByVersionCodeAndPackageName(apkMeta.getVersionCode().toString(), apk.getOriginalFilename())) {
                            throw new ServiceException("Apk版本重复");
                        }
                        clientVersion.setVersionCode(apkMeta.getVersionCode().toString());
                        clientVersion.setPackageName(apk.getOriginalFilename());
                    }
                    file.deleteOnExit();
                }
                return clientVersionConvert.po2vo(repository.saveAndFlush(clientVersion));
            }
            throw new RuntimeException("服务忙,请稍后再试");
        } catch (Exception e) {
            throw new ArgsException(e.getMessage());
        } finally {
            this.unLock(lock);
        }
    }

    public Boolean existById(Long id) {
        return repository.existsById(id);
    }

    public ClientVersionVO findById(Long id) {
        Optional<ClientVersion> optional = repository.findById(id);
        if (optional.isEmpty()) {
            throw new ArgsException("版本Id[{0}]不存在");
        }

        return clientVersionConvert.po2vo(optional.get());
    }
}
