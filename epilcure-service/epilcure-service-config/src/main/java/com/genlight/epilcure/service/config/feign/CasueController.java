package com.genlight.epilcure.service.config.feign;

import com.genlight.epilcure.api.config.feign.ICasueController;
import com.genlight.epilcure.api.config.pojo.vo.CasueVO;
import com.genlight.epilcure.service.config.service.CasueService;
import io.swagger.v3.oas.annotations.Hidden;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/6/12 15:27
 * @Version 1.0.0
 **/
@RestController("feignCasueController")
@RequestMapping
@Hidden
public class CasueController implements ICasueController {
    @Resource
    private CasueService casueService;

    @Override
    public Boolean existsById(Long id) {
        return casueService.existsById(id);
    }

    @Override
    public Boolean existsByIds(List<Long> ids) {
        return casueService.existsByIds(ids);
    }

    @Override
    public CasueVO findById(Long id) {
        return casueService.findById(id);
    }
}
