package com.genlight.epilcure.service.config.pojo.convert;

import com.genlight.epilcure.api.config.pojo.dto.ConfigsDTO;
import com.genlight.epilcure.api.config.pojo.vo.ConfigsVO;
import com.genlight.epilcure.common.core.pojo.convert.BaseConvert;
import com.genlight.epilcure.service.config.dao.entity.Configs;
import org.mapstruct.Mapper;

@Mapper(config = BaseConvert.class)
public interface ConfigsConvert extends BaseConvert<Configs, ConfigsVO, ConfigsDTO> {
}
