package com.genlight.epilcure.service.config.service;

import com.genlight.epilcure.api.config.pojo.bo.AlgorithmParamBO;
import com.genlight.epilcure.api.config.pojo.dto.AlgorithmParamDTO;
import com.genlight.epilcure.api.config.pojo.vo.AlgorithmParamVO;
import com.genlight.epilcure.common.core.service.BaseService;
import com.genlight.epilcure.common.core.service.exception.ArgsException;
import com.genlight.epilcure.common.rocketmq.RocketMqConstant;
import com.genlight.epilcure.service.config.constants.CacheConstants;
import com.genlight.epilcure.service.config.dao.entity.AlgorithmParam;
import com.genlight.epilcure.service.config.dao.entity.AlgorithmParam_;
import com.genlight.epilcure.service.config.dao.repository.AlgorithmParamRepository;
import com.genlight.epilcure.service.config.pojo.convert.AlgorithmParamConvert;
import jakarta.annotation.Resource;
import jakarta.persistence.criteria.Predicate;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.annotation.Caching;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @Date 2022/6/26 15:04
 * @Version 1.0.0
 **/
@Service
@CacheConfig(cacheNames = CacheConstants.CACHE_NAMES_ALGORITHM_PARAM)
public class AlgorithmParamService extends BaseService<AlgorithmParam, Long, AlgorithmParamRepository> {
    @Resource
    private AlgorithmParamConvert algorithmParamConvert;

    @Resource
    private RocketMQTemplate algorithmParamUpdateMqTemplate;

    @Transactional
    @CacheEvict(value = CacheConstants.CACHE_NAMES_ALGORITHM_PARAM_FINDS, allEntries = true)
    public AlgorithmParamVO addAlgorithmParam(AlgorithmParamDTO algorithmParamDTO) {
        AlgorithmParam algorithmParam = algorithmParamConvert.dto2po(algorithmParamDTO);
        return algorithmParamConvert.po2vo(repository.save(algorithmParam));
    }

    @Transactional
    @Caching(evict = {
            @CacheEvict(cacheNames = CacheConstants.CACHE_NAMES_ALGORITHM_PARAM_FINDS, allEntries = true),
            @CacheEvict(key = "#p0")
    })
    public void deleteAlgorithmParam(Long id) {
        if (!repository.existsById(id)) {
            throw new ArgsException("删除的参数Id[{0}]不存在", id);
        }
        repository.deleteById(id);
    }


    @Transactional(readOnly = true)
    @Cacheable(cacheNames = CacheConstants.CACHE_NAMES_ALGORITHM_PARAM_FINDS)
    public List<AlgorithmParamVO> findsByAlgorithm(Long algorithmId, Date updateTime) {
        Specification<AlgorithmParam> algorithmParamSpecification = (root, query, criteriaBuilder) -> {
            Predicate predicate = criteriaBuilder.conjunction();

            predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.join("algorithms").get("id"), algorithmId));
            if (Objects.nonNull(updateTime)) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.greaterThanOrEqualTo(root.get(AlgorithmParam_.UPDATE_TIME), updateTime));
            }
            return predicate;
        };

        return algorithmParamConvert.po2vo(repository.findAll(algorithmParamSpecification));
    }

    @Cacheable(key = "#p0")
    @Transactional(readOnly = true)
    public AlgorithmParamVO find(Long id) {
        Optional<AlgorithmParam> optional = repository.findById(id);
        if (optional.isEmpty()) {
            throw new ArgsException("参数Id[{0}]不存在", id);
        }

        return algorithmParamConvert.po2vo(optional.get());
    }

    @Transactional(readOnly = true)
    public Boolean exist(Long id) {
        return repository.existsById(id);
    }

    @Transactional(readOnly = true)
    public AlgorithmParam getById(Long id) {
        Optional<AlgorithmParam> optional = repository.findById(id);
        if (optional.isEmpty()) {
            throw new ArgsException("算法参数Id[{0}]不存在", id);
        }
        return optional.get();
    }


    @Transactional
    @Caching(evict = {
            @CacheEvict(cacheNames = CacheConstants.CACHE_NAMES_ALGORITHM_PARAM_FINDS, allEntries = true),
            @CacheEvict(key = "#p0")
    })
    public AlgorithmParamVO updateAlgorithmParam(Long id, AlgorithmParamDTO algorithmParamDTO) {
        Optional<AlgorithmParam> optional = repository.findById(id);
        if (optional.isEmpty()) {
            throw new ArgsException("修改的删除Id[{0}]不存在", id);
        }
        AlgorithmParam algorithmParam = optional.get();
        algorithmParamConvert.dto2po(algorithmParamDTO, algorithmParam);

        algorithmParamUpdateMqTemplate.convertAndSend(RocketMqConstant.ALGORITHM_PARAM_NAME_UPDATE_TOPIC, AlgorithmParamBO.builder().id(id).name(algorithmParam.getName()).build());
        return algorithmParamConvert.po2vo(repository.save(algorithmParam));
    }


}
