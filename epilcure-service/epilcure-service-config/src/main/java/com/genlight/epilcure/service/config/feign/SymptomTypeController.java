package com.genlight.epilcure.service.config.feign;

import com.genlight.epilcure.api.config.feign.ISymptomTypeController;
import com.genlight.epilcure.api.config.pojo.vo.SymptomTypeVO;
import com.genlight.epilcure.service.config.service.SymptomTypeService;
import io.swagger.v3.oas.annotations.Hidden;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR> maoyan
 * @Date : 2022/8/8 14:37
 * @Version :1.0.0
 */
@RestController("feignSymptomTypeController")
@RequestMapping
@Hidden
public class SymptomTypeController implements ISymptomTypeController {
    @Resource
    private SymptomTypeService symptomTypeService;

    @Override
    public Boolean existsById(Long id) {
        return symptomTypeService.existsById(id);
    }

    @Override
    public Boolean existsByIds(List<Long> ids) {
        return symptomTypeService.existsByIds(ids);
    }

    @Override
    public SymptomTypeVO findById(Long id) {
        return symptomTypeService.findById(id);
    }
}
