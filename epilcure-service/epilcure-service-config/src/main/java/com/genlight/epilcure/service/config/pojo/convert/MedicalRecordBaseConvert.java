package com.genlight.epilcure.service.config.pojo.convert;

import com.genlight.epilcure.api.config.pojo.dto.MedicalRecordBaseDTO;
import com.genlight.epilcure.api.config.pojo.vo.MedicalRecordBaseVO;
import com.genlight.epilcure.common.core.pojo.convert.BaseConvert;
import com.genlight.epilcure.service.config.dao.entity.MedicalRecordBase;
import org.mapstruct.MapperConfig;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/6/12 17:29
 * @Version 1.0.0
 **/
@MapperConfig(
        componentModel = "spring",
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        uses = DiseaseConvert.class
)
public interface MedicalRecordBaseConvert<
        Po extends MedicalRecordBase,
        Vo extends MedicalRecordBaseVO,
        Dto extends MedicalRecordBaseDTO> extends BaseConvert<Po, Vo, Dto> {

    @Override
    Vo po2vo(Po po);

    List<Vo> po2vo(List<Po> pos);

    Vo po2voBySummay(Po po);
}
