package com.genlight.epilcure.service.config.feign;

import com.genlight.epilcure.api.config.feign.IDiseaseController;
import com.genlight.epilcure.api.config.pojo.vo.DiseaseVO;
import com.genlight.epilcure.service.config.service.DiseaseService;
import io.swagger.v3.oas.annotations.Hidden;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @Date 2022/6/10 23:04
 * @Version 1.0.0
 **/
@RestController("feignDiseaseController")
@RequestMapping
@Hidden
public class DiseaseController implements IDiseaseController {
    @Resource
    private DiseaseService diseaseService;

    @Override
    public Boolean existsById(Long id) {
        return diseaseService.existsById(id);
    }

    @Override
    public DiseaseVO findById(Long id) {
        return diseaseService.findDiseaseById(id);
    }
}
