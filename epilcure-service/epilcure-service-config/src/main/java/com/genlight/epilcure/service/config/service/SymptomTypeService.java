package com.genlight.epilcure.service.config.service;


import com.genlight.epilcure.api.config.pojo.dto.SymptomTypeDTO;
import com.genlight.epilcure.api.config.pojo.vo.SymptomTypeVO;
import com.genlight.epilcure.service.config.constants.CacheConstants;
import com.genlight.epilcure.service.config.dao.entity.SymptomType;
import com.genlight.epilcure.service.config.dao.repository.SymptomTypeRepository;
import com.genlight.epilcure.service.config.pojo.convert.SymptomTypeConvert;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR> maoyan
 * @Date : 2022/8/5 17:45
 * @Version :1.0.0
 */
@Service
@CacheConfig(cacheNames = CacheConstants.CACHE_NAMES_SYMPTOM)
public class SymptomTypeService extends MedicalRecordBaseService<SymptomType, SymptomTypeVO, SymptomTypeDTO, SymptomTypeRepository, SymptomTypeConvert> {
    @Override
    @Cacheable(cacheNames = CacheConstants.CACHE_NAMES_SYMPTOM_FINDS)
    public List<SymptomTypeVO> finds(SymptomTypeDTO dto) {
        return super.finds(dto);
    }

    @Override
    @CacheEvict(cacheNames = CacheConstants.CACHE_NAMES_SYMPTOM_FINDS, allEntries = true)
    public SymptomTypeVO update(Long aLong, SymptomTypeDTO dto) {
        return super.update(aLong, dto);
    }

    @Override
    @CacheEvict(cacheNames = CacheConstants.CACHE_NAMES_SYMPTOM_FINDS, allEntries = true)
    public SymptomTypeVO add(SymptomTypeDTO dto) {
        return super.add(dto);
    }
}
