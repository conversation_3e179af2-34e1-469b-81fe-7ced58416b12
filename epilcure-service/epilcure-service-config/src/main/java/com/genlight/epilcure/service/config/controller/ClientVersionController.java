package com.genlight.epilcure.service.config.controller;

import com.fasterxml.jackson.annotation.JsonView;
import com.genlight.epilcure.api.config.pojo.dto.ClientVersionDTO;
import com.genlight.epilcure.api.config.pojo.vo.ClientVersionVO;
import com.genlight.epilcure.common.core.mvc.response.JsonResult;
import com.genlight.epilcure.common.core.pojo.valid.Add;
import com.genlight.epilcure.common.core.pojo.valid.Find;
import com.genlight.epilcure.service.config.service.ClientVersionService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

@RestController
@RequestMapping("/api/config/clientVersions/project/{id}")
@Tag(name = "ClientVersionController", description = "客户端版本相关接口")
public class ClientVersionController {
    @Resource
    private ClientVersionService clientVersionService;

    @PostMapping(consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @JsonView(Add.class)
    @Operation(summary = "新增版本")
    public JsonResult<ClientVersionVO> add(@RequestPart @Valid ClientVersionDTO clientVersionDTO, @RequestPart MultipartFile multipartFile) {
        return JsonResult.ok(clientVersionService.add(clientVersionDTO, multipartFile));
    }

    @GetMapping
    @JsonView(Find.class)
    @Operation(summary = "根据条件查询版本信息")
    public JsonResult<Page<ClientVersionVO>> finds(ClientVersionDTO clientVersionDTO, Pageable pageable) {
        return JsonResult.ok(clientVersionService.finds(clientVersionDTO, pageable));
    }

    @GetMapping("/project/{id}")
    @Operation(summary = "根据项目Id查询当前版本信息")
    public JsonResult<ClientVersionVO> findByProjectId(@PathVariable Long id) {
        return JsonResult.ok(clientVersionService.findClientByUserAndProjectId(id));
    }

    @PutMapping(value = "/{id}", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @JsonView(Add.class)
    @Operation(summary = "修改版本信息")
    public JsonResult<ClientVersionVO> update(@PathVariable Long id, @RequestPart ClientVersionDTO clientVersionDTO, @RequestPart(required = false) MultipartFile multipartFile) {
        return JsonResult.ok(clientVersionService.update(id, clientVersionDTO, multipartFile));
    }

    @PutMapping("/low/{id}")
    @JsonView(Add.class)
    @Operation(summary = "设置最低版本")
    public JsonResult updateLow(@PathVariable Long id) {
        clientVersionService.updateLow(id);
        return JsonResult.ok();
    }
}
