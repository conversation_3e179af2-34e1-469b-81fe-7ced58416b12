package com.genlight.epilcure.service.config.pojo.convert;

import com.genlight.epilcure.api.config.pojo.dto.AlgorithmDTO;
import com.genlight.epilcure.api.config.pojo.vo.AlgorithmVO;
import com.genlight.epilcure.common.core.pojo.convert.BaseConvert;
import com.genlight.epilcure.common.core.pojo.convert.qualified.Basic;
import com.genlight.epilcure.common.core.pojo.convert.qualified.Summary;
import com.genlight.epilcure.service.config.dao.entity.Algorithm;
import org.mapstruct.*;

import java.util.List;

@Mapper(config = BaseConvert.class, uses = {AlgorithmParamConvert.class, DiseaseConvert.class})
public interface AlgorithmConvert extends BaseConvert<Algorithm, AlgorithmVO, AlgorithmDTO> {
    @Basic
    @Mapping(target = "algorithmParams", ignore = true)
    @Mapping(target = "disease", ignore = true)
    @BeanMapping(nullValueCheckStrategy = NullValueCheckStrategy.ON_IMPLICIT_CONVERSION)
    @Override
    AlgorithmVO po2vo(Algorithm algorithm);

    @Summary
    @Mapping(target = "algorithmParams", qualifiedByName = "AlgorithmParamConvert")
    @Mapping(target = "disease", qualifiedBy = Basic.class, qualifiedByName = "DiseaseConvert")
    @BeanMapping(nullValueCheckStrategy = NullValueCheckStrategy.ON_IMPLICIT_CONVERSION)
    AlgorithmVO po2voBySummary(Algorithm algorithm);

    @IterableMapping(qualifiedBy = Basic.class)
    List<AlgorithmVO> po2vo(List<Algorithm> algorithms);
}
