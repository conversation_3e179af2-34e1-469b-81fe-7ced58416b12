package com.genlight.epilcure.service.config.service;

import com.genlight.epilcure.api.config.pojo.dto.ProjectDTO;
import com.genlight.epilcure.api.config.pojo.vo.ProjectVO;
import com.genlight.epilcure.common.core.service.BaseService;
import com.genlight.epilcure.common.core.service.exception.ServiceException;
import com.genlight.epilcure.service.config.constants.CacheConstants;
import com.genlight.epilcure.service.config.dao.entity.Project;
import com.genlight.epilcure.service.config.dao.entity.Project_;
import com.genlight.epilcure.service.config.dao.repository.ProjectRepository;
import com.genlight.epilcure.service.config.pojo.convert.ProjectConvert;
import jakarta.annotation.Resource;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @Date 2022/6/26 14:20
 * @Version 1.0.0
 **/
@Service
@CacheConfig(cacheNames = CacheConstants.CACHE_NAMES_PROJECTS)
public class ProjectService extends BaseService<Project, Long, ProjectRepository> {

    @Resource
    private ProjectConvert projectConvert;

    @Resource
    private RedissonClient redissonClient;

    @Cacheable(key = "#id")
    @Transactional(readOnly = true)
    public ProjectVO findProjectById(Long id) {
        Optional<Project> project = repository.findById(id);
        if (project.isEmpty()) {
            throw new ServiceException("查找的项目[{0}]不存在！", id);
        }
        return projectConvert.po2vo(project.get());
    }

    public Project getProjectById(Long id) {
        return repository.getReferenceById(id);
    }

    @Cacheable(key = "'exists::'+#id")
    @Transactional(readOnly = true)
    public boolean existsProjectById(Long id) {
        return repository.existsById(id);
    }

    @Transactional(readOnly = true)
    @Cacheable(cacheNames = CacheConstants.CACHE_NAMES_PROJECTS)
    public Page<ProjectVO> findProjectsByPageable(ProjectDTO projectDTO, Pageable pageable) {
        return projectConvert.po2vo(repository.findAll((Root<Project> root, CriteriaQuery<?> query, CriteriaBuilder criteriaBuilder) -> {
            Predicate predicate = criteriaBuilder.conjunction();
            if (Objects.nonNull(projectDTO.getType())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get(Project_.type), projectDTO.getType()));
            }
            if (StringUtils.hasText(projectDTO.getName())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get(Project_.name), projectDTO.getName()));
            }
            if (StringUtils.hasText(projectDTO.getCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get(Project_.code), projectDTO.getCode()));
            }
            if (StringUtils.hasText(projectDTO.getHardwareVersion())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get(Project_.hardwareVersion), projectDTO.getHardwareVersion()));
            }
            if (Objects.nonNull(projectDTO.getStatus())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get(Project_.STATUS), projectDTO.getStatus()));
            }
            return predicate;
        }, pageable));
    }

    @Transactional
    @CacheEvict(cacheNames = CacheConstants.CACHE_NAMES_PROJECTS, allEntries = true)
    public ProjectVO addProject(ProjectDTO projectDTO) {
        RLock lock = redissonClient.getLock("addProject-%s".formatted(projectDTO.getName()));
        try {
            if (lock.tryLock(2, 2, TimeUnit.SECONDS)) {
                if (repository.existsByName(projectDTO.getName())) {
                    throw new ServiceException("项目名称[{0}]已经存在！", projectDTO.getName());
                }
                if (repository.existsByCode(projectDTO.getCode())) {
                    throw new ServiceException("项目Code[{0}]已经存在！", projectDTO.getCode());
                }
                return projectConvert.po2vo(repository.saveAndFlush(projectConvert.dto2po(projectDTO)));
            }
            throw new ServiceException("操作太频繁，请稍后再试！");
        } catch (Exception e) {
            throw new RuntimeException(e);
        } finally {
            this.unLock(lock);
        }
    }

    @Transactional
    @CacheEvict(cacheNames = CacheConstants.CACHE_NAMES_PROJECTS, allEntries = true)
    public ProjectVO updateProject(Long id, ProjectDTO projectDTO) {
        Optional<Project> projectOptional = repository.findById(id);
        if (projectOptional.isEmpty()) {
            throw new ServiceException("更新的项目[{0}]不存在！", id);
        }
        Project project = projectOptional.get();
        if (StringUtils.hasText(projectDTO.getName()) && !projectDTO.getName().equals(project.getName())) {
            if (repository.existsByName(projectDTO.getName())) {
                throw new ServiceException("更新的项目名称[{0}]已经存在！", projectDTO.getName());
            }
        }
        return projectConvert.po2vo(repository.saveAndFlush(projectConvert.dto2po(projectDTO, project)));
    }
}
