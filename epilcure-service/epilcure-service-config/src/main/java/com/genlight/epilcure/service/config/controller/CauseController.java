package com.genlight.epilcure.service.config.controller;

import com.genlight.epilcure.api.config.pojo.dto.CasueDTO;
import com.genlight.epilcure.api.config.pojo.vo.CasueVO;
import com.genlight.epilcure.common.core.mvc.response.JsonResult;
import com.genlight.epilcure.common.core.pojo.valid.Add;
import com.genlight.epilcure.service.config.service.CasueService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/config/cause")
@Tag(name = "CauseController", description = "病因相关接口")
public class CauseController {
    @Autowired
    private CasueService casueService;

    @Operation(summary = "根据id获取病因")
    @GetMapping("/{id}")
    public JsonResult<CasueVO> find(@PathVariable Long id) {
        return JsonResult.ok(casueService.findById(id));
    }

    @Operation(summary = "添加病因类型")
    @PostMapping
    public JsonResult<String> add(@Validated(Add.class) @RequestBody CasueDTO casueDTO) {
        casueService.add(casueDTO);
        return JsonResult.ok("新增成功");
    }

    @Operation(summary = "修改病因信息")
    @PutMapping("/{id}")
    public JsonResult<String> update(@PathVariable Long id, @Valid @RequestBody CasueDTO casueDTO) {
        casueService.update(id, casueDTO);
        return JsonResult.ok("修改成功");
    }

    @GetMapping("/findAll")
    @Operation(summary = "获取病因列表")
    public JsonResult<List<CasueVO>> findAll(CasueDTO casueDTO) {
        return JsonResult.ok(casueService.finds(casueDTO));
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "删除病因")
    public JsonResult<String> delete(@PathVariable Long id) {
        casueService.delete(id);
        return JsonResult.ok("删除成功");
    }
}
