package com.genlight.epilcure.service.config.service;

import com.genlight.epilcure.api.config.pojo.dto.DiseaseDTO;
import com.genlight.epilcure.api.config.pojo.vo.DiseaseVO;
import com.genlight.epilcure.common.core.service.BaseService;
import com.genlight.epilcure.common.core.service.exception.ArgsException;
import com.genlight.epilcure.common.core.service.exception.ServiceException;
import com.genlight.epilcure.service.config.constants.CacheConstants;
import com.genlight.epilcure.service.config.dao.entity.Disease;
import com.genlight.epilcure.service.config.dao.repository.DiseaseRepository;
import com.genlight.epilcure.service.config.pojo.convert.DiseaseConvert;
import jakarta.annotation.Resource;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.annotation.Caching;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import java.util.List;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

@Service
@CacheConfig(cacheNames = CacheConstants.CACHE_NAMES_DISEASE)
public class DiseaseService extends BaseService<Disease, Long, DiseaseRepository> {

    @Resource
    private DiseaseConvert diseaseConvert;

    @Resource
    private RedissonClient redissonClient;

    /**
     * 查询Id是否存在
     *
     * @param id
     * @return
     */
    public boolean existsById(Long id) {
        return repository.existsById(id);
    }

    /**
     * 根据Id获取疾病类型
     *
     * @param id
     * @return
     */
    @Transactional(readOnly = true)
    @Cacheable(key = "#p0")
    public DiseaseVO findDiseaseById(Long id) {
        Optional<Disease> optional = repository.findById(id);
        if (optional.isEmpty()) {
            throw new ArgsException("疾病Id不存在");
        }
        return diseaseConvert.po2vo(optional.get());
    }

    public Disease getDiseaseById(Long id) {
        if (!repository.existsById(id)) {
            throw new ArgsException("疾病Id不存在");
        }
        return repository.getReferenceById(id);
    }

    /**
     * 添加疾病类型
     *
     * @param diseaseDTO
     * @return
     */
    @Transactional
    @CacheEvict(cacheNames = CacheConstants.CACHE_NAMES_DISEASE_FINDS, allEntries = true)
    public boolean addDisease(DiseaseDTO diseaseDTO) {
        RLock lock = redissonClient.getLock("addDisease-%s".formatted(diseaseDTO.getName()));
        try {
            if (lock.tryLock(2, 2, TimeUnit.SECONDS)) {
                boolean b = repository.existsByName(diseaseDTO.getName());
                Assert.isTrue(!b, "疾病名称已经存在");
                repository.saveAndFlush(diseaseConvert.dto2po(diseaseDTO));
                return true;
            }
            throw new ServiceException("操作太频繁，请稍后再试！");
        } catch (Exception e) {
            throw new RuntimeException(e);
        } finally {
            this.unLock(lock);
        }
    }

    /**
     * 修改疾病类型
     *
     * @param diseaseDTO
     * @return
     */
    @Transactional
    @Caching(evict = {
            @CacheEvict(cacheNames = CacheConstants.CACHE_NAMES_DISEASE_FINDS, allEntries = true),
            @CacheEvict(cacheNames = CacheConstants.CACHE_NAMES_DISEASE, key = "#p0")
    })
    public boolean editDisease(Long id, DiseaseDTO diseaseDTO) {
        Optional<Disease> disease = repository.findById(id);
        if (disease.isEmpty()) {
            throw new ArgsException("修改的疾病不存在");
        }
        diseaseConvert.dto2po(diseaseDTO, disease.get());
        repository.save(disease.get());
        return true;
    }

    /**
     * 获取所有疾病类型
     *
     * @return
     */
    @Transactional(readOnly = true)
    @Cacheable(cacheNames = CacheConstants.CACHE_NAMES_DISEASE_FINDS)
    public List<DiseaseVO> findAllDiseases() {
        return diseaseConvert.po2vo(repository.findAll());
    }

    @Transactional
    @CacheEvict(cacheNames = CacheConstants.CACHE_NAMES_DISEASE_FINDS, allEntries = true)
    public Boolean deleteDisease(Long id) {
        if (!repository.existsById(id)) {
            throw new ArgsException("删除得疾病Id[{}]不存在");
        }
        repository.deleteById(id);
        return true;
    }
}
