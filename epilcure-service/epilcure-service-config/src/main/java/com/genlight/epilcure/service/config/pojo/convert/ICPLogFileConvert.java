package com.genlight.epilcure.service.config.pojo.convert;

import com.genlight.epilcure.api.config.pojo.dto.ICPLogFileDTO;
import com.genlight.epilcure.api.config.pojo.vo.ICPLogFileVO;
import com.genlight.epilcure.common.core.pojo.convert.BaseConvert;
import com.genlight.epilcure.service.config.dao.entity.ICPLogFile;
import org.mapstruct.Mapper;

@Mapper(config = BaseConvert.class)
public interface ICPLogFileConvert extends BaseConvert<ICPLogFile, ICPLogFileVO, ICPLogFileDTO> {
}
