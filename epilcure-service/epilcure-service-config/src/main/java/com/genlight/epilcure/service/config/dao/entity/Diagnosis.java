package com.genlight.epilcure.service.config.dao.entity;

import jakarta.persistence.Cacheable;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR>
 * @Date 2022/6/11 14:19
 * @Version 1.0.0
 * @Desc 结论
 **/
@Getter
@Setter
@Entity
@Cacheable
@SuperBuilder
@NoArgsConstructor
@Table(name = "t_diagnosis")
public class Diagnosis extends MedicalRecordBase {
}
