package com.genlight.epilcure.service.config.service;

import com.genlight.epilcure.api.config.pojo.dto.DrugsDTO;
import com.genlight.epilcure.api.config.pojo.vo.DrugsVO;
import com.genlight.epilcure.service.config.constants.CacheConstants;
import com.genlight.epilcure.service.config.dao.entity.Drugs;
import com.genlight.epilcure.service.config.dao.repository.DrugsRepository;
import com.genlight.epilcure.service.config.pojo.convert.DrugsConvert;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/6/8 23:21
 * @Version 1.0.0
 **/
@Service
@CacheConfig(cacheNames = CacheConstants.CACHE_NAMES_DRUGS)
public class DrugsService extends MedicalRecordBaseService<Drugs, DrugsVO, DrugsDTO, DrugsRepository, DrugsConvert> {
    @Override
    @Cacheable(cacheNames = CacheConstants.CACHE_NAMES_DRUGS_FINDS, key = "#p0")
    public List<DrugsVO> finds(DrugsDTO dto) {
        return super.finds(dto);
    }

    @Override
    @CacheEvict(cacheNames = CacheConstants.CACHE_NAMES_DRUGS_FINDS, allEntries = true)
    public DrugsVO update(Long aLong, DrugsDTO dto) {
        return super.update(aLong, dto);
    }

    @Override
    @CacheEvict(cacheNames = CacheConstants.CACHE_NAMES_DRUGS_FINDS, allEntries = true)
    public DrugsVO add(DrugsDTO dto) {
        return super.add(dto);
    }

    @Override
    @CacheEvict(cacheNames = CacheConstants.CACHE_NAMES_DRUGS_FINDS, allEntries = true)
    public boolean delete(Long id) {
        return super.delete(id);
    }

    @Override
    @Cacheable(key = "#p0")
    public DrugsVO findById(Long id) {
        return super.findById(id);
    }
}
