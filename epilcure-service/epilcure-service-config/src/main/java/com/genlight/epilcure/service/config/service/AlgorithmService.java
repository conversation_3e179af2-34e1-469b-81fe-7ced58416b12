package com.genlight.epilcure.service.config.service;

import com.genlight.epilcure.api.config.pojo.bo.AlgorithmBO;
import com.genlight.epilcure.api.config.pojo.dto.AlgorithmDTO;
import com.genlight.epilcure.api.config.pojo.vo.AlgorithmVO;
import com.genlight.epilcure.common.core.service.BaseService;
import com.genlight.epilcure.common.core.service.exception.ArgsException;
import com.genlight.epilcure.common.rocketmq.RocketMqConstant;
import com.genlight.epilcure.service.config.constants.CacheConstants;
import com.genlight.epilcure.service.config.dao.entity.Algorithm;
import com.genlight.epilcure.service.config.dao.entity.Algorithm_;
import com.genlight.epilcure.service.config.dao.entity.Disease;
import com.genlight.epilcure.service.config.dao.repository.AlgorithmRepository;
import com.genlight.epilcure.service.config.pojo.convert.AlgorithmConvert;
import jakarta.annotation.Resource;
import jakarta.persistence.criteria.Predicate;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.annotation.Caching;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @Date 2022/6/26 14:20
 * @Version 1.0.0
 **/
@Service
@CacheConfig(cacheNames = CacheConstants.CACHE_NAMES_ALGORITHM)
public class AlgorithmService extends BaseService<Algorithm, Long, AlgorithmRepository> {
    @Resource
    private DiseaseService diseaseService;

    @Resource
    private AlgorithmParamService algorithmParamService;

    @Resource
    private AlgorithmConvert algorithmConvert;

    @Resource
    private RocketMQTemplate algorithmUpdateMqTemplate;

    /**
     * 添加算法配置
     *
     * @param algorithmDTO
     * @return
     */
    @Transactional
    @CacheEvict(cacheNames = CacheConstants.CACHE_NAMES_ALGORITHM_FINDS, allEntries = true)
    public AlgorithmVO addAlgorithm(AlgorithmDTO algorithmDTO) {
        Disease disease = diseaseService.getDiseaseById(algorithmDTO.getDiseaseId());
        Algorithm algorithm = algorithmConvert.dto2po(algorithmDTO);
        algorithm.setDisease(disease);
        algorithmDTO.getParamIds().forEach(id -> {
            algorithm.getAlgorithmParams().add(algorithmParamService.getById(id));
        });
        return algorithmConvert.po2voBySummary(repository.save(algorithm));
    }

    /**
     * 删除算法
     *
     * @param id
     * @return
     */
    @Transactional
    @Caching(evict = {
            @CacheEvict(cacheNames = CacheConstants.CACHE_NAMES_ALGORITHM_FINDS, allEntries = true),
            @CacheEvict(key = "#p0")
    })
    public boolean deleteAlgorithm(Long id) {
        if (!repository.existsById(id)) {
            throw new ArgsException("删除的算法Id[{0}]不存在", id);
        }
        repository.deleteById(id);
        return true;
    }

    /**
     * 根据id获取算法信息
     *
     * @param id
     * @return
     */
    @Transactional(readOnly = true)
    @Cacheable(key = "#p0")
    public AlgorithmVO find(Long id) {
        Optional<Algorithm> optional = repository.findById(id);
        if (optional.isEmpty()) {
            throw new ArgsException("算法Id[{0}]不存在");
        }
        return algorithmConvert.po2voBySummary(optional.get());
    }

    @Transactional(readOnly = true)
    public AlgorithmVO findByCode(String code) {
        Optional<Algorithm> optional = repository.findByCode(code);
        if (optional.isEmpty()) {
            throw new ArgsException("算法code[{0}]不存在", code);
        }
        return algorithmConvert.po2vo(optional.get());
    }

    @Transactional(readOnly = true)
    public Boolean existsById(Long id) {
        return repository.existsById(id);
    }

    @Transactional(readOnly = true)
    @Cacheable(cacheNames = CacheConstants.CACHE_NAMES_ALGORITHM_FINDS, key = "#p0")
    public List<AlgorithmVO> findsAlgorithm(AlgorithmDTO algorithmDTO) {
        Specification<Algorithm> algorithmSpecification = (root, query, criteriaBuilder) -> {
            Predicate predicate = criteriaBuilder.conjunction();
            if (StringUtils.hasText(algorithmDTO.getName())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.like(root.get(Algorithm_.NAME), "%" + algorithmDTO.getName() + "%"));
            }
            if (Objects.nonNull(algorithmDTO.getDiseaseId())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get(Algorithm_.DISEASE).get("id"), algorithmDTO.getDiseaseId()));
            }
            if (Objects.nonNull(algorithmDTO.getVersion())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.lessThanOrEqualTo(root.get(Algorithm_.VERSION), algorithmDTO.getVersion()));
            }
            if (Objects.nonNull(algorithmDTO.getUpdateTime())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.greaterThanOrEqualTo(root.get(Algorithm_.UPDATE_TIME), algorithmDTO.getUpdateTime()));
            }
            return predicate;
        };
        return algorithmConvert.po2vo(repository.findAll(algorithmSpecification));
    }

    @Transactional
    @Caching(evict = {
            @CacheEvict(key = "#p0"),
            @CacheEvict(cacheNames = CacheConstants.CACHE_NAMES_ALGORITHM_FINDS, allEntries = true)
    })
    public AlgorithmVO updateAlgorithm(Long id, AlgorithmDTO algorithmDTO) {
        Optional<Algorithm> optional = repository.findById(id);
        if (optional.isEmpty()) {
            throw new ArgsException("修改的算法Id[{0}]不存在", id);
        }

        Algorithm algorithm = optional.get();
        algorithmConvert.dto2po(algorithmDTO, algorithm);
        algorithmUpdateMqTemplate.convertAndSend(RocketMqConstant.ALGORITHM_NAME_UPDATE_TOPIC, AlgorithmBO.builder().id(id).name(algorithm.getName()).build());
        return algorithmConvert.po2vo(repository.save(algorithm));
    }

}
