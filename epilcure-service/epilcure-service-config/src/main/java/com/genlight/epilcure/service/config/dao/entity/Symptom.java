package com.genlight.epilcure.service.config.dao.entity;

import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;
import org.springframework.cache.annotation.Cacheable;

/**
 * <AUTHOR>
 * @Date 2022/6/11 12:00
 * @Version 1.0.0
 * @Desc 症状配置表
 **/
@Getter
@Setter
@Entity
@Accessors(chain = true)
@SuperBuilder
@Cacheable
@NoArgsConstructor
@DynamicInsert
@DynamicUpdate
@Table(name = "t_symptom")
public class Symptom extends MedicalRecordBase {
}
