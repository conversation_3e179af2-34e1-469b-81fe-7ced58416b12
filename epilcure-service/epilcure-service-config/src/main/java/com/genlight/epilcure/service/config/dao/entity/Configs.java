package com.genlight.epilcure.service.config.dao.entity;

import com.genlight.epilcure.common.core.dao.entity.TBase;
import jakarta.persistence.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;
import org.hibernate.annotations.Comment;

@Getter
@Setter
@Entity
@Cacheable
@SuperBuilder
@NoArgsConstructor
@Table(name = "t_configs", indexes = {
        @Index(name = "idx_key_version", columnList = "key,version", unique = true)
})
public class Configs extends TBase {
    @Comment("配置Key")
    @Column(nullable = false)
    private String key;
    @Comment("配置Value")
    @Column(nullable = false, length = 2048)
    private String value;
    @Comment("版本号")
    private String version;
    @Comment("备注")
    @Column(length = 1024)
    private String remark;
}
