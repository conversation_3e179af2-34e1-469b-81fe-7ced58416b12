package com.genlight.epilcure.service.config.service;

import com.genlight.epilcure.api.config.pojo.dto.DiagnosisDTO;
import com.genlight.epilcure.api.config.pojo.vo.DiagnosisVO;
import com.genlight.epilcure.service.config.constants.CacheConstants;
import com.genlight.epilcure.service.config.dao.entity.Diagnosis;
import com.genlight.epilcure.service.config.dao.repository.DiagnosisRepository;
import com.genlight.epilcure.service.config.pojo.convert.DiagnosisConvert;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.annotation.Caching;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/6/11 15:55
 * @Version 1.0.0
 **/
@Service
@CacheConfig(cacheNames = CacheConstants.CACHE_NAMES_DIAGNOSIS)
public class DiagnosisService extends MedicalRecordBaseService<Diagnosis, DiagnosisVO, DiagnosisDTO, DiagnosisRepository, DiagnosisConvert> {
    @Cacheable(cacheNames = CacheConstants.CACHE_NAMES_DIAGNOSIS_FINDS)
    @Override
    public List<DiagnosisVO> finds(DiagnosisDTO dto) {
        return super.finds(dto);
    }

    @CacheEvict(cacheNames = CacheConstants.CACHE_NAMES_DIAGNOSIS_FINDS, allEntries = true)
    @Caching(evict = {
            @CacheEvict(cacheNames = CacheConstants.CACHE_NAMES_DIAGNOSIS_FINDS, allEntries = true),
            @CacheEvict(cacheNames = CacheConstants.CACHE_NAMES_DIAGNOSIS, key = "#p0")
    })
    @Override
    public DiagnosisVO update(Long aLong, DiagnosisDTO dto) {
        return super.update(aLong, dto);
    }

    @CacheEvict(cacheNames = CacheConstants.CACHE_NAMES_DIAGNOSIS_FINDS, allEntries = true)
    @Override
    public DiagnosisVO add(DiagnosisDTO dto) {
        return super.add(dto);
    }
}
