package com.genlight.epilcure.service.patient.service;

import com.genlight.epilcure.api.config.feign.IDiseaseController;
import com.genlight.epilcure.common.core.service.BaseService;
import com.genlight.epilcure.service.patient.dao.entity.ArchivesNo;
import com.genlight.epilcure.service.patient.dao.repository.ArchivesNoRepository;
import jakarta.annotation.Resource;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

@Service
public class ArchivesNoService extends BaseService<ArchivesNo, Long, ArchivesNoRepository> {
    @Resource
    private RedissonClient redissonClient;

    @Resource
    private IDiseaseController iDiseaseController;

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public Integer getEpilcureNo() {
        RLock lock = redissonClient.getLock("EpilcureNo");
        try {
            if (lock.tryLock(2, 2, TimeUnit.SECONDS)) {
                List<ArchivesNo> list = repository.findAll();
                ArchivesNo archivesNo;
                if (list.isEmpty()) {
                    archivesNo = ArchivesNo.builder()
                            .iSBSArchivesNo("A00000")
                            .epilcureArchivesNo(99999)
                            .build();
                } else {
                    archivesNo = list.get(0);
                    if (Objects.isNull(archivesNo.getEpilcureArchivesNo())) {
                        archivesNo.setEpilcureArchivesNo(99999);
                    }
                }
                Integer result = archivesNo.getEpilcureArchivesNo();
                archivesNo.setEpilcureArchivesNo(result - 1);
                repository.saveAndFlush(archivesNo);
                return result;
            } else {
                throw new RuntimeException("服务忙,请稍后再试");
            }
        } catch (Exception ex) {
            throw new RuntimeException(ex);
        } finally {
            this.unLock(lock);
        }
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public String getNo() {

        RLock lock = redissonClient.getLock("ArchivesNo");
        try {
            if (lock.tryLock(2, 2, TimeUnit.SECONDS)) {
                List<ArchivesNo> list = repository.findAll();
                ArchivesNo archivesNo;
                if (list.isEmpty()) {
                    archivesNo = ArchivesNo.builder()
                            .iSBSArchivesNo("A00000")
                            .epilcureArchivesNo(99999)
                            .build();
                } else {
                    archivesNo = list.get(0);
                }
                int num = Integer.parseInt(archivesNo.getISBSArchivesNo().substring(1));
                char s = archivesNo.getISBSArchivesNo().charAt(0);
                int digit = Character.digit(s, 10);
                num++;
                if (num > 99999) {
                    digit++;
                    s = (char) digit;
                    num = 1;
                }
                String result = s + String.format("%05d", num);
                archivesNo.setISBSArchivesNo(result);

                repository.saveAndFlush(archivesNo);
                return result;
            } else {
                throw new RuntimeException("服务忙,请稍后再试");
            }
        } catch (Exception ex) {
            throw new RuntimeException(ex);
        } finally {
            this.unLock(lock);
        }
    }
}
