package com.genlight.epilcure.service.patient.controller;

import com.fasterxml.jackson.annotation.JsonView;
import com.genlight.epilcure.api.patient.pojo.dto.PatientDTO.EpilcureAddPatient;
import com.genlight.epilcure.api.patient.pojo.dto.PatientDeviceDTO;
import com.genlight.epilcure.api.patient.pojo.dto.PatientDeviceDTO.AddDevice;
import com.genlight.epilcure.api.patient.pojo.dto.SurgeryDTO;
import com.genlight.epilcure.api.patient.pojo.vo.SurgeryVO;
import com.genlight.epilcure.common.core.mvc.response.JsonResult;
import com.genlight.epilcure.common.core.pojo.valid.Add;
import com.genlight.epilcure.common.core.pojo.view.SummaryView;
import com.genlight.epilcure.service.patient.service.SurgeryService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

/**
 * <AUTHOR>
 * @Date 2022/6/5 13:15
 * @Version 1.0.0
 **/
@RestController
@RequestMapping("/api/patient/surgery")
@Tag(name = "SurgeryController", description = "手术信息相关接口")
public class SurgeryController {
    @Resource
    private SurgeryService surgeryService;

    @Operation(summary = "新增手术信息")
    @PostMapping(consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @JsonView(SummaryView.class)
    public JsonResult<SurgeryVO> addSurgery(@RequestPart(required = false) MultipartFile position0BeforeEegImage,
                                            @RequestPart(required = false) MultipartFile position1BeforeEegImage,
                                            @RequestPart(required = false) MultipartFile position0EegImage,
                                            @RequestPart(required = false) MultipartFile position1EegImage
            , @Validated({Add.class, EpilcureAddPatient.class}) @RequestPart SurgeryDTO surgeryDTO
            , @Validated(AddDevice.class) @RequestPart(required = false) PatientDeviceDTO patientDeviceDTO) {
        return JsonResult.ok(surgeryService.addSurgery(position0BeforeEegImage, position1BeforeEegImage, position0EegImage, position1EegImage, surgeryDTO, patientDeviceDTO));
    }

    @GetMapping("/{id}")
    @Operation(summary = "根据Id获取手术信息")
    public JsonResult<SurgeryVO> find(@PathVariable Long id) {
        return JsonResult.ok(surgeryService.findSurgeryById(id));
    }

    @GetMapping("/findByPatientId/{patientId}")
    @Operation(summary = "根据患者Id查询有效的手术信息")
    public JsonResult<SurgeryVO> findByPatientId(@PathVariable Long patientId) {
        return JsonResult.ok(surgeryService.findSurgeryByPatientId(patientId));
    }

    @Operation(summary = "修改手术信息")
    @PutMapping("/{id}")
    public JsonResult<SurgeryVO> update(@PathVariable Long id, @RequestBody SurgeryDTO surgeryDTO) {
        return JsonResult.ok(surgeryService.updateSurgery(id, surgeryDTO));
    }

}
