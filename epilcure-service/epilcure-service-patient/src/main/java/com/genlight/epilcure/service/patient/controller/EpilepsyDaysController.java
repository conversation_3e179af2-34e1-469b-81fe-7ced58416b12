package com.genlight.epilcure.service.patient.controller;

import com.fasterxml.jackson.annotation.JsonView;
import com.genlight.epilcure.common.core.mvc.response.JsonResult;
import com.genlight.epilcure.common.core.pojo.valid.Find;
import com.genlight.epilcure.common.core.pojo.valid.Update;
import com.genlight.epilcure.service.patient.pojo.dto.EpilepsyDaysDTO;
import com.genlight.epilcure.service.patient.pojo.vo.EpilepsyDaysVO;
import com.genlight.epilcure.service.patient.service.EpilepsyDaysService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR> maoyan
 * @Date : 2022/9/26 14:36
 * @Version :1.0.0
 */
@RestController
@RequestMapping("/api/patient/epilepsy")
@Tag(name = "EpilepsyDaysController", description = "癫痫发作天数相关接口")
public class EpilepsyDaysController {
    @Resource
    private EpilepsyDaysService epilepsyDaysService;

    @PutMapping("/epilepsyDays")
    @Operation(summary = "通过档案Id修改癫痫发作天数（包括新增功能）")
    @JsonView(Update.class)
    public JsonResult<EpilepsyDaysVO> updateSeizures(@Validated(Update.class) @RequestBody @JsonView(Update.class) EpilepsyDaysDTO epilepsyDaysDTO) {
        return JsonResult.ok(epilepsyDaysService.updateEpilepsyStatistics(epilepsyDaysDTO));
    }

    @GetMapping("{id}/epilepsyDays")
    @Operation(summary = "通过档案Id查找对应的癫痫发作天数")
    @JsonView(Find.class)
    public JsonResult<EpilepsyDaysVO> findSeizures(@PathVariable Long id) {
        return JsonResult.ok(epilepsyDaysService.findEpilepsyStatistics(id));
    }
}