package com.genlight.epilcure.service.patient.service;

import com.genlight.epilcure.service.patient.dao.entity.EpilepsyFrequency;
import com.genlight.epilcure.service.patient.dao.repository.EpilepsyFrequencyRepository;
import com.genlight.epilcure.service.patient.pojo.convert.EpilepsyFrequencyConvert;
import com.genlight.epilcure.service.patient.pojo.dto.EpilepsyFrequencyDTO;
import com.genlight.epilcure.service.patient.pojo.vo.EpilepsyFrenquencyVO;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR> maoyan
 * @Date : 2022/9/26 14:16
 * @Version :1.0.0
 */
@Service
public class EpilepsyFrequencyService extends EpilepsyService<EpilepsyFrequency, EpilepsyFrenquencyVO, EpilepsyFrequencyDTO, EpilepsyFrequencyRepository, EpilepsyFrequencyConvert> {
}