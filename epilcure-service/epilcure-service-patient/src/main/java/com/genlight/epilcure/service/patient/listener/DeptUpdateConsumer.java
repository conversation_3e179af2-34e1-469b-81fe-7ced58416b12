package com.genlight.epilcure.service.patient.listener;

import com.genlight.epilcure.api.user.pojo.bo.DeptBO;
import com.genlight.epilcure.common.rocketmq.RocketMqConstant;
import com.genlight.epilcure.service.patient.service.ArchivesService;
import com.genlight.epilcure.service.patient.service.PatientSurgerySchemeService;
import jakarta.annotation.Resource;
import org.apache.rocketmq.spring.annotation.MessageModel;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@RocketMQMessageListener(
        topic = RocketMqConstant.DEPT_NAME_UPDATE_TOPIC,
        consumerGroup = RocketMqConstant.DEPT_NAME_UPDATE_TOPIC,
        messageModel = MessageModel.CLUSTERING
)
public class DeptUpdateConsumer implements RocketMQListener<DeptBO> {

    @Autowired
    private ArchivesService archivesService;

    @Resource
    private PatientSurgerySchemeService patientSurgerySchemeService;

    @Override
    public void onMessage(DeptBO message) {
        archivesService.updateDeptName(message);
        patientSurgerySchemeService.updateDeptName(message);
    }
}
