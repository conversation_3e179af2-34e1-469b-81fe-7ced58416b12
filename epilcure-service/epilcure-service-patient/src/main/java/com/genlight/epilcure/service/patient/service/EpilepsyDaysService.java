package com.genlight.epilcure.service.patient.service;

import com.genlight.epilcure.service.patient.dao.entity.EpilepsyDays;
import com.genlight.epilcure.service.patient.dao.repository.EpilepsyDaysRepository;
import com.genlight.epilcure.service.patient.pojo.convert.EpilepsyDaysConvert;
import com.genlight.epilcure.service.patient.pojo.dto.EpilepsyDaysDTO;
import com.genlight.epilcure.service.patient.pojo.vo.EpilepsyDaysVO;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR> maoyan
 * @Date : 2022/9/26 14:23
 * @Version :1.0.0
 */
@Service
public class EpilepsyDaysService extends EpilepsyService<EpilepsyDays, EpilepsyDaysVO, EpilepsyDaysDTO, EpilepsyDaysRepository, EpilepsyDaysConvert> {
}