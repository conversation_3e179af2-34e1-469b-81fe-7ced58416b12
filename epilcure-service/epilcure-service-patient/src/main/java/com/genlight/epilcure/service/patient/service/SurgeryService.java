package com.genlight.epilcure.service.patient.service;

import com.genlight.epilcure.api.device.feign.IDeviceController;
import com.genlight.epilcure.api.device.feign.IImpedanceLogController;
import com.genlight.epilcure.api.device.pojo.bo.DeviceBO;
import com.genlight.epilcure.api.device.pojo.vo.BppVO;
import com.genlight.epilcure.api.patient.enums.ArchivesStatus;
import com.genlight.epilcure.api.patient.pojo.dto.*;
import com.genlight.epilcure.api.patient.pojo.vo.*;
import com.genlight.epilcure.common.core.constants.HttpCode;
import com.genlight.epilcure.common.core.dao.enums.Status;
import com.genlight.epilcure.common.core.service.BaseService;
import com.genlight.epilcure.common.core.service.exception.ArgsException;
import com.genlight.epilcure.common.core.service.exception.ServiceException;
import com.genlight.epilcure.common.core.util.MinioUtils;
import com.genlight.epilcure.service.patient.constants.CacheConstants;
import com.genlight.epilcure.service.patient.constants.FileConstants;
import com.genlight.epilcure.service.patient.dao.entity.Patient;
import com.genlight.epilcure.service.patient.dao.entity.PatientSurgeryScheme;
import com.genlight.epilcure.service.patient.dao.entity.Surgery;
import com.genlight.epilcure.service.patient.dao.repository.SurgeryRepository;
import com.genlight.epilcure.service.patient.pojo.convert.SurgeryConvert;
import io.seata.spring.annotation.GlobalTransactional;
import jakarta.annotation.Resource;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.annotation.Caching;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @Date 2022/6/3 17:41
 * @Descript 手术服务类
 * @Version 1.0.0
 **/
@Service
@CacheConfig(cacheNames = CacheConstants.CACHE_NAMES_SURGERY)
public class SurgeryService extends BaseService<Surgery, Long, SurgeryRepository> {
    @Resource
    private SurgeryConvert surgeryConvert;

    @Resource
    private PatientService patientService;

    @Resource
    private PatientDeviceService patientDeviceService;

    @Resource
    private PatientSurgerySchemeService patientSurgerySchemeService;

    @Resource
    private ArchivesService archivesService;

    @Resource
    private MinioUtils minioUtils;

    @Resource
    private RedissonClient redissonClient;

    @Resource
    private IDeviceController iDeviceController;

    @Resource
    private IImpedanceLogController iImpedanceLogController;

    @Caching(evict = {
            @CacheEvict(cacheNames = CacheConstants.CACHE_NAMES_SURGERY_PAGE, allEntries = true)
    })
    @Transactional
    @GlobalTransactional(rollbackFor = Exception.class)
    public SurgeryVO addSurgery(MultipartFile position0BeforeEegImage
            , MultipartFile position1BeforeEegImage
            , MultipartFile position0EegImage
            , MultipartFile position1EegImage
            , SurgeryDTO surgeryDTO
            , PatientDeviceDTO patientDeviceDTO) {
        RLock lock = redissonClient.getLock("AddSurgery%s".formatted(surgeryDTO.getPatientId()));
        try {
            if (lock.tryLock(2, 2, TimeUnit.SECONDS)) {
                if (Objects.nonNull(patientDeviceDTO)) {
                    patientDeviceDTO.setSurgeryDate(null); // 防止重复添加手术信息
                    patientDeviceService.addDevice(patientDeviceDTO);
                }

                if (!patientService.existsById(surgeryDTO.getPatientId())) {
                    throw new ArgsException("患者[{0}]不存在", surgeryDTO.getPatientId());
                }


                if (position0EegImage != null) {
                    Optional<SurgeryTouchPointDTO> optional = surgeryDTO.getSurgeryTouchPoints().stream().filter(tp -> !tp.getBefore() && tp.getPosition() == 0).findFirst();
                    if (optional.isEmpty()) {
                        throw new ArgsException("缝合后触点[{0}]检测信息不存在", 0);
                    }
                    SurgeryTouchPointDTO surgeryTouchPointDTO = optional.get();
                    surgeryTouchPointDTO.setEegFile(minioUtils.upload(FileConstants.SurgeryEegDIR, position0EegImage));
                }

                if (position1EegImage != null) {
                    Optional<SurgeryTouchPointDTO> optional = surgeryDTO.getSurgeryTouchPoints().stream().filter(tp -> !tp.getBefore() && tp.getPosition() == 1).findFirst();
                    if (optional.isEmpty()) {
                        throw new ArgsException("缝合后触点[{0}]检测信息不存在", 1);
                    }
                    SurgeryTouchPointDTO surgeryTouchPointDTO = optional.get();
                    surgeryTouchPointDTO.setEegFile(minioUtils.upload(FileConstants.SurgeryEegDIR, position1EegImage));
                }

                if (position0BeforeEegImage != null) {
                    Optional<SurgeryTouchPointDTO> optional = surgeryDTO.getSurgeryTouchPoints().stream().filter(tp -> tp.getBefore() && tp.getPosition() == 0).findFirst();
                    if (optional.isEmpty()) {
                        throw new ArgsException("缝合后触点[{0}]检测信息不存在", 0);
                    }
                    SurgeryTouchPointDTO surgeryTouchPointDTO = optional.get();
                    surgeryTouchPointDTO.setEegFile(minioUtils.upload(FileConstants.SurgeryEegDIR, position0BeforeEegImage));
                }

                if (position1BeforeEegImage != null) {
                    Optional<SurgeryTouchPointDTO> optional = surgeryDTO.getSurgeryTouchPoints().stream().filter(tp -> tp.getBefore() && tp.getPosition() == 1).findFirst();
                    if (optional.isEmpty()) {
                        throw new ArgsException("缝合后触点[{0}]检测信息不存在", 0);
                    }
                    SurgeryTouchPointDTO surgeryTouchPointDTO = optional.get();
                    surgeryTouchPointDTO.setEegFile(minioUtils.upload(FileConstants.SurgeryEegDIR, position1BeforeEegImage));
                }

                // 检查设备
                PatientDeviceVO patientDeviceVO = patientDeviceService.findByPatientStatus(surgeryDTO.getPatientId(), Status.ADD);
                if (Objects.isNull(patientDeviceVO)) {
                    if (Objects.nonNull(patientDeviceService.findByPatientStatus(surgeryDTO.getPatientId(), Status.ENABLED))) {
                        throw new ServiceException(HttpCode.SURGERY);
                    }
                    throw new ArgsException("患者[{0}]不存在设备信息", surgeryDTO.getPatientId());
                }

                Surgery surgery = surgeryConvert.dto2po(surgeryDTO);
                // 修改档案状态
                ArchivesVO archivesVO = archivesService.findById(surgeryDTO.getArchivesId());
                if (archivesVO.getStatus().equals(ArchivesStatus.Finished)) {
                    throw new ArgsException("该档案[{0}]已经完成手术", surgeryDTO.getArchivesId());
                }

                if (!archivesVO.getPatient().getId().equals(surgeryDTO.getPatientId())) {
                    throw new ArgsException("档案患者与手术患者不符");
                }

                iDeviceController.expireDevice(DeviceBO.builder()
                        .patientId(archivesVO.getPatient().getId())
                        .bcmId(archivesVO.getPatient().getPatientDeviceVO().getBcmId())
                        .bpp(BppVO.builder().id(archivesVO.getPatient().getPatientDeviceVO().getBppId()).build())
                        .electrodeIds(archivesVO.getPatient().getPatientDeviceVO().getSurgeryElectrodes().stream().map(SurgeryElectrodeVO::getElectrodeId).toList())
                        .build());

                // 修改合同为完成状态
                archivesService.updateFinishedArchives(archivesVO.getId(), surgeryDTO.getPatientId(), ArchivesDTO
                        .builder()
                        .orgId(archivesVO.getOrgId())
                        .deptId(archivesVO.getDeptId())
                        .status(ArchivesStatus.Finished)
                        .editStatus(Status.DISABLED)
                        .build());


                // 修改患者设备信息为可用状态
                patientDeviceService.updatePatientDeviceStatus(surgeryDTO.getPatientId());
                // 保存手术信息
                surgery.setPatient(Patient.builder().id(surgeryDTO.getPatientId()).build());
                PatientSurgerySchemeVO patientSurgerySchemeVO = patientSurgerySchemeService.findByArchivesId(surgeryDTO.getArchivesId());
                if (Objects.isNull(patientSurgerySchemeVO)) {
                    throw new ArgsException("患者[{0}]不存在手术计划", surgeryDTO.getPatientId());
                }

                surgery.setPatientSurgeryScheme(PatientSurgeryScheme.builder().id(patientSurgerySchemeVO.getId()).build());

                if (Objects.nonNull(surgery.getSurgeryTouchPoints())) {
                    surgery.getSurgeryTouchPoints().forEach(st -> {

                        if (Objects.isNull(patientDeviceVO.getSurgeryElectrodes())
                                || patientDeviceVO.getSurgeryElectrodes().stream().filter(se -> se.getPosition().equals((int) st.getPosition())).findFirst().isEmpty()) {
                            throw new ArgsException("通道[{0}]不存在设备", st.getPosition());
                        }

                        st.setSurgery(surgery);
                    });
                }
                if (Objects.isNull(surgery.getSurgeryDate())) {
                    surgery.setSurgeryDate(new Date());
                }


                List<Surgery> list = repository.findByPatientId(surgeryDTO.getPatientId());
                if (Objects.nonNull(list)) {
                    list.forEach(s -> s.setStatus(Status.DISABLED));
                }


                SurgeryVO surgeryVO = surgeryConvert.po2vo(repository.save(surgery));

                surgeryVO.setPatientSurgeryScheme(patientSurgerySchemeVO);

                patientService.update(surgeryDTO.getPatientId(), PatientDTO.builder()
                        .surgeryDate(surgery.getSurgeryDate())
                        .build());

                return surgeryVO;
            }
            throw new RuntimeException("服务器忙,请稍后再试");

        } catch (Exception ex) {
            throw new RuntimeException(ex);
        } finally {
            this.unLock(lock);
        }
    }

    /**
     * 根据Id获取手术信息
     *
     * @param id 主键
     * @return 修改后的手术信息View Object
     */
    @Cacheable(key = "#p0")
    @Transactional(readOnly = true)
    public SurgeryVO findSurgeryById(Long id) {
        Optional<Surgery> optional = repository.findById(id);
        if (optional.isEmpty()) {
            throw new ArgsException("手术信息id[{}]不存在", id);
        }
        return surgeryConvert.po2vo(optional.get());
    }

    /**
     * 根据患者id获取最新的手术信息
     *
     * @param id 患者Id
     * @return 手术信息View Object
     */
    @Cacheable(cacheNames = CacheConstants.CACHE_NAMES_SURGERY_PAGE, key = "#p0")
    @Transactional(readOnly = true)
    public SurgeryVO findSurgeryByPatientId(Long id) {

        if (!patientService.existsById(id)) {
            throw new ArgsException("患者Id[{0}]不存在", id);
        }

        Optional<Surgery> optional = repository.findByPatientIdAndStatus(id, Status.ENABLED);
        if (optional.isEmpty()) {
            return SurgeryVO.builder().build();
        }
        return surgeryConvert.po2vo(optional.get());
    }


    @Transactional
    @GlobalTransactional(rollbackFor = Exception.class)
    @Cacheable(cacheNames = CacheConstants.CACHE_NAMES_SURGERY_PAGE, key = "#p0.patientId")
    public void updateImpedance(SurgeryDTO surgeryDTO) {
        SurgeryVO surgeryVO = findSurgeryByPatientId(surgeryDTO.getPatientId());
        if (Objects.nonNull(surgeryVO.getId()))
            updateSurgery(surgeryVO.getId(), surgeryDTO);
    }

    @Caching(evict = {
            @CacheEvict(cacheNames = CacheConstants.CACHE_NAMES_SURGERY_PAGE, allEntries = true),
            @CacheEvict(key = "#p0")
    })
    @Transactional
    public SurgeryVO updateSurgery(Long id, SurgeryDTO surgeryDTO) {
        Optional<Surgery> optional = repository.findById(id);
        if (optional.isEmpty()) {
            throw new ArgsException("修改的手术Id[{0}]不存在", id);
        }
        Surgery surgery = optional.get();
        surgeryConvert.dto2po(surgeryDTO, surgery);
        return surgeryConvert.po2vo(repository.save(surgery));
    }
}
