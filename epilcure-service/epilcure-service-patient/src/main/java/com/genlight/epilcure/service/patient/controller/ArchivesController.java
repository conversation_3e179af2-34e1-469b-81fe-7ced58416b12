package com.genlight.epilcure.service.patient.controller;

import com.fasterxml.jackson.annotation.JsonView;
import com.genlight.epilcure.api.patient.enums.ArchivesStatus;
import com.genlight.epilcure.api.patient.pojo.bo.ArchivesUpdateBO;
import com.genlight.epilcure.api.patient.pojo.dto.*;
import com.genlight.epilcure.api.patient.pojo.dto.PatientDTO.EpilcureAddPatient;
import com.genlight.epilcure.api.patient.pojo.vo.ArchivesVO;
import com.genlight.epilcure.common.core.mvc.annotation.MultiRequestBody;
import com.genlight.epilcure.common.core.mvc.response.JsonResult;
import com.genlight.epilcure.common.core.pojo.valid.Add;
import com.genlight.epilcure.common.core.pojo.valid.Find;
import com.genlight.epilcure.common.core.pojo.valid.Update;
import com.genlight.epilcure.service.patient.service.ArchivesNoService;
import com.genlight.epilcure.service.patient.service.ArchivesService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/patient/archives")
@Tag(name = "ArchivesController", description = "档案相关接口")
public class ArchivesController {
    @Resource
    private ArchivesService archivesService;

    @Resource
    private ArchivesNoService archivesNoService;

    @PostMapping
    @Operation(summary = "新增档案")
    @JsonView(Add.class)
    public JsonResult<ArchivesVO> add(@Validated(Add.class) @RequestBody @JsonView(Add.class) ArchivesDTO archivesDTO) {
        return JsonResult.ok(archivesService.add(archivesDTO));
    }

    @GetMapping("/{id}")
    @Operation(summary = "根据Id获取档案信息")
    public JsonResult<ArchivesVO> find(@PathVariable Long id) {
        return JsonResult.ok(archivesService.findById(id));
    }

    @PostMapping("/patients")
    @Operation(summary = "新增档案附带患者信息,epilcure专用")
    @JsonView(EpilcureAddPatient.class)
    public JsonResult<ArchivesVO> addAndPatient(@Validated({EpilcureAddPatient.class}) @MultiRequestBody ArchivesDTO archivesDTO
            , @Validated(EpilcureAddPatient.class) @MultiRequestBody PatientDTO patientDTO
            , @Validated(EpilcureAddPatient.class) @MultiRequestBody PatientDeviceDTO patientDeviceDTO
            , @Validated(EpilcureAddPatient.class) @MultiRequestBody PatientSurgerySchemeDTO patientSurgerySchemeDTO
            , @Validated(EpilcureAddPatient.class) @MultiRequestBody MedicalRecordDTO medicalRecordDTO
            , @Validated(EpilcureAddPatient.class) @MultiRequestBody ContractDTO contractDTO) {
        return JsonResult.ok(archivesService.addAndPatient(archivesDTO, patientDTO, patientDeviceDTO, patientSurgerySchemeDTO, medicalRecordDTO, contractDTO));
    }

    @PostMapping("/patients/1.0")
    @Operation(summary = "新增档案附带患者信息,epilcure专用")
    @JsonView(EpilcureAddPatient.class)
    public JsonResult<ArchivesVO> addAndPatient(@Validated({EpilcureAddPatient.class}) @MultiRequestBody ArchivesDTO archivesDTO
            , @Validated(EpilcureAddPatient.class) @MultiRequestBody PatientDTO patientDTO
            , @Validated(EpilcureAddPatient.class) @MultiRequestBody PatientSurgerySchemeDTO patientSurgerySchemeDTO
            , @Validated(EpilcureAddPatient.class) @MultiRequestBody MedicalRecordDTO medicalRecordDTO
            , @Validated(EpilcureAddPatient.class) @MultiRequestBody ContractDTO contractDTO) {
        return JsonResult.ok(archivesService.addAndPatient(archivesDTO, patientDTO, null, patientSurgerySchemeDTO, medicalRecordDTO, contractDTO));
    }

    @GetMapping("/finds")
    @Operation(summary = "根据条件查询档案信息")
    @JsonView(Find.class)
    public JsonResult<Page<ArchivesVO>> finds(@JsonView(Find.class) ArchivesDTO archivesDTO, Pageable pageable) {
        return JsonResult.ok(archivesService.finds(archivesDTO, pageable));
    }

    @GetMapping
    @Operation(summary = "根据档案编号查询")
    public JsonResult<ArchivesVO> findByNo(String archivesNo) {
        return JsonResult.ok(archivesService.findByArchivesNo(archivesNo));
    }

    @PutMapping("/{id}")
    @Operation(summary = "根据Id修改档案信息")
    public JsonResult<ArchivesVO> update(@PathVariable Long id, @RequestBody @JsonView(Update.class) ArchivesDTO archivesDTO) {
        return JsonResult.ok(archivesService.update(id, archivesDTO));
    }

    @GetMapping("/bcmSn/{bcmSn}/{archivesStatus}")
    @Operation(summary = "根据BcmSn查询档案信息")
    public JsonResult<ArchivesVO> findByBcmSn(@PathVariable String bcmSn, @PathVariable ArchivesStatus archivesStatus) {
        return JsonResult.ok(archivesService.findByBcmSn(bcmSn, archivesStatus));
    }

    @PutMapping("/{id}/patientAndContract")
    @Operation(summary = "修改档案以及患者信息")
    public JsonResult<Void> update(@PathVariable Long id, @RequestBody @JsonView(Update.class) ArchivesUpdateBO archivesUpdateBO) {
        archivesService.update(id, archivesUpdateBO);
        return JsonResult.ok();
    }
}
