package com.genlight.epilcure.service.patient.listener;

import com.genlight.epilcure.common.rocketmq.RocketMqConstant;
import com.genlight.epilcure.common.rocketmq.pojo.bo.AddSchemeBO;
import com.genlight.epilcure.service.patient.service.PatientService;
import jakarta.annotation.Resource;
import org.apache.rocketmq.spring.annotation.MessageModel;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.stereotype.Component;

@Component
@RocketMQMessageListener(
        topic = "${scheme.schemeUpdate.topic}",
        consumerGroup = "${scheme.schemeUpdate.group}",
        messageModel = MessageModel.CLUSTERING
)
public class SchemeUpdateConsumer implements RocketMQListener<AddSchemeBO> {
    @Resource
    private PatientService patientService;

    @Override
    public void onMessage(AddSchemeBO message) {
        patientService.updateSchemeDate(message);
    }
}
