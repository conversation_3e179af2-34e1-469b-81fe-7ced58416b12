package com.genlight.epilcure.service.patient.controller;

import com.fasterxml.jackson.annotation.JsonView;
import com.genlight.epilcure.api.patient.pojo.dto.ContractDTO;
import com.genlight.epilcure.api.patient.pojo.vo.ContractVO;
import com.genlight.epilcure.common.core.mvc.response.JsonResult;
import com.genlight.epilcure.common.core.pojo.valid.Add;
import com.genlight.epilcure.service.patient.service.ContractService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @Date 2022/7/2 13:53
 * @Version 1.0.0
 **/
@RestController
@RequestMapping("/api/patient/contracts")
@Tag(name = "ContractController", description = "联系人相关接口")
public class ContractController {
    @Resource
    private ContractService contractService;

    @PostMapping
    @Operation(summary = "新增联系人信息")
    @JsonView(Add.class)
    public JsonResult<ContractVO> add(@Validated(Add.class) @RequestBody ContractDTO contractDTO) {
        return JsonResult.ok(contractService.addContract(contractDTO));
    }

//    @DeleteMapping("/{id}")
//    @Operation(summary = "删除合同信息")
//    public JsonResult<String> delete(@PathVariable Long id) {
//        contractService.
//        return JsonResult.ok("删除成功");
//    }

    @GetMapping("/{id}")
    @Operation(summary = "根据Id获取联系人信息")
    public JsonResult<ContractVO> find(@PathVariable Long id) {
        return JsonResult.ok(contractService.getContractById(id));
    }

    @GetMapping("/patients/{id}")
    @Operation(summary = "根据患者id获取联系人信息")
    public JsonResult<ContractVO> findByPatientId(@PathVariable Long id) {
        return JsonResult.ok(contractService.getContractByPatientId(id));
    }
}
