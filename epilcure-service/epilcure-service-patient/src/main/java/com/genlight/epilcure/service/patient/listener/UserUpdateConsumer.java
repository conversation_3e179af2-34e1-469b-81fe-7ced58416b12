package com.genlight.epilcure.service.patient.listener;

import com.genlight.epilcure.api.user.pojo.bo.UserBO;
import com.genlight.epilcure.common.rocketmq.RocketMqConstant;
import com.genlight.epilcure.service.patient.service.PatientSurgerySchemeService;
import jakarta.annotation.Resource;
import org.apache.rocketmq.spring.annotation.MessageModel;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.stereotype.Component;

@Component
@RocketMQMessageListener(
        topic = RocketMqConstant.USER_NAME_UPDATE_TOPIC,
        consumerGroup = RocketMqConstant.USER_NAME_UPDATE_PATIENT_GROUP,
        messageModel = MessageModel.CLUSTERING
)
public class UserUpdateConsumer implements RocketMQListener<UserBO> {
    @Resource
    private PatientSurgerySchemeService patientSurgerySchemeService;

    @Override
    public void onMessage(UserBO message) {
        patientSurgerySchemeService.updateSurgeryUser(message);
    }
}
