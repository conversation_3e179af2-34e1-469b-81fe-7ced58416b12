package com.genlight.epilcure.service.patient.controller;

import com.genlight.epilcure.api.patient.pojo.dto.TestDTO;
import com.genlight.epilcure.api.patient.pojo.vo.TestVO;
import com.genlight.epilcure.common.core.mvc.response.JsonResult;
import com.genlight.epilcure.common.core.pojo.valid.Add;
import com.genlight.epilcure.common.core.pojo.valid.Update;
import com.genlight.epilcure.service.patient.service.TestService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 */
@RestController
@RequestMapping("/api/patient/tests")
@Tag(name = "PatientTestController", description = "测试名单相关接口")
public class PatientTestController {

    @Resource
    private TestService testService;

    @GetMapping("/{id}")
    @Operation(summary = "根据ID查询测试名单")
    public JsonResult<TestVO> findTestById(@PathVariable Long id) {
        return JsonResult.ok(testService.findTestById(id));
    }

    @GetMapping("/users/{id}")
    @Operation(summary = "查询指定患者的存在的测试名单")
    public JsonResult<Page<TestVO>> findHasRolesByPatient(@PathVariable Long id, TestDTO testDTO, Pageable pageable) {
        return JsonResult.ok(testService.findTestsByPageable(testDTO.setPatientId(id).setPatientEquals(true), pageable));
    }

    @GetMapping("/users/not/{id}")
    @Operation(summary = "查询指定患者的未存在的测试名单")
    public JsonResult<Page<TestVO>> findHasNotRolesByPatient(@PathVariable Long id, TestDTO testDTO, Pageable pageable) {
        return JsonResult.ok(testService.findTestsByPageable(testDTO.setPatientId(id).setPatientEquals(false), pageable));
    }

    @GetMapping
    @Operation(summary = "根据动态条件查询测试名单")
    public JsonResult<Page<TestVO>> findTestsByPageable(TestDTO testDTO, Pageable pageable) {
        return JsonResult.ok(testService.findTestsByPageable(testDTO, pageable));
    }

    @PostMapping
    @Operation(summary = "新增测试名单")
    public JsonResult<TestVO> addTest(@Validated(Add.class) @RequestBody TestDTO testDTO) {
        return JsonResult.ok(testService.addTest(testDTO));
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "删除测试名单")
    public JsonResult<Void> deleteTest(@PathVariable Long id) {
        testService.deleteTest(id);
        return JsonResult.ok();
    }

    @PutMapping("/{id}")
    @Operation(summary = "修改测试名单")
    public JsonResult<TestVO> updateTest(@PathVariable Long id, @Validated(Update.class) @RequestBody TestDTO testDTO) {
        return JsonResult.ok(testService.updateTest(id, testDTO));
    }

    @PutMapping("/enabled")
    @Operation(summary = "批量启用测试名单")
    public JsonResult<Void> enabledTests(@RequestBody List<Long> ids) {
        testService.enabledTests(ids);
        return JsonResult.ok();
    }

    @PutMapping("/disabled")
    @Operation(summary = "批量禁用测试名单")
    public JsonResult<TestVO> disabledTests(@RequestBody List<Long> ids) {
        testService.disabledTests(ids);
        return JsonResult.ok();
    }

    @PutMapping("/enabled/{id}")
    @Operation(summary = "启用测试名单")
    public JsonResult<TestVO> enabledTest(@PathVariable Long id) {
        return JsonResult.ok(testService.enabledTest(id));
    }

    @PutMapping("/disabled/{id}")
    @Operation(summary = "禁用测试名单")
    public JsonResult<TestVO> disabledTest(@PathVariable Long id) {
        return JsonResult.ok(testService.disabledTest(id));
    }

    @PutMapping("/{id}/patients/associated")
    @Operation(summary = "关联患者")
    public JsonResult<Void> associatedPatients(@PathVariable Long id, @RequestBody List<Long> patientIds) {
        testService.associatedPatients(id, patientIds);
        return JsonResult.ok();
    }

    @PutMapping("/{id}/patients/dissociated")
    @Operation(summary = "解除患者关联")
    public JsonResult<Void> dissociatedPatients(@PathVariable Long id, @RequestBody List<Long> patientIds) {
        testService.dissociatedPatients(id, patientIds);
        return JsonResult.ok();
    }
}
