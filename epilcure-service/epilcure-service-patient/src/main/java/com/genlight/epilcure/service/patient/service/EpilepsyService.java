package com.genlight.epilcure.service.patient.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.type.TypeFactory;
import com.genlight.epilcure.common.core.pojo.convert.BaseConvert;
import com.genlight.epilcure.common.core.service.BaseService;
import com.genlight.epilcure.service.patient.dao.entity.EpilepsyStatisticsBase;
import com.genlight.epilcure.service.patient.dao.repository.EpilepsyBaseRepository;
import com.genlight.epilcure.service.patient.pojo.dto.EpilepsyStatisticsDTO;
import com.genlight.epilcure.service.patient.pojo.vo.EpilepsyStatisticsBaseVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR> maoyan
 * @Date : 2022/9/26 13:42
 * @Version :1.0.0
 */
@Slf4j
public class EpilepsyService<Po extends EpilepsyStatisticsBase,
        Vo extends EpilepsyStatisticsBaseVO,
        Dto extends EpilepsyStatisticsDTO,
        Repository extends EpilepsyBaseRepository<Po, Long>,
        Convert extends BaseConvert<Po, Vo, Dto>> extends BaseService<Po, Long, Repository> {
    @Autowired
    protected Repository epilepsyRepository;
    @Autowired
    protected Convert convert;
    @Autowired
    private ArchivesService archivesService;
    @Autowired
    protected ObjectMapper objectMapper;

    @Transactional
    public Vo updateEpilepsyStatistics(Dto dto) {
        archivesService.getArchivesById(dto.getArchivesId());
        Optional<Po> poOptional = repository.findByArchivesId(dto.getArchivesId());
        Po po;
        if (poOptional.isEmpty()) {
            //新增记录
            po = convert.dto2po(dto);
        } else {
            //更新记录
            po = poOptional.get();
        }
        po.setArchives(archivesService.getArchivesById(dto.getArchivesId()));
        try {
            po.setEpilepsyStatisticsListString(objectMapper.writeValueAsString(dto.getEpilepsyStatisticsList()));
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
        Vo vo = convert.po2vo(repository.saveAndFlush(po));
        try {
            vo.setEpilepsyStatisticsList(objectMapper.readValue(po.getEpilepsyStatisticsListString(), TypeFactory.defaultInstance().constructCollectionType(List.class, Integer.class)));
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
        return vo;
    }

    @Transactional
    public Vo findEpilepsyStatistics(Long id) {
        archivesService.getArchivesById(id);
        Optional<Po> poOptional = repository.findByArchivesId(id);
        if (poOptional.isEmpty()) {
            return null;
        } else {
            Po po = poOptional.get();
            Vo vo = convert.po2vo(po);
            try {
                vo.setEpilepsyStatisticsList(objectMapper.readValue(po.getEpilepsyStatisticsListString(), TypeFactory.defaultInstance().constructCollectionType(List.class, Integer.class)));
            } catch (JsonProcessingException e) {
                throw new RuntimeException(e);
            }
            return vo;
        }
    }
}
