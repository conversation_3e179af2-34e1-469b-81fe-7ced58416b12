package com.genlight.epilcure.service.patient.controller;

import com.fasterxml.jackson.annotation.JsonView;
import com.genlight.epilcure.common.core.mvc.response.JsonResult;
import com.genlight.epilcure.common.core.pojo.valid.Add;
import com.genlight.epilcure.common.core.pojo.valid.Find;
import com.genlight.epilcure.common.core.pojo.valid.Update;
import com.genlight.epilcure.common.core.pojo.view.BasicView;
import com.genlight.epilcure.service.patient.pojo.dto.PatientCollectDTO;
import com.genlight.epilcure.service.patient.pojo.vo.PatientCollectVO;
import com.genlight.epilcure.service.patient.service.PatientCollectService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR> maoyan
 * @Date : 2022/7/26 11:46
 * @Version :1.0.0
 */
@RestController
@RequestMapping("/api/patient/patient_collect")
@Tag(name = "PatientCollectController", description = "用户报告收藏相关接口")
public class PatientCollectController {
    @Resource
    private PatientCollectService patientCollectService;

    @PostMapping
    @Operation(summary = "添加收藏")
    @JsonView(Add.class)
    public JsonResult<PatientCollectVO> addCollects(@JsonView(Add.class) @Valid @RequestBody PatientCollectDTO collectDTO) {
        return JsonResult.ok(patientCollectService.addCollect(collectDTO));
    }

    @PutMapping("/{id}")
    @Operation(summary = "根据收藏id修改收藏")
    @JsonView(Update.class)
    public JsonResult<PatientCollectVO> updateCollects(@PathVariable Long id, @JsonView(Update.class) PatientCollectDTO collectDTO) {
        return JsonResult.ok(patientCollectService.updateCollect(id, collectDTO));
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "根据收藏id删除收藏")
    @JsonView(BasicView.class)
    public JsonResult<String> deleteCollects(@PathVariable Long id) {
        patientCollectService.deleteCollect(id);
        return JsonResult.ok("删除成功");
    }

    @GetMapping
    @Operation(summary = "获取本用户收藏的患者报告")
    @JsonView(Find.class)
    public JsonResult<Page<PatientCollectVO>> findCollects(@JsonView(Find.class) PatientCollectDTO collectDTO, Pageable pageable) {
        return JsonResult.ok(patientCollectService.findCollectByPageable(collectDTO, pageable));
    }
}
