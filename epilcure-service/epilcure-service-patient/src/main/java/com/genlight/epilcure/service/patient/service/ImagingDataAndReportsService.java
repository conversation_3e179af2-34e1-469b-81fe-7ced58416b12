package com.genlight.epilcure.service.patient.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.genlight.epilcure.common.core.service.BaseService;
import com.genlight.epilcure.common.core.service.exception.ServiceException;
import com.genlight.epilcure.common.core.util.MinioUtils;
import com.genlight.epilcure.service.patient.constants.FileConstants;
import com.genlight.epilcure.service.patient.dao.entity.Archives;
import com.genlight.epilcure.service.patient.dao.entity.ImagingDataAndReports;
import com.genlight.epilcure.service.patient.dao.repository.ImagingDataAndReportsRepository;
import com.genlight.epilcure.service.patient.pojo.convert.ImagingDataAndReportsConvert;
import com.genlight.epilcure.service.patient.pojo.dto.ImagingDataAndReportsDTO;
import com.genlight.epilcure.service.patient.pojo.vo.ImagingDataAndReportsVO;
import jakarta.annotation.Resource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR> maoyan
 * @Date : 2022/8/17 16:45
 * @Version :1.0.0
 */
@Service
public class ImagingDataAndReportsService extends BaseService<ImagingDataAndReports, Long, ImagingDataAndReportsRepository> {
    @Autowired
    private MinioUtils minioUtils;
    @Resource
    private ImagingDataAndReportsConvert imagingDataAndReportsConvert;
    @Resource
    private ObjectMapper objectMapper;
    @Resource
    private ArchivesService archivesService;

    @Transactional
    public ImagingDataAndReportsVO deleteUrlByArchivesId(Long id, ImagingDataAndReportsDTO imagingDataAndReportsDTO) {
        ImagingDataAndReports imagingDataAndReports;
        Optional<ImagingDataAndReports> imagingDataAndReportsOptional;
        if (!repository.existsById(id)) {
            throw new ServiceException("档案[{0}]的影像学资料不存在！", imagingDataAndReportsDTO.getArchivesId());
        } else {
            imagingDataAndReportsOptional = repository.findById(id);
        }
        imagingDataAndReports = imagingDataAndReportsOptional.get();
        if (!imagingDataAndReportsDTO.getArchivesId().equals(imagingDataAndReports.getArchives().getId())) {
            throw new ServiceException("档案Id错误！");
        }
        imagingDataAndReportsConvert.dto2po(imagingDataAndReportsDTO, imagingDataAndReports);
        return imagingDataAndReportsConvert.po2vo(repository.saveAndFlush(imagingDataAndReports));
    }

    @Transactional
    public ImagingDataAndReportsVO findImagingDataAndReportsByArchivesId(ImagingDataAndReportsDTO imagingDataAndReportsDTO) {
        ImagingDataAndReports imagingDataAndReports;
        repository.existsByArchivesId(imagingDataAndReportsDTO.getArchivesId());
        if (!repository.existsByArchivesId(imagingDataAndReportsDTO.getArchivesId())) {
            imagingDataAndReports = ImagingDataAndReports.builder().build();
            Archives archives = archivesService.getArchivesById(imagingDataAndReportsDTO.getArchivesId());
            imagingDataAndReports.setArchives(archives);
            imagingDataAndReports.setPicturesOfImagingDataList(new ArrayList<>());
            imagingDataAndReports.setPicturesOfReportList(new ArrayList<>());
            imagingDataAndReports.setPicturesOfEegList(new ArrayList<>());
        } else {
            Optional<ImagingDataAndReports> optional = repository.findByArchivesId(imagingDataAndReportsDTO.getArchivesId());
            imagingDataAndReports = optional.get();
        }
        ImagingDataAndReports reports = repository.saveAndFlush(imagingDataAndReports);
        ImagingDataAndReportsVO imagingDataAndReportsVO = imagingDataAndReportsConvert.po2vo(reports);
        return imagingDataAndReportsVO;
    }

    @Transactional
    public ImagingDataAndReportsVO addImagingDataAndReports(Long archivesId, MultipartFile picturesOfImagingData, MultipartFile picturesOfReports, MultipartFile picturesOfEeg) {
        ImagingDataAndReports imagingDataAndReports;
        Optional<ImagingDataAndReports> optional = repository.findByArchivesId(archivesId);
        if (!optional.isEmpty()) {
            imagingDataAndReports = optional.get();
        } else {
//           throw new ServiceException("档案[{0}]的影像学资料不存在！", archivesId);
            imagingDataAndReports = ImagingDataAndReports.builder().build();
            imagingDataAndReports.setArchives(archivesService.getArchivesById(archivesId));
            imagingDataAndReports.setPicturesOfImagingDataList(new ArrayList<>());
            imagingDataAndReports.setPicturesOfReportList(new ArrayList<>());
            imagingDataAndReports.setPicturesOfEegList(new ArrayList<>());
        }
        if (Objects.nonNull(picturesOfImagingData)) {
            imagingDataAndReports.setPicturesOfImagingDataList(addImages(FileConstants.IMAGINGDATAANDREPORTSDTO_PICTURESOFIMAGINGDATA, imagingDataAndReports.getPicturesOfImagingDataList(), picturesOfImagingData));
        }
        if (Objects.nonNull(picturesOfReports)) {
            imagingDataAndReports.setPicturesOfReportList(addImages(FileConstants.IMAGINGDATAANDREPORTSDTO_RICTURESOFREPORTS, imagingDataAndReports.getPicturesOfReportList(), picturesOfReports));
        }
        if (Objects.nonNull(picturesOfEeg)) {
            imagingDataAndReports.setPicturesOfEegList(addImages(FileConstants.IMAGINGDATAANDREPORTSDTO_PICTURESOFEEG, imagingDataAndReports.getPicturesOfEegList(), picturesOfEeg));
        }
        ImagingDataAndReports reports = repository.saveAndFlush(imagingDataAndReports);
        ImagingDataAndReportsVO imagingDataAndReportsVO = imagingDataAndReportsConvert.po2vo(reports);
        return imagingDataAndReportsVO;
    }

    private List<String> addImages(String url, List<String> pathsList, MultipartFile file) {
        String uploadPath = minioUtils.upload(url, file);
        for (String p : pathsList) {
            if (p.equals(uploadPath)) {
                throw new ServiceException("该张图片名称已经存在！");
            }
        }
        pathsList.add(uploadPath);
        return pathsList;
    }
}
