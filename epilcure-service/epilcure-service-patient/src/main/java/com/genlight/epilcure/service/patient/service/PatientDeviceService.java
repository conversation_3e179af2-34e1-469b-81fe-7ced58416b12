package com.genlight.epilcure.service.patient.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.type.TypeFactory;
import com.genlight.epilcure.api.config.feign.IConfigController;
import com.genlight.epilcure.api.config.pojo.vo.ConfigsVO;
import com.genlight.epilcure.api.device.enums.DeviceStatus;
import com.genlight.epilcure.api.device.feign.*;
import com.genlight.epilcure.api.device.pojo.bo.DeviceBO;
import com.genlight.epilcure.api.device.pojo.dto.BcmDTO;
import com.genlight.epilcure.api.device.pojo.dto.ElectrodeDTO;
import com.genlight.epilcure.api.device.pojo.vo.*;
import com.genlight.epilcure.api.patient.enums.ArchivesStatus;
import com.genlight.epilcure.api.patient.pojo.dto.ContractElectrodeModelDTO;
import com.genlight.epilcure.api.patient.pojo.dto.PatientDeviceDTO;
import com.genlight.epilcure.api.patient.pojo.dto.SurgeryDTO;
import com.genlight.epilcure.api.patient.pojo.dto.SurgeryElectrodeDTO;
import com.genlight.epilcure.api.patient.pojo.vo.ArchivesVO;
import com.genlight.epilcure.api.patient.pojo.vo.PatientDeviceVO;
import com.genlight.epilcure.common.core.constants.HttpCode;
import com.genlight.epilcure.common.core.dao.enums.Status;
import com.genlight.epilcure.common.core.service.BaseService;
import com.genlight.epilcure.common.core.service.exception.ArgsException;
import com.genlight.epilcure.common.core.service.exception.ServiceException;
import com.genlight.epilcure.service.patient.constants.CacheConstants;
import com.genlight.epilcure.service.patient.constants.PatientConstants;
import com.genlight.epilcure.service.patient.dao.entity.ContractElectrodeModel;
import com.genlight.epilcure.service.patient.dao.entity.Patient;
import com.genlight.epilcure.service.patient.dao.entity.PatientDevice;
import com.genlight.epilcure.service.patient.dao.entity.SurgeryElectrode;
import com.genlight.epilcure.service.patient.dao.repository.PatientDeviceRepository;
import com.genlight.epilcure.service.patient.pojo.convert.PatientDeviceConvert;
import com.genlight.epilcure.service.patient.pojo.convert.SurgeryElectrodeConvert;
import com.genlight.epilcure.service.patient.pojo.vo.CheckDeviceVO;
import feign.codec.DecodeException;
import io.seata.spring.annotation.GlobalTransactional;
import jakarta.annotation.Nullable;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.annotation.Caching;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
@CacheConfig(cacheNames = CacheConstants.CACHE_NAMES_PATIENT_DEVICE)
public class PatientDeviceService extends BaseService<PatientDevice, Long, PatientDeviceRepository> {

    @Resource
    private PatientDeviceConvert patientDeviceConvert;

    @Resource
    private SurgeryElectrodeConvert surgeryElectrodeConvert;
    @Resource
    private PatientService patientService;

    @Resource
    private SurgeryService surgeryService;

    @Resource
    private ArchivesService archivesService;

    @Resource
    private IBcmModelController iBcmModelController;
    @Resource
    private IBcmController iBcmController;

    @Resource
    private IBppModelController iBppModelController;
    @Resource
    private IBppController iBppController;

    @Resource
    private IElectrodeModelController iElectrodeModelController;

    @Resource
    private IElectrodeController iElectrodeController;

    @Resource
    private IConfigController iConfigController;

    @Resource
    private IDeviceController iDeviceController;

    @Resource
    private IImpedanceLogController iImpedanceLogController;

    @Resource
    private ObjectMapper objectMapper;

    @Transactional
    @GlobalTransactional(rollbackFor = Exception.class)
    public PatientDeviceVO addDeviceISBS(PatientDeviceDTO patientDeviceDTO) {
        PatientDeviceVO patientDeviceVO = addElectrodeAndModel(null, patientDeviceDTO);
        patientDeviceVO = addBcmAndModel(patientDeviceVO.getId(), patientDeviceDTO);
        if (Objects.nonNull(patientDeviceDTO.getSurgeryDate())) {
            surgeryService.addSurgery(null, null, null, null, SurgeryDTO.builder()
                    .patientId(patientDeviceDTO.getPatientId())
                    .surgeryDate(patientDeviceDTO.getSurgeryDate())
                    .archivesId(patientDeviceDTO.getArchivesId())
                    .build(), null);
        }
        return patientDeviceVO;
    }

    @Transactional(readOnly = true)
    public List<CheckDeviceVO> checkDeviceISBS(PatientDeviceDTO patientDeviceDTO) {
        ArrayList<CheckDeviceVO> result = new ArrayList<>();
        HashSet<String> hashSet = new HashSet<>();
        patientDeviceDTO.getSurgeryElectrodes().forEach(electrode -> {
            try {
                checkElectrode(patientDeviceDTO, electrode, hashSet);
            } catch (ArgsException argsException) {
                try {
                    if (Objects.nonNull(argsException.getDataStatus())) {
                        HashMap<String, String> hashMap = objectMapper.readValue(argsException.getDataStatus(), TypeFactory.defaultInstance().constructMapType(HashMap.class, String.class, String.class));

                        result.add(CheckDeviceVO.builder()
                                .msg(argsException.getMessage())
                                .httpCode(Objects.nonNull(argsException.getHttpCode()) ? argsException.getHttpCode().getCode() : 0)
                                .sn(electrode.getElectrodeSn())
                                .id(electrode.getElectrodeId())
                                .deviceStatus(Integer.parseInt(hashMap.get("deviceStatus")))
                                .build());
                        TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                    } else {
                        throw new RuntimeException(argsException);
                    }
                } catch (JsonProcessingException e) {
                    throw new RuntimeException(e);
                }
            } catch (DecodeException dec) {
                result.add(CheckDeviceVO.builder()
                        .msg(dec.getMessage())
                        .sn(electrode.getElectrodeSn())
                        .id(electrode.getElectrodeId())
                        .build());
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            }
        });
        try {
            checkBcm(patientDeviceDTO);
        } catch (ArgsException argsException) {
            try {
                HashMap<String, String> hashMap = objectMapper.readValue(argsException.getDataStatus(), TypeFactory.defaultInstance().constructMapType(HashMap.class, String.class, String.class));
                result.add(CheckDeviceVO.builder()
                        .msg(argsException.getMessage())
                        .httpCode(Objects.nonNull(argsException.getHttpCode()) ? argsException.getHttpCode().getCode() : 0)
                        .sn(patientDeviceDTO.getBcmSn())
                        .id(patientDeviceDTO.getBcmId())
                        .deviceStatus(Integer.parseInt(hashMap.get("deviceStatus")))
                        .build());
            } catch (JsonProcessingException e) {
                throw new RuntimeException(e);
            }
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
        } catch (DecodeException dec) {
            result.add(CheckDeviceVO.builder()
                    .msg(dec.getMessage())
                    .sn(patientDeviceDTO.getBcmSn())
                    .id(patientDeviceDTO.getBcmId())
                    .build());
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
        }
        return result;
    }

    @Transactional(readOnly = true)
    public void checkDevice(PatientDeviceDTO patientDeviceDTO) {
        if (!patientService.existsById(patientDeviceDTO.getPatientId())) {
            throw new ArgsException("患者[{0}]不存在", patientDeviceDTO.getPatientId());
        }

        ArchivesVO archivesVO = archivesService.findById(patientDeviceDTO.getArchivesId());
        if (archivesVO.getStatus().equals(ArchivesStatus.Finished)) {
            throw new ArgsException("档案[{0}]已完成手术", patientDeviceDTO.getArchivesId());
        }
        DeviceBO deviceBO = DeviceBO.builder()
                .patientId(patientDeviceDTO.getPatientId())
                .bcmSn(patientDeviceDTO.getBcmSn())
                .bcmId(patientDeviceDTO.getBcmId())
                .electrodeSns(patientDeviceDTO.getSurgeryElectrodes().stream().map(SurgeryElectrodeDTO::getElectrodeSn).collect(Collectors.toList()))
                .build();

        deviceBO = iDeviceController.findDevice(deviceBO);

        Optional<PatientDevice> optional = repository.findFirstByPatientIdAndStatus(patientDeviceDTO.getPatientId(), Status.ADD);
        if (optional.isEmpty()) {
            optional = Optional.of(PatientDevice.builder()
                    .bcmModelId(deviceBO.getBcm().getModelId())
                    .bcmId(deviceBO.getBcm().getId())
                    .build());
            PatientDevice device = optional.get();
            device.setContractElectrodeModels(deviceBO.getElectrodes().stream().map(e -> {
                byte position = patientDeviceDTO.getSurgeryElectrodes().stream().filter(se -> se.getElectrodeSn().equals(e.getSn())).findFirst().orElseThrow().getPosition().byteValue();
                return ContractElectrodeModel.builder()
                        .electrodeModelId(e.getModelId())
                        .position(position)
                        .patientDevice(device)
                        .build();
            }).collect(Collectors.toSet()));
        }
        PatientDevice patientDevice = optional.get();

        patientDeviceDTO.setBppModelId(patientDevice.getBppModelId());
        patientDeviceDTO.setBcmModelId(patientDevice.getBcmModelId());
        patientDeviceDTO.setContractElectrodeModels(new ArrayList<>());
        patientDevice.getContractElectrodeModels().forEach(ce -> patientDeviceDTO.getContractElectrodeModels().add(ContractElectrodeModelDTO.builder()
                .electrodeModelId(ce.getElectrodeModelId())
                .position((int) ce.getPosition())
                .build()));

        checkBcm(patientDeviceDTO);
        checkElectrode(patientDeviceDTO);
    }

    //    @GlobalTransactional(rollbackFor = Exception.class)
    @Transactional
    @Caching(evict = {
            @CacheEvict(cacheNames = CacheConstants.CACHE_NAMES_PATIENT, key = "#p0.patientId"),
            @CacheEvict(cacheNames = CacheConstants.CACHE_NAMES_PATIENT_DEVICE_PAGE, allEntries = true),
            @CacheEvict(key = "#p0.patientId")
    })
    public PatientDeviceVO addDevice(PatientDeviceDTO patientDeviceDTO) {
        if (!patientService.existsById(patientDeviceDTO.getPatientId())) {
            throw new ArgsException("患者[{0}]不存在", patientDeviceDTO.getPatientId());
        }
        ArchivesVO archivesVO = archivesService.findById(patientDeviceDTO.getArchivesId());
        if (archivesVO.getStatus().equals(ArchivesStatus.Finished)) {
            throw new ServiceException("该档案已完成手术");
        }

        DeviceBO deviceBO = DeviceBO.builder()
                .patientId(patientDeviceDTO.getPatientId())
                .bcmSn(patientDeviceDTO.getBcmSn())
                .bcmId(patientDeviceDTO.getBcmId())
                .electrodeSns(patientDeviceDTO.getSurgeryElectrodes().stream().map(SurgeryElectrodeDTO::getElectrodeSn).collect(Collectors.toList()))
                .surgeryDate(patientDeviceDTO.getSurgeryDate())
                .build();

        deviceBO = iDeviceController.findDevice(deviceBO);

        Optional<PatientDevice> optional = repository.findFirstByPatientIdAndStatus(patientDeviceDTO.getPatientId(), Status.ADD);

        if (optional.isEmpty()) {
            optional = Optional.of(PatientDevice.builder()
                    .bcmModelId(deviceBO.getBcm().getModelId())
                    .bcmId(deviceBO.getBcm().getId())
                    .build());
            PatientDevice device = optional.get();
            device.setContractElectrodeModels(deviceBO.getElectrodes().stream().map(e -> {
                byte position = patientDeviceDTO.getSurgeryElectrodes().stream().filter(se -> se.getElectrodeSn().equals(e.getSn())).findFirst().orElseThrow().getPosition().byteValue();
                return ContractElectrodeModel.builder()
                        .electrodeModelId(e.getModelId())
                        .position(position)
                        .patientDevice(device)
                        .build();
            }).collect(Collectors.toSet()));
        }


        PatientDevice patientDevice = optional.get();

        patientDeviceDTO.setBppModelId(patientDevice.getBppModelId());
        patientDeviceDTO.setBcmModelId(patientDevice.getBcmModelId());
        patientDeviceDTO.setContractElectrodeModels(new ArrayList<>());
        patientDevice.getContractElectrodeModels().forEach(ce -> patientDeviceDTO.getContractElectrodeModels().add(ContractElectrodeModelDTO.builder()
                .electrodeModelId(ce.getElectrodeModelId())
                .position((int) ce.getPosition())
                .build()));

        // 如果已经存在此设备，新增记录
        if (Objects.nonNull(patientDevice.getBcmId())
                || Objects.nonNull(patientDevice.getBppId())
                || !patientDevice.getSurgeryElectrodes().isEmpty()) {
            PatientDeviceDTO deviceDTO = PatientDeviceDTO.builder()
                    .patientId(patientDeviceDTO.getPatientId())
                    .bppModelId(patientDevice.getBppModelId())
                    .bcmModelId(patientDevice.getBcmModelId())
                    .build();
            if (!patientDevice.getContractElectrodeModels().isEmpty()) {
                deviceDTO.setContractElectrodeModels(new ArrayList<>());
                patientDevice.getContractElectrodeModels().forEach(ce -> {
                    deviceDTO.getContractElectrodeModels().add(ContractElectrodeModelDTO.builder()
                            .electrodeModelId(ce.getElectrodeModelId())
                            .position((int) ce.getPosition())
                            .build());
                });
            }

            addModel(deviceDTO);
            return addDevice(patientDeviceDTO);
        }
        deviceBO.setElectrodeStatus(DeviceStatus.used);

        BcmVO bcmVO = checkBcm(patientDeviceDTO, deviceBO.getBcm());
        // 修改设备状态为使用中
        if (Objects.nonNull(patientDeviceDTO.getBcmId())) {
//            iBcmController.update(patientDeviceDTO.getBcmId(), BcmDTO.builder()
//                    .deviceStatus(DeviceStatus.used)
//                    .patientId(patientDeviceDTO.getPatientId())
//                    .build());

            deviceBO.setBcmStatus(DeviceStatus.used);
            deviceBO.setBcmId(patientDeviceDTO.getBcmId());
            if (Objects.nonNull(bcmVO.getBpp())) {
                addBppAndModel(patientDevice.getId(), PatientDeviceDTO.builder()
                        .bppId(bcmVO.getBpp().getId())
                        .bppModelId(bcmVO.getBpp().getModelId())
                        .patientId(patientDeviceDTO.getPatientId())
                        .build(), deviceBO);
            }

        }

        checkElectrode(patientDeviceDTO, deviceBO);

        patientDeviceConvert.dto2poDevice(patientDeviceDTO, patientDevice);

        deviceBO = iDeviceController.updateDevice(deviceBO);
        patientDevice.setBcmImplantationDate(deviceBO.getBcm().getImplantationDate());
        DeviceBO finalDeviceBO = deviceBO;
        patientDevice.getSurgeryElectrodes().forEach(e -> {
            e.setPatientDevice(patientDevice).setImplantationDate(finalDeviceBO.getElectrodes().stream().filter(el -> el.getId().equals(e.getElectrodeId())).findFirst().orElseThrow().getImplantationDate());
        });
        patientDevice.setProtocolVersions(bcmVO.getProtocolVersions());


        if (Objects.nonNull(patientDeviceDTO.getSurgeryDate())) {

            ImpedanceLogVO impedanceLogVO = iImpedanceLogController.findBySn(patientDevice.getBcmSn());
            surgeryService.addSurgery(null, null, null, null, SurgeryDTO.builder()
                    .patientId(patientDeviceDTO.getPatientId())
                    .surgeryDate(patientDeviceDTO.getSurgeryDate())
                    .archivesId(patientDeviceDTO.getArchivesId())
                    .impedance(Objects.nonNull(impedanceLogVO) ? impedanceLogVO.getImpedance() : null)
                    .build(), null);
        }


        return patientDeviceConvert.po2vo(repository.save(patientDevice));
    }

    //    @GlobalTransactional(rollbackFor = Exception.class)
    @Transactional
    @Caching(evict = {
            @CacheEvict(cacheNames = CacheConstants.CACHE_NAMES_PATIENT, key = "#p0.patientId"),
            @CacheEvict(cacheNames = CacheConstants.CACHE_NAMES_PATIENT_DEVICE_PAGE, allEntries = true),
            @CacheEvict(key = "#p0.patientId")
    })
    public PatientDeviceVO addModel(PatientDeviceDTO patientDeviceDTO) {
        if (!patientService.existsById(patientDeviceDTO.getPatientId())) {
            throw new ArgsException("患者[{0}]不存在", patientDeviceDTO.getPatientId());
        }

        checkDeviceModel(patientDeviceDTO);

        List<PatientDevice> patientDeviceList = repository.findByPatientIdAndStatus(patientDeviceDTO.getPatientId(), Status.ADD);
        if (Objects.nonNull(patientDeviceList)) patientDeviceList.forEach(p -> p.setStatus(Status.DISABLED));

        PatientDevice patientDevice = patientDeviceConvert.dto2poModel(patientDeviceDTO);
        patientDevice.setPatient(Patient.builder().id(patientDeviceDTO.getPatientId()).build());
        patientDevice.getContractElectrodeModels().forEach(ce -> ce.setPatientDevice(patientDevice));

        return patientDeviceConvert.po2vo(repository.save(patientDevice));
    }

    @GlobalTransactional(rollbackFor = Exception.class)
    @Transactional
    @Caching(evict = {
            @CacheEvict(cacheNames = CacheConstants.CACHE_NAMES_PATIENT, key = "#p1.patientId"),
            @CacheEvict(cacheNames = CacheConstants.CACHE_NAMES_PATIENT_DEVICE_PAGE, allEntries = true),
            @CacheEvict(key = "#p1.patientId")
    })
    public PatientDeviceVO addBcmAndModel(@Nullable Long deviceId, PatientDeviceDTO patientDeviceDTO) {
        checkDeviceModel(patientDeviceDTO);
        checkBcm(patientDeviceDTO);
        PatientDevice patientDevice;
        if (Objects.nonNull(deviceId)) {
            Optional<PatientDevice> optional = repository.findById(deviceId);
            if (optional.isEmpty()) {
                throw new ArgsException("患者设备[{0}]不存在", deviceId);
            }
            patientDevice = optional.get();

            if (Objects.nonNull(patientDevice.getBcmId())) {
                // 复制数据新建患者设备
                if (!patientDevice.getContractElectrodeModels().isEmpty()) {
                    patientDeviceDTO.setContractElectrodeModels(patientDevice.getContractElectrodeModels().stream().map(ce -> ContractElectrodeModelDTO.builder()
                            .position((int) ce.getPosition())
                            .electrodeModelId(ce.getElectrodeModelId())
                            .build()).collect(Collectors.toList()));
                }

                patientDeviceDTO.setBppModelId(patientDevice.getBppModelId());
                PatientDeviceVO patientDeviceVO = addModel(patientDeviceDTO);

                if (!patientDevice.getSurgeryElectrodes().isEmpty()) {
                    patientDeviceDTO.setSurgeryElectrodes(patientDevice.getSurgeryElectrodes().stream().map(se ->
                            SurgeryElectrodeDTO.builder()
                                    .target(se.getTarget())
                                    .location(se.getLocation())
                                    .position(se.getPosition())
                                    .electrodeId(se.getElectrodeId())
                                    .build()).collect(Collectors.toList()));
                    addElectrodeAndModel(patientDeviceVO.getId(), patientDeviceDTO);
                }


                return addBcmAndModel(patientDeviceVO.getId(), patientDeviceDTO);
            }

            patientDeviceConvert.dto2poBcm(patientDeviceDTO, patientDevice);
        } else {
            patientDevice = patientDeviceConvert.dto2poBcm(patientDeviceDTO);
            List<PatientDevice> patientDeviceList = repository.findByPatientId(patientDeviceDTO.getPatientId());
            if (Objects.nonNull(patientDeviceList))
                patientDeviceList.forEach(p -> p.setStatus(Status.DISABLED));
            patientDevice.setPatient(Patient.builder().id(patientDeviceDTO.getPatientId()).build());
        }

        BcmVO bcmVO = iBcmController.update(patientDeviceDTO.getBcmId(), BcmDTO.builder()
                .deviceStatus(DeviceStatus.used)
                .patientId(patientDeviceDTO.getPatientId())
                .surgeryDate(Optional.ofNullable(patientDeviceDTO.getSurgeryDate()).orElse(new Date()))
                .build());
        patientDevice.setBcmImplantationDate(bcmVO.getImplantationDate());
        PatientDeviceVO patientDeviceVO = patientDeviceConvert.po2vo(repository.save(patientDevice));
        if (Objects.nonNull(bcmVO.getBpp())) {
            addBppAndModel(patientDeviceVO.getId(), PatientDeviceDTO.builder()
                    .bppId(bcmVO.getBpp().getId())
                    .bppModelId(bcmVO.getBpp().getModelId())
                    .patientId(patientDeviceDTO.getPatientId())
                    .build(), null);
        }
        return patientDeviceVO;
    }

    @GlobalTransactional(rollbackFor = Exception.class)
    @Transactional
    @Caching(evict = {
            @CacheEvict(cacheNames = CacheConstants.CACHE_NAMES_PATIENT, key = "#p1.patientId"),
            @CacheEvict(cacheNames = CacheConstants.CACHE_NAMES_PATIENT_DEVICE_PAGE, allEntries = true),
            @CacheEvict(key = "#p1.patientId")
    })
    public PatientDeviceVO addElectrodeAndModel(@Nullable Long deviceId, PatientDeviceDTO patientDeviceDTO) {
        checkDeviceModel(patientDeviceDTO);
        checkElectrode(patientDeviceDTO);

        PatientDevice patientDevice;
        if (Objects.nonNull(deviceId)) {
            Optional<PatientDevice> optional = repository.findById(deviceId);
            if (optional.isEmpty()) {
                throw new ArgsException("患者设备[{0}]不存在", deviceId);
            }


            patientDevice = optional.get();


            if (!patientDevice.getSurgeryElectrodes().isEmpty()) {
                patientDeviceDTO.setBcmModelId(patientDevice.getBcmModelId());
                patientDeviceDTO.setBppModelId(patientDevice.getBppModelId());
                PatientDeviceVO patientDeviceVO = addModel(patientDeviceDTO);

                if (Objects.nonNull(patientDevice.getBcmId())) {
                    patientDeviceDTO.setBcmId(patientDevice.getBcmId());
                    addBcmAndModel(patientDeviceVO.getId(), patientDeviceDTO);
                }

                patientDeviceDTO.getContractElectrodeModels().forEach(ce -> ce.setUsed(false));
                return addElectrodeAndModel(patientDeviceVO.getId(), patientDeviceDTO);
            }
            patientDeviceConvert.dto2poEle(patientDeviceDTO, patientDevice);

        } else {
            patientDevice = patientDeviceConvert.dto2poEle(patientDeviceDTO);
            List<PatientDevice> patientDeviceList = repository.findByPatientId(patientDeviceDTO.getPatientId());
            if (Objects.nonNull(patientDeviceList))
                patientDeviceList.forEach(p -> p.setStatus(Status.DISABLED));
            patientDevice.setPatient(Patient.builder().id(patientDeviceDTO.getPatientId()).build());
        }

        patientDevice.getContractElectrodeModels().forEach(ce -> ce.setPatientDevice(patientDevice));
        patientDeviceDTO.getSurgeryElectrodes().forEach(e -> {
            ElectrodeVO electrodeVO = iElectrodeController.update(e.getElectrodeId(), ElectrodeDTO.builder()
                    .deviceStatus(DeviceStatus.used)
                    .surgeryDate(Optional.ofNullable(patientDeviceDTO.getSurgeryDate()).orElse(new Date()))
                    .patientId(patientDeviceDTO.getPatientId())
                    .build());
            SurgeryElectrode surgeryElectrode = patientDevice.getSurgeryElectrodes().stream().filter(se -> se.getElectrodeId().equals(electrodeVO.getId())).findFirst().orElseThrow();
            surgeryElectrode.setPatientDevice(patientDevice)
                    .setImplantationDate(electrodeVO.getImplantationDate());
        });

        return patientDeviceConvert.po2vo(repository.save(patientDevice));
    }

    //    @GlobalTransactional(rollbackFor = Exception.class)
    @Transactional
    @Caching(evict = {
            @CacheEvict(cacheNames = CacheConstants.CACHE_NAMES_PATIENT, key = "#p1.patientId"),
            @CacheEvict(cacheNames = CacheConstants.CACHE_NAMES_PATIENT_DEVICE_PAGE, allEntries = true),
            @CacheEvict(key = "#p1.patientId")
    })
    public PatientDeviceVO addBppAndModel(Long deviceId, PatientDeviceDTO patientDeviceDTO, @Nullable DeviceBO deviceBO) {
        checkDeviceModel(patientDeviceDTO);
        if (Objects.nonNull(deviceBO)) {
            checkBpp(patientDeviceDTO, deviceBO.getBcm().getBpp());
        } else {
            checkBpp(patientDeviceDTO);
        }

        PatientDevice patientDevice;

        Optional<PatientDevice> optional = repository.findById(deviceId);
        if (optional.isEmpty()) {
            throw new ArgsException("患者设备[{0}]不存在", deviceId);
        }
        patientDevice = optional.get();
        patientDeviceConvert.dto2poBpp(patientDeviceDTO, patientDevice);


        return patientDeviceConvert.po2vo(repository.save(patientDevice));
    }


    @GlobalTransactional(rollbackFor = Exception.class)
    @Transactional
    @Caching(evict = {
            @CacheEvict(cacheNames = CacheConstants.CACHE_NAMES_PATIENT, key = "#p1.patientId"),
            @CacheEvict(cacheNames = CacheConstants.CACHE_NAMES_PATIENT_DEVICE_PAGE, allEntries = true),
            @CacheEvict(key = "#p1.patientId")
    })
    public PatientDeviceVO add(PatientDeviceDTO patientDeviceDTO) {
        addModel(patientDeviceDTO);
        return addDevice(patientDeviceDTO);
    }

    @Transactional(readOnly = true)
    @Cacheable(cacheNames = CacheConstants.CACHE_NAMES_PATIENT_DEVICE_PAGE)
    public PatientDeviceVO findByPatientStatus(Long patientId, Status... status) {
        for (Status s : status) {
            Optional<PatientDevice> optional = repository.findFirstByPatientIdAndStatus(patientId, s);
            if (optional.isPresent()) {
                PatientDeviceVO patientDeviceVO = patientDeviceConvert.po2vo(optional.get());
                if (Objects.nonNull(patientDeviceVO.getBcmId())) {
                    BcmVO bcmVO = iBcmController.find(patientDeviceVO.getBcmId());
                    patientDeviceVO.setSoftwareVersion(bcmVO.getSoftwareVersion());
                    patientDeviceVO.setHardwareVersion(bcmVO.getHardwareVersion());
                }
                return patientDeviceVO;
            }
        }
        return null;
    }

    @Transactional
    @Caching(evict = {
            @CacheEvict(cacheNames = CacheConstants.CACHE_NAMES_PATIENT, key = "#p0"),
            @CacheEvict(cacheNames = CacheConstants.CACHE_NAMES_PATIENT_DEVICE_PAGE, allEntries = true),
            @CacheEvict(key = "#p0")
    })
    public PatientDeviceVO updatePatientDeviceStatus(Long patientId) {
        Optional<PatientDevice> optional = repository.findFirstByPatientIdAndStatus(patientId, Status.ADD);
        if (optional.isEmpty()) {
            throw new ArgsException("患者[{0}]不存在新增状态的设备信息", patientId);
        }
        Optional<PatientDevice> enabledPatientDeviceOptional = repository.findFirstByPatientIdAndStatus(patientId, Status.ENABLED);
        if (enabledPatientDeviceOptional.isPresent()) {
            enabledPatientDeviceOptional.get().setStatus(Status.DISABLED);
        }
        PatientDevice patientDevice = optional.get();
        patientDevice.setStatus(Status.ENABLED);
        return patientDeviceConvert.po2vo(repository.save(patientDevice));
    }

    @GlobalTransactional(rollbackFor = Exception.class)
    @Transactional
    private void checkDeviceModel(PatientDeviceDTO patientDeviceDTO) {
        if (Objects.nonNull(patientDeviceDTO.getBcmModelId()) && !iBcmModelController.exist(patientDeviceDTO.getBcmModelId())) {
            throw new ArgsException("刺激器设备类型[{0}]不存在", patientDeviceDTO.getBcmModelId());
        }

        if (Objects.nonNull(patientDeviceDTO.getBppModelId()) && !iBppModelController.exist(patientDeviceDTO.getBppModelId())) {
            throw new ArgsException("程控仪设备类型[{0}]不存在", patientDeviceDTO.getBppModelId());
        }
        if (Objects.nonNull(patientDeviceDTO.getContractElectrodeModels()) && !patientDeviceDTO.getContractElectrodeModels().isEmpty()) {
            patientDeviceDTO.getContractElectrodeModels().forEach(e -> {
                if (!iElectrodeModelController.exist(e.getElectrodeModelId())) {
                    throw new ArgsException("电极型号[{0}]不存在", e.getElectrodeModelId());
                }
            });
        }
    }

    @Transactional(readOnly = true)
    private BcmVO checkBcm(PatientDeviceDTO patientDeviceDTO, BcmVO bcmVO) {
        checkDeviceStatus(patientDeviceDTO, bcmVO, "刺激器", null);

        if (!bcmVO.getModelId().equals(patientDeviceDTO.getBcmModelId())) {
            throw new ArgsException("刺激器型号[{0}]不在合同中", HttpCode.MODEL_NOT_EXISTS_IN_ARCHIVES, bcmVO.getModelId());
        }
        patientDeviceDTO.setBcmSn(bcmVO.getSn());
        patientDeviceDTO.setBcmId(bcmVO.getId());
        patientDeviceDTO.setBcmMac(bcmVO.getMac());

        return bcmVO;
    }

    @Transactional(readOnly = true)
    private BcmVO checkBcm(PatientDeviceDTO patientDeviceDTO) {

        BcmVO bcmVO;
        if (Objects.nonNull(patientDeviceDTO.getBcmId()) && patientDeviceDTO.getBcmId() != 0) {
            bcmVO = iBcmController.find(patientDeviceDTO.getBcmId());
        } else {
            bcmVO = iBcmController.find(patientDeviceDTO.getBcmSn());
        }

        return checkBcm(patientDeviceDTO, bcmVO);
    }

    @GlobalTransactional(rollbackFor = Exception.class)
    @Transactional
    private BppVO checkBpp(PatientDeviceDTO patientDeviceDTO, BppVO bppVO) {

        checkDeviceStatus(patientDeviceDTO, bppVO, "程控仪", null);

        if (!bppVO.getModelId().equals(patientDeviceDTO.getBppModelId())) {
            throw new ArgsException("程控仪型号[{0}]不在合同中", bppVO.getModelId());
        }

        patientDeviceDTO.setBppSn(bppVO.getSn());
        patientDeviceDTO.setBppId(bppVO.getId());
        return bppVO;
    }

    @GlobalTransactional(rollbackFor = Exception.class)
    @Transactional
    private BppVO checkBpp(PatientDeviceDTO patientDeviceDTO) {

        BppVO bppVO;
        if (Objects.nonNull(patientDeviceDTO.getBppId()) && patientDeviceDTO.getBppId() != 0) {
            bppVO = iBppController.find(patientDeviceDTO.getBppId());
        } else {
            bppVO = iBppController.find(patientDeviceDTO.getBppSn());
        }

        return checkBpp(patientDeviceDTO, bppVO);
    }

    @Transactional
    private void checkDeviceStatus(PatientDeviceDTO patientDeviceDTO, DeviceVO deviceVO, String name, @Nullable Integer position) {
        if (!deviceVO.getDeviceStatus().equals(DeviceStatus.qualified)) {
            Map<String, String> hashMap = new HashMap<>();
            hashMap.put("deviceStatus", String.valueOf(deviceVO.getDeviceStatus().ordinal()));
            hashMap.put("deviceSn", deviceVO.getSn());
            hashMap.put("devicePosition", Objects.nonNull(position) ? String.valueOf(position) : null);
            try {
                if (!deviceVO.getDeviceStatus().equals(DeviceStatus.used)) {
                    throw new ArgsException(objectMapper.writeValueAsString(hashMap), "[{0}][{1}]状态不合法", HttpCode.STATUS_EXCEPTION, name, deviceVO.getSn());

                } else {
                    if (!deviceVO.getPatientId().equals(patientDeviceDTO.getPatientId())) {
                        throw new ArgsException(objectMapper.writeValueAsString(hashMap), "{0}{1}序列号已被使用", HttpCode.USED_EXCEPTION, name, Objects.isNull(position) ? "" : position.equals(0) ? "一" : "二");
                    }
                }
            } catch (JsonProcessingException e) {
                throw new RuntimeException(e);
            }
        }
    }

    @Transactional(readOnly = true)
    private void checkElectrode(PatientDeviceDTO patientDeviceDTO, SurgeryElectrodeDTO surgeryElectrode, Set<String> electrodeSns, ElectrodeVO electrodeVO) {

        if (Objects.nonNull(surgeryElectrode.getLocationIndex())) {
            ConfigsVO locationConfig = iConfigController.findByKey(PatientConstants.LOCATION_TYPE);
            try {
                List<String> locations = objectMapper.readValue(locationConfig.getValue(), TypeFactory.defaultInstance().constructCollectionType(List.class, String.class));
                if (surgeryElectrode.getLocationIndex() >= locations.size()) {
                    throw new ArgsException("植入[{0}]位置不合法", HttpCode.POSITION_EXCEPTION, surgeryElectrode.getLocation());
                }
                surgeryElectrode.setLocation(locations.get(surgeryElectrode.getLocationIndex()));

            } catch (JsonProcessingException e) {
                throw new RuntimeException(e);
            }
        }


        if (Objects.nonNull(surgeryElectrode.getTargetIndex())) {
            ConfigsVO targetConfig = iConfigController.findByKey(PatientConstants.TARGET_TYPE);
            try {
                List<String> targets = objectMapper.readValue(targetConfig.getValue(), TypeFactory.defaultInstance().constructCollectionType(List.class, String.class));
                if (surgeryElectrode.getTargetIndex() >= targets.size()) {
                    throw new ArgsException("靶点[{0}]不合法", HttpCode.TARGET_EXCEPTION, targets.size());
                }
                surgeryElectrode.setTarget(targets.get(surgeryElectrode.getTargetIndex()));
            } catch (JsonProcessingException e) {
                throw new RuntimeException(e);
            }
        }


        checkDeviceStatus(patientDeviceDTO, electrodeVO, "电极", surgeryElectrode.getPosition());


        ElectrodeVO finalElectrodeVO = electrodeVO;
        Optional<ContractElectrodeModelDTO> electrodeModelDTOOptional = patientDeviceDTO.getContractElectrodeModels().stream().filter(c -> c.getElectrodeModelId().equals(finalElectrodeVO.getModelId()) && !c.isUsed()).findFirst();
        if (electrodeModelDTOOptional.isEmpty()) {
            throw new ArgsException("电极型号[{0}]不在合同中", HttpCode.MODEL_NOT_EXISTS_IN_ARCHIVES, electrodeVO.getModelId());
        }
        electrodeModelDTOOptional.get().setUsed(true);

        surgeryElectrode.setElectrodeSn(electrodeVO.getSn());
        surgeryElectrode.setElectrodeId(electrodeVO.getId());
        if (electrodeSns.contains(electrodeVO.getSn())) {
            throw new ArgsException("电极序列号不可重复使用，请检查后重试");
        }
        electrodeSns.add(electrodeVO.getSn());
    }

    @Transactional(readOnly = true)
    private void checkElectrode(PatientDeviceDTO patientDeviceDTO, SurgeryElectrodeDTO surgeryElectrode, Set<String> electrodeSns) {
        ElectrodeVO electrodeVO = null;
        if (Objects.nonNull(surgeryElectrode.getElectrodeId())) {
            electrodeVO = iElectrodeController.find(surgeryElectrode.getElectrodeId());
        } else if (Objects.nonNull(surgeryElectrode.getElectrodeSn())) {
            electrodeVO = iElectrodeController.findsBySn(surgeryElectrode.getElectrodeSn(), surgeryElectrode.getPosition());
        }

        checkElectrode(patientDeviceDTO, surgeryElectrode, electrodeSns, electrodeVO);
    }

    @Transactional
    private void checkElectrode(PatientDeviceDTO patientDeviceDTO, DeviceBO deviceBO) {
        Set<String> electrodeSns = new HashSet<>();

        for (int i = 0; i < patientDeviceDTO.getSurgeryElectrodes().size(); i++) {
            SurgeryElectrodeDTO surgeryElectrode = patientDeviceDTO.getSurgeryElectrodes().get(i);
            checkElectrode(patientDeviceDTO, surgeryElectrode, electrodeSns, deviceBO.getElectrodes().get(i));
        }
        if (patientDeviceDTO.getContractElectrodeModels().stream().anyMatch(c -> !c.isUsed())) {
            throw new ArgsException("电极数量或型号与合同中不匹配");
        }
    }

    @Transactional
    private void checkElectrode(PatientDeviceDTO patientDeviceDTO) {
        Set<String> electrodeSns = new HashSet<>();
        for (SurgeryElectrodeDTO surgeryElectrode : patientDeviceDTO.getSurgeryElectrodes()) {
            checkElectrode(patientDeviceDTO, surgeryElectrode, electrodeSns);
        }
        if (patientDeviceDTO.getContractElectrodeModels().stream().anyMatch(c -> !c.isUsed())) {
            throw new ArgsException("电极数量或型号与合同中不匹配");
        }
    }


    @Caching(evict = {
            @CacheEvict(cacheNames = CacheConstants.CACHE_NAMES_PATIENT, key = "#p0"),
            @CacheEvict(cacheNames = CacheConstants.CACHE_NAMES_PATIENT_DEVICE_PAGE, allEntries = true),
            @CacheEvict(key = "#p0")
    })
    @Transactional
    public void updateElectrode(Long patientId, PatientDeviceDTO patientDeviceDTO) {
        Optional<PatientDevice> optional = repository.findFirstByPatientIdAndStatus(patientId, Status.ENABLED);
        if (optional.isEmpty()) {
            throw new ServiceException("患者设备不存在或未完成手术");
        }
        PatientDevice patientDevice = optional.get();
        Set<SurgeryElectrode> surgeryElectrodes = patientDevice.getSurgeryElectrodes();

        for (SurgeryElectrodeDTO surgeryElectrode : patientDeviceDTO.getSurgeryElectrodes()) {
            Optional<SurgeryElectrode> electrode = surgeryElectrodes.stream().filter(s -> s.getElectrodeId().equals(surgeryElectrode.getElectrodeId())).findFirst();
            surgeryElectrodeConvert.dto2po(surgeryElectrode, electrode.get());
        }

        repository.save(patientDevice);
    }
}
