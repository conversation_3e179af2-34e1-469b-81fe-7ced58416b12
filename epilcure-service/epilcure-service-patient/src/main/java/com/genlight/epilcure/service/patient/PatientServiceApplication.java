package com.genlight.epilcure.service.patient;

import io.seata.spring.annotation.datasource.EnableAutoDataSourceProxy;
import net.bytebuddy.agent.ByteBuddyAgent;
import org.aspectj.weaver.loadtime.Agent;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.instrument.InstrumentationSavingAgent;

import java.lang.instrument.Instrumentation;

@EnableDiscoveryClient
@EnableAutoDataSourceProxy
@SpringBootApplication(scanBasePackages = "com.genlight.epilcure")
public class PatientServiceApplication {

    public static void main(String[] args) {

        Instrumentation instrumentation = ByteBuddyAgent.install();
        Agent.agentmain("", instrumentation);
        InstrumentationSavingAgent.agentmain("", instrumentation);

        SpringApplication.run(PatientServiceApplication.class, args);
    }
}
