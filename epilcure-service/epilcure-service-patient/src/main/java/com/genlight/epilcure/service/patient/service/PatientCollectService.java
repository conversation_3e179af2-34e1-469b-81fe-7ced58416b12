package com.genlight.epilcure.service.patient.service;

import com.genlight.epilcure.common.core.service.BaseService;
import com.genlight.epilcure.common.core.service.exception.ArgsException;
import com.genlight.epilcure.common.core.service.exception.ServiceException;
import com.genlight.epilcure.service.patient.dao.entity.Patient;
import com.genlight.epilcure.service.patient.dao.entity.PatientCollect;
import com.genlight.epilcure.service.patient.dao.entity.PatientCollect_;
import com.genlight.epilcure.service.patient.dao.entity.Patient_;
import com.genlight.epilcure.service.patient.dao.repository.PatientCollectRepository;
import com.genlight.epilcure.service.patient.pojo.convert.PatientCollectConvert;
import com.genlight.epilcure.service.patient.pojo.dto.PatientCollectDTO;
import com.genlight.epilcure.service.patient.pojo.vo.PatientCollectVO;
import jakarta.annotation.Resource;
import jakarta.persistence.criteria.Predicate;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Objects;
import java.util.Optional;

@Service
public class PatientCollectService extends BaseService<PatientCollect, Long, PatientCollectRepository> {

    @Resource
    private PatientService patientService;

    @Resource
    private PatientCollectConvert patientCollectConvert;

    //增
    @Transactional
    public PatientCollectVO addCollect(PatientCollectDTO dto) {

        //如果重复存在
        System.out.println(getUserId());
        if (repository.existsByPatientIdAndUserId(Long.parseLong(dto.getCId()), getUserId())) {
            throw new ArgsException("已经收藏该报告");
        }
        PatientCollect patientCollect = patientCollectConvert.dto2po(dto);
        Patient patient = patientService.getPatientById(Long.parseLong(dto.getCId()));
        patientCollect.setPatient(patient);
        patientCollect.setUserId(getUserId());
//        patientCollect.setCreateTime()

        return patientCollectConvert.po2vo(repository.save(patientCollect));
    }

    @Transactional
    public Page<PatientCollectVO> findCollectByPageable(PatientCollectDTO dto, Pageable pageable) {
        Specification<PatientCollect> poSpecification = (root, query, criteriaBuilder) -> {
            Predicate predicate = criteriaBuilder.conjunction();
            if (Objects.nonNull(dto.getStartTime())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.greaterThanOrEqualTo(root.get(PatientCollect_.patient).get(Patient_.createTime), dto.getStartTime()));
            }
            if (Objects.nonNull(dto.getEndTime())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.lessThanOrEqualTo(root.get(PatientCollect_.patient).get(Patient_.createTime), dto.getEndTime()));
            }
            predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get(PatientCollect_.userId), getUserId()));
            return predicate;
        };
        return patientCollectConvert.po2vo(repository.findAll(poSpecification, pageable));
    }

    //删
    @Transactional
    public void deleteCollect(Long id) {
        //如果不存在
        Optional<PatientCollect> collectOptional = repository.findById(id);
        if (collectOptional.isEmpty()) {
            throw new ServiceException("删除的收藏[{0}]不存在！", id);
        }
        repository.deleteById(id);
    }

    //改
    @Transactional
    public PatientCollectVO updateCollect(Long id, PatientCollectDTO dto) {
        Optional<PatientCollect> collectOptional = repository.findById(id);
        if (collectOptional.isEmpty()) {
            throw new ServiceException("更新的收藏[{0}]不存在！", id);
        }
        PatientCollect po = collectOptional.get();
        po.setRemark(dto.getRemark());
        return patientCollectConvert.po2vo(repository.saveAndFlush(po));
    }
}
