package com.genlight.epilcure.service.patient.service;

import com.genlight.epilcure.api.patient.pojo.dto.PatientSurgerySchemeDTO;
import com.genlight.epilcure.api.patient.pojo.vo.ArchivesVO;
import com.genlight.epilcure.api.patient.pojo.vo.PatientSurgerySchemeVO;
import com.genlight.epilcure.api.patient.pojo.vo.PatientVO;
import com.genlight.epilcure.api.user.feign.IUserController;
import com.genlight.epilcure.api.user.pojo.bo.DeptBO;
import com.genlight.epilcure.api.user.pojo.bo.OrgBO;
import com.genlight.epilcure.api.user.pojo.bo.UserBO;
import com.genlight.epilcure.api.user.pojo.vo.UserVO;
import com.genlight.epilcure.common.core.dao.enums.Status;
import com.genlight.epilcure.common.core.service.BaseService;
import com.genlight.epilcure.common.core.service.exception.ArgsException;
import com.genlight.epilcure.service.patient.constants.CacheConstants;
import com.genlight.epilcure.service.patient.dao.entity.Archives;
import com.genlight.epilcure.service.patient.dao.entity.Patient;
import com.genlight.epilcure.service.patient.dao.entity.PatientSurgeryScheme;
import com.genlight.epilcure.service.patient.dao.repository.PatientSurgerySchemeRepository;
import com.genlight.epilcure.service.patient.pojo.convert.PatientSurgerySchemeConvert;
import jakarta.annotation.Resource;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

@Service
@CacheConfig(cacheNames = CacheConstants.CACHE_NAMES_PATIENT_SURGERY_SCHEME)
public class PatientSurgerySchemeService extends BaseService<PatientSurgeryScheme, Long, PatientSurgerySchemeRepository> {

    @Resource
    private PatientSurgerySchemeConvert patientSurgerySchemeConvert;

    @Resource
    private PatientService patientService;
    @Resource
    private ArchivesService archivesService;
    @Resource
    private IUserController iUserController;


    @CacheEvict(key = "#p0.archivesId")
    @Transactional
    public PatientSurgerySchemeVO add(PatientSurgerySchemeDTO patientSurgerySchemeDTO) {
        PatientVO patientVO = patientService.findPatientBasicById(patientSurgerySchemeDTO.getPatientId());
        UserVO userVO = iUserController.findById(patientSurgerySchemeDTO.getUserId());
        ArchivesVO archivesVO = archivesService.findById(patientSurgerySchemeDTO.getArchivesId());
        // todo: 检查user是否有为此患者手术的权限
        if (!userVO.getOrg().getId().equals(archivesVO.getOrgId())) {
            throw new ArgsException("此医生所属医院[{0}]与患者所属医院不匹配[{1}]", userVO.getOrg().getName(), archivesVO.getOrgName());
        }

        Optional<PatientSurgeryScheme> optional = repository.findByArchivesIdAndStatus(patientSurgerySchemeDTO.getArchivesId(), Status.ENABLED);
        if (optional.isPresent()) {
            optional.get().setStatus(Status.DISABLED);
        }

        patientSurgerySchemeDTO.setAdviserId(getUserId());
        patientSurgerySchemeDTO.setAdviserName(getNikeName());
        patientSurgerySchemeDTO.setUserName(userVO.getName());
        patientSurgerySchemeDTO.setUserPhone(userVO.getMobile());
        patientSurgerySchemeDTO.setOrgId(userVO.getOrg().getId());
        patientSurgerySchemeDTO.setDeptId(userVO.getDept().getId());
        patientSurgerySchemeDTO.setOrgName(userVO.getOrg().getName());
        patientSurgerySchemeDTO.setDeptName(userVO.getDept().getName());


        PatientSurgeryScheme patientSurgeryScheme = patientSurgerySchemeConvert.dto2po(patientSurgerySchemeDTO);
        patientSurgeryScheme.setPatient(Patient.builder().id(patientVO.getId()).build());
        patientSurgeryScheme.setArchives(Archives.builder().id(patientSurgerySchemeDTO.getArchivesId()).build());
        return patientSurgerySchemeConvert.po2vo(repository.save(patientSurgeryScheme));
    }

    @Cacheable(key = "#p0")
    @Transactional(readOnly = true)
    public PatientSurgerySchemeVO findByArchivesId(Long archivesId) {
        Optional<PatientSurgeryScheme> optional = repository.findByArchivesIdAndStatus(archivesId, Status.ENABLED);
        if (optional.isEmpty()) {
            return null;
        }
        return patientSurgerySchemeConvert.po2vo(optional.get());
    }

    @Transactional(readOnly = true)
    public PatientSurgerySchemeVO find(Long id) {
        Optional<PatientSurgeryScheme> optional = repository.findById(id);
        if (optional.isEmpty()) {
            throw new ArgsException("手术计划[{0}]不存在", id);
        }
        return patientSurgerySchemeConvert.po2vo(optional.get());
    }

    @CacheEvict(allEntries = true)
    @Transactional
    public boolean updateSurgeryUser(UserBO userBO) {
        List<PatientSurgeryScheme> list = repository.findByUserId(userBO.getId());
        list.stream().forEach(p -> {
            p.setUserName(userBO.getName());
        });
        repository.saveAll(list);

        list = repository.findByAdviserId(userBO.getId());
        list.stream().forEach(p -> {
            p.setUserName(userBO.getName());
        });
        repository.saveAll(list);
        return true;
    }

    @Transactional
    @CacheEvict(cacheNames = {CacheConstants.CACHE_NAMES_PATIENT_PAGE, CacheConstants.CACHE_NAMES_PATIENT}, allEntries = true)
    public boolean updateOrgName(OrgBO message) {

        List<PatientSurgeryScheme> patientSurgerySchemes = repository.findByOrgId(message.getId());
        for (PatientSurgeryScheme patientSurgeryScheme : patientSurgerySchemes) {
            patientSurgeryScheme.setOrgName(message.getName());
        }
        repository.saveAll(patientSurgerySchemes);
        return true;
    }

    @Transactional
    @CacheEvict(cacheNames = {CacheConstants.CACHE_NAMES_PATIENT_PAGE, CacheConstants.CACHE_NAMES_PATIENT}, allEntries = true)
    public boolean updateDeptName(DeptBO message) {

        List<PatientSurgeryScheme> patientSurgerySchemes = repository.findByDeptId(message.getId());
        for (PatientSurgeryScheme patientSurgeryScheme : patientSurgerySchemes) {
            patientSurgeryScheme.setDeptName(message.getName());
        }
        repository.saveAll(patientSurgerySchemes);
        return true;
    }
}