package com.genlight.epilcure.service.patient.controller;

import com.fasterxml.jackson.annotation.JsonView;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.genlight.epilcure.api.patient.pojo.dto.PatientDTO;
import com.genlight.epilcure.api.patient.pojo.vo.PatientVO;
import com.genlight.epilcure.common.core.mvc.response.JsonResult;
import com.genlight.epilcure.common.core.pojo.valid.Add;
import com.genlight.epilcure.common.core.pojo.valid.Find;
import com.genlight.epilcure.common.core.pojo.view.BasicView;
import com.genlight.epilcure.common.core.pojo.view.SummaryView;
import com.genlight.epilcure.service.patient.pojo.dto.PatientChangeMobileDTO;
import com.genlight.epilcure.service.patient.pojo.vo.PatientAgeStatisticsVO;
import com.genlight.epilcure.service.patient.pojo.vo.PatientGenderStatisticsVO;
import com.genlight.epilcure.service.patient.pojo.vo.PatientHospitalStatisticsVO;
import com.genlight.epilcure.service.patient.pojo.vo.PatientProvinceStatisticsVO;
import com.genlight.epilcure.service.patient.service.PatientService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/5/15 23:28
 * @Version 1.0.0
 **/
@RestController
@RequestMapping("/api/patient/patients")
@Tag(name = "PatientController", description = "患者相关接口")
public class PatientController {
    @Resource
    private PatientService patientService;


//    @PostMapping("/login")
//    @Operation(summary = "患者登录接口")
//    public JsonResult<PatientVO> login(@RequestBody PatientLoginDTO patientLoginDTO) {
//        JwtUtils.createJWT()
//    }


    @PostMapping("/changeMobile")
    @Operation(summary = "患者更换手机号码")
    public JsonResult changePatientMobile(@RequestBody @Valid PatientChangeMobileDTO patientChangeMobileDTO) {
        patientService.updatePatientMobile(patientChangeMobileDTO);
        return JsonResult.ok();
    }


    @GetMapping("/finds")
    @Operation(summary = "分页获取患者列表")
    @JsonView(BasicView.class)
    public JsonResult<Page<PatientVO>> finds(@JsonView(Find.class) PatientDTO patientDTO, Pageable pageable) throws JsonProcessingException {
        return JsonResult.ok(patientService.searchPatientPage(patientDTO, pageable));
    }

    @PostMapping
    @JsonView(BasicView.class)
    @Operation(summary = "添加患者信息")
    public JsonResult<PatientVO> addPatientSample(@JsonView(Add.class) @Valid @RequestBody PatientDTO patientDTO) {
        return JsonResult.ok(patientService.addPatient(patientDTO));
    }

    @PutMapping("/{id}")
    @Operation(summary = "修改患者信息")
    public JsonResult<PatientVO> updatePatientSample(@PathVariable Long id, @JsonView(Add.class) @Valid @RequestBody PatientDTO patientDTO) {
        return JsonResult.ok(patientService.update(id, patientDTO));
    }

    @PostMapping("/getValid/{mobile}")
    @Operation(summary = "登录后获取其他手机验证码")
    public JsonResult<String> getValidCode(@PathVariable String mobile) {
        return JsonResult.ok(patientService.getValid(mobile));
    }

    @GetMapping("/{id}")
    @Operation(summary = "根据Id获取患者信息")
    @JsonView(SummaryView.class)
    public JsonResult<PatientVO> find(@PathVariable Long id) {
        return JsonResult.ok(patientService.findPatientById(id));
    }

    @GetMapping("/findByBcmSn")
    @Operation(summary = "根据刺激器签名获取患者信息")
    @JsonView(SummaryView.class)
    public JsonResult<PatientVO> findByBcmSn(String bcmSn) {
        return JsonResult.ok(patientService.findByBcmSn(bcmSn));
    }

    @PostMapping("/addPasswrod")
    @Operation(summary = "创建患者登录密码")
    @JsonView(SummaryView.class)
    public JsonResult<PatientVO> addPassword(@RequestPart String password) {
        return JsonResult.ok(patientService.updatePatientPassword(password));
    }

    @GetMapping("/statisticsByGender/diseases/{id}")
    @Operation(summary = "根据疾病Id统计按照省份划分的患者性别分布情况")
    public JsonResult<List<PatientGenderStatisticsVO>> statisticsByGender(@PathVariable Long id) {
        return JsonResult.ok(patientService.statisticsByGender(id));
    }

    @GetMapping("/statisticsByAge/diseases/{id}")
    @Operation(summary = "根据疾病Id统计患者年龄分布情况")
    public JsonResult<List<PatientAgeStatisticsVO>> statisticsByAge(@PathVariable Long id) {
        System.out.println("ss");
        return JsonResult.ok(patientService.statisticsByAge(id));
    }

    @GetMapping("/statisticsByHospital/diseases/{id}")
    @Operation(summary = "根据疾病Id统计患者医院分布情况")
    public JsonResult<List<PatientHospitalStatisticsVO>> statisticsByHospital(@PathVariable Long id) {
        return JsonResult.ok(patientService.statisticsByHospital(id));
    }

    @GetMapping("/statisticsByProvince/diseases/{id}")
    @Operation(summary = "根据疾病Id统计患者省份分布情况")
    public JsonResult<List<PatientProvinceStatisticsVO>> statisticsByProvince(@PathVariable Long id) {
        return JsonResult.ok(patientService.statisticsByProvince(id));
    }

    @GetMapping("/tests/{id}")
    @Operation(summary = "查询指定测试名单下的所有患者")
    public JsonResult<Page<PatientVO>> findUsersByTest(@PathVariable Long id, PatientDTO patientDTO, Pageable pageable) {
        return JsonResult.ok(patientService.findPatientsByPageable(patientDTO.setTestId(id).setTestEquals(true), pageable));
    }

    @GetMapping("/tests/not/{id}")
    @Operation(summary = "查询不包含指定测试名单的所有患者")
    public JsonResult<Page<PatientVO>> findUsersByNotTest(@PathVariable Long id, PatientDTO patientDTO, Pageable pageable) {
        return JsonResult.ok(patientService.findPatientsByPageable(patientDTO.setTestId(id).setTestEquals(false), pageable));
    }
}
