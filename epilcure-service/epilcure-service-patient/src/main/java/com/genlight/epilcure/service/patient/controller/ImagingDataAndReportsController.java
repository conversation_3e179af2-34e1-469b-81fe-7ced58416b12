package com.genlight.epilcure.service.patient.controller;

import com.fasterxml.jackson.annotation.JsonView;
import com.genlight.epilcure.common.core.mvc.response.JsonResult;
import com.genlight.epilcure.common.core.pojo.valid.Add;
import com.genlight.epilcure.common.core.pojo.valid.Find;
import com.genlight.epilcure.common.core.pojo.valid.Update;
import com.genlight.epilcure.service.patient.pojo.dto.ImagingDataAndReportsDTO;
import com.genlight.epilcure.service.patient.pojo.vo.ImagingDataAndReportsVO;
import com.genlight.epilcure.service.patient.service.ImagingDataAndReportsService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

/**
 * <AUTHOR> maoyan
 * @Date : 2022/8/17 16:36
 * @Version :1.0.0
 */
@RestController
@RequestMapping("/api/patient/imaging_data_and_reports")
@Tag(name = "ImagingDataAndReportsController", description = "影像学资料和报告")
public class ImagingDataAndReportsController {
    @Resource
    private ImagingDataAndReportsService imagingDataAndReportsService;

    @PostMapping(consumes = MediaType.MULTIPART_FORM_DATA_VALUE, path = "/archives/{id}")
    @Operation(summary = "新增影像学资料、报告和脑电图片")
    @JsonView(Add.class)
    public JsonResult<ImagingDataAndReportsVO> addImagingDataAndReports(@PathVariable Long id, @RequestPart(required = false) MultipartFile picturesOfImagingData, @RequestPart(required = false) MultipartFile picturesOfReports, @RequestPart(required = false) MultipartFile picturesOfEeg) {
        return JsonResult.ok(imagingDataAndReportsService.addImagingDataAndReports(id, picturesOfImagingData, picturesOfReports, picturesOfEeg));
    }

    @GetMapping
    @Operation(summary = "查询程控方案对应的影像学资料和报告")
    @JsonView(Find.class)
    public JsonResult<ImagingDataAndReportsVO> findImagingDataAndReportsBySchemeId(@Validated(Find.class) ImagingDataAndReportsDTO imagingDataAndReportsDTO) {
        return JsonResult.ok(imagingDataAndReportsService.findImagingDataAndReportsByArchivesId(imagingDataAndReportsDTO));
    }

    @PutMapping("/{id}")
    @Operation(summary = "更新程控方案对应的影像学资料和报告中的图片信息（软删除某张图片）")
    @JsonView(Update.class)
    public JsonResult<ImagingDataAndReportsVO> deleteUrlBySchemeId(@PathVariable Long id, @Validated(Update.class) @RequestBody ImagingDataAndReportsDTO imagingDataAndReportsDTO) {
        return JsonResult.ok(imagingDataAndReportsService.deleteUrlByArchivesId(id, imagingDataAndReportsDTO));
    }
}
