package com.genlight.epilcure.service.patient.dao.entity;

import com.genlight.epilcure.api.device.pojo.enums.ProtocolVersions;
import com.genlight.epilcure.common.core.dao.entity.TEntity;
import com.genlight.epilcure.common.core.dao.enums.Status;
import com.genlight.epilcure.common.core.dao.generator.annotation.SignOrder;
import jakarta.persistence.*;
import lombok.*;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;
import org.hibernate.annotations.Comment;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import java.util.Date;
import java.util.HashSet;
import java.util.Set;

@Setter
@Getter
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@Entity
@Cacheable
@DynamicInsert
@DynamicUpdate
@SignOrder(0)
@Table(name = "t_patient_device", indexes = {
        @Index(name = "idx_patient_status", columnList = "patient_id,status")
})
public class PatientDevice extends TEntity {
    @Comment("bcm型号Id")
    @Column
    @SignOrder(1)
    private Long bcmModelId;

    @Comment("bpp型号Id")
    @Column
    @SignOrder(2)
    private Long bppModelId;

    @Builder.Default
    @ToString.Exclude
    @OneToMany(mappedBy = "patientDevice", cascade = CascadeType.ALL)
    private Set<ContractElectrodeModel> contractElectrodeModels = new HashSet<>();

    @Comment("植入的刺激器Id")
    @Column
    @SignOrder(3)
    private Long bcmId;

    @Comment("刺激器签名")
    @Column
    @SignOrder(4)
    private String bcmSn;

    @Comment("协议版本")
    @Enumerated(EnumType.ORDINAL)
    @SignOrder(5)
    private ProtocolVersions protocolVersions;

    @Comment("使用的程控仪Id")
    @Column
    @SignOrder(6)
    private Long bppId;

    @Comment("程控仪签名")
    @Column
    @SignOrder(7)
    private String bppSn;

    @Comment("是否验证")
    @Column(name = "is_check")
    @SignOrder(8)
    private Boolean check;

    @ToString.Exclude
    @Builder.Default
    @OneToMany(mappedBy = "patientDevice", cascade = CascadeType.ALL, orphanRemoval = true)
    private Set<SurgeryElectrode> surgeryElectrodes = new HashSet<>();

    @ToString.Exclude
    @Comment("所属患者")
    @ManyToOne
    private Patient patient;

    @Enumerated(EnumType.ORDINAL)
    @Comment("状态")
    @Column(columnDefinition = "int default 3")
    @SignOrder(9)
    private Status status;

    @Comment("刺激器mac")
    @SignOrder(10)
    private String bcmMac;

    @Comment("刺激器植入时间")
    @Column
    @SignOrder(11)
    private Date bcmImplantationDate;
}
