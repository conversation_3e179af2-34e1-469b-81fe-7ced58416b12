package com.genlight.epilcure.service.patient.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.type.TypeFactory;
import com.genlight.epilcure.api.config.feign.IConfigController;
import com.genlight.epilcure.api.config.pojo.vo.ConfigsVO;
import com.genlight.epilcure.api.config.pojo.vo.IDConfigVO;
import com.genlight.epilcure.api.device.feign.IBcmController;
import com.genlight.epilcure.api.device.pojo.vo.BcmVO;
import com.genlight.epilcure.api.patient.enums.IdCardType;
import com.genlight.epilcure.api.patient.pojo.dto.PatientDTO;
import com.genlight.epilcure.api.patient.pojo.vo.PatientVO;
import com.genlight.epilcure.api.user.feign.IOrgController;
import com.genlight.epilcure.api.user.pojo.vo.OrgVO;
import com.genlight.epilcure.common.core.constants.HttpCode;
import com.genlight.epilcure.common.core.dao.enums.SmsType;
import com.genlight.epilcure.common.core.dao.enums.Status;
import com.genlight.epilcure.common.core.dysms.SmsService;
import com.genlight.epilcure.common.core.pojo.bo.ValidSmsBO;
import com.genlight.epilcure.common.core.service.BaseService;
import com.genlight.epilcure.common.core.service.exception.ArgsException;
import com.genlight.epilcure.common.core.util.RSAUtils;
import com.genlight.epilcure.common.rocketmq.pojo.bo.AddSchemeBO;
import com.genlight.epilcure.service.patient.constants.CacheConstants;
import com.genlight.epilcure.service.patient.constants.PatientConstants;
import com.genlight.epilcure.service.patient.dao.entity.Patient;
import com.genlight.epilcure.service.patient.dao.entity.Patient_;
import com.genlight.epilcure.service.patient.dao.entity.Test;
import com.genlight.epilcure.service.patient.dao.entity.Test_;
import com.genlight.epilcure.service.patient.dao.repository.PatientRepository;
import com.genlight.epilcure.service.patient.pojo.convert.PatientConvert;
import com.genlight.epilcure.service.patient.pojo.dto.PatientChangeMobileDTO;
import com.genlight.epilcure.service.patient.pojo.vo.PatientAgeStatisticsVO;
import com.genlight.epilcure.service.patient.pojo.vo.PatientGenderStatisticsVO;
import com.genlight.epilcure.service.patient.pojo.vo.PatientHospitalStatisticsVO;
import com.genlight.epilcure.service.patient.pojo.vo.PatientProvinceStatisticsVO;
import jakarta.annotation.Resource;
import jakarta.persistence.criteria.*;
import lombok.extern.slf4j.Slf4j;
import net.sourceforge.pinyin4j.PinyinHelper;
import net.sourceforge.pinyin4j.format.HanyuPinyinOutputFormat;
import net.sourceforge.pinyin4j.format.exception.BadHanyuPinyinOutputFormatCombination;
import org.jetbrains.annotations.NotNull;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.annotation.Caching;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.regex.Pattern;

@Slf4j
@Service
@CacheConfig(cacheNames = CacheConstants.CACHE_NAMES_PATIENT)
public class PatientService extends BaseService<Patient, Long, PatientRepository> {
    @Resource
    private PatientConvert patientConvert;

    @Resource
    private ContractService contractService;

    @Resource
    private IOrgController iOrgController;

    @Resource
    private PatientDeviceService patientDeviceService;

    @Resource
    private ArchivesService archivesService;

    @Resource
    private SmsService smsService;

    @Resource
    private IBcmController iBcmController;

    @Resource
    private IConfigController iConfigController;

    @Resource
    private ObjectMapper objectMapper;

    @Resource
    private PasswordEncoder passwordEncoder;

    @Resource
    private RSAUtils rsaUtils;

    @Resource
    private HanyuPinyinOutputFormat hanyuPinyinOutputFormat;


    @Transactional(readOnly = true)
    @Cacheable(cacheNames = {CacheConstants.CACHE_NAMES_PATIENT_PAGE})
    public Page<PatientVO> findPatientsByPageable(PatientDTO patientDTO, Pageable pageable) {
        return patientConvert.po2vo(repository.findAll((Root<Patient> root, CriteriaQuery<?> query, CriteriaBuilder criteriaBuilder) -> {

            Predicate predicate = criteriaBuilder.conjunction();

            if (StringUtils.hasText(patientDTO.getName())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.like(root.get(Patient_.name), "%" + patientDTO.getName() + "%"));
            }
            if (StringUtils.hasText(patientDTO.getMobile())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.like(root.get(Patient_.mobile), "%" + patientDTO.getMobile() + "%"));
            }
            if (Objects.nonNull(patientDTO.getSex())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get(Patient_.sex), patientDTO.getSex()));
            }
            if (Objects.nonNull(patientDTO.getTestId())) {
                if (patientDTO.isTestEquals()) {
                    predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.join(Patient_.tests).get(Test_.id), patientDTO.getTestId()));
                } else {
                    Subquery<Long> subQuery = query.subquery(Long.class);
                    Root<Patient> subRoot = subQuery.from(Patient.class);
                    subQuery.select(subRoot.get(Patient_.id)).where(criteriaBuilder.equal(subRoot.join(Patient_.tests).get(Test_.id), patientDTO.getTestId()));
                    predicate = criteriaBuilder.and(predicate, root.get(Patient_.id).in(subQuery).not());
                }
            }
            return predicate;
        }, pageable));
    }

    @CacheEvict(cacheNames = {CacheConstants.CACHE_NAMES_PATIENT_PAGE}, allEntries = true)
    @Transactional
    public PatientVO addPatient(PatientDTO patientDTO) {


        Optional<Patient> optional = repository.findByIdCardTypeAndIdCard(patientDTO.getIdCardType(), patientDTO.getIdCard());
        Patient patient;
        if (optional.isPresent()) {
            patient = optional.get();
            if (Objects.nonNull(patientDTO.getMobile()) && repository.existsByMobileAndIdNot(patientDTO.getMobile(), patient.getId())) {
                throw new ArgsException("手机号码[{0}]已存在", patientDTO.getMobile());
            }
            patientConvert.dto2po(patientDTO, patient);
        } else {
            if (Objects.nonNull(patientDTO.getMobile()) && repository.existsByMobile(patientDTO.getMobile())) {
                throw new ArgsException("手机号码[{0}]已存在", patientDTO.getMobile());
            }

            patient = patientConvert.dto2po(patientDTO);
        }

        checkID(patientDTO.getIdCardType(), patientDTO.getIdCard());


        StringBuilder py = getPyName(patientDTO.getName());
        patient.setPyName(py.toString());
        patient.setSortTime(new Date());
        patient = repository.save(patient);
        PatientVO patientVO = patientConvert.po2vo(patient);

        archivesService.updatePatient(patientDTO.getArchivesId(), patientVO.getId());
        return patientVO;
    }

    private StringBuilder getPyName(String patientName) {
        char[] chars = patientName.trim().toCharArray();
        StringBuilder py = new StringBuilder();
        int i = 0;
        for (char aChar : chars) {
            try {
                if (Character.toString(aChar).matches("[\\u4E00-\\u9FA5]+")) {

                    String[] pys = PinyinHelper.toHanyuPinyinStringArray(aChar, hanyuPinyinOutputFormat);
                    if (chars.length < 3) {
                        py.append(pys[0].charAt(0)).append(pys[0].charAt(1));
                    } else if (chars.length == 3) {
                        if (i < 2) {
                            py.append(pys[0].charAt(0));
                        } else {
                            py.append(pys[0].charAt(0)).append(pys[0].charAt(1));
                        }
                    } else {
                        py.append(pys[0].charAt(0));
                    }

                } else {
                    py.append(aChar);
                }
            } catch (BadHanyuPinyinOutputFormatCombination e) {
                throw new RuntimeException(e);
            }
            i++;
            if (i >= (chars.length > 3 ? 4 : 3)) {
                break;
            }
        }
        return py;
    }

    private void checkID(IdCardType idCardType, String idCard) {
        ConfigsVO configsVO = iConfigController.findByKey(PatientConstants.ID_CARD_TYPE);
        List<IDConfigVO> idConfigs;
        try {
            idConfigs = objectMapper.readValue(configsVO.getValue(), TypeFactory.defaultInstance().constructCollectionType(List.class, IDConfigVO.class));
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }


        IDConfigVO idConfigVO = idCardType.ordinal() >= idConfigs.size() ? null : idConfigs.get(idCardType.ordinal());
        if (Objects.nonNull(idConfigVO) && !Pattern.matches(idConfigVO.getRegex(), idCard)) {
            throw new ArgsException("[{0}]证件号[{1}]不合法", idCardType, idCard);
        }
    }

    @Caching(evict = {
            @CacheEvict(key = "#p0")
    })
    @Transactional
    public PatientVO updatePatientPassword(String password) {
        Optional<Patient> optional = repository.findById(getUserId());
        if (optional.isEmpty()) {
            throw new ArgsException("修改的患者Id[{0}]不存在", getUserId());
        }
        Patient patient = optional.get();
        patient.setPassword(passwordEncoder.encode(rsaUtils.decryptPassword(password)));
        return patientConvert.po2vo(repository.save(patient));
    }

    @Transactional
    public String getValid(String mobile) {
        int captcha = (int) ((Math.random() * 9 + 1) * 100000);
        return smsService.sendSms(mobile, SmsType.Valid, ValidSmsBO.builder().code(String.valueOf(captcha)).build());
    }

    @Caching(evict = {
            @CacheEvict(key = "#p0")
    })
    @Transactional
    public PatientVO updatePatientMobile(PatientChangeMobileDTO patientChangeMobileDTO) {

        if (!isPatient()) {
            throw new ArgsException("此接口只支持患者调用");
        }

        Optional<Patient> optional = repository.findByMobileAndStatus(getUserName(), Status.ENABLED);

        if (optional.isEmpty()) {
            throw new ArgsException("患者手机号码[{0}]不存在", getUserName());
        }

        if (repository.existsByMobile(patientChangeMobileDTO.getMobile())) {
            throw new ArgsException("患者手机号码[{0}]已存在", HttpCode.PATIENT_CHANGE_MOBILE_EXISTS, patientChangeMobileDTO.getMobile());
        }
        smsService.checkValid(patientChangeMobileDTO.getMobile(), patientChangeMobileDTO.getUuid(), patientChangeMobileDTO.getCode());

        Patient patient = optional.get();
        patient.setMobile(patientChangeMobileDTO.getMobile());

        return patientConvert.po2vo(repository.save(patient));
    }


    @CacheEvict(key = "#p0")
    @Transactional
    public boolean deletePatient(Long id) {
        if (repository.existsById(id)) {
            throw new ArgsException("删除的患者id不存在");
        }
        repository.deleteById(id);
        return true;
    }

    @Cacheable(key = "#p0")
    @Transactional(readOnly = true)
    public PatientVO findPatientById(Long id) {
        Optional<Patient> optional = repository.findById(id);
        if (optional.isEmpty()) {
            throw new ArgsException("患者信息[{0}]不存在", id);
        }
        PatientVO patientVO = patientConvert.po2vo(optional.get());
        patientVO.setContractVO(contractService.getContractByPatientId(patientVO.getId()));// 只查询有效信息返回.避免级联查询所有合同
        patientVO.setPatientDeviceVO((patientDeviceService.findByPatientStatus(patientVO.getId(), Status.ENABLED, Status.ADD)));
        return patientVO;
    }

    @Cacheable(key = "'card' + #p0")
    @Transactional(readOnly = true)
    public String findIdByCardId(String cardId) {
        Optional<Patient> patientOptional = repository.findByIdCard(cardId);
        if (patientOptional.isEmpty()) {
            return String.valueOf(Long.MAX_VALUE);
        } else {
            return patientOptional.get().getId().toString();
        }
    }

    @Transactional(readOnly = true)
    public PatientVO findPatientBasicById(Long id) {
        Optional<Patient> optional = repository.findById(id);
        if (optional.isEmpty()) {
            throw new ArgsException("患者信息[{0}]不存在", id);
        }

        return patientConvert.po2vo(optional.get());
    }

    @Transactional(readOnly = true)
    public PatientVO findByBcmSn(String bcmSn) {
        BcmVO bcmVO = iBcmController.find(bcmSn);
        if (Objects.isNull(bcmVO) || Objects.isNull(bcmVO.getPatientId())) {
            throw new ArgsException("该刺激器无关联患者");
        }
        return findPatientById(bcmVO.getPatientId());
    }

    @Transactional(readOnly = true)
    public Patient getPatientById(Long id) {
        if (!repository.existsById(id)) {
            throw new ArgsException("患者信息[{0}]不存在", id);
        }
        return repository.getReferenceById(id);
    }

    @Transactional(readOnly = true)
    public Boolean existsById(Long id) {
        return repository.existsById(id);
    }

    @Transactional(readOnly = true)
    public Page<PatientVO> searchPatientPage(PatientDTO patientDTO, Pageable pageable) throws JsonProcessingException {
        Specification<Patient> specification = searchPatientParam(patientDTO);

        return patientConvert.po2vo(repository.findAll(specification, pageable));
    }

    public PatientVO findByPhone(String phone) {
        Optional<Patient> optional = repository.findByMobileAndStatus(phone, Status.ENABLED);
        if (optional.isEmpty()) {
            throw new ArgsException("未查询到当前手机号码", phone);
        } else {
            Patient patient = optional.get();
            return patientConvert.po2vo(patient);
        }
    }

    @Transactional
    @Caching(evict = {
            @CacheEvict(cacheNames = CacheConstants.CACHE_NAMES_PATIENT_PAGE, allEntries = true),
            @CacheEvict(cacheNames = CacheConstants.CACHE_NAMES_ARCHIVES, allEntries = true),
            @CacheEvict(key = "#p0")
    })
    public PatientVO update(Long id, PatientDTO patientDTO) {

        Optional<Patient> optional = repository.findById(id);
        if (optional.isEmpty()) {
            throw new ArgsException("患者[{0}]不存在", id);
        }
        Patient patient = optional.get();


        if (Objects.nonNull(patientDTO.getIdCardType())) {
            checkID(patientDTO.getIdCardType(), patientDTO.getIdCard());
            if (repository.existsByIdCardTypeAndIdCardAndIdNot(patientDTO.getIdCardType(), patientDTO.getIdCard(), patient.getId())) {
                throw new ArgsException("[{0}]证件号[{1}]已存在", patientDTO.getIdCardType(), patientDTO.getIdCard());
            }
        }

        if (Objects.nonNull(patientDTO.getMobile()) && !patientDTO.getMobile().equals(patient.getMobile())) {
            if (repository.existsByMobile(patientDTO.getMobile())) {
                throw new ArgsException("手机号码[{0}]已存在", patientDTO.getMobile());
            }
        }

        if (Objects.nonNull(patientDTO.getName()) && !patientDTO.getName().equals(patient.getName())) {
            patient.setPyName(getPyName(patientDTO.getName()).toString());
        }

        if (Objects.nonNull(patientDTO.getSurgeryDate())) {
            patientDTO.setSortTime(patientDTO.getSurgeryDate());
        }

        if (Objects.nonNull(patientDTO.getLastSchemeDate())) {
            patientDTO.setSortTime(patientDTO.getLastSchemeDate());
        }

        log.info("修改患者信息为{0}", patientDTO);

        patientConvert.dto2po(patientDTO, patient);
        return patientConvert.po2vo(repository.saveAndFlush(patient));
    }


    private Specification<Patient> searchPatientParam(PatientDTO patientDTO) {
        List<Long> orgIds = getOrgIds();
        return (root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();
            if (StringUtils.hasText(patientDTO.getName())) {
                predicates.add(criteriaBuilder.like(root.get("name"), "%" + patientDTO.getName() + "%"));
            }
            if (StringUtils.hasText(patientDTO.getIdCard())) {
                predicates.add(criteriaBuilder.like(root.get("idCard"), "%" + patientDTO.getIdCard() + "%"));
            }
            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };
    }

    @Caching(evict = {
            @CacheEvict(cacheNames = {CacheConstants.CACHE_NAMES_PATIENT_PAGE}, allEntries = true),
            @CacheEvict(key = "#p0.patientId")
    })
    @Transactional
    public void updateSchemeDate(@NotNull AddSchemeBO addSchemeBO) {
        log.info("收到程控日期更改消息%s%s".formatted(addSchemeBO.getPatientId(), addSchemeBO.getSchemeDate(), addSchemeBO.getScheduledDate()));

        this.update(addSchemeBO.getPatientId(), PatientDTO.builder()
                .lastSchemeDate(addSchemeBO.getSchemeDate())
                .scheduledDate(addSchemeBO.getScheduledDate())
                .build());
    }

    @Transactional(readOnly = true)
    public List<PatientGenderStatisticsVO> statisticsByGender(Long diseaseId) {
        return repository.statisticsByGenderAndProvince(diseaseId);
    }

    @Transactional(readOnly = true)
    public List<PatientProvinceStatisticsVO> statisticsByProvince(Long diseaseId) {
        return repository.statisticsByProvince(diseaseId);
    }

    @Transactional(readOnly = true)
    public List<PatientHospitalStatisticsVO> statisticsByHospital(Long diseaseId) {
        List<OrgVO> orges = iOrgController.findOrges();
        List<Long> orgIds = new ArrayList<>();
        for (OrgVO orge : orges) {
            orgIds.add(orge.getId());
        }
        List<PatientHospitalStatisticsVO> patientHospitalStatisticsVOS = repository.statisticsByHospital(diseaseId, orgIds);
        for (PatientHospitalStatisticsVO patientHospitalStatisticsVO : patientHospitalStatisticsVOS) {
            Long currentOrgId = patientHospitalStatisticsVO.getHospitalId();
            for (OrgVO orge : orges) {
                if (orge.getId().equals(currentOrgId)) {
                    patientHospitalStatisticsVO.setProvince(orge.getAddress().getProvince());
                    patientHospitalStatisticsVO.setLongitude(orge.getAddress().getLongitude());
                    patientHospitalStatisticsVO.setLatitude(orge.getAddress().getLatitude());
                }
            }
        }
        return patientHospitalStatisticsVOS;
    }

    @Transactional(readOnly = true)
    public List<PatientAgeStatisticsVO> statisticsByAge(Long diseaseId) {
        List<Patient> patients = repository.findByDiseaseId(diseaseId);
        ArrayList<PatientAgeStatisticsVO> list = new ArrayList<>();
        for (int i = 0; i < 11; i++) {
            list.add(PatientAgeStatisticsVO.builder().age(i * 10).count(0L).build());
        }

        patients.forEach(p -> {
            Calendar instance = Calendar.getInstance();
            instance.setTime(p.getBirthday());
            int patientYear = instance.get(Calendar.YEAR);

            int currentYear = (Calendar.getInstance().get(Calendar.YEAR));
            int age = currentYear - patientYear;
            if (!(age < 0)) {
                PatientAgeStatisticsVO ageStatisticsVO = list.get(age / 10);
                ageStatisticsVO.setCount(ageStatisticsVO.getCount() + 1);
            }

        });
        return list;
    }

    @Transactional(readOnly = true)
    public boolean existsPatientByTest(Test test) {
        return repository.existsByTestsContains(test);
    }
}
