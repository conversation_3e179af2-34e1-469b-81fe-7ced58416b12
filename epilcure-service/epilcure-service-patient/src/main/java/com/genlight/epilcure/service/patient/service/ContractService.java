package com.genlight.epilcure.service.patient.service;

import com.genlight.epilcure.api.patient.pojo.dto.ContractDTO;
import com.genlight.epilcure.api.patient.pojo.vo.ContractVO;
import com.genlight.epilcure.common.core.dao.enums.Status;
import com.genlight.epilcure.common.core.service.BaseService;
import com.genlight.epilcure.common.core.service.exception.ArgsException;
import com.genlight.epilcure.service.patient.constants.CacheConstants;
import com.genlight.epilcure.service.patient.dao.entity.Contract;
import com.genlight.epilcure.service.patient.dao.entity.Patient;
import com.genlight.epilcure.service.patient.dao.repository.ContractRepository;
import com.genlight.epilcure.service.patient.pojo.convert.ContractConvert;
import jakarta.annotation.Resource;
import jakarta.persistence.criteria.Predicate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.annotation.Caching;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Service
@CacheConfig(cacheNames = CacheConstants.CACHE_NAMES_CONTRACT)
public class ContractService extends BaseService<Contract, Long, ContractRepository> {

    @Autowired
    private ContractConvert contractConvert;
    @Resource
    private PatientService patientService;

    @Transactional
    @Caching(evict = {
            @CacheEvict(cacheNames = CacheConstants.CACHE_NAMES_CONTRACT_FINDS, allEntries = true),
            @CacheEvict(cacheNames = CacheConstants.CACHE_NAMES_CONTRACT_PAGE, allEntries = true)
    })
    public ContractVO addContract(ContractDTO contractDTO) {
        if (!patientService.existsById(contractDTO.getPatientId())) {
            throw new ArgsException("患者[{0}]不存在", contractDTO.getPatientId());
        }


        Contract contract = contractConvert.dto2po(contractDTO);
        //这段代码没用上
        List<Contract> byPatientIdAndStatusIn = repository.findByPatientIdAndStatus(contractDTO.getPatientId(), Status.ENABLED);
        for (Contract patientContract : byPatientIdAndStatusIn) {
            patientContract.setStatus(Status.DISABLED);
        }
        contract.setPatient(Patient.builder().id(contractDTO.getPatientId()).build());
        contract.setAdviserId(getUserId());
        return contractConvert.po2vo(repository.save(contract));
    }

    @Cacheable(cacheNames = CacheConstants.CACHE_NAMES_CONTRACT_FINDS, key = "#p0")
    @Transactional(readOnly = true)
    public ContractVO getContractByPatientId(Long patientId) {
        if (!repository.existsByPatientId(patientId)) {
            return null;
        }
        List<Contract> contracts = repository.findByPatientIdAndStatus(patientId, Status.ENABLED);
        return contractConvert.po2vo(contracts.get(0));
    }

    @Transactional(readOnly = true)
    @Cacheable(key = "#p0")
    public ContractVO getContractById(Long id) {
        Optional<Contract> optional = repository.findById(id);
        if (optional.isEmpty()) {
            throw new ArgsException("档案Id[{0}]不存在", id);
        }
        return contractConvert.po2vo(optional.get());
    }

    @Cacheable(cacheNames = CacheConstants.CACHE_NAMES_CONTRACT_PAGE)
    public List<ContractVO> searchContract(ContractDTO contractDTO) {
        Specification<Contract> contractSpecification = searchConTractParam(contractDTO);
        return contractConvert.po2vo(repository.findAll(contractSpecification));
    }

    @Transactional
    @Caching(evict = {
            @CacheEvict(cacheNames = CacheConstants.CACHE_NAMES_CONTRACT_PAGE, allEntries = true),
            @CacheEvict(cacheNames = CacheConstants.CACHE_NAMES_CONTRACT_FINDS, allEntries = true),
            @CacheEvict(key = "#p0")
    })
    public boolean updateContract(Long id, ContractDTO contractDTO) {
        Optional<Contract> optional = repository.findById(id);
        Contract contract = optional.get();
        contractConvert.dto2po(contractDTO, contract);
        repository.save(contract);
        return true;
    }


    private Specification<Contract> searchConTractParam(ContractDTO contractDTO) {
        return (root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();
            if (contractDTO.getPatientId() != null) {
                predicates.add(criteriaBuilder.equal(root.get("patientId"), contractDTO.getPatientId()));
            }
            if (contractDTO.getStatus() != null) {
                predicates.add(criteriaBuilder.equal(root.get("status"), contractDTO.getStatus()));
            }
            return criteriaBuilder.and(predicates.toArray(new Predicate[predicates.size()]));
        };
    }

//    @Caching(evict = {
//            @CacheEvict(cacheNames = {CacheConstants.CACHE_NAMES_CONTRACT,
//                    CacheConstants.CACHE_NAMES_CONTRACT_FINDS,
//                    CacheConstants.CACHE_NAMES_CONTRACT}, allEntries = true)
//    })
//    @Transactional
//    public boolean updateContractUser(UserBO userBO) {
//        List<Contract> list = repository.findByUserId(userBO.getId());
//        list.stream().forEach(c -> {
//            c.setUserName(userBO.getName());
//        });
//
//        repository.saveAll(list);
//        return true;
//    }


}
