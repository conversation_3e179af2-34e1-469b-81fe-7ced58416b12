package com.genlight.epilcure.service.patient.pojo.convert;

import com.genlight.epilcure.api.patient.pojo.dto.PatientDeviceDTO;
import com.genlight.epilcure.api.patient.pojo.vo.PatientDeviceVO;
import com.genlight.epilcure.common.core.pojo.convert.BaseConvert;
import com.genlight.epilcure.service.patient.dao.entity.PatientDevice;
import org.mapstruct.*;

@Mapper(config = BaseConvert.class)
public interface PatientDeviceConvert extends BaseConvert<PatientDevice, PatientDeviceVO, PatientDeviceDTO> {

    @Mapping(target = "contractElectrodeModels", ignore = true)
    @Mapping(target = "bcmModelId", ignore = true)
    @Mapping(target = "bppModelId", ignore = true)
    @BeanMapping(nullValueCheckStrategy = NullValueCheckStrategy.ON_IMPLICIT_CONVERSION)
    PatientDevice dto2poDevice(PatientDeviceDTO patientDeviceDTO, @MappingTarget PatientDevice patientDevice);

    @Mapping(target = "bcmId", ignore = true)
    @Mapping(target = "bcmSn", ignore = true)
    @Mapping(target = "bppId", ignore = true)
    @Mapping(target = "bppSn", ignore = true)
    @Mapping(target = "surgeryElectrodes", ignore = true)
    @Mapping(target = "check", ignore = true)
    @Mapping(target = "status", expression = "java(java.util.Optional.ofNullable(patientDeviceDTO.getStatus()).orElse(com.genlight.epilcure.common.core.dao.enums.Status.ADD))")
    @BeanMapping(nullValueCheckStrategy = NullValueCheckStrategy.ON_IMPLICIT_CONVERSION)
    PatientDevice dto2poModel(PatientDeviceDTO patientDeviceDTO);


    @Mapping(target = "bppId", ignore = true)
    @Mapping(target = "bppSn", ignore = true)
    @Mapping(target = "bppModelId", ignore = true)
    @Mapping(target = "surgeryElectrodes", ignore = true)
    @Mapping(target = "contractElectrodeModels", ignore = true)
    @Mapping(target = "status", expression = "java(java.util.Optional.ofNullable(patientDeviceDTO.getStatus()).orElse(com.genlight.epilcure.common.core.dao.enums.Status.ADD))")
    @BeanMapping(nullValueCheckStrategy = NullValueCheckStrategy.ON_IMPLICIT_CONVERSION)
    PatientDevice dto2poBcm(PatientDeviceDTO patientDeviceDTO);

    @Mapping(target = "bppId", ignore = true)
    @Mapping(target = "bppSn", ignore = true)
    @Mapping(target = "bppModelId", ignore = true)
    @Mapping(target = "surgeryElectrodes", ignore = true)
    @Mapping(target = "contractElectrodeModels", ignore = true)
    @BeanMapping(nullValueCheckStrategy = NullValueCheckStrategy.ON_IMPLICIT_CONVERSION)
    PatientDevice dto2poBcm(PatientDeviceDTO patientDeviceDTO, @MappingTarget PatientDevice patientDevice);

    @Mapping(target = "bcmId", ignore = true)
    @Mapping(target = "bcmSn", ignore = true)
    @Mapping(target = "bcmModelId", ignore = true)
    @Mapping(target = "bppId", ignore = true)
    @Mapping(target = "bppSn", ignore = true)
    @Mapping(target = "bppModelId", ignore = true)
    @Mapping(target = "check", ignore = true)
    @Mapping(target = "status", expression = "java(java.util.Optional.ofNullable(patientDeviceDTO.getStatus()).orElse(com.genlight.epilcure.common.core.dao.enums.Status.ADD))")
    @BeanMapping(nullValueCheckStrategy = NullValueCheckStrategy.ON_IMPLICIT_CONVERSION)
    PatientDevice dto2poEle(PatientDeviceDTO patientDeviceDTO);

    @Mapping(target = "bcmId", ignore = true)
    @Mapping(target = "bcmSn", ignore = true)
    @Mapping(target = "bcmModelId", ignore = true)
    @Mapping(target = "bppId", ignore = true)
    @Mapping(target = "bppSn", ignore = true)
    @Mapping(target = "bppModelId", ignore = true)
    @Mapping(target = "check", ignore = true)
    @Mapping(target = "contractElectrodeModels", ignore = true)
    @BeanMapping(nullValueCheckStrategy = NullValueCheckStrategy.ON_IMPLICIT_CONVERSION)
    PatientDevice dto2poEle(PatientDeviceDTO patientDeviceDTO, @MappingTarget PatientDevice patientDevice);

    @Mapping(target = "bcmId", ignore = true)
    @Mapping(target = "bcmSn", ignore = true)
    @Mapping(target = "bcmModelId", ignore = true)
    @Mapping(target = "surgeryElectrodes", ignore = true)
    @Mapping(target = "contractElectrodeModels", ignore = true)
    @Mapping(target = "check", ignore = true)
    @Mapping(target = "status", expression = "java(java.util.Optional.ofNullable(patientDeviceDTO.getStatus()).orElse(com.genlight.epilcure.common.core.dao.enums.Status.ADD))")
    @BeanMapping(nullValueCheckStrategy = NullValueCheckStrategy.ON_IMPLICIT_CONVERSION)
    PatientDevice dto2poBpp(PatientDeviceDTO patientDeviceDTO);

    @Mapping(target = "bcmId", ignore = true)
    @Mapping(target = "bcmSn", ignore = true)
    @Mapping(target = "bcmModelId", ignore = true)
    @Mapping(target = "surgeryElectrodes", ignore = true)
    @Mapping(target = "contractElectrodeModels", ignore = true)
    @Mapping(target = "check", ignore = true)
    @BeanMapping(nullValueCheckStrategy = NullValueCheckStrategy.ON_IMPLICIT_CONVERSION)
    PatientDevice dto2poBpp(PatientDeviceDTO patientDeviceDTO, @MappingTarget PatientDevice patientDevice);
}
