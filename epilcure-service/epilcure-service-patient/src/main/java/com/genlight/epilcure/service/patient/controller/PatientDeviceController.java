package com.genlight.epilcure.service.patient.controller;

import com.fasterxml.jackson.annotation.JsonView;
import com.genlight.epilcure.api.patient.pojo.dto.PatientDeviceDTO;
import com.genlight.epilcure.api.patient.pojo.dto.PatientDeviceDTO.AddBcm;
import com.genlight.epilcure.api.patient.pojo.dto.PatientDeviceDTO.AddDevice;
import com.genlight.epilcure.api.patient.pojo.dto.PatientDeviceDTO.AddEle;
import com.genlight.epilcure.api.patient.pojo.dto.PatientDeviceDTO.AddModel;
import com.genlight.epilcure.api.patient.pojo.vo.PatientDeviceVO;
import com.genlight.epilcure.common.core.dao.enums.Status;
import com.genlight.epilcure.common.core.mvc.response.JsonResult;
import com.genlight.epilcure.common.core.pojo.convert.qualified.Basic;
import com.genlight.epilcure.common.core.pojo.valid.Add;
import com.genlight.epilcure.common.core.pojo.view.SummaryView;
import com.genlight.epilcure.service.patient.pojo.vo.CheckDeviceVO;
import com.genlight.epilcure.service.patient.service.PatientDeviceService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/patient/PatientDevices")
@Tag(name = "PatientDeviceController", description = "患者设备相关接口")
public class PatientDeviceController {
    @Resource
    private PatientDeviceService patientDeviceService;

    @GetMapping("/patients/{id}")
    @Operation(summary = "根据患者Id查询患者设备")
    public JsonResult<PatientDeviceVO> find(@PathVariable Long id) {
        return JsonResult.ok(patientDeviceService.findByPatientStatus(id, Status.ENABLED, Status.ADD));
    }

    @PostMapping
    @Operation(summary = "添加完整患者设备信息")
    public JsonResult<PatientDeviceVO> addSummary(@JsonView(Add.class) @Validated(Add.class) @RequestBody PatientDeviceDTO patientDeviceDTO) {
        return JsonResult.ok(patientDeviceService.add(patientDeviceDTO));
    }

    @PostMapping("/addDeviceModel")
    @Operation(summary = "添加患者设备型号")
    @JsonView(Basic.class)
    public JsonResult<PatientDeviceVO> addModel(@JsonView(AddModel.class) @Validated(AddModel.class) @RequestBody PatientDeviceDTO patientDeviceDTO) {
        return JsonResult.ok(patientDeviceService.addModel(patientDeviceDTO));
    }

    @GetMapping("/find/patient/{patientId}/status/{status}")
    @Operation(summary = "根据状态获取设备信息")
    public JsonResult<PatientDeviceVO> findByStatus(@PathVariable Long patientId, @PathVariable Status status) {
        return JsonResult.ok(patientDeviceService.findByPatientStatus(patientId, status));
    }

    @PostMapping("/addDevice")
    @Operation(summary = "添加患者设备")
    @JsonView(SummaryView.class)
    public JsonResult<PatientDeviceVO> addDevice(@JsonView(AddDevice.class) @Validated(AddDevice.class) @RequestBody PatientDeviceDTO patientDeviceDTO) {
        return JsonResult.ok(patientDeviceService.addDevice(patientDeviceDTO));
    }

    @PostMapping("/addDeviceOffline")
    @Operation(summary = "添加患者设备")
    @JsonView(SummaryView.class)
    public JsonResult<PatientDeviceVO> addDeviceOffline(@JsonView(SummaryView.class) @Validated({AddBcm.class, AddEle.class}) @RequestBody PatientDeviceDTO patientDeviceDTO) {
        return JsonResult.ok(patientDeviceService.addDeviceISBS(patientDeviceDTO));
    }

    @PostMapping("checkDeviceOffline")
    @Operation(summary = "校验设备合法性")
    @JsonView(SummaryView.class)
    public JsonResult<List<CheckDeviceVO>> checkDeviceOffline(@JsonView(SummaryView.class) @Validated({AddBcm.class, AddEle.class}) @RequestBody PatientDeviceDTO patientDeviceDTO) {
        return JsonResult.ok(patientDeviceService.checkDeviceISBS(patientDeviceDTO));
    }

    @PostMapping("/checkDevice")
    @Operation(summary = "检测设备信息")
    public JsonResult<String> checkDevice(@JsonView(AddDevice.class) @Validated(AddDevice.class) @RequestBody PatientDeviceDTO patientDeviceDTO) {
        patientDeviceService.checkDevice(patientDeviceDTO);
        return JsonResult.ok("检测成功");
    }

    @PostMapping({"/addBcm/{id}"})
    @Operation(summary = "已有设备信息,添加患者刺激器信息")
    @JsonView(SummaryView.class)
    public JsonResult<PatientDeviceVO> addBcm(@PathVariable Long id, @JsonView(AddBcm.class) @Validated(AddBcm.class) @RequestBody PatientDeviceDTO patientDeviceDTO) {
        return JsonResult.ok(patientDeviceService.addBcmAndModel(id, patientDeviceDTO));
    }

    @PostMapping({"/addBcm"})
    @Operation(summary = "添加患者刺激器信息")
    @JsonView(SummaryView.class)
    public JsonResult<PatientDeviceVO> addBcm(@JsonView(AddBcm.class) @Validated(AddBcm.class) @RequestBody PatientDeviceDTO patientDeviceDTO) {
        return JsonResult.ok(patientDeviceService.addBcmAndModel(null, patientDeviceDTO));
    }

    @PostMapping("/addEle/{id}")
    @Operation(summary = "添加患者电极信息")
    @JsonView(SummaryView.class)
    public JsonResult<PatientDeviceVO> addEle(@PathVariable Long id, @JsonView(AddEle.class) @Validated(AddEle.class) @RequestBody PatientDeviceDTO patientDeviceDTO) {
        return JsonResult.ok(patientDeviceService.addElectrodeAndModel(id, patientDeviceDTO));
    }

    @PostMapping("/addEle")
    @Operation(summary = "添加患者电极信息")
    @JsonView(SummaryView.class)
    public JsonResult<PatientDeviceVO> addEle(@JsonView(AddEle.class) @Validated(AddEle.class) @RequestBody PatientDeviceDTO patientDeviceDTO) {
        return JsonResult.ok(patientDeviceService.addElectrodeAndModel(null, patientDeviceDTO));
    }
}
