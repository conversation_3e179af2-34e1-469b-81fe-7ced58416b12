package com.genlight.epilcure.service.patient.constants;

/**
 * <AUTHOR>
 * @Date 2022/6/4 17:09
 * @Version 1.0.0
 **/
public interface CacheConstants {
    String CACHE_NAMES_PATIENT = "patient";
    String CACHE_NAMES_PATIENT_PAGE = "patientPage";


    String CACHE_NAMES_CONTRACT = "contract";

    String CACHE_NAMES_CONTRACT_FINDS = "contractFinds";

    String CACHE_NAMES_CONTRACT_PAGE = "contractPage";

    String CACHE_NAMES_SURGERY = "surgery";

    String CACHE_NAMES_SURGERY_PAGE = "surgeryPage";

    String CACHE_NAMES_MEDICALRECORD = "medicalRecord";

    String CACHE_NAMES_MEDICALRECORD_PAGE = "medicalRecordPage";

    String CACHE_NAMES_MEDICALRECORD_PATIENT = "medicalRecordPatient";

    String CACHE_NAMES_PATIENT_DEVICE = "patientDevice";

    String CACHE_NAMES_PATIENT_DEVICE_PAGE = "patientDevicePage";

    String CACHE_NAMES_PATIENT_SURGERY_SCHEME = "patientSurgeryScheme";

    String CACHE_NAMES_PATIENT_SURGERY_SCHEME_FINDS = "patientSurgerySchemeFinds";

    String CACHE_NAMES_ARCHIVES = "archives";

    String CACHE_TEST = "patient::test";

    String CACHE_TEST_LIST = "patient::test::list";
}
