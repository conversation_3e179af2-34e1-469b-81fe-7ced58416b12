package com.genlight.epilcure.service.patient.controller;

import com.fasterxml.jackson.annotation.JsonView;
import com.genlight.epilcure.common.core.mvc.response.JsonResult;
import com.genlight.epilcure.common.core.pojo.valid.Find;
import com.genlight.epilcure.common.core.pojo.valid.Update;
import com.genlight.epilcure.service.patient.pojo.dto.EpilepsyFrequencyDTO;
import com.genlight.epilcure.service.patient.pojo.vo.EpilepsyFrenquencyVO;
import com.genlight.epilcure.service.patient.service.EpilepsyFrequencyService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR> maoyan
 * @Date : 2022/9/26 14:27
 * @Version :1.0.0
 */
@RestController
@RequestMapping("/api/patient/epilepsy")
@Tag(name = "EpilepsyFrequencyController", description = "癫痫发作频率相关接口")
public class EpilepsyFrequencyController {
    @Resource
    private EpilepsyFrequencyService epilepsyFrequencyService;

    @PutMapping("/epilepsyFrequency")
    @Operation(summary = "通过档案Id修改癫痫发作频率（包括新增功能）")
    @JsonView(Update.class)
    public JsonResult<EpilepsyFrenquencyVO> updateSeizures(@Validated(Update.class) @RequestBody @JsonView(Update.class) EpilepsyFrequencyDTO epilepsyFrequencyDTO) {
        return JsonResult.ok(epilepsyFrequencyService.updateEpilepsyStatistics(epilepsyFrequencyDTO));
    }

    @GetMapping("{id}/epilepsyFrenquency")
    @Operation(summary = "通过档案Id查找对应的癫痫发作频率")
    @JsonView(Find.class)
    public JsonResult<EpilepsyFrenquencyVO> findSeizures(@PathVariable Long id) {
        return JsonResult.ok(epilepsyFrequencyService.findEpilepsyStatistics(id));
    }

}