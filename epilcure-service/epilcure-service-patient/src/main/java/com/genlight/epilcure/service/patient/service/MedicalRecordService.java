package com.genlight.epilcure.service.patient.service;

import com.genlight.epilcure.api.config.feign.*;
import com.genlight.epilcure.api.patient.pojo.dto.*;
import com.genlight.epilcure.api.patient.pojo.vo.MedicalRecordVO;
import com.genlight.epilcure.common.core.dao.enums.Status;
import com.genlight.epilcure.common.core.service.BaseService;
import com.genlight.epilcure.common.core.service.exception.ArgsException;
import com.genlight.epilcure.service.patient.constants.CacheConstants;
import com.genlight.epilcure.service.patient.dao.entity.*;
import com.genlight.epilcure.service.patient.dao.repository.MedicalRecordRepository;
import com.genlight.epilcure.service.patient.pojo.convert.MedicalRecordConvert;
import jakarta.annotation.Resource;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.annotation.Caching;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @Date 2022/6/12 13:53
 * @Version 1.0.0
 **/
@Service
@CacheConfig(cacheNames = CacheConstants.CACHE_NAMES_MEDICALRECORD)
public class MedicalRecordService extends BaseService<MedicalRecord, Long, MedicalRecordRepository> {
    @Resource
    private IDrugsController iDrugsController;

    @Resource
    private ICasueController iCasueController;

    @Resource
    private ISymptomController iSymptomController;
    @Resource
    private ISymptomTypeController iSymptomTypeController;

    @Resource
    private IDiagnosisController iDiagnosisController;
    @Resource
    private PatientService patientService;

    @Resource
    private ArchivesService archivesService;

    @Resource
    private MedicalRecordConvert medicalRecordConvert;


    /**
     * 新增病历
     */
    @Transactional
    @Caching(evict = {
            @CacheEvict(cacheNames = CacheConstants.CACHE_NAMES_MEDICALRECORD_PATIENT, key = "#p0.getArchivesId()"),
            @CacheEvict(cacheNames = CacheConstants.CACHE_NAMES_MEDICALRECORD_PAGE, allEntries = true)
    })
    public MedicalRecordVO addMedicalRecord(MedicalRecordDTO medicalRecordDTO) {
        if (!patientService.existsById(medicalRecordDTO.getPatientId())) {
            throw new ArgsException("患者[{0}]不存在", medicalRecordDTO.getPatientId());
        }

        // 检查数据合法性
        checkMedicalRecord(medicalRecordDTO);

        medicalRecordDTO.setUserId(getUserId());
        MedicalRecord medicalRecord = medicalRecordConvert.dto2po(medicalRecordDTO);
        medicalRecord.setPatient(Patient.builder().id(medicalRecordDTO.getPatientId()).build());
        medicalRecord.setArchives(Archives.builder().id(medicalRecordDTO.getArchivesId()).build());
        initMedicalRecord(medicalRecord);

        // 修改患者复诊时间
        patientService.update(medicalRecordDTO.getPatientId(), PatientDTO.builder().scheduledDate(medicalRecord.getScheduledDate()).build());

        if (Objects.nonNull(medicalRecordDTO.getMedicalRecordNo())) {
            archivesService.update(medicalRecordDTO.getArchivesId(), ArchivesDTO.builder()
                    .medicalRecordNo(medicalRecordDTO.getMedicalRecordNo()).build());
        }

        List<MedicalRecord> list = repository.findByArchivesIdAndPatientIdAndStatus(medicalRecordDTO.getArchivesId(), medicalRecordDTO.getPatientId(), Status.ENABLED);
        list.forEach(mr -> mr.setStatus(Status.DISABLED));
        MedicalRecordVO medicalRecordVO = medicalRecordConvert.po2vo(repository.save(medicalRecord));
        return medicalRecordVO;
    }

    @Transactional(readOnly = true)
    @Cacheable(cacheNames = CacheConstants.CACHE_NAMES_MEDICALRECORD_PAGE, key = "#p0+#p1")
    public Page<MedicalRecordVO> finds(MedicalRecordDTO medicalRecordDTO, Pageable pageable) {
        return medicalRecordConvert.po2vo(repository.findAll((Root<MedicalRecord> root, CriteriaQuery<?> query, CriteriaBuilder criteriaBuilder) -> {
            Predicate predicate = criteriaBuilder.conjunction();
            if (Objects.nonNull(medicalRecordDTO.getUserId())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("userId"), medicalRecordDTO.getUserId()));
            }
            if (Objects.nonNull(medicalRecordDTO.getPatientId())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("patient.id"), medicalRecordDTO.getPatientId()));
            }
            if (Objects.nonNull(medicalRecordDTO.getArchivesId())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("archives.id"), medicalRecordDTO.getArchivesId()));
            }
            return predicate;
        }, pageable));
    }

    @Transactional(readOnly = true)
    @Cacheable(cacheNames = CacheConstants.CACHE_NAMES_MEDICALRECORD_PATIENT, key = "#p0")
    public MedicalRecordVO findLastByArchivesId(Long archivesId) {
        Optional<MedicalRecord> optional = repository.findByArchivesIdAndStatus(archivesId, Status.ENABLED);
        if (optional.isPresent()) {
            return medicalRecordConvert.po2vo(optional.get());
        }
        return null;
    }

    @Caching(evict = {
            @CacheEvict(cacheNames = CacheConstants.CACHE_NAMES_MEDICALRECORD_PAGE, allEntries = true),
            @CacheEvict(cacheNames = CacheConstants.CACHE_NAMES_MEDICALRECORD_PATIENT, allEntries = true),
            @CacheEvict(key = "#p0")
    })
    @Transactional
    public MedicalRecordVO update(Long id, MedicalRecordDTO medicalRecordDTO) {
        Optional<MedicalRecord> optional = repository.findById(id);
        if (optional.isEmpty()) {
            throw new ArgsException("修改的病历Id[{0}]不存在");
        }
        checkMedicalRecord(medicalRecordDTO);
        MedicalRecord medicalRecord = optional.get();

        if (Objects.nonNull(medicalRecordDTO.getPatientCauses())) {
            medicalRecord.getPatientCauses().clear();
        }

        if (Objects.nonNull(medicalRecordDTO.getPatientDrugs())) {
            medicalRecord.getPatientDrugs().clear();
        }

        if (Objects.nonNull(medicalRecordDTO.getPatientSymptoms())) {
            medicalRecord.getPatientSymptoms().clear();
        }

        if (Objects.nonNull(medicalRecordDTO.getPatientSymptomTypes())) {
            medicalRecord.getPatientSymptomTypes().clear();
        }

        if (Objects.nonNull(medicalRecordDTO.getPatientDiagnosis())) {
            medicalRecord.getPatientDiagnosis().clear();
        }

        medicalRecordConvert.dto2po(medicalRecordDTO, medicalRecord);
        Patient patient = patientService.getPatientById(medicalRecordDTO.getPatientId());
        patient.setScheduledDate(medicalRecord.getScheduledDate());

        archivesService.updateMedicalNo(medicalRecordDTO.getArchivesId(), medicalRecordDTO.getMedicalRecordNo());

        initMedicalRecord(medicalRecord);
        medicalRecord = repository.saveAndFlush(medicalRecord);
        return medicalRecordConvert.po2vo(medicalRecord);
    }

    private void initMedicalRecord(MedicalRecord medicalRecord) {
        if (Objects.nonNull(medicalRecord.getPatientCauses())) {
            for (PatientCause patientCause : medicalRecord.getPatientCauses()) {
                patientCause.setMedicalRecord(medicalRecord);
            }
        }

        if (Objects.nonNull(medicalRecord.getPatientDrugs())) {
            for (PatientDrugs patientDrug : medicalRecord.getPatientDrugs()) {
                patientDrug.setMedicalRecord(medicalRecord);
            }
            System.out.println(medicalRecord.getPatientDrugs());
        }
        System.out.println(medicalRecord.getPatientDrugs());
        if (Objects.nonNull(medicalRecord.getPatientSymptoms())) {
            for (PatientSymptom patientSymptom : medicalRecord.getPatientSymptoms()) {
                patientSymptom.setMedicalRecord(medicalRecord);
            }
        }

        if (Objects.nonNull(medicalRecord.getPatientSymptomTypes())) {
            for (PatientSymptomType patientSymptomType : medicalRecord.getPatientSymptomTypes()) {
                patientSymptomType.setMedicalRecord(medicalRecord);
            }
        }

        if (Objects.nonNull(medicalRecord.getPatientDiagnosis())) {
            for (PatientDiagnosis patientDiagnosisPo : medicalRecord.getPatientDiagnosis()) {
                patientDiagnosisPo.setMedicalRecord(medicalRecord);
            }
        }
    }


    @Transactional
    private void checkMedicalRecord(MedicalRecordDTO medicalRecordDTO) {
        List<PatientDrugsDTO> patientDrugs = medicalRecordDTO.getPatientDrugs();
        List<Long> ids = new ArrayList<>();
        if (Objects.nonNull(patientDrugs)) {
            for (PatientDrugsDTO patientDrug : patientDrugs) {
                ids.add(patientDrug.getDrugsId());
            }
        }
        iDrugsController.existsByIds(ids);

        ids.clear();
        List<PatientCauseDTO> patientCauses = medicalRecordDTO.getPatientCauses();
        if (Objects.nonNull(patientCauses)) {
            for (PatientCauseDTO patientCause : patientCauses) {
                ids.add(patientCause.getCauseId());
            }
        }
        iCasueController.existsByIds(ids);
        ids.clear();

        List<PatientSymptomDTO> patientSymptoms = medicalRecordDTO.getPatientSymptoms();
        if (Objects.nonNull(patientSymptoms)) {
            for (PatientSymptomDTO patientSymptom : patientSymptoms) {
                ids.add(patientSymptom.getSymptomId());
            }
        }
        iSymptomController.existsByIds(ids);
        ids.clear();

        List<PatientSymptomTypeDTO> patientSymptomTypes = medicalRecordDTO.getPatientSymptomTypes();
        if (Objects.nonNull(patientSymptomTypes)) {
            for (PatientSymptomTypeDTO patientSymptomType : patientSymptomTypes) {
                ids.add(patientSymptomType.getSymptomTypeId());
            }
        }
        iSymptomTypeController.existsByIds(ids);
        ids.clear();

        List<PatientDiagnosisDTO> patientDiagnosis = medicalRecordDTO.getPatientDiagnosis();
        if (Objects.nonNull(patientDiagnosis)) {
            for (PatientDiagnosisDTO diagnosis : patientDiagnosis) {
                ids.add(diagnosis.getDiagnosisId());
            }
        }
        iDiagnosisController.existsByIds(ids);
    }
}
