package com.genlight.epilcure.service.patient.controller;

import com.fasterxml.jackson.annotation.JsonView;
import com.genlight.epilcure.api.patient.pojo.dto.MedicalRecordDTO;
import com.genlight.epilcure.api.patient.pojo.vo.MedicalRecordVO;
import com.genlight.epilcure.common.core.mvc.response.JsonResult;
import com.genlight.epilcure.common.core.pojo.valid.Add;
import com.genlight.epilcure.common.core.pojo.valid.Update;
import com.genlight.epilcure.service.patient.service.MedicalRecordService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @Date 2022/6/16 21:13
 * @Version 1.0.0
 **/
@RestController
@RequestMapping("/api/patient/medicalRecords")
@Tag(name = "MedicalRecordController", description = "病历相关接口")
public class MedicalRecordController {
    @Resource
    private MedicalRecordService medicalRecordService;

    @PostMapping
    @Operation(summary = "新增病历资料")
    @JsonView(Add.class)
    public JsonResult<MedicalRecordVO> add(@Validated(Add.class) @RequestBody MedicalRecordDTO medicalRecordDTO) {
        return JsonResult.ok(medicalRecordService.addMedicalRecord(medicalRecordDTO));
    }

    @PutMapping("/{id}")
    @Operation(summary = "修改病历资料")
    @JsonView(Update.class)
    public JsonResult<MedicalRecordVO> update(@PathVariable Long id, @Validated(Update.class) @RequestBody MedicalRecordDTO medicalRecordDTO) {
        return JsonResult.ok(medicalRecordService.update(id, medicalRecordDTO));
    }

    @GetMapping("/archives/{id}")
    @Operation(summary = "根据患者Id获取最新病历信息")
    public JsonResult<MedicalRecordVO> find(@PathVariable Long id) {
        return JsonResult.ok(medicalRecordService.findLastByArchivesId(id));
    }

    @GetMapping("/finds")
    @Operation(summary = "根据条件获取病历信息")
    public JsonResult<Page<MedicalRecordVO>> finds(MedicalRecordDTO medicalRecordDTO, Pageable pageable) {
        return JsonResult.ok(medicalRecordService.finds(medicalRecordDTO, pageable));
    }
}
