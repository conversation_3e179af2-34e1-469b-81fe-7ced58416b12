package com.genlight.epilcure.service.patient.service;

import com.genlight.epilcure.api.patient.enums.ArchivesStatus;
import com.genlight.epilcure.api.patient.enums.IdCardType;
import com.genlight.epilcure.api.patient.pojo.bo.ArchivesUpdateBO;
import com.genlight.epilcure.api.patient.pojo.dto.*;
import com.genlight.epilcure.api.patient.pojo.vo.ArchivesVO;
import com.genlight.epilcure.api.patient.pojo.vo.ContractVO;
import com.genlight.epilcure.api.patient.pojo.vo.PatientVO;
import com.genlight.epilcure.api.user.feign.IDeptController;
import com.genlight.epilcure.api.user.feign.IOrgController;
import com.genlight.epilcure.api.user.feign.IUserController;
import com.genlight.epilcure.api.user.pojo.bo.DeptBO;
import com.genlight.epilcure.api.user.pojo.bo.OrgBO;
import com.genlight.epilcure.api.user.pojo.vo.DeptVO;
import com.genlight.epilcure.api.user.pojo.vo.OrgVO;
import com.genlight.epilcure.api.user.pojo.vo.UserVO;
import com.genlight.epilcure.common.core.constants.HttpCode;
import com.genlight.epilcure.common.core.dao.enums.Status;
import com.genlight.epilcure.common.core.service.BaseService;
import com.genlight.epilcure.common.core.service.exception.ArgsException;
import com.genlight.epilcure.common.core.service.exception.ServiceException;
import com.genlight.epilcure.service.patient.constants.CacheConstants;
import com.genlight.epilcure.service.patient.dao.entity.Archives;
import com.genlight.epilcure.service.patient.dao.entity.Archives_;
import com.genlight.epilcure.service.patient.dao.entity.Patient;
import com.genlight.epilcure.service.patient.dao.entity.Patient_;
import com.genlight.epilcure.service.patient.dao.repository.ArchivesRepository;
import com.genlight.epilcure.service.patient.pojo.convert.ArchivesConvert;
import io.seata.spring.annotation.GlobalTransactional;
import jakarta.annotation.Resource;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.Join;
import jakarta.persistence.criteria.Predicate;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Service
@CacheConfig(cacheNames = CacheConstants.CACHE_NAMES_ARCHIVES)
public class ArchivesService extends BaseService<Archives, Long, ArchivesRepository> {
    @Resource
    private ArchivesConvert archivesConvert;

    @Resource
    private ArchivesNoService archivesNoService;

    @Resource
    private PatientService patientService;

    @Resource
    private PatientDeviceService patientDeviceService;

    @Resource
    private MedicalRecordService medicalRecordService;
    @Resource
    private PatientSurgerySchemeService patientSurgerySchemeService;

    @Resource
    private ContractService contractService;

    @Resource
    private IOrgController iOrgController;

    @Resource
    private IDeptController iDeptController;

    @Resource
    private IUserController iUserController;

    @Transactional
    public ArchivesVO add(ArchivesDTO archivesDTO) {
        if (repository.existsByArchivesNo(archivesDTO.getArchivesNo())) {
            throw new ArgsException("该合同序列号已被使用");
        }

        if (Objects.isNull(archivesDTO.getArchivesNo())) {
            archivesDTO.setArchivesNo("ISBS-" + archivesNoService.getNo());
        }

        Archives archives = archivesConvert.dto2po(archivesDTO);

        OrgVO orgVO = iOrgController.findOrgById(archivesDTO.getOrgId());
        DeptVO deptVO = iDeptController.findDeptById(archivesDTO.getDeptId());
        archives.setOrgName(orgVO.getName());
        archives.setDeptName(deptVO.getName());
        archives.setDeptPhone(deptVO.getPhone());

        return archivesConvert.po2vo(repository.save(archives));
    }

    @GlobalTransactional(rollbackFor = Exception.class)
    @Transactional
    public ArchivesVO addAndPatient(ArchivesDTO archivesDTO
            , PatientDTO patientDTO
            , PatientDeviceDTO patientDeviceDTO
            , PatientSurgerySchemeDTO patientSurgerySchemeDTO
            , MedicalRecordDTO medicalRecordDTO
            , ContractDTO contractDTO) {
        ArchivesVO archivesVO = add(archivesDTO);

        if (Objects.isNull(patientDTO.getIdCardType())) patientDTO.setIdCardType(IdCardType.Custom);
        patientDTO.setArchivesId(archivesVO.getId());
        PatientVO patientVO = patientService.addPatient(patientDTO);
        archivesVO.setPatient(patientVO);


        if(patientDTO.getIdCardType().equals(IdCardType.Custom)){
            OrgVO orgVO = iOrgController.findOrgById(archivesDTO.getOrgId());
            if(Objects.nonNull(orgVO.getCustomCode())){
                if(patientDTO.getIdCard().indexOf(orgVO.getCustomCode())!=0){
                    throw new ServiceException("[{0}]证件号与组织编号比配");
                }
            }
        }

        // 兼容epilcure.新增患者时添加额外信息
        contractDTO.setPatientId(patientVO.getId());
        patientVO.setContractVO(contractService.addContract(contractDTO));


        medicalRecordDTO.setPatientId(patientVO.getId());
        medicalRecordDTO.setArchivesId(archivesVO.getId());
        patientVO.setMedicalRecordVO(medicalRecordService.addMedicalRecord(medicalRecordDTO));


        patientSurgerySchemeDTO.setPatientId(patientVO.getId());
        patientSurgerySchemeDTO.setArchivesId(archivesVO.getId());
        patientVO.setPatientSurgerySchemeVO(patientSurgerySchemeService.add(patientSurgerySchemeDTO));

        if (Objects.nonNull(patientDeviceDTO)) {
            patientDeviceDTO.setPatientId(patientVO.getId());
            patientVO.setPatientDeviceVO(patientDeviceService.addModel(patientDeviceDTO));
        }

        return archivesVO;
    }

    @Transactional
    public ArchivesVO update(Long id, ArchivesDTO archivesDTO) {
        Optional<Archives> optional = repository.findById(id);
        if (optional.isEmpty()) {
            throw new ArgsException("档案[{0}]不存在", id);
        }
        Archives archives = optional.get();
        if (archives.getEditStatus().equals(Status.DISABLED)) {
            throw new ArgsException("当前档案不可编辑");
        }
        archivesConvert.dto2po(archivesDTO, archives);
        return archivesConvert.po2vo(repository.save(archives));
    }

    @Transactional
    public void update(Long id, ArchivesUpdateBO archivesUpdateBO) {
        Optional<Archives> optional = repository.findById(id);
        if (optional.isEmpty()) {
            throw new ArgsException("档案[{0}]不存在", id);
        }
        Patient patient = optional.get().getPatient();
        if (Objects.nonNull(archivesUpdateBO.getPatientDTO())) {
            patientService.update(patient.getId(), archivesUpdateBO.getPatientDTO());
        }

        if (Objects.nonNull(archivesUpdateBO.getContractDTO())) {
            ContractVO vo = contractService.getContractByPatientId(patient.getId());
            contractService.updateContract(vo.getId(), archivesUpdateBO.getContractDTO());
        }

        if (Objects.nonNull(archivesUpdateBO.getPatientDeviceDTO())) {
            patientDeviceService.updateElectrode(patient.getId(), archivesUpdateBO.getPatientDeviceDTO());
        }
    }

    @Transactional
    public void updateMedicalNo(Long id, String medicalNo) {
        Optional<Archives> optional = repository.findById(id);
        if (optional.isEmpty()) {
            throw new ArgsException("档案[{0}]不存在", id);
        }
        Archives archives = optional.get();
        archivesConvert.dto2po(ArchivesDTO.builder()
                .medicalRecordNo(medicalNo)
                .build(), archives);
        archivesConvert.po2vo(repository.save(archives));
    }

    @Transactional
    public void updateFinishedArchives(Long id, Long patientId, ArchivesDTO archivesDTO) {
        // 将相同医院相同科室的档案过期
        List<Archives> list = repository.findByPatientIdAndOrgIdAndDeptIdAndStatusNotAndIdNot(patientId, archivesDTO.getOrgId(), archivesDTO.getDeptId(), ArchivesStatus.Expire, id);
        list.forEach(a -> a.setStatus(ArchivesStatus.Expire));
        Optional<Archives> optional = repository.findById(id);
        if (optional.isEmpty()) {
            throw new ArgsException("档案[{0}]不存在", id);
        }
        Archives archives = optional.get();
        archivesConvert.dto2po(archivesDTO, archives);
        archivesConvert.po2vo(repository.save(archives));
    }

    @Transactional
    public void updatePatient(Long id, Long patientId) {
        Optional<Archives> optional = repository.findById(id);
        if (optional.isEmpty()) {
            throw new ArgsException("档案[{0}]不存在", id);
        }

        Archives archives = optional.get();
        if (Objects.nonNull(archives.getPatient())) {
            throw new ArgsException("档案[{0}]已有归属患者");
        }

        if (!patientService.existsById(patientId)) {
            throw new ArgsException("患者[{0}]不存在");
        }

        Patient patient = patientService.getPatientById(patientId);
        archives.setPatient(patient);

        archivesConvert.po2vo(repository.save(archives));
    }

    @Transactional(readOnly = true)
    public ArchivesVO findByArchivesNo(String archivesNo) {
        Optional<Archives> optional = repository.findByArchivesNo(archivesNo);

        if (optional.isEmpty()) {
            throw new ArgsException(HttpCode.ARCHIVES_NOT_EXISTS);
        }
        Archives archives = optional.get();

        if (!getOrgIds().contains(archives.getOrgId())) {
            throw new ArgsException(HttpCode.ARCHIVES_NOT_EXISTS);
        }
        ArchivesVO archivesVO = archivesConvert.po2voBySummary(archives);
        return getArchivesFullVO(archivesVO);
    }

    @Transactional(readOnly = true)
    public ArchivesVO findByBcmSn(String sn, ArchivesStatus archivesStatus) {
        PatientVO patientVO = patientService.findByBcmSn(sn);

        UserVO userVO = iUserController.findById(getUserId());
        Optional<Archives> optional = repository.findFirstByPatientIdAndOrgIdAndStatus(patientVO.getId(), userVO.getOrg().getId(), archivesStatus);
        if (optional.isPresent()) {
            Archives archives = optional.get();
            return getArchivesFullVO(archivesConvert.po2voBySummary(archives));
        } else {
            throw new ArgsException("档案信息不存在", HttpCode.ARCHIVES_NOT_EXISTS);
        }
    }

    @Transactional(readOnly = true)
    public ArchivesVO findById(Long id) {
        Optional<Archives> optional = repository.findById(id);
        if (optional.isEmpty()) {
            throw new ArgsException("档案[{0}]不存在", id);
        }
        Archives archives = optional.get();
        if (archives.getStatus().equals(ArchivesStatus.Expire)) {
            throw new ArgsException("档案[{0}]已过期", id);
        }
        return getArchivesFullVO(archivesConvert.po2voBySummary(archives));
    }

    @Transactional(readOnly = true)
    public Page<ArchivesVO> finds(ArchivesDTO archivesDTO, Pageable pageable) {
        Specification<Archives> specification = (root, query, criteriaBuilder) -> {
            Predicate predicate = criteriaBuilder.conjunction();
            if (Objects.nonNull(archivesDTO.getArchivesNo())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.like(root.get("archivesNo"), "%" + archivesDTO.getArchivesNo() + "%"));
            }
            if (Objects.nonNull(archivesDTO.getStatus())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("status"), archivesDTO.getStatus()));
            }
            if (Objects.nonNull(archivesDTO.getNoStatus())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.notEqual(root.get(Archives_.STATUS), archivesDTO.getNoStatus()));
            }
            if (Objects.nonNull(archivesDTO.getFindHasScheme()) && archivesDTO.getFindHasScheme()) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.isNotNull(root.join(Archives_.PATIENT).get(Patient_.LAST_SCHEME_DATE)));

                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get(Archives_.STATUS), ArchivesStatus.Finished));

            }
            if (Objects.nonNull(archivesDTO.getOrgId())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get(Archives_.ORG_ID), archivesDTO.getOrgId()));
            }

            if (Objects.nonNull(archivesDTO.getDeptId())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get(Archives_.DEPT_ID), archivesDTO.getDeptId()));
            }

            if (Objects.nonNull(archivesDTO.getPatientName())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.like(root.join(Archives_.PATIENT).get(Patient_.NAME), "%" + archivesDTO.getPatientName() + "%"));
            }

            if (Objects.nonNull(archivesDTO.getPyName())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.like(root.join(Archives_.PATIENT).get(Patient_.PY_NAME), "%" + archivesDTO.getPyName() + "%"));
            }

            if (Objects.nonNull(archivesDTO.getPatientId())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get(Archives_.PATIENT).get(Patient_.ID), archivesDTO.getPatientId()));
            }

            if (Objects.nonNull(archivesDTO.getIdCard())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.like(root.join(Archives_.PATIENT).get(Patient_.ID_CARD), "%" + archivesDTO.getIdCard() + "%"));
            }

            if (Objects.nonNull(archivesDTO.getIdCardType())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.join(Archives_.PATIENT).get(Patient_.ID_CARD_TYPE), archivesDTO.getIdCardType()));
            }

            if (Objects.nonNull(archivesDTO.getDiseaseId())) {
                Join<Object, Object> medicalRecords = root.join("medicalRecords");
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(medicalRecords.get("diseaseId"), archivesDTO.getDiseaseId()));
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(medicalRecords.get("status"), Status.ENABLED));
            }

            if (Objects.nonNull(archivesDTO.getMedicalRecordNo())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.like(root.get(Archives_.MEDICAL_RECORD_NO), "%" + archivesDTO.getMedicalRecordNo() + "%"));
            }

            if (Objects.nonNull(archivesDTO.getStartDate())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.greaterThan(root.get("createTime"), archivesDTO.getStartDate()));
            }

            if (Objects.nonNull(archivesDTO.getEndDate())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.greaterThan(root.get("createTime"), archivesDTO.getEndDate()));
            }

            if (Objects.nonNull(archivesDTO.getStartSortTime())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.greaterThanOrEqualTo(root.get(Archives_.PATIENT).get(Patient_.SORT_TIME), archivesDTO.getStartSortTime()));
            }

            if (Objects.nonNull(archivesDTO.getEndSortTime())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.lessThanOrEqualTo(root.get(Archives_.PATIENT).get(Patient_.SORT_TIME), archivesDTO.getEndSortTime()));
            }

            predicate = criteriaBuilder.and(predicate, criteriaBuilder.isNotNull(root.get(Archives_.PATIENT)));

            CriteriaBuilder.In<Object> in = criteriaBuilder.in(root.get("orgId"));
            getOrgIds().forEach(in::value);
            predicate = criteriaBuilder.and(predicate, in);
            return predicate;
        };
        return archivesConvert.po2vo(repository.findAll(specification, pageable));
    }

    @Transactional(readOnly = true)
    private ArchivesVO getArchivesFullVO(ArchivesVO archivesVO) {
        if (Objects.nonNull(archivesVO.getPatient())) {

            archivesVO.getPatient().setPatientDeviceVO(patientDeviceService.findByPatientStatus(archivesVO.getPatient().getId(), Status.ENABLED, Status.ADD));


            archivesVO.getPatient().setMedicalRecordVO(medicalRecordService.findLastByArchivesId(archivesVO.getId()));


            archivesVO.getPatient().setPatientSurgerySchemeVO(patientSurgerySchemeService.findByArchivesId(archivesVO.getId()));


            archivesVO.getPatient().setContractVO(contractService.getContractByPatientId(archivesVO.getPatient().getId()));

        }

        return archivesVO;
    }

    @Transactional
    @CacheEvict(cacheNames = {CacheConstants.CACHE_NAMES_PATIENT_PAGE, CacheConstants.CACHE_NAMES_PATIENT}, allEntries = true)
    public void updateOrgName(OrgBO message) {

        List<Archives> archivesList = repository.findByOrgId(message.getId());
        for (Archives archives : archivesList) {
            archives.setOrgName(message.getName());
        }
        repository.saveAll(archivesList);
    }

    @Transactional
    @CacheEvict(cacheNames = {CacheConstants.CACHE_NAMES_PATIENT_PAGE, CacheConstants.CACHE_NAMES_PATIENT}, allEntries = true)
    public void updateDeptName(DeptBO message) {

        List<Archives> archivesList = repository.findByDeptId(message.getId());
        for (Archives archives : archivesList) {
            archives.setDeptName(message.getName());
        }
        repository.saveAll(archivesList);
    }

    @Transactional(readOnly = true)
    public Archives getArchivesById(Long id) {
        if (!repository.existsById(id)) {
            throw new ArgsException("档案信息[{0}]不存在", id);
        }
        return repository.getReferenceById(id);
    }


}
