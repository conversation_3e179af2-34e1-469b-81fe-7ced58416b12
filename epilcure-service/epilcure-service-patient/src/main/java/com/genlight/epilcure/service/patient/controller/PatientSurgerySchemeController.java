package com.genlight.epilcure.service.patient.controller;

import com.fasterxml.jackson.annotation.JsonView;
import com.genlight.epilcure.api.patient.pojo.dto.PatientSurgerySchemeDTO;
import com.genlight.epilcure.api.patient.pojo.vo.PatientSurgerySchemeVO;
import com.genlight.epilcure.common.core.mvc.response.JsonResult;
import com.genlight.epilcure.common.core.pojo.valid.Add;
import com.genlight.epilcure.service.patient.service.PatientSurgerySchemeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/patient/surgerySchemes")
@Tag(name = "PatientSurgerySchemeController", description = "手术计划相关接口")
public class PatientSurgerySchemeController {
    @Resource
    private PatientSurgerySchemeService patientSurgerySchemeService;

    @PostMapping
    @Operation(summary = "新增手术计划")
    public JsonResult<PatientSurgerySchemeVO> add(@Valid @RequestBody @JsonView(Add.class) PatientSurgerySchemeDTO patientSurgerySchemeDTO) {
        return JsonResult.ok(patientSurgerySchemeService.add(patientSurgerySchemeDTO));
    }

    @GetMapping("/{id}")
    @Operation(summary = "根据Id获取手术计划")
    public JsonResult<PatientSurgerySchemeVO> find(@PathVariable Long id) {
        return JsonResult.ok(patientSurgerySchemeService.find(id));
    }

    @GetMapping("/archives/{id}")
    @Operation(summary = "根据档案Id获取有效的手术计划信息")
    public JsonResult<PatientSurgerySchemeVO> findByPatientEnabled(@PathVariable Long id) {
        return JsonResult.ok(patientSurgerySchemeService.findByArchivesId(id));
    }
}
