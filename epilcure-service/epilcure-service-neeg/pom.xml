<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <artifactId>epilcure-service</artifactId>
        <groupId>com.genlight</groupId>
        <version>${revision}</version>
    </parent>

    <artifactId>epilcure-service-neeg</artifactId>

    <dependencies>
        <dependency>
            <groupId>com.genlight</groupId>
            <artifactId>epilcure-api-neeg</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>biz.source_code.dsp</groupId>
            <artifactId>dsp-collection</artifactId>
        </dependency>
        <dependency>
            <groupId>commons-net</groupId>
            <artifactId>commons-net</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.shardingsphere</groupId>
            <artifactId>shardingsphere-jdbc-core</artifactId>
            <version>${shardingsphere.version}</version>
        </dependency>
        <dependency>
            <groupId>com.genlight</groupId>
            <artifactId>epilcure-api-patient</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.genlight</groupId>
            <artifactId>epilcure-api-device</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.genlight</groupId>
            <artifactId>epilcure-api-scheme</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.genlight</groupId>
            <artifactId>epilcure-api-config</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
        </dependency>
        <dependency>
            <groupId>com.github.rwl</groupId>
            <artifactId>jtransforms</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-seata</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>io.seata</groupId>
                    <artifactId>seata-spring-boot-starter</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>io.seata</groupId>
            <artifactId>seata-spring-boot-starter</artifactId>
        </dependency>
    </dependencies>
    <build>
        <plugins>
            <plugin>
                <groupId>com.google.cloud.tools</groupId>
                <artifactId>jib-maven-plugin</artifactId>
                <version>${jib-maven-plugin.version}</version>
                <configuration>
                    <container>
                        <jvmFlags>
                            <jvmFlag>-Xms256m</jvmFlag>
                            <jvmFlag>-Xmx2048m</jvmFlag>
                            <jvmFlag>--add-opens=java.base/java.lang=ALL-UNNAMED</jvmFlag>
                            <jvmFlag>--add-opens=java.base/java.util=ALL-UNNAMED</jvmFlag>
                            <jvmFlag>--add-opens=java.base/java.text=ALL-UNNAMED</jvmFlag>
                            <jvmFlag>--add-opens=java.base/java.time=ALL-UNNAMED</jvmFlag>
                            <jvmFlag>--add-opens=java.base/java.lang.reflect=ALL-UNNAMED</jvmFlag>
                        </jvmFlags>
                    </container>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
