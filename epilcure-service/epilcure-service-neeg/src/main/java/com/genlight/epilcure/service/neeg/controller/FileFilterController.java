package com.genlight.epilcure.service.neeg.controller;

import com.genlight.epilcure.api.neeg.pojo.dto.UploadFileFilterDTO;
import com.genlight.epilcure.api.neeg.pojo.vo.UploadFileFilterVO;
import com.genlight.epilcure.common.core.mvc.response.JsonResult;
import com.genlight.epilcure.service.neeg.service.UploadFileFilterService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/eeg/file_filter")
@Tag(name = "FileFilterController", description = "文件过滤相关接口")
public class FileFilterController {

    @Resource
    private UploadFileFilterService uploadFileFilterService;

    @GetMapping("/finds")
    @Operation(summary = "分页检索文件过滤列表")
    public JsonResult<Page<UploadFileFilterVO>> finds(UploadFileFilterDTO uploadFileFilterDTO, Pageable pageable) {
        return JsonResult.ok(uploadFileFilterService.findUploadFileFilterByPageable(uploadFileFilterDTO, pageable));
    }
}
