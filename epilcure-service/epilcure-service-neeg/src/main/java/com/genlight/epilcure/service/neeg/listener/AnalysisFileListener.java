package com.genlight.epilcure.service.neeg.listener;

import com.genlight.epilcure.api.neeg.enums.FileType;
import com.genlight.epilcure.service.neeg.dao.entity.UploadFile;
import com.genlight.epilcure.service.neeg.service.EegService;
import com.genlight.epilcure.service.neeg.service.StimulationService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;


@Slf4j
@Service
@RocketMQMessageListener(topic = "${rocketmq.consumer.file_analysis.topic}", consumerGroup = "${rocketmq.consumer.file_analysis.group}")
public class AnalysisFileListener implements RocketMQListener<List<UploadFile>> {
    @Resource
    private EegService eegService;

    @Resource
    private StimulationService stimulationService;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Override
    public void onMessage(List<UploadFile> uploadFiles) {

        UploadFile uploadFile = uploadFiles.get(uploadFiles.size() - 1);
        if (Objects.isNull(stringRedisTemplate.opsForValue().get(uploadFile.getId().toString()))) {
            log.error("跳过文件[{}]", uploadFile.getId());
            return;
        }


        if (uploadFile.getType().equals(FileType.STIMULATION)) {
            stimulationService.processStimulation(uploadFile);
        } else if (uploadFile.getType().equals(FileType.EDF)) {

        } else {
            eegService.processEeg(uploadFile, uploadFiles.size() > 1 ? uploadFiles.get(0) : null);
        }

        stringRedisTemplate.delete(uploadFile.getId().toString());

//        if (FileType.STIMULATION.equals(message.getUploadFileVO().getType())) {
//            stimulationService.addStimulationList(message);
//        } else {
//            eegService.addEegList(message);
//        }
    }
}
