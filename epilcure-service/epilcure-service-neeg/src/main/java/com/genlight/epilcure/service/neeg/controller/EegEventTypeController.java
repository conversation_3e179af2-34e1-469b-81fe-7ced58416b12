package com.genlight.epilcure.service.neeg.controller;

import com.genlight.epilcure.common.core.mvc.response.JsonResult;
import com.genlight.epilcure.service.neeg.pojo.dto.EegEventTypeDTO;
import com.genlight.epilcure.api.neeg.pojo.vo.EegEventTypeVO;
import com.genlight.epilcure.service.neeg.service.EegEventTypeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/eeg/eegEventType")
@Tag(name = "EegEventTypeController", description = "脑电事件类型相关接口")
public class EegEventTypeController {

    @Resource
    private EegEventTypeService eegEventTypeService;

    @GetMapping("/patient/{patientId}")
    @Operation(summary = "根据患者Id查询事件类型")
    public JsonResult<List<EegEventTypeVO>> findByPatientId(@PathVariable Long patientId) {
        return JsonResult.ok(eegEventTypeService.finds(patientId));
    }

    @PostMapping
    @Operation(summary = "新增事件类型")
    public JsonResult<EegEventTypeVO> add(@RequestBody EegEventTypeDTO eegEventTypeDTO) {
        return JsonResult.ok(eegEventTypeService.addEegEventType(eegEventTypeDTO));
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "删除事件类型")
    public JsonResult<Void> delete(@PathVariable Long id) {
        eegEventTypeService.delete(id);
        return JsonResult.ok();
    }

    @PutMapping("/{id}")
    @Operation(summary = "修改事件类型")
    public JsonResult<EegEventTypeVO> update(@PathVariable Long id, @RequestBody EegEventTypeDTO eegEventTypeDTO) {
        return JsonResult.ok(eegEventTypeService.update(id, eegEventTypeDTO));
    }
}
