package com.genlight.epilcure.service.neeg.service;

import com.genlight.epilcure.common.core.dao.enums.Status;
import com.genlight.epilcure.common.core.service.BaseService;
import com.genlight.epilcure.common.core.service.exception.ServiceException;
import com.genlight.epilcure.service.neeg.constants.CacheNames;
import com.genlight.epilcure.service.neeg.dao.entity.Eeg;
import com.genlight.epilcure.service.neeg.dao.entity.EegEvent;
import com.genlight.epilcure.service.neeg.dao.entity.EegEventType;
import com.genlight.epilcure.service.neeg.dao.entity.EegEvent_;
import com.genlight.epilcure.service.neeg.dao.repository.EegEventRepository;
import com.genlight.epilcure.service.neeg.pojo.convert.EegEventConvert;
import com.genlight.epilcure.service.neeg.pojo.dto.EegEventDTO;
import com.genlight.epilcure.service.neeg.pojo.vo.EegEventVO;
import jakarta.annotation.Resource;
import jakarta.persistence.criteria.Predicate;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

@Service
public class EegEventService extends BaseService<EegEvent, Long, EegEventRepository> {

    @Resource
    private EegEventConvert eegEventConvert;

    @Resource
    private EegEventTypeService eegEventTypeService;

    @Resource
    private EegService eegService;

    @CacheEvict(cacheNames = {CacheNames.CACHE_EEG_EVENT_STATS, CacheNames.CACHE_EEG_LIST}, allEntries = true)
    @Transactional
    public EegEventVO add(EegEventDTO eegEventDTO) {
        Optional<EegEventType> optional = eegEventTypeService.fetchById(eegEventDTO.getEegEventTypeId());
        if (optional.isEmpty()) {
            throw new ServiceException("事件类型[{0}]不存在", eegEventDTO.getEegEventTypeId());
        }
        EegEvent eegEvent = eegEventConvert.dto2po(eegEventDTO);


        eegEvent.setEegEventType(optional.get());
        List<Eeg> eegs = eegService.findEegs(eegEventDTO.getPatientId(), eegEventDTO.getTriggerDate(), Optional.ofNullable(eegEventDTO.getEndDate()).orElse(eegEventDTO.getTriggerDate()));

        if (!eegs.isEmpty()) {
            eegEvent.setEeg(eegs.get(0));

            if (eegs.get(0).getEegEventType().stream().filter(e -> e.getId().equals(eegEventDTO.getEegEventTypeId())).findFirst().isEmpty()) {
                eegs.get(0).getEegEventType().add(EegEventType.builder().id(eegEventDTO.getEegEventTypeId()).build());
            }
        }
        return eegEventConvert.po2vo(repository.saveAndFlush(eegEvent));
    }


    @CacheEvict(cacheNames = CacheNames.CACHE_EEG_EVENT_STATS, allEntries = true)
    @Transactional
    public EegEventVO edit(Long id, EegEventDTO eegEventDTO) {
        Optional<EegEvent> optional = repository.findById(id);
        if (optional.isEmpty()) {
            throw new ServiceException("事件[{0}]不存在", id);
        }

        EegEvent eegEvent = optional.get();
        eegEventConvert.dto2po(eegEventDTO, eegEvent);
        if (Objects.isNull(eegEvent.getEeg())) {
            List<Eeg> eegs = eegService.findEegs(eegEvent.getPatientId(), eegEvent.getTriggerDate(), Optional.ofNullable(eegEvent.getEndDate()).orElse(eegEvent.getTriggerDate()));
            if (!eegs.isEmpty()) {
                eegEvent.setEeg(eegs.get(0));
            }
        }
        return eegEventConvert.po2vo(repository.save(eegEvent));
    }

    @CacheEvict(cacheNames = CacheNames.CACHE_EEG_EVENT_STATS, allEntries = true)
    @Transactional
    public void delete(Long id) {
        Optional<EegEvent> optional = repository.findById(id);
        if (optional.isEmpty()) {
            throw new ServiceException("事件[{0}]不存在", id);
        }
        EegEvent eegEvent = optional.get();
        eegEvent.setStatus(Status.DELETED);
        repository.saveAndFlush(eegEvent);
        if (Objects.nonNull(eegEvent.getEeg())) {
            if (!repository.existsByEegIdAndStatusAndEegEventTypeId(eegEvent.getEeg().getId(), Status.ENABLED, eegEvent.getEegEventTypeId())) {
                List<EegEventType> deletedList = new ArrayList<>();
                eegEvent.getEeg().getEegEventType().stream().filter(e -> e.getId().equals(eegEvent.getEegEventTypeId())).forEach(deletedList::add);
                deletedList.forEach(eegEvent.getEeg().getEegEventType()::remove);
                eegService.saveEeg(eegEvent.getEeg());
            }
        }

    }


    @Cacheable(cacheNames = CacheNames.CACHE_EEG_EVENT_STATS)
    @Transactional(readOnly = true)
    public Map<Long, Map<String, Long>> stats(Integer statsType, EegEventDTO eegEventDTO) {
        List<EegEvent> list = repository.findByPatientIdAndStatusAndTriggerDateBetween(eegEventDTO.getPatientId(), Status.ENABLED, eegEventDTO.getTriggerDate(), eegEventDTO.getEndDate());


        Map<Long, Map<String, Long>> result = new HashMap<>();
        String datePattern;
        if (statsType == 1) {// 按天统计
            datePattern = "yyyy-MM";
        } else {
            datePattern = "yyyy-MM-dd";
        }

        list.forEach(l -> {
            String date = DateFormatUtils.format(l.getTriggerDate(), datePattern);
            Map<String, Long> longMap;
            if (!result.containsKey(l.getEegEventType().getId())) {
                longMap = new HashMap<>();
                result.put(l.getEegEventType().getId(), longMap);
            } else {
                longMap = result.get(l.getEegEventType().getId());
            }
            longMap.put(date, longMap.computeIfAbsent(date, v -> 0L) + 1);
        });

        return result;
    }


    @Transactional(readOnly = true)
    public Page<EegEventVO> finds(EegEventDTO eegEventDTO, Pageable pageable) {
        Specification<EegEvent> specification = (root, query, criteriaBuilder) -> {
            Predicate predicate = criteriaBuilder.conjunction();
            if (Objects.nonNull(eegEventDTO.getEegEventTypeId())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get(EegEvent_.EEG_EVENT_TYPE).get(com.genlight.epilcure.service.neeg.dao.entity.EegEventType_.ID), eegEventDTO.getEegEventTypeId()));
            }

            if (Objects.nonNull(eegEventDTO.getPatientId())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get(EegEvent_.PATIENT_ID), eegEventDTO.getPatientId()));
            }

            if (Objects.nonNull(eegEventDTO.getEegId())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get(EegEvent_.EEG).get(com.genlight.epilcure.service.neeg.dao.entity.Eeg_.ID), eegEventDTO.getEegId()));
            }

            if (Objects.nonNull(eegEventDTO.getTriggerDate())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.greaterThanOrEqualTo(root.get(EegEvent_.TRIGGER_DATE), eegEventDTO.getTriggerDate()));
            }

            if (Objects.nonNull(eegEventDTO.getEndDate())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.lessThanOrEqualTo(root.get(EegEvent_.TRIGGER_DATE), eegEventDTO.getEndDate()));
            }

            if (Objects.nonNull(eegEventDTO.getStatus())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get(EegEvent_.STATUS), eegEventDTO.getStatus()));
            }

            return predicate;
        };

        return eegEventConvert.po2vo(repository.findAll(specification, pageable));
    }

}
