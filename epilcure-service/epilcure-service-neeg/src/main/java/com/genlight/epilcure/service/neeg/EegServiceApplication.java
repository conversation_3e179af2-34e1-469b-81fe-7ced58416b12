package com.genlight.epilcure.service.neeg;

import io.seata.spring.annotation.datasource.EnableAutoDataSourceProxy;
import net.bytebuddy.agent.ByteBuddyAgent;
import org.aspectj.weaver.loadtime.Agent;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.context.annotation.AdviceMode;
import org.springframework.instrument.InstrumentationSavingAgent;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

import java.lang.instrument.Instrumentation;

@EnableScheduling
@EnableDiscoveryClient
@EnableAutoDataSourceProxy
@EnableAsync(mode = AdviceMode.ASPECTJ)
@SpringBootApplication(scanBasePackages = "com.genlight.epilcure")
public class EegServiceApplication {

    public static void main(String[] args) {
        Instrumentation instrumentation = ByteBuddyAgent.install();
        Agent.agentmain("", instrumentation);
        InstrumentationSavingAgent.agentmain("", instrumentation);
        SpringApplication.run(EegServiceApplication.class, args);
    }
}
