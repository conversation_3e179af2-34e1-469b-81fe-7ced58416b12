package com.genlight.epilcure.service.neeg.controller;

import com.fasterxml.jackson.annotation.JsonView;
import com.genlight.epilcure.api.neeg.pojo.bo.EegCalculationBO;
import com.genlight.epilcure.api.neeg.pojo.dto.EegCalculationJobDTO;
import com.genlight.epilcure.api.neeg.pojo.vo.EegCalculationJobVO;
import com.genlight.epilcure.common.core.mvc.response.JsonResult;
import com.genlight.epilcure.common.core.pojo.valid.Add;
import com.genlight.epilcure.service.neeg.service.EegCalculationJobService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/eeg/eeg_calculation")
@Tag(name = "EegCalculationJobController", description = "算法推荐相关接口")
public class EegCalculationJobController {

    @Resource
    private EegCalculationJobService eegCalculationJobService;

    @Operation(summary = "新增推荐工单")
    @PostMapping
    public JsonResult<EegCalculationJobVO> add(@JsonView({Add.class}) @RequestBody @Validated(Add.class) EegCalculationJobDTO eegCalculationJobDTO) {
        return JsonResult.ok(eegCalculationJobService.add(eegCalculationJobDTO));
    }

    @Operation(summary = "删除工单")
    @DeleteMapping("/{id}")
    public JsonResult<Void> delete(@PathVariable Long id) {
        eegCalculationJobService.delete(id);
        return JsonResult.ok();
    }

    @Operation(summary = "查询工单")
    @GetMapping
    public JsonResult<Page<EegCalculationJobVO>> finds(EegCalculationJobDTO eegCalculationJobDTO, Pageable pageable) {
        return JsonResult.ok(eegCalculationJobService.finds(eegCalculationJobDTO, pageable));
    }


    @Operation(summary = "工单回调接口")
    @PostMapping("/callback/{id}")
    public JsonResult<Void> callback(@PathVariable Long id, @RequestBody EegCalculationBO result) {
        eegCalculationJobService.update(id, result);
        return JsonResult.ok();
    }


    @Operation(summary = "失败工单重试接口")
    @PostMapping("/retry/{id}")
    public JsonResult<EegCalculationJobVO> retry(@PathVariable Long id) {
        return JsonResult.ok(eegCalculationJobService.retry(id));
    }
}
