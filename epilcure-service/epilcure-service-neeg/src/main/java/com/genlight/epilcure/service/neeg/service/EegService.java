package com.genlight.epilcure.service.neeg.service;

import biz.source_code.dsp.filter.*;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.type.TypeFactory;
import com.genlight.epilcure.api.config.feign.IConfigController;
import com.genlight.epilcure.api.config.pojo.vo.ConfigsVO;
import com.genlight.epilcure.api.device.feign.IBcmController;
import com.genlight.epilcure.api.device.pojo.vo.BcmVO;
import com.genlight.epilcure.api.neeg.enums.*;
import com.genlight.epilcure.api.neeg.pojo.bo.ParseEegFileParam;
import com.genlight.epilcure.api.neeg.pojo.dto.EegDTO;
import com.genlight.epilcure.api.neeg.pojo.dto.EegDataFilterDTO;
import com.genlight.epilcure.api.neeg.pojo.vo.EegVO;
import com.genlight.epilcure.api.patient.feign.IPatientController;
import com.genlight.epilcure.api.patient.pojo.vo.PatientVO;
import com.genlight.epilcure.api.scheme.feign.IDetectionSchemeController;
import com.genlight.epilcure.api.scheme.pojo.dto.SchemeDTO;
import com.genlight.epilcure.api.scheme.pojo.vo.DetectionSchemeVO;
import com.genlight.epilcure.common.core.constants.HttpCode;
import com.genlight.epilcure.common.core.dao.generator.SnowflakeAlg;
import com.genlight.epilcure.common.core.service.BaseService;
import com.genlight.epilcure.common.core.service.exception.ArgsException;
import com.genlight.epilcure.common.core.service.exception.ServiceException;
import com.genlight.epilcure.common.core.util.MinioUtils;
import com.genlight.epilcure.service.neeg.constants.CacheNames;
import com.genlight.epilcure.service.neeg.constants.FileConstants;
import com.genlight.epilcure.service.neeg.dao.entity.*;
import com.genlight.epilcure.service.neeg.dao.repository.EegLfpRepository;
import com.genlight.epilcure.service.neeg.dao.repository.EegLfpTypeRepository;
import com.genlight.epilcure.service.neeg.dao.repository.EegRepository;
import com.genlight.epilcure.service.neeg.dao.repository.StimulationRepository;
import com.genlight.epilcure.service.neeg.edf.EDFwriter;
import com.genlight.epilcure.service.neeg.pojo.convert.EegConvert;
import com.genlight.epilcure.service.neeg.pojo.vo.EegTagVO;
import com.genlight.epilcure.service.neeg.properties.EegProperties;
import com.genlight.epilcure.service.neeg.utils.EegFileOldParse;
import com.genlight.epilcure.service.neeg.utils.EegFileParse;
import com.genlight.epilcure.service.neeg.utils.ZipFileParse;
import io.netty.buffer.ByteBuf;
import jakarta.annotation.Resource;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.rocketmq.client.producer.LocalTransactionState;
import org.apache.rocketmq.client.producer.SendStatus;
import org.apache.rocketmq.client.producer.TransactionSendResult;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.annotation.Caching;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.ListOperations;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import org.springframework.util.StringUtils;
import org.springframework.util.unit.DataSize;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.net.URLEncoder;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.time.Instant;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * <AUTHOR>
 * @version 1.0.0
 */
@Slf4j
@Service
@CacheConfig(cacheNames = CacheNames.CACHE_EEG)
public class EegService extends BaseService<Eeg, Long, EegRepository> {

    @Resource
    private IDetectionSchemeController iDetectionSchemeController;
    @Resource
    private MinioUtils minioUtils;

    @Resource
    private EegConvert eegConvert;

    @Resource
    private EegFileParse eegFileParse;

    @Resource
    private EegFileOldParse eegFileOldParse;

    @Resource
    private ObjectMapper objectMapper;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    HashOperations<String, Object, Object> stringObjectObjectHashOperations;

    ListOperations<String, String> stringStringListOperations;

    @Resource
    private RedissonClient redissonClient;

    @Resource
    private EegCollectService eegCollectService;

    @Resource
    private EegTagService eegTagService;

    @Resource
    private UploadFileService uploadFileService;

    @Resource
    private EegLfpService eegLfpService;

    @Resource
    private EegLfpRepository eegLfpRepository;

    @Resource
    private EegLfpTypeRepository eegLfpTypeRepository;

    @Resource
    private StimulationRepository stimulationRepository;

    @Resource
    private RocketMQTemplate rocketMQTemplate;

    @Resource
    private EegProperties eegProperties;

    @Resource
    private SnowflakeAlg snowflakeAlg;

    @Value("${eeg.stimulation-mark.timeout:1}")
    private int eegStimulationMarkTimeout;

    @Resource
    private IConfigController iConfigController;

    @Resource
    private IPatientController iPatientController;

    @Resource
    private IBcmController iBcmController;


    @Cacheable(key = "#id")
    @Transactional(readOnly = true)
    public EegVO findEegById(Long id) {
        Optional<Eeg> eegOptional = repository.findById(id);
        if (eegOptional.isEmpty()) {
            throw new ServiceException("查找的脑电[{0}]不存在！", id);
        }
        return eegConvert.po2vo(eegOptional.get());
    }

    @Transactional(readOnly = true)
    public Optional<Eeg> getEegById(Long id) {
        return repository.findById(id);
    }

    @Transactional
    public void saveEeg(Eeg eeg) {
        repository.saveAndFlush(eeg);
    }

    @Transactional(readOnly = true)
    public List<Eeg> findByIds(List<Long> ids, Long patientId) {
        return repository.findByIdInAndPatientId(ids, patientId);
    }

    //    @Scheduled(fixedDelay = 1, timeUnit = TimeUnit.SECONDS)
    @Transactional
    public void updatePolarity() {

        List<Eeg> all = repository.findByCaseIdNotNullAndAnodeIsNotLike();

        all.stream().forEach(e -> {
            e.setAnodes(new ArrayList<>());
            e.setCathodes(new ArrayList<>());
            try {
                e.setPositions(objectMapper.readValue(e.getPosition(), new TypeReference<>() {
                }));
                List<DetectionSchemeVO> detectionSchemeVO;
                if (Objects.nonNull(e.getCaseId()) && !e.getCaseId().equals("0")) {
                    detectionSchemeVO = iDetectionSchemeController.get(e.getCaseId());
                } else {
                    detectionSchemeVO = iDetectionSchemeController.getNearest(SchemeDTO.builder()
                            .patientId(e.getPatientId())
                            .schemeDate(e.getTriggerTime()).build());

                }

                if (Objects.nonNull(detectionSchemeVO) && !detectionSchemeVO.isEmpty()) {
                    e.getPositions().forEach(element -> {
                        Optional<DetectionSchemeVO> optional = detectionSchemeVO.stream().filter(d -> d.getTouchPoint().getPosition().equals(element - 1)).findFirst();
                        if (optional.isPresent()) {
                            e.getAnodes().add(optional.get().getTouchPoint().getPolarity().indexOf(1) + 1);
                            e.getCathodes().add(optional.get().getTouchPoint().getPolarity().indexOf(2) + 1);
                        }
                    });
                }
                e.setAnode(objectMapper.writeValueAsString(e.getAnodes()));
                e.setCathode(objectMapper.writeValueAsString(e.getCathodes()));
            } catch (JsonProcessingException ex) {
                throw new RuntimeException(ex);
            }
        });

        repository.saveAll(all);

    }

//    @Scheduled(fixedDelay = 10, timeUnit = TimeUnit.SECONDS)
//    @Transactional
//    public void updateMaxMinValues() {
//        List<Eeg> all = repository.findByMaxValuesIsNull(PageRequest.of(0, 1000)).getContent();
//        all.stream().forEach(e -> {
//            try {
//                List<Integer> positions = objectMapper.readValue(e.getPosition(), new TypeReference<>() {
//                });
//                List<Integer> maxValues = new ArrayList<>();
//                List<Integer> minValues = new ArrayList<>();
//                positions.forEach(p -> {
//                    Integer maxValue = -999999, minValue = 999999;
//                    List<EegData> collect = e.getEegData().stream().filter(d -> d.getPosition().equals(p)).collect(Collectors.toList());
//                    for (EegData c : collect) {
//                        ByteBuf buf = ZipFileParse.unCompressData(c.getData());
//                        while (buf.readableBytes() > 0) {
//                            int value = buf.readUnsignedShort();
//                            maxValue = Math.max(maxValue, value);
//                            minValue = Math.min(minValue, value);
//                        }
//                    }
//                    maxValues.add(maxValue);
//                    minValues.add(minValue);
//
//                });
//
//                e.setMaxValues(objectMapper.writeValueAsString(maxValues));
//                e.setMinValues(objectMapper.writeValueAsString(minValues));
//            } catch (JsonProcessingException ex) {
//                throw new RuntimeException(ex);
//            }
//        });
//
//        repository.saveAll(all);
//
//    }

//    @Scheduled(fixedDelay = 10, timeUnit = TimeUnit.SECONDS)
//    @Transactional
//    public void updateMaxMinValues() {
//
//        List<Eeg> all = repository.findAll();
//        all.stream().forEach(e -> {
//            try {
//                List<Integer> positions = objectMapper.readValue(e.getPosition(), new TypeReference<>() {
//                });
//                List<Integer> mins = objectMapper.readValue(e.getMinValues(), new TypeReference<>() {
//                });
//                List<Integer> maxs = objectMapper.readValue(e.getMaxValues(), new TypeReference<>() {
//                });
//                List<Integer> maxValues = new ArrayList<>();
//                List<Integer> minValues = new ArrayList<>();
//                if (mins.size() > positions.size()) {
//
//                    int i = 0;
//                    int minLength = mins.size() / positions.size();
//                    positions.forEach(p -> {
//                        mins.subList(i, i + minLength).sort((a, b) -> b - a);
//                        maxs.subList(i, i + minLength).sort((a, b) -> a - b);
//                        minValues.add(mins.get(0));
//                        maxValues.add(maxs.get(0));
//                    });
//
//                    e.setMaxValues(objectMapper.writeValueAsString(maxValues));
//                    e.setMinValues(objectMapper.writeValueAsString(minValues));
//                }
//            } catch (JsonProcessingException ex) {
//                throw new RuntimeException(ex);
//            }
//        });
//
//        repository.saveAll(all);
//
//    }

    @Transactional(readOnly = true)
    @Cacheable(cacheNames = CacheNames.CACHE_EEG_LIST, key = "#root.target.getUserId() + '-' + #eegDTO + '-' + #pageable")
    public Page<EegVO> findEegsByPageable(EegDTO eegDTO, Pageable pageable) {
        if (Objects.isNull(eegDTO.getPatientId())) {
            throw new ServiceException("必须指定患者查询");
        }
        Page<Eeg> eegs = repository.findAll((Root<Eeg> root, CriteriaQuery<?> query, CriteriaBuilder criteriaBuilder) -> {
            Predicate predicate = criteriaBuilder.conjunction();
            if (Objects.nonNull(eegDTO.getPatientId())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get(Eeg_.patientId), eegDTO.getPatientId()));
            }
            if (Objects.nonNull(eegDTO.getCaseId())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get(Eeg_.caseId), eegDTO.getCaseId()));
            }
            if (Objects.nonNull(eegDTO.getTriggerModel())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get(Eeg_.triggerModel), eegDTO.getTriggerModel()));
            } else if (Objects.nonNull(eegDTO.getFilterTriggerModel())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.notEqual(root.get(Eeg_.triggerModel), eegDTO.getFilterTriggerModel()));
            }
            if (Objects.nonNull(eegDTO.getTriggerModels())) {
                CriteriaBuilder.In<EegTriggerType> in = criteriaBuilder.in(root.get(Eeg_.triggerModel));
                eegDTO.getTriggerModels().forEach(o -> {
                    in.value(o);
                });
                predicate = criteriaBuilder.and(predicate, in);
            }

            if (Objects.nonNull(eegDTO.getEegEventTypeIds())) {

                CriteriaBuilder.In<Long> in = criteriaBuilder.in(root.get(Eeg_.EEG_EVENT_TYPE).get(EegEventType_.ID));
                eegDTO.getEegEventTypeIds().forEach(o -> {
                    in.value(Long.parseLong(o));
                });
                predicate = criteriaBuilder.and(predicate, in);
                query.distinct(true);
            }
            if (Objects.nonNull(eegDTO.getSegment())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get(Eeg_.segment), eegDTO.getSegment()));
            }
            if (StringUtils.hasText(eegDTO.getFilename())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.like(root.get(Eeg_.file).get(UploadFile_.name), "%" + eegDTO.getFilename() + "%"));
            }
            if (Objects.nonNull(eegDTO.getTriggerTimeBegin())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.greaterThanOrEqualTo(root.get(Eeg_.triggerTime), eegDTO.getTriggerTimeBegin()));
            }
            if (Objects.nonNull(eegDTO.getTriggerTimeEnd())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.lessThanOrEqualTo(root.get(Eeg_.triggerTime), eegDTO.getTriggerTimeEnd()));
            }
            if (Objects.nonNull(eegDTO.getDuration())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.greaterThanOrEqualTo(root.get(Eeg_.duration), eegDTO.getDuration().floatValue()));
            }
            if (Objects.nonNull(eegDTO.getUserId())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.lessThanOrEqualTo(root.join(Eeg_.eegCollects).get(EegCollect_.userId), eegDTO.getUserId()));
            }
            if (Objects.nonNull(eegDTO.getAndMark()) && eegDTO.getAndMark()) {
                if (Objects.nonNull(eegDTO.getMark())) {
                    predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get(Eeg_.mark), eegDTO.getMark()));
                }
                if (Objects.nonNull(eegDTO.getSelfMark())) {
                    predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get(Eeg_.selfMark), eegDTO.getSelfMark()));
                }
                if (Objects.nonNull(eegDTO.getStimulationMark())) {
                    predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get(Eeg_.stimulationMark), eegDTO.getStimulationMark()));
                }
            } else {
                if (Objects.nonNull(eegDTO.getMark()) || Objects.nonNull(eegDTO.getSelfMark()) || Objects.nonNull(eegDTO.getStimulationMark())) {
                    Predicate orPredicate = criteriaBuilder.disjunction();
                    if (Objects.nonNull(eegDTO.getMark())) {
                        orPredicate = criteriaBuilder.or(orPredicate, criteriaBuilder.equal(root.get(Eeg_.mark), eegDTO.getMark()));
                    }
                    if (Objects.nonNull(eegDTO.getSelfMark())) {
                        orPredicate = criteriaBuilder.or(orPredicate, criteriaBuilder.equal(root.get(Eeg_.selfMark), eegDTO.getSelfMark()));
                    }
                    if (Objects.nonNull(eegDTO.getStimulationMark())) {
                        orPredicate = criteriaBuilder.or(orPredicate, criteriaBuilder.equal(root.get(Eeg_.stimulationMark), eegDTO.getStimulationMark()));
                    }
                    query.where(predicate, orPredicate);
                    return query.getRestriction();
                }
            }
            return predicate;
        }, pageable);
        eegs.getContent().forEach(eeg -> eeg.setCollect(eegCollectService.existsByUserIdAndEeg(getUserId(), eeg)));
        return eegConvert.po2vo(eegs);
    }

    @Transactional
    public void uploadEegFile(MultipartFile eegFile, String fileDir, FileType fileType) {
        uploadEegFile(eegFile, null, fileDir, fileType, false, "0.0");
    }

    @Transactional
    public void uploadEegFile(MultipartFile eegFile, String fileDir, FileType fileType, Boolean reallyZero) {
        uploadEegFile(eegFile, null, fileDir, fileType, reallyZero, "0.0");
    }

    @Transactional
    public void uploadEegFile(MultipartFile eegFile, MultipartFile eegEventFile, String fileDir, Boolean reallyZero, FileType fileType) {
        uploadEegFile(eegFile, eegEventFile, fileDir, fileType, reallyZero, "0.0");
    }

    /**
     * 这个方法不要加事务
     */
    @Transactional
    public void uploadEegFile(MultipartFile eegFile, MultipartFile eegEventFile, String fileDir, FileType fileType, Boolean reallyZero, String version) {
        if (FileConstants.EEG.equals(fileDir)) {
            if (!Pattern.matches("^[^-]+-[^-]+-[^-]+.*$", eegFile.getOriginalFilename())) {
                throw new ServiceException("文件名错误");
            }
        } else {
            if (!Pattern.matches("^[^-]+-[^-]+.*$", eegFile.getOriginalFilename())) {
                throw new ServiceException("文件名错误");
            }
        }

        try {
            byte[] zipHeader = Arrays.copyOfRange(eegFile.getBytes(), 0, 4);
            if (!(Arrays.equals(FileConstants.ZIP_HEADER_1, zipHeader) || Arrays.equals(FileConstants.ZIP_HEADER_2, zipHeader))) {
                throw new ServiceException("Zip文件头错误");
            }
            RLock lock = redissonClient.getLock("uploadEegFile-%s".formatted(eegFile.getOriginalFilename()));
            try {
                if (lock.tryLock(2, 2, TimeUnit.MINUTES)) {
                    if (uploadFileService.existedUploadFile(eegFile.getOriginalFilename())) {
                        throw new ArgsException(HttpCode.FILE_EXISTS);
                    }
                    Instant start = Instant.now();
                    String filename = minioUtils.upload(fileDir, eegFile);
                    if (!StringUtils.hasText(filename)) {
                        throw new ServiceException("文件上传失败");
                    }
                    log.info("压缩包[{}]上传完成，大小[{}]MB，耗时[{}]ms", eegFile.getOriginalFilename(), DataSize.ofBytes(eegFile.getSize()).toMegabytes(), Duration.between(start, Instant.now()).toMillis());

                    List<UploadFile> files = new ArrayList<>();
                    files.add(UploadFile.builder().id(snowflakeAlg.nextId()).url(filename).name(eegFile.getOriginalFilename()).type(fileType).status(FileStatus.UPLOADED).userId(getUserId()).retryOldParse(false).reallyZero(reallyZero).fileVersion(version).build());
                    stringRedisTemplate.opsForValue().set(files.get(0).getId().toString(), files.get(0).getId().toString());
                    if (Objects.nonNull(eegEventFile)) {
                        filename = minioUtils.upload(fileDir, eegEventFile);
                        if (!StringUtils.hasText(filename)) {
                            throw new ServiceException("文件上传失败");
                        }
                        UploadFile eventFile = UploadFile.builder().id(snowflakeAlg.nextId()).url(filename).name(eegEventFile.getOriginalFilename()).type(FileType.EEG_EVENT).status(FileStatus.UPLOADED).userId(getUserId()).retryOldParse(false).build();
//                        files.get(0).setUploadFile(eventFile);
                        files.add(0, eventFile);
                    }

                    TransactionSendResult transactionSendResult = rocketMQTemplate.sendMessageInTransaction(eegProperties.getEegAnalysis().getTopic(), MessageBuilder
                            .withPayload(files)
                            .build(), files);
                    if (!transactionSendResult.getSendStatus().equals(SendStatus.SEND_OK) && transactionSendResult.getLocalTransactionState().equals(LocalTransactionState.COMMIT_MESSAGE)) {
                        throw new ServiceException(HttpCode.ERROR);
                    }
                } else {
                    throw new ServiceException("操作太频繁，请稍后再试！");
                }

            } catch (Exception e) {
                throw new RuntimeException(e);
            } finally {
                lock.unlock();
            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    //    @Async
    @Transactional
    public void processEeg(UploadFile uploadFile, UploadFile eegEventFile) {

        RLock lock = redissonClient.getLock("parseFile" + uploadFile.getId());
        Optional<UploadFile> fetch;
        boolean parseLfp = "0.0".equals(uploadFile.getFileVersion());
        try {
            if (lock.tryLock(2, 2, TimeUnit.SECONDS)) {
                fetch = uploadFileService.fetchById(uploadFile.getId());

                if (fetch.isEmpty()) {
                    log.error("文件Id{}不存在", uploadFile.getId());
                    return;
                }
                UploadFile uploadFileEntity = fetch.get();
                FileStatus fileStatus = uploadFileEntity.getStatus();
                if (fileStatus.equals(FileStatus.PARSING)) {
                    log.error("文件[{}]解析中", uploadFileEntity.getId());
                    return;
                } else if (fileStatus.equals(FileStatus.PARSE_SUCCESS)) {
                    List<Eeg> eegs = repository.findByFileId(uploadFileEntity.getId());
                    if (parseLfp) {
                        addEegToPending(eegs);
                    }
                    return;
                }
                uploadFileEntity.setStatus(FileStatus.PARSING);
                uploadFileService.saveUploadFile(uploadFileEntity);
            } else {
                throw new ServiceException("服务器忙,请稍后再试");
            }
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        } finally {
            lock.unlock();
        }

        try {
            if (!minioUtils.existedObject(uploadFile.getUrl())) {
                log.error(String.format("文件不存在id:%d", uploadFile.getId()));
                return;
            }
            InputStream ins = minioUtils.getObject(uploadFile.getUrl());
            Function<ParseEegFileParam, List<Eeg>> parseFunction = null;
            Function<ParseEegFileParam, List<Eeg>> parseOldFunction = null;
            Function<ByteBuf, Integer> readDurationIndexFunction = (Objects.isNull(uploadFile.getFileVersion()) || "0.0".equals(uploadFile.getFileVersion())) ? null : eegFileParse::readDurationIndex;
            Function<ByteBuf, EegPosition> readPositionFunction = (Objects.isNull(uploadFile.getFileVersion()) || "0.0".equals(uploadFile.getFileVersion())) ? eegFileParse::readEegPosition : eegFileParse::readEegPosition_0_1;
            List<EegEvent> eegEvents = null;

            switch (uploadFile.getType()) {
                case EEG -> {
                    parseFunction = eegFileParse::parseEegFile;
                    parseOldFunction = uploadFile.getRetryOldParse() ? eegFileOldParse::parseEegFile : null;

                }
                case EEG_TIME -> {
                    parseFunction = eegFileParse::parseEegFileTiming;
                }
                case EEG_SAMPLE -> {
                    parseFunction = eegFileParse::parseEegSampleFile;
                    parseOldFunction = uploadFile.getRetryOldParse() ? eegFileOldParse::parseEegSampleFile : null;
                }
                case EEG_SAMPLE_BINARY -> {
                    parseFunction = eegFileParse::parseEegSampleBinaryFile;
                    parseOldFunction = eegFileParse::parseEegSampleBinaryFile;
                    if (Objects.nonNull(eegEventFile)) {
                        InputStream inputStream = minioUtils.getObject(eegEventFile.getUrl());
                        String eegEventFileStr = ZipFileParse.unCompressData(inputStream.readAllBytes()).toString(Charset.forName("UTF-8"));

                        eegEvents = objectMapper.readValue(eegEventFileStr, new TypeReference<>() {
                        });
                        eegEvents.forEach(e -> {
                            if (Objects.isNull(e.getTriggerDate())) {
                                e.setTriggerDate(e.getEndDate());
                            }
                            if (Objects.isNull(e.getEndDate())) {
                                e.setEndDate(e.getTriggerDate());
                            }
                        });
                    }
                }
                case EEG_TIMESHARING -> {
                    parseFunction = eegFileParse::parseEegFileHasDuration;
                }
            }
            List<Eeg> eegs = addEegList(uploadFile, eegEvents, ins, parseFunction, parseOldFunction, readPositionFunction, readDurationIndexFunction, uploadFile.getRetryOldParse());

            if (parseLfp) {

                TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
                    @Override
                    public void afterCompletion(int status) {
                        TransactionSynchronization.super.afterCompletion(status);
                        addEegToPending(eegs);
                    }
                });

            }
        } catch (Exception ex) {
            throw new RuntimeException(ex);
        }


    }

    @Transactional
    public EegVO addEeg(Eeg eeg, UploadFile uploadFile) {
        uploadFile.setStatus(FileStatus.PARSE_SUCCESS);
        return eegConvert.po2vo(repository.save(eeg));
    }

    @Transactional
    @Caching(evict = {@CacheEvict(cacheNames = CacheNames.CACHE_EEG_LIST, allEntries = true),
            @CacheEvict(cacheNames = CacheNames.CACHE_EEG_EVENT_STATS, allEntries = true)})
    public List<Eeg> addEegList(UploadFile uploadFile, List<EegEvent> eegEvents, InputStream ins, Function<ParseEegFileParam, List<Eeg>> parseFunction, Function<ParseEegFileParam, List<Eeg>> parseOldFunction, Function<ByteBuf, EegPosition> readPositionFunction, Function<ByteBuf, Integer> readDurationIndexFunction, Boolean force) {
        List<Eeg> eegs;
        try {
            byte[] data = IOUtils.toByteArray(ins);
            ins.close();
            Class<?> m;
            ParseEegFileParam parseEegFileParam = ParseEegFileParam.builder()
                    .fileName(uploadFile.getName())
                    .in(new ByteArrayInputStream(data))
                    .readPosition(readPositionFunction)
                    .reallyZero(uploadFile.getReallyZero())
                    .readDurationIndex(readDurationIndexFunction)
                    .force(force)
                    .build();
            try {
                eegs = parseFunction.apply(parseEegFileParam);
                m = parseFunction.getClass().getDeclaredFields()[0].getType();
            } catch (Exception e) {
                uploadFile.setDescription(e.getMessage());
                if ((uploadFile.getRetryOldParse() || force) && Objects.nonNull(parseOldFunction)) {
                    parseEegFileParam.setForce(true);
                    parseEegFileParam.getIn().reset();
                    eegs = parseOldFunction.apply(parseEegFileParam);
                    m = parseOldFunction.getClass().getDeclaredFields()[0].getType();
                } else {
                    throw e;
                }
            }

            List<EegEvent> finalEegEvents = eegEvents;

            eegs.forEach(eeg -> {
                eeg.setFile(uploadFile);
                try {
                    eeg.setPosition(objectMapper.writeValueAsString(eeg.getPositions()));

                    if (Objects.nonNull(eeg.getCaseId()) && eeg.getAnodes().isEmpty()) {
                        List<DetectionSchemeVO> detectionSchemeVO = iDetectionSchemeController.get(eeg.getCaseId());
                        eeg.getPositions().forEach(element -> {
                            Optional<DetectionSchemeVO> optional = detectionSchemeVO.stream().filter(d -> d.getTouchPoint().getPosition().equals(element - 1)).findFirst();
                            if (optional.isPresent()) {
                                eeg.getAnodes().add(optional.get().getTouchPoint().getPolarity().indexOf(1) + 1);
                                eeg.getCathodes().add(optional.get().getTouchPoint().getPolarity().indexOf(2) + 1);
                            }
                        });
                    }

                    eeg.setAnode(objectMapper.writeValueAsString(eeg.getAnodes()));

                    eeg.setCathode(objectMapper.writeValueAsString(eeg.getCathodes()));

                    if (Objects.nonNull(finalEegEvents)) {

                        log.info("eegEvents:{}", finalEegEvents);
                        Date endDate = new Date(eeg.getTriggerTime().getTime() + (int) ((eeg.getDuration() + Optional.ofNullable(eeg.getDurationTag()).orElse(0f)) * 1000));
                        List<EegEvent> collect = finalEegEvents.stream().filter(event -> Optional.ofNullable(event.getTriggerDate()).orElse(event.getEndDate()).after(eeg.getTriggerTime()) && event.getTriggerDate().before(endDate)).collect(Collectors.toList());
                        collect.forEach(event -> {
                            event.setEeg(eeg);
                            event.setEegEventType(EegEventType.builder().id(event.getEegEventTypeId()).build());
                            eeg.getEegEvent().add(event);
                            eeg.getEegEventType().add(event.getEegEventType());
                        });
                        log.info("collect:{}", collect);
                        eeg.getEegEvent().addAll(collect);
                    }
                } catch (JsonProcessingException e) {
                    throw new RuntimeException(e);
                }
            });
            uploadFile.setStatus(FileStatus.PARSE_SUCCESS);
            uploadFile.setParseMethod(m.getName());
            uploadFile.setPatientId(parseEegFileParam.getPatientId());
            uploadFile.setDataStartTime(parseEegFileParam.getStartDate());
            uploadFile.setDataEndTime(parseEegFileParam.getEndDate());
            uploadFileService.saveUploadFile(uploadFile);

            Instant start = Instant.now();
            repository.saveAll(eegs);
            log.info("压缩包[{}]保存完成，耗时[{}]ms", uploadFile.getName(), Duration.between(start, Instant.now()).toMillis());
        } catch (Exception e) {
            uploadFile.setDescription(e.getMessage());
            uploadFile.setStatus(FileStatus.PARSE_FAIL);
            uploadFile.setRetryOldParse(true);
            uploadFileService.saveUploadFile(uploadFile);
            log.error(e.getMessage(), e);
            return Collections.emptyList();
        }

        return eegs;
    }

    @Transactional
    public void addEegToPending(List<Eeg> eegs) {
        RLock lock = redissonClient.getLock("addEegToPending");
        if (Objects.isNull(stringStringListOperations)) {
            stringStringListOperations = stringRedisTemplate.opsForList();
        }
        if (Objects.isNull(stringObjectObjectHashOperations)) {
            stringObjectObjectHashOperations = stringRedisTemplate.opsForHash();
        }
        try {
            if (lock.tryLock(10, 10, TimeUnit.SECONDS)) {
                for (Eeg eeg : eegs) {
                    if (eeg.getStimulationMark() != EegStimulationMark.None) continue;
                    String eegId = eeg.getId().toString();
                    String key = "epilcure::eeg::pending::" + eegId;
                    stringStringListOperations.leftPush("epilcure::eeg::pending::list", eegId);
                    log.info(String.valueOf(stringStringListOperations.size("epilcure::eeg::pending::list")));
                    stringObjectObjectHashOperations.put(key, "patient", eeg.getPatientId().toString());
                    stringObjectObjectHashOperations.put(key, "frequency", eeg.getFrequency().toString());
                    stringObjectObjectHashOperations.put(key, "trigger_time", String.valueOf(eeg.getTriggerTime().getTime()));
                    stringObjectObjectHashOperations.put(key, "duration", eeg.getDuration().toString());
                    stringObjectObjectHashOperations.put(key, "create_time", String.valueOf(System.currentTimeMillis()));
                }
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        } finally {
            this.unLock(lock);
        }
    }


    @Scheduled(fixedDelay = 10, timeUnit = TimeUnit.MINUTES)
    @Transactional
    public void generateStimulationLfp() {
        if (Objects.isNull(stringStringListOperations)) {
            stringStringListOperations = stringRedisTemplate.opsForList();
        }
        if (Objects.isNull(stringObjectObjectHashOperations)) {
            stringObjectObjectHashOperations = stringRedisTemplate.opsForHash();
        }
        String compare = null;
        Long size = stringRedisTemplate.opsForList().size("epilcure::eeg::pending::list");
        Long count = 0L;
        while (true) {
            String eegId = stringRedisTemplate.opsForList().leftPop("epilcure::eeg::pending::list");
            if (Objects.isNull(eegId) || eegId.equals(compare)) {
                break;
            }
            boolean success;
            try {
                success = generateStimulationLfp0(eegId);
            } catch (Exception e) {
                e.printStackTrace();
                success = false;
            }
            if (success) {
                stringRedisTemplate.delete("epilcure::eeg::pending::" + eegId);
            } else {
                if (Objects.isNull(compare)) {
                    compare = eegId;
                }
                stringRedisTemplate.opsForList().rightPush("epilcure::eeg::pending::list", eegId);
            }

            count++;

            // 防止多线程处理时，死循环
            if (count >= size) break;
        }
    }

    @Transactional
    public Boolean generateStimulationLfp0(String eegId) {
        String key = "epilcure::eeg::pending::" + eegId;

        // 如果缓存中找不到该条脑电,重新缓存数据
        if (!stringObjectObjectHashOperations.hasKey(key, "patient")) {
            Optional<Eeg> optional = repository.findById(Long.parseLong(eegId));
            if (optional.isEmpty()) {
                log.error("标记处理时未找到脑电数据[{}]", eegId);
                return false;
            }
            addEegToPending(Arrays.stream(new Eeg[]{optional.get()}).toList());
        }
        long patientId = Long.parseLong(Objects.requireNonNull(stringObjectObjectHashOperations.get(key, "patient")).toString());
        long triggerTime = Long.parseLong(Objects.requireNonNull(stringObjectObjectHashOperations.get(key, "trigger_time")).toString());
        float duration = Float.parseFloat(Objects.requireNonNull(stringObjectObjectHashOperations.get(key, "duration")).toString());
        long frequency = Long.parseLong(Objects.requireNonNull(stringObjectObjectHashOperations.get(key, "frequency")).toString());
        if (!generateStimulationLfp0(Long.parseLong(eegId), patientId, triggerTime, frequency, duration)) {
            // 超过未找到放弃
            Eeg eeg = repository.getReferenceById(Long.parseLong(eegId));
            long createTime = Long.parseLong((Optional.ofNullable(stringObjectObjectHashOperations.get(key, "create_time")).orElse("0")).toString());
            if ((System.currentTimeMillis() - createTime) > (86400000L * eegStimulationMarkTimeout)) {
                repository.save(eeg.setStimulationMark(EegStimulationMark.TIMEOUT));
                return true;
            }
            return false;
        }
        log.info("找到脑电[{}]刺激标记", eegId);
        return true;
    }


    @Transactional(readOnly = true)
    public List<Eeg> findEegs(Long patientId, Date start, Date end) {
        return repository.getByDateRange(patientId, start, end);
    }

    @Caching(evict = {
            @CacheEvict(cacheNames = CacheNames.CACHE_EEG_LIST, allEntries = true),
            @CacheEvict(cacheNames = CacheNames.CACHE_EEG_LFP_LIST, allEntries = true)
    })
    public boolean generateStimulationLfp0(long eegId, long patientId, long triggerTime, long frequency, float duration) {
        Date begin = new Date(triggerTime);
        Date end = new Date(triggerTime + (int) (duration * 1000));
        List<Stimulation> stimulationList = stimulationRepository.getByPatientIdAndTriggerTimeBetweenOrderByTriggerTimeAsc(patientId, begin, end);
        if (!stimulationList.isEmpty()) {
            Eeg eeg = repository.getReferenceById(eegId);
            if (!EegStimulationMark.MARKED.equals(eeg.getStimulationMark())) {
                for (Stimulation stimulation : stimulationList) {
                    Long onset = (stimulation.getTriggerTime().getTime() - triggerTime) / 1000 * frequency;
                    eegLfpRepository.save(EegLfp.builder()
                            .userId(0L)
                            .segment(0L)
                            .onset(onset)
                            .duration(0L)
                            .lfpType(eegLfpTypeRepository.getReferenceById(99226276971286529L))
                            .mark(stimulation.getStimulationMark())
                            .updatable(false).eeg(eeg).build());
                }
                repository.save(eeg.setStimulationMark(EegStimulationMark.MARKED));
            }
            return true;
        }
        return false;
    }

//    @Transactional
//    public void updateEegInfo() {
//        List<Eeg> eegs = repository.findAll();
//        ArrayList<Integer> list = new ArrayList<>();
//        list.add(-99999);
//        list.add(0);
//        eegs.stream().forEach(eeg -> {
//            eeg.setMinValue(999999);
//            eeg.setMaxValue(-999999);
//            AtomicReference<Float> duration = new AtomicReference<>((float) 0);
//
//            Set<EegData> eegDataSet = eeg.getEegData();
//            eegDataSet.forEach(data -> {
//                ByteBuf buf = ZipFileParse.unCompressData(data.getData());
//
//                duration.updateAndGet(v -> (v + (buf.readableBytes() * .5f / eeg.getFrequency())));
//                while (buf.readableBytes() > 0) {
//                    Integer value = buf.readUnsignedShort();
//                    eeg.setMinValue(Math.min(eeg.getMinValue(), value));
//                    eeg.setMaxValue(Math.max(eeg.getMaxValue(), value));
//                }
//            });
//
//            try {
//                eeg.setDuration(duration.get() / objectMapper.readValue(eeg.getPosition(), new TypeReference<List<Float>>() {
//                }).size());
//            } catch (JsonProcessingException e) {
//                throw new RuntimeException(e);
//            }
//        });
//
//        repository.saveAll(eegs);
//    }

    @Transactional
    public ResponseEntity<byte[]> getClassicResponseEntity(Long id, EegDataFilterDTO eegDataFilterDTO) {
        Optional<Eeg> eegOptional = repository.findById(id);
        if (eegOptional.isEmpty()) {
            throw new ServiceException("脑电[{0}]不存在！", id);
        }

        // 按通道分组
        Eeg eeg = eegOptional.get();
        Map<Integer, Set<EegData>> dataMap = new TreeMap<>();
        for (EegData data : eeg.getEegData()) {
            dataMap.computeIfAbsent(data.getPosition(), k -> new TreeSet<>(Comparator.comparingInt(EegData::getSegment))).add(data);
        }

        // 按通道生成ZipEntry
        byte[] bytes;
        try (ByteArrayOutputStream out = new ByteArrayOutputStream(); ZipOutputStream zos = new ZipOutputStream(out)) {
            for (Map.Entry<Integer, Set<EegData>> entry : dataMap.entrySet()) {
                zos.putNextEntry(new ZipEntry("P" + entry.getKey() + ".txt"));
                OutputStreamWriter writer = new OutputStreamWriter(zos);

                // 构造IirFilterCoefficients
                IirFilterCoefficients design = null;
                IirFilter iirFilter = null;
                if (Objects.nonNull(eegDataFilterDTO) && eegDataFilterDTO.isValid()) {
                    if (eegDataFilterDTO.getFilterPassType().equals(FilterPassType.lowpass)) {
                        design = IirFilterDesignFisher.design(FilterPassType.lowpass, FilterCharacteristicsType.butterworth, eegDataFilterDTO.getI(), 0., eegDataFilterDTO.getV1() / eeg.getFrequency(), 0);
                    } else if (eegDataFilterDTO.getFilterPassType().equals(FilterPassType.highpass)) {
                        design = IirFilterDesignFisher.design(FilterPassType.highpass, FilterCharacteristicsType.butterworth, eegDataFilterDTO.getI(), 0., eegDataFilterDTO.getV1() / eeg.getFrequency(), 0);
                    } else {
                        design = IirFilterDesignFisher.design(FilterPassType.bandpass, FilterCharacteristicsType.butterworth, eegDataFilterDTO.getI(), 0., eegDataFilterDTO.getV1() / eeg.getFrequency(), eegDataFilterDTO.getV2() / eeg.getFrequency());
                    }
                    iirFilter = new IirFilter(design);
                }

                boolean isFirst = true;
                for (EegData data : entry.getValue()) {
                    ByteBuf buf = ZipFileParse.unCompressData(data.getData());
                    if (buf.readableBytes() > 0) {
                        if (isFirst) {
                            isFirst = false;
                        } else {
                            writer.write(",");
                        }
                        if (Objects.nonNull(design)) {
                            writer.write(String.valueOf((int) iirFilter.step(buf.readUnsignedShort())));
                        } else {
                            writer.write(String.valueOf(buf.readUnsignedShort()));
                        }
                        while (buf.readableBytes() > 0) {
                            writer.write(",");
                            if (Objects.nonNull(design)) {
                                writer.write(String.valueOf((int) iirFilter.step(buf.readUnsignedShort())));
                            } else {
                                writer.write(String.valueOf(buf.readUnsignedShort()));
                            }
                        }
                    }
                }
                zos.closeEntry();
            }
            zos.finish();
            bytes = out.toByteArray();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

        // 生成文件名
        String filename = String.format("U%s-C%s-M%s-T%tF-%tT.zip", eeg.getPatientId(), Optional.ofNullable(eeg.getCaseId()).orElse("0"), eeg.getTriggerModel().ordinal(), eeg.getTriggerTime(), eeg.getTriggerTime());
        HttpHeaders headers = new HttpHeaders();
        headers.add("Content-Disposition", "form-data; name=\"attachment\"; filename=\"" + URLEncoder.encode(filename, StandardCharsets.UTF_8) + "\"");
        headers.setContentLength(bytes.length);
        headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
        headers.setAccessControlExposeHeaders(List.of("*"));
        return new ResponseEntity<>(bytes, headers, HttpStatus.OK);
    }

    @Transactional(readOnly = true)
    public ResponseEntity<byte[]> getEdfResponseEntity(EegDTO eegDTO) {
        Page<EegVO> eegs = this.findEegsByPageable(eegDTO, PageRequest.of(0, 999999));
        ByteArrayOutputStream bos = new ByteArrayOutputStream();
        try (ZipOutputStream zipOutputStream = new ZipOutputStream(bos)) {

            for (EegVO eeg : eegs.getContent()) {
                byte[] bytes = getEdfBytes(eeg.getId(), EegDataFilterDTO.builder().build());
                String filename = String.format("U%s-C%s-M%s-T%tF-%tT.edf", eeg.getPatientId(), Optional.ofNullable(eeg.getCaseId()).orElse("0"), eeg.getTriggerModel().ordinal(), eeg.getTriggerTime(), eeg.getTriggerTime());

                zipOutputStream.putNextEntry(new ZipEntry(filename));
                zipOutputStream.write(bytes);
            }
            zipOutputStream.close();
            bos.close();
            byte[] bytes = bos.toByteArray();
            HttpHeaders headers = new HttpHeaders();
            headers.add("Content-Disposition", "form-data; name=\"attachment\"; filename=\"" + URLEncoder.encode(eegDTO.getPatientId() + ".zip", StandardCharsets.UTF_8) + "\"");
            headers.setContentLength(bytes.length);
            headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
            headers.setAccessControlExposeHeaders(List.of("*"));
            return new ResponseEntity<>(bytes, headers, HttpStatus.OK);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }

    }

    @Transactional
    public byte[] getEdfBytes(Long id, EegDataFilterDTO eegDataFilterDTO) {
        Optional<Eeg> eegOptional = repository.findById(id);
        if (eegOptional.isEmpty()) {
            throw new ServiceException("脑电[{0}]不存在！", id);
        }

        // 按通道分组
        Eeg eeg = eegOptional.get();
        Map<Integer, Set<EegData>> dataMap = new HashMap<>();
        long length = eeg.getEegData().size();
        if (Objects.nonNull(eeg.getAnode())) {
            try {
                eeg.getAnodes().addAll(objectMapper.readValue(eeg.getAnode(), TypeFactory.defaultInstance().constructCollectionType(List.class, Integer.class)));
            } catch (JsonProcessingException e) {
                log.error(e.getMessage());
            }
        }
        if (Objects.nonNull(eeg.getCathode())) {
            try {
                eeg.getCathodes().addAll(objectMapper.readValue(eeg.getCathode(), TypeFactory.defaultInstance().constructCollectionType(List.class, Integer.class)));
            } catch (JsonProcessingException e) {
                log.error(e.getMessage());
            }
        }
        if (eeg.getTriggerModel().equals(EegTriggerType.TimeSharing)) {
            Map<Integer, List<EegData>> collect = eeg.getEegData().stream().sorted(Comparator.comparing(EegData::getTriggerDate)).collect(Collectors.groupingBy(EegData::getPosition));
            length = eeg.getEegData().size() / collect.size();
            for (int i = 0; i < length; i++) {
                int finalI = i;
                collect.forEach((k, v) -> {
                    dataMap.computeIfAbsent(finalI * collect.size() + k, s -> new TreeSet<>(Comparator.comparingInt(EegData::getSegment))).add(v.get(finalI));
                });
            }
        } else {
            for (EegData data : eeg.getEegData()) {
                dataMap.computeIfAbsent(data.getPosition(), k -> new TreeSet<>(Comparator.comparingInt(EegData::getSegment))).add(data);
            }
        }


        byte[] bytes;
        try {
            // 生成edf文件
            int edfSignals = dataMap.size(); // 信号数
            File file = File.createTempFile(String.valueOf(id), ".edf"); // edf临时文件
            EDFwriter hdl = new EDFwriter(file.getAbsolutePath(), EDFwriter.EDFLIB_FILETYPE_EDFPLUS, edfSignals);

            // 添加edf数据头->基本信息
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(eeg.getTriggerTime());
            int year = calendar.get(Calendar.YEAR);
            int month = calendar.get(Calendar.MONTH) + 1;
            int day = calendar.get(Calendar.DAY_OF_MONTH);
            int hour = calendar.get(Calendar.HOUR_OF_DAY);
            int minute = calendar.get(Calendar.MINUTE);
            int second = calendar.get(Calendar.SECOND);
            hdl.setDataRecordDuration(1000000);
            hdl.setPatientName(String.valueOf(eeg.getPatientId()));
            hdl.setPatientCode(String.valueOf(eeg.getPatientId()));
            hdl.setPatientGender(2);
            hdl.setPatientAdditional("");
            hdl.setStartDateTime(year, month, day, hour, minute, second, 0);
            hdl.setEquipment("");
            hdl.setTechnician("");
            hdl.setAdministrationCode("");
            hdl.setAdditionalRecordingInfo("");

            // 添加edf数据头->通道信息
            int s = 0, record = 0, frequency = eeg.getFrequency();
            Map<Integer, List<Double>> signalData = new TreeMap<>();
            int maxLength = 0;

            int chanelSize = eeg.getEegData().stream().collect(Collectors.groupingBy(EegData::getPosition)).size();
            for (Map.Entry<Integer, Set<EegData>> entry : dataMap.entrySet()) {

                // 构造IirFilterCoefficients
                IirFilter iirFilter = null;
                IirFilterCoefficients design = null;
                if (Objects.nonNull(eegDataFilterDTO) && eegDataFilterDTO.isValid()) {
                    if (eegDataFilterDTO.getFilterPassType().equals(FilterPassType.lowpass)) {
                        design = IirFilterDesignFisher.design(FilterPassType.lowpass, FilterCharacteristicsType.butterworth, eegDataFilterDTO.getI(), 0., eegDataFilterDTO.getV1() / eeg.getFrequency(), 0);
                    } else if (eegDataFilterDTO.getFilterPassType().equals(FilterPassType.highpass)) {
                        design = IirFilterDesignFisher.design(FilterPassType.highpass, FilterCharacteristicsType.butterworth, eegDataFilterDTO.getI(), 0., eegDataFilterDTO.getV1() / eeg.getFrequency(), 0);
                    } else {
                        design = IirFilterDesignFisher.design(FilterPassType.bandpass, FilterCharacteristicsType.butterworth, eegDataFilterDTO.getI(), 0., eegDataFilterDTO.getV1() / eeg.getFrequency(), eegDataFilterDTO.getV2() / eeg.getFrequency());
                    }
                    iirFilter = new IirFilter(design);
                }

                PatientVO patientVO = iPatientController.findById(eeg.getPatientId());
                BcmVO bcmVO = iBcmController.find(patientVO.getPatientDeviceVO().getBcmId());
                Optional<ConfigsVO> optional = Optional.empty();
                if (bcmVO.getSn().contains("ISBS")) {
                    optional = Optional.of(iConfigController.findByKey("ISBS_eeg_ratio"));
                }


                double ratio = Double.parseDouble(optional.orElse(ConfigsVO.builder().value("0.472").build()).getValue());

                // 解压收集数据
                List<Double> dataList = signalData.computeIfAbsent(s, k -> new ArrayList<>(entry.getValue().size()));
                int index = 0;
                for (EegData data : entry.getValue()) {
                    ByteBuf buf = ZipFileParse.unCompressData(data.getData());
                    List<EegTag> list = eeg.getEegTag().stream().filter(et -> et.getPosition() == entry.getKey() % chanelSize).toList();

                    list = list.stream().filter(et -> {
                        try {
                            HashMap<String, String> hashMap = objectMapper.readValue(et.getMark(), TypeFactory.defaultInstance().constructMapType(HashMap.class, String.class, String.class));
                            return hashMap.getOrDefault("anode", "").equals(eeg.getAnodes().get(entry.getKey() - 1).toString())
                                    && hashMap.getOrDefault("cathode", "").equals(eeg.getCathodes().get(entry.getKey() - 1).toString());
                        } catch (JsonProcessingException e) {
                            log.error(e.getMessage());
                            return false;
                        }
                    }).toList();
                    while (buf.readableBytes() > 0) {
                        int value;
                        if (Objects.nonNull(design)) {
                            //noinspection
                            value = (int) iirFilter.step(buf.readUnsignedShort());
                        } else {
                            value = buf.readUnsignedShort();
                        }

                        dataList.add((value - 32768) * ratio); // 转微伏值

                        int finalIndex = index;

                        List<EegTag> tags = list.stream().filter(l -> l.getOnset() == finalIndex).toList();

                        if (!list.isEmpty()) {
                            tags.forEach(t -> dataList.addAll(Arrays.stream(new double[t.getLength().intValue()]).boxed().toList()));
                        }

                        index++;

                    }
                }

                // 设置通道信息（边界值，频率，标签名）
                double physMax = dataList.stream().max(Comparator.comparingDouble(Double::doubleValue)).orElse(0.0);
                double physMin = dataList.stream().min(Comparator.comparingDouble(Double::doubleValue)).orElse(0.0);
                hdl.setPhysicalMaximum(s, 32767);
                hdl.setPhysicalMinimum(s, -32767);
                hdl.setDigitalMaximum(s, 32767);
                hdl.setDigitalMinimum(s, -32767);
                hdl.setPhysicalDimension(s, "uV");
                hdl.setPreFilter(s, "0.0Hz - 100.0");
                hdl.setSampleFrequency(s, frequency);
                String label = "EEG00" + ((entry.getKey() - 1) % eeg.getEegData().stream().collect(Collectors.groupingBy(EegData::getPosition)).size() + 1);
                if (eeg.getAnodes().size() >= entry.getKey()) {
                    label += "," + eeg.getAnodes().get(entry.getKey() - 1) + "+";
                }
                if (eeg.getCathodes().size() >= entry.getKey()) {
                    label += "," + eeg.getCathodes().get(entry.getKey() - 1) + "-";
                }
                hdl.setSignalLabel(s, label);
                record = Math.max((int) Math.ceil(dataList.size() * 1d / frequency), record);
                maxLength = Math.max(dataList.size(), maxLength);
                s++;
            }

            // 添加edf消息体->通道数据
            int i, j;
            for (i = 0; i < record; i++) {
                int start = i * frequency;
                for (Map.Entry<Integer, List<Double>> entry : signalData.entrySet()) {
                    List<Double> subList = null;
                    if (start < entry.getValue().size()) {
                        subList = entry.getValue().subList(start, Math.min(start + frequency, entry.getValue().size()));

                        while (subList.size() < frequency) {
                            subList.add(0d);
                        }
                    }
                    hdl.writePhysicalSamples(Objects.nonNull(subList) ? subList.stream().mapToDouble(Double::doubleValue).toArray() : new double[frequency]);
                }
            }

            // 添加edf消息体->标签注解数据
            if (eeg.getMark() || eeg.getStimulationMark().equals(EegStimulationMark.MARKED) || eeg.getSelfMark()) {
                for (EegLfp lfp : eeg.getEegLfp()) {
                    hdl.writeAnnotation((long) (lfp.getOnset() / (eeg.getFrequency() * 1.0d) * 1000f * 10f), (long) (lfp.getDuration() / (eeg.getFrequency() * 1.0d) * 10f), lfp.getMark());
                }
            }

            if (!eeg.getEegEvent().isEmpty()) {
                eeg.getEegEvent().forEach(ee -> {
                    hdl.writeAnnotation((ee.getTriggerDate().getTime() - eeg.getTriggerTime().getTime()) * 10, Objects.nonNull(ee.getEndDate()) ? (ee.getEndDate().getTime() - ee.getTriggerDate().getTime()) * 10 : 0, ee.getEegEventType().getName());
                });
            }

            List<EegTagVO> tagList = eegTagService.findByEegId(id);

            if (!tagList.isEmpty()) {
                List<EegTagVO> collect = tagList.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(EegTagVO::getOnset))), ArrayList::new));

                for (EegTagVO eegTag : collect) {
                    Map<String, String> map = objectMapper.readValue(eegTag.getMark(), TypeFactory.defaultInstance().constructMapType(HashMap.class, String.class, String.class));

                    String label = "";
                    // 如果包含出点信息
                    if (map.containsKey("anode")) {
                        label = map.get("anode") + "+," + map.get("cathode") + "-";
                    }


                    if (eegTag.getTagType().equals(TagType.Stimulation))
                        hdl.writeAnnotation((long) (eegTag.getOnset() / (eeg.getFrequency() * 1.0d) * 1000f * 10f), 0, label + map.get("data") + "{length:\"" + eegTag.getLength() + "\"}");
                    else
                        hdl.writeAnnotation((long) (eegTag.getOnset() / (eeg.getFrequency() * 1.0d) * 1000f * 10f), 0, label + "{missLength:\"" + eegTag.getLength() + "\"}");
                }
            }
            hdl.close();
            bytes = IOUtils.toByteArray(new FileInputStream(file));
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return bytes;
    }

    @Transactional
    public ResponseEntity<byte[]> getEdfResponseEntity(Long id, EegDataFilterDTO eegDataFilterDTO) {
        byte[] bytes = getEdfBytes(id, eegDataFilterDTO);
        Optional<Eeg> eegOptional = repository.findById(id);
        Eeg eeg = eegOptional.get();
        // 生成文件名
        String filename = String.format("U%s-C%s-M%s-T%tF-%tT.edf", eeg.getPatientId(), Optional.ofNullable(eeg.getCaseId()).orElse("0"), eeg.getTriggerModel().ordinal(), eeg.getTriggerTime(), eeg.getTriggerTime());
        HttpHeaders headers = new HttpHeaders();
        headers.add("Content-Disposition", "form-data; name=\"attachment\"; filename=\"" + URLEncoder.encode(filename, StandardCharsets.UTF_8) + "\"");
        headers.setContentLength(bytes.length);
        headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
        headers.setAccessControlExposeHeaders(List.of("*"));
        return new ResponseEntity<>(bytes, headers, HttpStatus.OK);
    }

    @Transactional
    public Boolean recheckStimulationMark(Long id) {
        Optional<Eeg> eegOptional = repository.findById(id);
        if (eegOptional.isEmpty()) {
            throw new ServiceException("检查的脑电[{0}]不存在！", id);
        }
        Eeg eeg = eegOptional.get();
        if (EegStimulationMark.MARKED.equals(eeg.getStimulationMark())) {
            throw new ServiceException("检查的脑电已有刺激标记！", id);
        }
        boolean result2 = false;

        boolean result1 = generateStimulationLfp0(eeg.getId(), eeg.getPatientId(), eeg.getTriggerTime().getTime(), eeg.getFrequency(), eeg.getDuration());
        if (!eeg.getSelfMark())
            result2 = eegLfpService.generateSelfLfp0(eeg);
        return result1 || result2;
    }
}
