package com.genlight.epilcure.service.neeg.service;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.read.listener.PageReadListener;
import com.genlight.epilcure.api.neeg.enums.EegLfpSelfMark;
import com.genlight.epilcure.api.neeg.enums.FileStatus;
import com.genlight.epilcure.api.neeg.enums.FileType;
import com.genlight.epilcure.api.neeg.pojo.dto.EegLfpDTO;
import com.genlight.epilcure.api.neeg.pojo.dto.EegLfpSelfStatsDTO;
import com.genlight.epilcure.api.neeg.pojo.vo.EegLfpSelfStatsVO;
import com.genlight.epilcure.api.neeg.pojo.vo.EegLfpVO;
import com.genlight.epilcure.api.patient.feign.IArchivesController;
import com.genlight.epilcure.api.patient.feign.IPatientController;
import com.genlight.epilcure.api.patient.pojo.vo.ArchivesVO;
import com.genlight.epilcure.common.core.constants.HttpCode;
import com.genlight.epilcure.common.core.service.BaseSpecificationService;
import com.genlight.epilcure.common.core.service.exception.ServiceException;
import com.genlight.epilcure.common.core.util.MinioUtils;
import com.genlight.epilcure.service.neeg.constants.CacheNames;
import com.genlight.epilcure.service.neeg.constants.FileConstants;
import com.genlight.epilcure.service.neeg.dao.entity.*;
import com.genlight.epilcure.service.neeg.dao.repository.*;
import com.genlight.epilcure.service.neeg.pojo.convert.EegLfpConvert;
import jakarta.annotation.Resource;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanWrapperImpl;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.*;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @version 1.0.0
 */
@Service
@Slf4j
@CacheConfig(cacheNames = CacheNames.CACHE_EEG_LFP)
public class EegLfpService extends BaseSpecificationService<EegLfp, Long, EegLfpRepository> {

    @Resource
    private EegService eegService;

    @Resource
    private EegLfpConvert eegLfpConvert;

    @Resource
    private RedissonClient redissonClient;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private EegRepository eegRepository;

    @Resource
    private EegLfpRepository eegLfpRepository;

    @Resource
    private EegLfpTypeRepository eegLfpTypeRepository;

    @Resource
    private EegLfpSelfRepository eegLfpSelfRepository;

    @Resource
    private EegLfpSelfTempRepository eegLfpSelfTempRepository;

    @Resource
    private IPatientController iPatientController;

    @Resource
    private IArchivesController iArchivesController;

    @Resource
    private EegLfpSelfStatsRepository eegLfpSelfStatsRepository;

    @Resource
    private UploadFileService uploadFileService;

    @Resource
    private MinioUtils minioUtils;

    @Value("${eeg.lfp-self-mark.timeout:1}")
    private int eegLfpSelfMarkTimeout;

    @Cacheable(key = "#id")
    @Transactional(readOnly = true)
    public EegLfpVO findEegLfpById(Long id) {
        Optional<EegLfp> eegLfpOptional = repository.findById(id);
        if (eegLfpOptional.isEmpty()) {
            throw new ServiceException("查找的脑电标记[{0}]不存在！", id);
        }
        return eegLfpConvert.po2vo(eegLfpOptional.get());
    }

    @Cacheable(cacheNames = CacheNames.CACHE_EEG_LFP_LIST, key = "#p0")
    public List<EegLfpVO> findEegLfpByEegId(Long eegId) {
        return eegLfpConvert.po2vo(repository.findByEegId(eegId));
    }

    @Transactional(readOnly = true)
    @Cacheable(cacheNames = CacheNames.CACHE_EEG_LFP_LIST)
    public Page<EegLfpVO> findEegLfpsByPageable(EegLfpDTO eegLfpDTO, Pageable pageable) {
        if (Objects.isNull(eegLfpDTO.getEegId())) {
            throw new ServiceException("必须指定脑电查询");
        }
        return eegLfpConvert.po2vo(repository.findAll(generateJpaSpecification(eegLfpDTO), pageable));
    }

    @Transactional(readOnly = true)
    @Cacheable(cacheNames = CacheNames.CACHE_EEG_LFP_LIST)
    public List<EegLfpVO> findEegLfps(EegLfpDTO eegLfpDTO) {
        if (Objects.isNull(eegLfpDTO.getEegId())) {
            throw new ServiceException("必须指定脑电查询");
        }
        return eegLfpConvert.po2vo(repository.findAll(generateJpaSpecification(eegLfpDTO)));
    }

    protected Specification<EegLfp> generateJpaSpecification(EegLfpDTO eegLfpDTO) {
        return (Root<EegLfp> root, CriteriaQuery<?> query, CriteriaBuilder criteriaBuilder) -> {
            Predicate predicate = criteriaBuilder.conjunction();
            if (Objects.nonNull(eegLfpDTO.getUserId())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get(EegLfp_.userId), eegLfpDTO.getUserId()));
            }
            if (Objects.nonNull(eegLfpDTO.getSegment())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get(EegLfp_.segment), eegLfpDTO.getSegment()));
            }
            if (Objects.nonNull(eegLfpDTO.getEegId())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get(EegLfp_.eeg).get(Eeg_.id), eegLfpDTO.getEegId()));
            }
            if (Objects.nonNull(eegLfpDTO.getEegLfpTypeId())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get(EegLfp_.lfpType).get(EegLfpType_.id), eegLfpDTO.getEegLfpTypeId()));
            }
            return predicate;
        };
    }

    @Transactional
    @Caching(evict = {@CacheEvict(cacheNames = CacheNames.CACHE_EEG, allEntries = true), @CacheEvict(cacheNames = CacheNames.CACHE_EEG_LIST, allEntries = true), @CacheEvict(cacheNames = CacheNames.CACHE_EEG_LFP_LIST, allEntries = true)})
    public List<EegLfpVO> addEegLfp(List<EegLfpDTO> eegLfpDTOS) {
        ArrayList<EegLfpVO> result = new ArrayList<>();
        eegLfpDTOS.forEach(e -> {
            result.add(addEegLfp(e));
        });
        return result;
    }

    @Transactional
    @Caching(evict = {@CacheEvict(cacheNames = CacheNames.CACHE_EEG, allEntries = true), @CacheEvict(cacheNames = CacheNames.CACHE_EEG_LIST, allEntries = true), @CacheEvict(cacheNames = CacheNames.CACHE_EEG_LFP_LIST, allEntries = true)})
    public EegLfpVO addEegLfp(EegLfpDTO eegLfpDTO) {
        Optional<Eeg> eegOptional = eegService.getEegById(eegLfpDTO.getEegId());
        if (eegOptional.isEmpty()) {
            throw new ServiceException("脑电数据[{0}]不存在！", eegLfpDTO.getEegId());
        }
        Optional<EegLfpType> eegLfpTypeOptional = eegLfpTypeRepository.findById(eegLfpDTO.getEegLfpTypeId());
        if (eegLfpTypeOptional.isEmpty()) {
            throw new ServiceException("脑电标记类型[{0}]不存在！", eegLfpDTO.getEegId());
        }
        eegService.saveEeg(eegOptional.get().setMark(true));
        EegLfp lfp = eegLfpConvert.dto2po(eegLfpDTO);
        lfp.setUpdatable(true);
        lfp.setUserId(getUserId());
        lfp.setEeg(eegOptional.get());
        lfp.setLfpType(eegLfpTypeOptional.get());
        return eegLfpConvert.po2vo(repository.saveAndFlush(lfp));
    }

    @Transactional
    @Caching(evict = {@CacheEvict(cacheNames = CacheNames.CACHE_EEG, allEntries = true), @CacheEvict(cacheNames = CacheNames.CACHE_EEG_LIST, allEntries = true), @CacheEvict(cacheNames = CacheNames.CACHE_EEG_LFP_LIST, allEntries = true)})
    public void deleteEegLfps(List<Long> ids) {
        ids.forEach(id -> deleteEegLfp(id));
    }

    @Transactional
    @Caching(evict = {@CacheEvict(key = "#id"), @CacheEvict(cacheNames = CacheNames.CACHE_EEG, allEntries = true), @CacheEvict(cacheNames = CacheNames.CACHE_EEG_LIST, allEntries = true), @CacheEvict(cacheNames = CacheNames.CACHE_EEG_LFP_LIST, allEntries = true)})
    public void deleteEegLfp(Long id) {
        Optional<EegLfp> eegLfpOptional = repository.findById(id);
        if (eegLfpOptional.isEmpty()) {
            throw new ServiceException("删除的脑电标记[{0}]不存在！", id);
        }
        EegLfp lfp = eegLfpOptional.get();
        if (!lfp.getUpdatable()) {
            throw new ServiceException("脑电标记[{0}]不允许删除！", id);
        }

        if (!repository.existsByEegIdAndIdNotAndUpdatable(lfp.getEeg().getId(), lfp.getId(), Boolean.TRUE)) {
            lfp.getEeg().setMark(false);
            eegRepository.save(lfp.getEeg());
        }

        repository.delete(lfp);
    }

    @Transactional
    @Caching(put = {@CachePut(key = "#id")}, evict = {@CacheEvict(cacheNames = CacheNames.CACHE_EEG_LFP_LIST, allEntries = true)})
    public EegLfpVO updateEegLfp(Long id, EegLfpDTO eegLfpDTO) {
        Optional<EegLfp> eegLfpOptional = repository.findById(id);
        if (eegLfpOptional.isEmpty()) {
            throw new ServiceException("更新的脑电标记[{0}]不存在！", id);
        }
        EegLfp lfp = eegLfpOptional.get();
        if (!lfp.getUpdatable()) {
            throw new ServiceException("脑电标记[{0}]不允许修改！", id);
        }
        lfp = eegLfpConvert.dto2po(eegLfpDTO, lfp);
        if (Objects.nonNull(eegLfpDTO.getEegLfpTypeId()) && !eegLfpDTO.getEegLfpTypeId().equals(lfp.getLfpType().getId())) {
            Optional<EegLfpType> eegLfpTypeOptional = eegLfpTypeRepository.findById(eegLfpDTO.getEegLfpTypeId());
            if (eegLfpTypeOptional.isEmpty()) {
                throw new ServiceException("脑电标记类型[{0}]不存在！", eegLfpDTO.getEegId());
            }
            lfp.setLfpType(eegLfpTypeOptional.get());
        }
        return eegLfpConvert.po2vo(repository.saveAndFlush(lfp));
    }

    /**
     * 这个方法不要加事务
     */
    public void uploadLfpFile(MultipartFile lfpFile) {
        processEegLfpSelf(processEegLfpSelfData(lfpFile));
    }

    @Async
    public void processEegLfpSelf(List<EegLfpSelf> lfpSelfList) {
        addSelfLfpToPending(lfpSelfList);
//        generateSelfLfp();
    }

    @Transactional
    @Caching(evict = {@CacheEvict(cacheNames = CacheNames.CACHE_EEG_LFP_SELF_STATS, allEntries = true)})
    public List<EegLfpSelf> processEegLfpSelfData(MultipartFile lfpFile) {
        RLock lock = redissonClient.getLock("addStimulationList");

        try {
            if (lock.tryLock(1, 1, TimeUnit.MINUTES)) {
                Map<String, Long> patientIds = new HashMap<>();
                List<EegLfpSelf> lfpSelfList = new ArrayList<>();


                try {
                    EasyExcel.read(lfpFile.getInputStream(), EegLfpSelf.class, new PageReadListener<EegLfpSelf>(lfpSelfList::addAll)).sheet().doRead();
                } catch (Exception e) {
                    throw new ServiceException("上传失败，请检查后重新上传");
                }

                if (uploadFileService.existedUploadFile(lfpFile.getOriginalFilename())) {
                    throw new ServiceException(HttpCode.FILE_EXISTS);
                }

                // 保存文件
                String upload = minioUtils.upload(FileConstants.EPILEPSY_DIARY, lfpFile);
                uploadFileService.saveUploadFile(UploadFile.builder()
                        .name(lfpFile.getOriginalFilename())
                        .url(upload)
                        .status(FileStatus.UPLOADED)
                        .type(FileType.EPILEPSY_DIARY)
                        .userId(getUserId())
                        .retryOldParse(false)
                        .build());

                Calendar date = Calendar.getInstance();
                Calendar time = Calendar.getInstance();
                lfpSelfList.forEach(l -> {
                    // 设置患者ID
                    if (!patientIds.containsKey(l.getCardId())) {
                        Long patientId = iPatientController.findIdByCardId(l.getCardId());
                        if (patientId == Long.MAX_VALUE) {
                            throw new ServiceException("找不到患者信息");
                        }
                        patientIds.put(l.getCardId(), patientId);
                    }
                    l.setPatientId(patientIds.get(l.getCardId()));
                    l.setEegLfpSelfMark(EegLfpSelfMark.None);

                    // 合并日期和时间
                    date.setTime(l.getDate());
                    time.setTime(l.getTime());
                    time.set(date.get(Calendar.YEAR), date.get(Calendar.MONTH), date.get(Calendar.DATE));
                    l.setTriggerTime(time.getTime());
                });

                Set<String> existed = new HashSet<>();
                for (Iterator<EegLfpSelf> iterator = lfpSelfList.iterator(); iterator.hasNext(); ) {
                    EegLfpSelf lfpSelf = iterator.next();
                    String key = lfpSelf.getPatientId() + ":" + lfpSelf.getTriggerTime().getTime() + ":" + lfpSelf.getDuration();

                    // 过滤excel中的重复数据
                    if (existed.contains(key)) {
                        iterator.remove();
                        continue;
                    } else {
                        existed.add(key);
                    }

                    // 过滤数据库中的重复数据
                    if (eegLfpSelfRepository.existsByPatientIdAndTriggerTimeAndDuration(lfpSelf.getPatientId(), lfpSelf.getTriggerTime(), lfpSelf.getDuration())) {
                        iterator.remove();
                    }
                }

                // 保存数据
                if (!lfpSelfList.isEmpty()) {
                    addEegLfpSelfStatsList(lfpSelfList);
                    return eegLfpSelfRepository.saveAllAndFlush(lfpSelfList);
                } else {
                    return Collections.emptyList();
                }
            }
            throw new ServiceException("操作太频繁，请稍后再试！");
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        } finally {
            this.unLock(lock);
        }
    }

    @Transactional
    public void addEegLfpSelfStatsList(List<EegLfpSelf> eegLfpSelfList) {

        // 收集数据
        Calendar calendar = Calendar.getInstance();
        Map<Date, BeanWrapperImpl> stats = new HashMap<>();
        for (EegLfpSelf s : eegLfpSelfList) {
            calendar.setTime(s.getTriggerTime());
            String hour = "h" + calendar.get(Calendar.HOUR_OF_DAY);
            calendar.set(Calendar.HOUR_OF_DAY, 0);
            calendar.set(Calendar.MINUTE, 0);
            calendar.set(Calendar.SECOND, 0);
            calendar.set(Calendar.MILLISECOND, 0);
            Date date = calendar.getTime();
            BeanWrapperImpl ss = stats.computeIfAbsent(date, v -> new BeanWrapperImpl(EegLfpSelfStats.builder().patientId(s.getPatientId()).triggerDate(date).build()));
            ss.setPropertyValue(hour, Optional.ofNullable((Integer) ss.getPropertyValue(hour)).orElse(0) + 1);
        }

        // 增量统计
        for (Map.Entry<Date, BeanWrapperImpl> entry : stats.entrySet()) {
            EegLfpSelfStats s = (EegLfpSelfStats) entry.getValue().getWrappedInstance();
            EegLfpSelfStats u = eegLfpSelfStatsRepository.findByPatientIdAndTriggerDate(s.getPatientId(), entry.getKey());
            if (Objects.nonNull(u)) {
                u.setH0(u.getH0() + s.getH0());
                u.setH1(u.getH1() + s.getH1());
                u.setH2(u.getH2() + s.getH2());
                u.setH3(u.getH3() + s.getH3());
                u.setH4(u.getH4() + s.getH4());
                u.setH5(u.getH5() + s.getH5());
                u.setH6(u.getH6() + s.getH6());
                u.setH7(u.getH7() + s.getH7());
                u.setH8(u.getH8() + s.getH8());
                u.setH9(u.getH9() + s.getH9());
                u.setH10(u.getH10() + s.getH10());
                u.setH11(u.getH11() + s.getH11());
                u.setH12(u.getH12() + s.getH12());
                u.setH13(u.getH13() + s.getH13());
                u.setH14(u.getH14() + s.getH14());
                u.setH15(u.getH15() + s.getH15());
                u.setH16(u.getH16() + s.getH16());
                u.setH17(u.getH17() + s.getH17());
                u.setH18(u.getH18() + s.getH18());
                u.setH19(u.getH19() + s.getH19());
                u.setH20(u.getH20() + s.getH20());
                u.setH21(u.getH21() + s.getH21());
                u.setH22(u.getH22() + s.getH22());
                u.setH23(u.getH23() + s.getH23());
                eegLfpSelfStatsRepository.saveAndFlush(u);
            } else {
                eegLfpSelfStatsRepository.saveAndFlush(s);
            }
        }
    }

    @Transactional
    public void addSelfLfpToPending(List<EegLfpSelf> eegLfpSelves) {
        if (!eegLfpSelves.isEmpty()) {
            log.info("待处理的标记条数" + eegLfpSelves.size());
            RLock lock = redissonClient.getLock("addLfpToPending");
            try {
                if (lock.tryLock(10, 10, TimeUnit.SECONDS)) {

                    List<EegLfpSelfTemp> list = new ArrayList<>();
                    eegLfpSelves.forEach(e -> {
                        list.add(EegLfpSelfTemp.builder()
                                .id(e.getId())
                                .patientId(e.getPatientId())
                                .triggerTime(e.getTriggerTime())
                                .duration(e.getDuration())
                                .remark(e.getRemark())
                                .createTime(e.getCreateTime())
                                .build());
                    });

                    eegLfpSelfTempRepository.saveAllAndFlush(list);
                    for (EegLfpSelfTemp eegLfpSelfTemp : list) {
                        String id = eegLfpSelfTemp.getId().toString();
                        stringRedisTemplate.opsForList().leftPush("epilcure::eegLfp::pending::list", id);
                    }
                }
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            } finally {
                this.unLock(lock);
            }
        } else {
            log.info("待处理的标记条数" + eegLfpSelves.size());
        }
    }

    @Transactional
    public void init() {
        List<EegLfpSelf> lfpSelfList = eegLfpSelfRepository.getByEegLfpSelfMark(EegLfpSelfMark.None);
        eegLfpSelfTempRepository.deleteAll();
        stringRedisTemplate.delete("epilcure::eegLfp::pending::list");
        addSelfLfpToPending(lfpSelfList);
    }


    @Transactional
    @Scheduled(fixedRate = 300, timeUnit = TimeUnit.SECONDS, initialDelay = 300)
    public void generateSelfLfp() {
        String compare = null;

        Long size = stringRedisTemplate.opsForList().size("epilcure::eegLfp::pending::list");
        Long count = 0L;
        while (true) {
            String lfpId = stringRedisTemplate.opsForList().leftPop("epilcure::eegLfp::pending::list");
            if (Objects.isNull(lfpId) || lfpId.equals(compare)) {
                break;
            }
            boolean success;
            try {
                success = generateSelfLfp0(lfpId);
            } catch (Exception e) {
                e.printStackTrace();
                success = false;
            }
            if (!success) {
                if (Objects.isNull(compare)) {
                    compare = lfpId;
                }
                stringRedisTemplate.opsForList().rightPush("epilcure::eegLfp::pending::list", lfpId);
            }

            count++;
            if (count >= size) break;
        }
    }

    @Transactional
    @Caching(evict = {@CacheEvict(cacheNames = CacheNames.CACHE_EEG, allEntries = true), @CacheEvict(cacheNames = CacheNames.CACHE_EEG_LIST, allEntries = true), @CacheEvict(cacheNames = CacheNames.CACHE_EEG_LFP_LIST, allEntries = true)})
    public boolean generateSelfLfp0(String lfpId) {
        String key = "epilcure::eegLfp::pending::" + lfpId;

        Optional<EegLfpSelfTemp> optional = eegLfpSelfTempRepository.findById(Long.parseLong(lfpId));
        if (optional.isEmpty()) {
            // 删除临时记录
            return true;
        }

        EegLfpSelfTemp eegLfpSelfTemp = optional.get();
        long patientId = eegLfpSelfTemp.getPatientId();
        long triggerTime = eegLfpSelfTemp.getTriggerTime().getTime();
        long duration = eegLfpSelfTemp.getDuration();
        Object remark = eegLfpSelfTemp.getRemark();

        Date begin = new Date(triggerTime);
        Date end = new Date(triggerTime + duration * 1000);
        List<Eeg> eegs = eegRepository.getByDateRange(patientId, begin, end);
        if (eegs.isEmpty()) {
            // 超过未找到放弃
            EegLfpSelf eegLfpSelf = eegLfpSelfRepository.getReferenceById(Long.parseLong(lfpId));
            long createTime = eegLfpSelfTemp.getCreateTime().getTime();
            if ((System.currentTimeMillis() - createTime) > (86400000L * eegLfpSelfMarkTimeout)) {
                eegLfpSelf.setEegLfpSelfMark(EegLfpSelfMark.TIMEOUT);
                eegLfpSelfRepository.save(eegLfpSelf);

                // 删除临时记录
                eegLfpSelfTempRepository.deleteById(Long.parseLong(lfpId));

                log.info("[{0}未找到脑电数据标记,已超时]", lfpId);

                return true;
            }
            return false;
        }

        EegLfpSelf eegLfpSelf = eegLfpSelfRepository.getReferenceById(Long.parseLong(lfpId));
        if (!EegLfpSelfMark.MARKED.equals(eegLfpSelf.getEegLfpSelfMark())) {
            long selfTriggerBegin = begin.getTime();
            long selfTriggerEnd = end.getTime();
            for (Eeg eeg : eegs) {
                long eegTriggerBegin = eeg.getTriggerTime().getTime();
                long eegTriggerEnd = eegTriggerBegin + (int) (eeg.getDuration() * 1000);
                long begins = (selfTriggerBegin > eegTriggerBegin ? (selfTriggerBegin - eegTriggerBegin) : 0L) / 1000 * eeg.getFrequency();
                long ends = (Math.min(selfTriggerEnd, eegTriggerEnd) - eegTriggerBegin) / 1000 * eeg.getFrequency();
                eegLfpRepository.save(EegLfp.builder().userId(0L).segment(0L).onset(begins).duration(duration * 1000).mark(Objects.isNull(remark) ? "{\"end\":" + ends + "}" : "{\"end\":" + ends + ",\"remark\":" + remark + "}").updatable(false).eeg(eeg).lfpType(eegLfpTypeRepository.getReferenceById(99226455128543234L)).build());
                eeg.setSelfMark(true);
                eegRepository.save(eeg);
            }
            eegLfpSelf.setEegLfpSelfMark(EegLfpSelfMark.MARKED);
            eegLfpSelfRepository.save(eegLfpSelf);
        }

        // 删除临时记录
        eegLfpSelfTempRepository.deleteById(Long.parseLong(lfpId));
        return true;
    }

    @Caching(evict = {@CacheEvict(cacheNames = CacheNames.CACHE_EEG, allEntries = true), @CacheEvict(cacheNames = CacheNames.CACHE_EEG_LIST, allEntries = true), @CacheEvict(cacheNames = CacheNames.CACHE_EEG_LFP_LIST, allEntries = true)})
    public boolean generateSelfLfp0(Eeg eeg) {
        Date begin = eeg.getTriggerTime();
        Date end = new Date(begin.getTime() + (int) (eeg.getDuration() * 1000));
        List<EegLfpSelf> eegLfpSelfList = eegLfpSelfRepository.getByPatientIdAndTriggerTimeBetweenOrderByTriggerTimeAsc(eeg.getPatientId(), begin, end);
        if (!eegLfpSelfList.isEmpty()) {
            long eegTriggerBegin = begin.getTime();
            long eegTriggerEnd = end.getTime();
            for (EegLfpSelf eegLfpSelf : eegLfpSelfList) {
                long selfTriggerBegin = eegLfpSelf.getTriggerTime().getTime();
                long selfTriggerEnd = selfTriggerBegin + eegLfpSelf.getDuration() * 1000;
                long begins = (selfTriggerBegin > eegTriggerBegin ? (selfTriggerBegin - eegTriggerBegin) : 0L) / 1000 * eeg.getFrequency();
                long ends = (Math.min(selfTriggerEnd, eegTriggerEnd) - eegTriggerBegin) / 1000 * eeg.getFrequency();
                eegLfpRepository.save(EegLfp.builder().userId(0L).segment(0L).onset(begins).duration(eegLfpSelf.getDuration() * 1000L).mark(Objects.isNull(eegLfpSelf.getRemark()) ? "{\"end\":" + ends + "}" : "{\"end\":" + ends + ",\"remark\":" + eegLfpSelf.getRemark() + "}").updatable(false).eeg(eeg).lfpType(eegLfpTypeRepository.getReferenceById(99226455128543234L)).build());
            }
            eeg.setSelfMark(true);
            eegRepository.save(eeg);
            return true;
        }
        return false;
    }

    @Transactional(readOnly = true)
    @Cacheable(cacheNames = CacheNames.CACHE_EEG_LFP_SELF_STATS)
    public EegLfpSelfStatsVO findEegLfpSelfStats(EegLfpSelfStatsDTO eegLfpSelfStatsDTO) {
        EegLfpSelfStatsVO eegLfpSelfStatsVO = new EegLfpSelfStatsVO();
        eegLfpSelfStatsVO.setPatientId(eegLfpSelfStatsDTO.getPatientId());
        eegLfpSelfStatsVO.setStatsType(eegLfpSelfStatsDTO.getStatsType());

        if (eegLfpSelfStatsVO.getStatsType() == 4) { // 平均每月发作次数（发作总次数/天数/28）-日精度
            if (Objects.isNull(eegLfpSelfStatsDTO.getBeginDate())) {
                throw new ServiceException("统计的开始时间不能为空!");
            }

            if (Objects.isNull(eegLfpSelfStatsDTO.getEndDate())) {
                throw new ServiceException("统计的结束时间不能为空!");
            }
            eegLfpSelfStatsVO.setValue(eegLfpSelfStatsRepository.statsAvgByMonth(eegLfpSelfStatsDTO.getPatientId(), eegLfpSelfStatsDTO.getBeginDate(), eegLfpSelfStatsDTO.getEndDate()).orElse(0.0).toString());
        } else if (eegLfpSelfStatsVO.getStatsType() == 5) { // 平均每月发作次数（发作总次数/天数/28）-小时精度
            if (Objects.isNull(eegLfpSelfStatsDTO.getBeginTime())) {
                throw new ServiceException("统计的开始时间不能为空!");
            }

            if (Objects.isNull(eegLfpSelfStatsDTO.getEndTime())) {
                throw new ServiceException("统计的结束时间不能为空!");
            }
            eegLfpSelfStatsVO.setValue(eegLfpSelfStatsRepository.statsAvgByMonth0(eegLfpSelfStatsDTO.getPatientId(), eegLfpSelfStatsDTO.getBeginTime(), eegLfpSelfStatsDTO.getEndTime()).orElse(0.0).toString());
        } else if (eegLfpSelfStatsVO.getStatsType() == 6) { // 平均每月发作次数（发作总次数/天数/28）-秒精度
            if (Objects.isNull(eegLfpSelfStatsDTO.getBeginTime())) {
                throw new ServiceException("统计的开始时间不能为空!");
            }

            if (Objects.isNull(eegLfpSelfStatsDTO.getEndTime())) {
                throw new ServiceException("统计的结束时间不能为空!");
            }
            eegLfpSelfStatsVO.setValue(eegLfpSelfRepository.statsAvgByMonth(eegLfpSelfStatsDTO.getPatientId(), eegLfpSelfStatsDTO.getBeginTime(), eegLfpSelfStatsDTO.getEndTime()).orElse(0.0).toString());
        } else if (eegLfpSelfStatsVO.getStatsType() == 3) {   // 每周0-23小时的汇总
            Date rhythmEndDate = Optional.ofNullable(eegLfpSelfStatsDTO.getRhythmEndDate()).orElse(new Date());
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(rhythmEndDate);
            calendar.add(Calendar.WEEK_OF_YEAR, -Optional.ofNullable(eegLfpSelfStatsDTO.getRhythmBefore()).orElse(12));
            Date rhythmBeginDate = calendar.getTime();

            eegLfpSelfStatsVO.setValueList(new HashMap<>());
            for (String[] values : eegLfpSelfStatsRepository.statsSumValueByWeek(eegLfpSelfStatsDTO.getPatientId(), rhythmBeginDate, rhythmEndDate)) {
                eegLfpSelfStatsVO.getValueList().put(values[0], Arrays.copyOfRange(values, 1, values.length));
            }
        } else {
            if (Objects.isNull(eegLfpSelfStatsDTO.getArchivesId())) {
                throw new ServiceException("档案ID不能为空!");
            }
            ArchivesVO archivesVO = iArchivesController.findById(eegLfpSelfStatsDTO.getArchivesId());
            if (Objects.nonNull(archivesVO) && Objects.nonNull(archivesVO.getPatient().getSurgeryDate())) {

                // 修正手术时间
                Calendar calendar = Calendar.getInstance();
                calendar.setTime(archivesVO.getPatient().getSurgeryDate());
                calendar.set(Calendar.HOUR_OF_DAY, 0);
                calendar.set(Calendar.MINUTE, 0);
                calendar.set(Calendar.SECOND, 0);
                eegLfpSelfStatsVO.setSurgeryDate(calendar.getTime());

                // 恢复期开始
                calendar.set(Calendar.HOUR_OF_DAY, 23);
                calendar.set(Calendar.MINUTE, 59);
                calendar.set(Calendar.SECOND, 59);
                Date recoverBegin = calendar.getTime();

                // 修正开机时间
                calendar.setTime(Optional.ofNullable(archivesVO.getPatient().getMedicalRecordVO().getPowerUpDate()).orElse(new Date(archivesVO.getPatient().getSurgeryDate().getTime() + 86400000L * 28)));
                calendar.set(Calendar.HOUR_OF_DAY, 23);
                calendar.set(Calendar.MINUTE, 59);
                calendar.set(Calendar.SECOND, 59);
                eegLfpSelfStatsVO.setPowerUpDate(calendar.getTime());

                // 恢复期结束
                calendar.set(Calendar.HOUR_OF_DAY, 0);
                calendar.set(Calendar.MINUTE, 0);
                calendar.set(Calendar.SECOND, 0);
                Date recoverEnd = calendar.getTime();

                Date beginDate = null, endDate = null;
                if (Objects.nonNull(eegLfpSelfStatsDTO.getBeforeSurgery())) {
                    calendar.setTime(eegLfpSelfStatsVO.getSurgeryDate());
                    calendar.add(Calendar.DAY_OF_YEAR, -eegLfpSelfStatsDTO.getBeforeSurgery() * 28);
                    beginDate = calendar.getTime();
                }

                if (Objects.nonNull(eegLfpSelfStatsDTO.getAfterPowerUp())) {
                    calendar.setTime(eegLfpSelfStatsVO.getPowerUpDate());
                    calendar.add(Calendar.DAY_OF_YEAR, eegLfpSelfStatsDTO.getAfterPowerUp() * 28);
                    endDate = calendar.getTime();
                }

                if (eegLfpSelfStatsVO.getStatsType() == 1) {   // 每月发作次数
                    eegLfpSelfStatsVO.setValues(new HashMap<>());
                    for (String[] values : eegLfpSelfStatsRepository.statsCountByMonth(eegLfpSelfStatsDTO.getPatientId(), eegLfpSelfStatsVO.getSurgeryDate(), eegLfpSelfStatsVO.getPowerUpDate(), recoverBegin, recoverEnd, beginDate, endDate)) {
                        eegLfpSelfStatsVO.getValues().put(values[0], values[1]);
                    }
                } else if (eegLfpSelfStatsVO.getStatsType() == 2) {   // 每月发作天数
                    eegLfpSelfStatsVO.setValues(new HashMap<>());
                    for (String[] values : eegLfpSelfStatsRepository.statsDaysByMonth(eegLfpSelfStatsDTO.getPatientId(), eegLfpSelfStatsVO.getSurgeryDate(), eegLfpSelfStatsVO.getPowerUpDate(), recoverBegin, recoverEnd, beginDate, endDate)) {
                        eegLfpSelfStatsVO.getValues().put(values[0], values[1]);
                    }
                }
            }
        }
        return eegLfpSelfStatsVO;
    }

    @Transactional(readOnly = true)
    public Map<Long, Integer> statsEegLfpByEegId(Long eegId) {
        List<Long[]> result = repository.statsLfpTypeByEegId(eegId);
        Map<Long, Integer> stats = new HashMap<>();
        for (Long[] data : result) {
            stats.put(data[0], data[1].intValue());
        }
        return stats;
    }

    @Transactional(readOnly = true)
    public boolean existsEegLfpByEegLfpType(EegLfpType eegLfpType) {
        return repository.existsByLfpType(eegLfpType);
    }
}
