package com.genlight.epilcure.service.neeg.dao.repository;

import com.genlight.epilcure.common.core.dao.enums.Status;
import com.genlight.epilcure.service.neeg.dao.entity.EegEvent;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;

import java.util.Date;
import java.util.List;

public interface EegEventRepository extends JpaRepository<EegEvent, Long>, JpaSpecificationExecutor<EegEvent> {
    List<EegEvent> findByPatientIdAndStatusAndTriggerDateBetween(Long patientId, Status status, Date startDate, Date endDate);

    @Query("""
            select case when count(e)>0 then true else false end from EegEvent e where e.eeg.id=:eegId and e.status=:status and e.eegEventType.id=:eegEventTypeId
            """)
    Boolean existsByEegIdAndStatusAndEegEventTypeId(Long eegId, Status status, Long eegEventTypeId);
}
