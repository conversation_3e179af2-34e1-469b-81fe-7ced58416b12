package com.genlight.epilcure.service.neeg.shardingsphere;

import org.apache.shardingsphere.driver.jdbc.context.JDBCContext;
import org.apache.shardingsphere.driver.jdbc.core.connection.ShardingSphereConnection;
import org.apache.shardingsphere.driver.state.DriverState;
import org.apache.shardingsphere.mode.manager.ContextManager;

import java.sql.Connection;

public final class OKDriverState implements DriverState {

    @Override
    public Connection getConnection(final String databaseName, final ContextManager contextManager, final JDBCContext jdbcContext) {
        return new ShardingSphereConnectionProxy(new ShardingSphereConnection(databaseName, contextManager, jdbcContext));
    }

    @Override
    public String getType() {
        return "OK";
    }
}
