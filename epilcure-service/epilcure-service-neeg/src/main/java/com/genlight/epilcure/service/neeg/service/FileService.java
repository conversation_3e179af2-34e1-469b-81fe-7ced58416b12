package com.genlight.epilcure.service.neeg.service;

import com.genlight.epilcure.api.device.feign.IBcmModelController;
import com.genlight.epilcure.api.device.feign.IBppModelController;
import com.genlight.epilcure.api.device.feign.IElectrodeModelController;
import com.genlight.epilcure.api.device.pojo.vo.BcmModelVO;
import com.genlight.epilcure.api.device.pojo.vo.BppModelVO;
import com.genlight.epilcure.api.device.pojo.vo.ElectrodeModelVO;
import com.genlight.epilcure.api.neeg.enums.FileStatus;
import com.genlight.epilcure.api.neeg.enums.FileType;
import com.genlight.epilcure.api.neeg.pojo.dto.UploadFileDTO;
import com.genlight.epilcure.api.neeg.pojo.vo.UploadFileVO;
import com.genlight.epilcure.api.patient.enums.IdCardType;
import com.genlight.epilcure.api.patient.feign.IArchivesController;
import com.genlight.epilcure.api.patient.feign.IPatientController;
import com.genlight.epilcure.api.patient.pojo.dto.*;
import com.genlight.epilcure.api.user.feign.IUserController;
import com.genlight.epilcure.api.user.pojo.vo.UserVO;
import com.genlight.epilcure.common.core.constants.HttpCode;
import com.genlight.epilcure.common.core.dao.enums.Gender;
import com.genlight.epilcure.common.core.pojo.dto.AddressDTO;
import com.genlight.epilcure.common.core.service.BaseService;
import com.genlight.epilcure.common.core.service.exception.ArgsException;
import com.genlight.epilcure.common.core.service.exception.BaseException;
import com.genlight.epilcure.common.core.service.exception.ServiceException;
import com.genlight.epilcure.common.core.util.MinioUtils;
import com.genlight.epilcure.service.neeg.constants.FileConstants;
import com.genlight.epilcure.service.neeg.dao.entity.Eeg;
import com.genlight.epilcure.service.neeg.dao.entity.UploadFile;
import com.genlight.epilcure.service.neeg.dao.entity.UploadFile_;
import com.genlight.epilcure.service.neeg.dao.repository.UploadFileRepository;
import com.genlight.epilcure.service.neeg.edf.EDFException;
import com.genlight.epilcure.service.neeg.edf.EDFreader;
import com.genlight.epilcure.service.neeg.pojo.convert.UploadFileConvert;
import com.genlight.epilcure.service.neeg.properties.EegProperties;
import com.genlight.epilcure.service.neeg.utils.EdfFileParse;
import com.genlight.epilcure.service.neeg.utils.EegFileOldParse;
import com.genlight.epilcure.service.neeg.utils.EegFileParse;
import io.seata.spring.annotation.GlobalTransactional;
import jakarta.annotation.Resource;
import jakarta.persistence.criteria.Predicate;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.core.annotation.Order;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.util.unit.DataSize;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.time.Duration;
import java.time.Instant;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

@Service
@Slf4j
@Order(2)
public class FileService extends BaseService<UploadFile, Long, UploadFileRepository> implements ApplicationRunner {

    @Autowired
    private MinioUtils minioUtils;

    @Resource
    private EegService eegService;

    @Resource
    private EegLfpService eegLfpService;

    @Resource
    private EegFileParse eegFileParse;

    @Resource
    private EegFileOldParse eegFileOldParse;

    @Resource
    private UploadFileService uploadFileService;

    @Resource
    private StimulationService stimulationService;

    @Resource
    private UploadFileConvert uploadFileConvert;

    @Resource
    private EdfFileParse edfFileParse;

    @Resource
    private RedissonClient redissonClient;

    @Resource
    private IPatientController iPatientController;
    @Resource
    private IArchivesController iArchivesController;

    @Resource
    private IBcmModelController iBcmModelController;

    @Resource
    private IBppModelController iBppModelController;

    @Resource
    private IElectrodeModelController iElectrodeModelController;

    @Resource
    private RocketMQTemplate rocketMQTemplate;

    @Resource
    private StringRedisTemplate stringRedisTemplate;


    @Resource
    private EegProperties eegProperties;

    @Resource
    private IUserController iUserController;

    @Transactional(readOnly = true)
    public Page<UploadFileVO> finds(UploadFileDTO uploadFileDTO, Pageable pageable) {
        Specification<UploadFile> specification = (root, query, criteriaBuilder) -> {
            Predicate predicate = criteriaBuilder.conjunction();
            if (Objects.nonNull(uploadFileDTO.getStartDate())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.greaterThanOrEqualTo(root.get(UploadFile_.CREATE_TIME), uploadFileDTO.getStartDate()));
            }
            if (Objects.nonNull(uploadFileDTO.getEndDate())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.lessThanOrEqualTo(root.get(UploadFile_.CREATE_TIME), uploadFileDTO.getEndDate()));
            }
            if (Objects.nonNull(uploadFileDTO.getStatus())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get(UploadFile_.STATUS), uploadFileDTO.getStatus()));
            }
            if (Objects.nonNull(uploadFileDTO.getName())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.like(root.get(UploadFile_.NAME), "%" + uploadFileDTO.getName() + "%"));
            }
            if (Objects.nonNull(uploadFileDTO.getType())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get(UploadFile_.TYPE), uploadFileDTO.getType()));
            }
            return predicate;
        };
        return uploadFileConvert.po2vo(repository.findAll(specification, pageable));
    }

    public void reParseFile(Long fileId) {
        reParseFile0(uploadFileService.getById(fileId).get());
    }

    public void run(ApplicationArguments args) {

        eegLfpService.init();

        for (UploadFile uploadFile : repository.getByStatus(FileStatus.UPLOADED)) {
            try {
                reParseFile0(uploadFile);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    public void reParseFile0(UploadFile uploadFile) {
        try {
            if (uploadFile.getType().equals(FileType.EEG_EVENT)
                    || uploadFile.getType().equals(FileType.EPILEPSY_DIARY)) return;
            if (uploadFile.getStatus() == FileStatus.PARSING) {
                throw new ServiceException("文件正在解析中");
            } else if (uploadFile.getStatus() == FileStatus.PARSE_SUCCESS) {
                throw new ServiceException("文件已解析完成");
            }
            uploadFile.setRetryOldParse(true);
            ArrayList<UploadFile> uploadFiles = new ArrayList<>();
            uploadFiles.add(uploadFile);
            if (Objects.nonNull(uploadFile.getUploadFile())) {
                uploadFiles.add(0, uploadFile.getUploadFile());
            }
            stringRedisTemplate.opsForValue().set(uploadFile.getId().toString(), uploadFile.getId().toString());
            rocketMQTemplate.convertAndSend(eegProperties.getEegAnalysis().getTopic(), uploadFiles);

        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public void parseEdf(MultipartFile edf) {
        UploadFile uploadFile = null;
        RLock lock = redissonClient.getLock("uploadEdfFile-%s".formatted(edf.getOriginalFilename()));
        try {
            if (lock.tryLock(2, 2, TimeUnit.MINUTES)) {
                if (uploadFileService.existedUploadFile(edf.getOriginalFilename())) {
                    throw new ArgsException(HttpCode.FILE_EXISTS);
                }
                Instant start = Instant.now();
                String filename = minioUtils.upload(FileConstants.EDF, edf);
                if (!StringUtils.hasText(filename)) {
                    throw new ServiceException("文件上传失败");
                }
                log.info("压缩包[{}]上传完成，大小[{}]MB，耗时[{}]ms", edf.getOriginalFilename(), DataSize.ofBytes(edf.getSize()).toMegabytes(), Duration.between(start, Instant.now()).toMillis());

                uploadFile = uploadFileService.saveUploadFile(UploadFile.builder().name(edf.getOriginalFilename()).type(FileType.EDF).status(FileStatus.UPLOADED).url(filename).userId(getUserId()).hasDuration(false).retryOldParse(false).build());

                processEdf(edf, uploadFile);
            } else {
                throw new ServiceException("操作太频繁，请稍后再试！");
            }

        } catch (Exception e) {
            throw new RuntimeException(e);
        } finally {
            this.unLock(lock);
        }

    }

    @Async
    @GlobalTransactional(rollbackFor = Exception.class)
    @Transactional
    public void processEdf(MultipartFile edf, UploadFile uploadFile) {
        try {
            String[] split = Objects.requireNonNull(edf.getOriginalFilename()).split("\\.");
            File file = File.createTempFile(split[0], split[1]);
            edf.transferTo(file);
            EDFreader edfReader = new EDFreader(file.getPath());
            Long patientId = findOrAddArchives(edfReader, uploadFile);
            Eeg eeg = edfFileParse.parseEdfFile(edfReader, uploadFile, patientId);
            uploadFile.setPatientId(patientId);
            uploadFile.setDataStartTime(eeg.getTriggerTime());
            uploadFile.setDataEndTime(eeg.getTriggerTime());
            eegService.addEeg(eeg, uploadFile);
            file.deleteOnExit();
        } catch (IOException | EDFException e) {
            throw new RuntimeException(e);
        }
    }

    @GlobalTransactional(rollbackFor = Exception.class)
    @Transactional
    public Long findOrAddArchives(EDFreader edfReader, UploadFile file) {
        Long patientId = iPatientController.findIdByCardId(file.getName().split("_")[0]);
        UserVO userVO = iUserController.findById(file.getUserId());

        List<BcmModelVO> bcmModelVOList = iBcmModelController.findAll(1L);
        List<BppModelVO> bppModelVOList = iBppModelController.finds(1L);
        List<ElectrodeModelVO> electrodeModelVOList = iElectrodeModelController.finds(1L);

        if (Objects.isNull(bcmModelVOList) || bcmModelVOList.size() <= 0) {
            throw new ArgsException("系统无刺激器型号配置");
        }

        if (Objects.isNull(bppModelVOList) || bppModelVOList.size() <= 0) {
            throw new ArgsException("系统无程控仪型号配置");
        }

        if (Objects.isNull(electrodeModelVOList) || electrodeModelVOList.size() <= 0) {
            throw new ArgsException("系统无电极型号配置");
        }

        if (patientId.equals(Long.MAX_VALUE)) {
            ArchivesDTO archivesDTO = ArchivesDTO.builder()
                    .diseaseId(1L)
                    .orgId(userVO.getOrg().getId())
                    .deptId(userVO.getDept().getId())
                    .archivesNo("BCM-" + String.valueOf(LocalDate.now().getYear()).substring(2) + "-ZZ-M" + iArchivesController.findEpilcureNo())
                    .build();
            PatientDTO patientDTO = PatientDTO.builder()
                    .archivesId(1L)
                    .name(edfReader.getPatientName())
                    .sex(Gender.MALE)
                    .birthday(new Date())
                    .idCardType(IdCardType.Custom)
                    .mobile("18000000000")
                    .idCard(file.getName().split("_")[0])
                    .address(AddressDTO.builder()
                            .country("中国")
                            .province("浙江省")
                            .city("杭州市")
                            .district("余杭区")
                            .street("数字健康小镇")
                            .latitude(0D)
                            .longitude(0D)
                            .build())
                    .build();
            List<ContractElectrodeModelDTO> contractElectrodeModelDTOList = new ArrayList<>();
            contractElectrodeModelDTOList.add(ContractElectrodeModelDTO.builder()
                    .position(0)
                    .electrodeModelId(electrodeModelVOList.get(0).getId())
                    .build());
            PatientDeviceDTO patientDeviceDTO = PatientDeviceDTO.builder()
                    .bcmModelId(bcmModelVOList.get(0).getId())
                    .bppModelId(bppModelVOList.get(0).getId())
                    .contractElectrodeModels(contractElectrodeModelDTOList)
                    .build();
            PatientSurgerySchemeDTO patientSurgerySchemeDTO = PatientSurgerySchemeDTO.builder()
                    .userId(userVO.getId())
                    .surgeryDate(new Date())
                    .build();
            // 创建新的档案
            patientId = iArchivesController.addArchivesAndPatient(archivesDTO, patientDTO, patientDeviceDTO
                    , patientSurgerySchemeDTO, MedicalRecordDTO.builder().diseaseId(1L).build(), ContractDTO.builder()
                            .kinName(userVO.getName())
                            .kinMobile(userVO.getMobile())
                            .kinRelationShip("")
                            .build()).getPatient().getId();
        }

        return patientId;
    }


    public List<UploadFileVO> upload_0_1(MultipartFile[] eegFiles, MultipartFile[] stimulationFiles) {
        List<UploadFileVO> uploadFileVOList = new ArrayList<>();
        if (Objects.nonNull(eegFiles)) {
            for (MultipartFile eegFile : eegFiles) {
                try {
                    eegService.uploadEegFile(eegFile, null, FileConstants.EEG, FileType.EEG, false, "0.1");
                } catch (Exception ex) {
                    if (ex.getCause() instanceof ArgsException || ex.getCause() instanceof ServiceException) {
                        uploadFileVOList.add(UploadFileVO.builder()
                                .name(eegFile.getOriginalFilename())
                                .description(String.valueOf(((BaseException) ex.getCause()).getHttpCode().getCode()))
                                .build());
                    } else {
                        uploadFileVOList.add(UploadFileVO.builder()
                                .name(eegFile.getOriginalFilename())
                                .description(ex.getMessage())
                                .build());
                    }

                    log.error(ex.getMessage());
                }
            }
        }

        if (Objects.nonNull(stimulationFiles)) {
            uploadStimulationFile(stimulationFiles, uploadFileVOList);
        }

        return uploadFileVOList;
    }


    public List<UploadFileVO> upload(MultipartFile[] eegFiles, MultipartFile[] stimulationFiles) {
        List<UploadFileVO> uploadFileVOList = new ArrayList<>();
        if (Objects.nonNull(eegFiles)) {
            for (MultipartFile eegFile : eegFiles) {
                try {
                    eegService.uploadEegFile(eegFile, FileConstants.EEG, FileType.EEG);
                } catch (Exception ex) {
                    if (ex.getCause() instanceof ArgsException || ex.getCause() instanceof ServiceException) {
                        uploadFileVOList.add(UploadFileVO.builder()
                                .name(eegFile.getOriginalFilename())
                                .description(String.valueOf(((BaseException) ex.getCause()).getHttpCode().getCode()))
                                .build());
                    } else {
                        uploadFileVOList.add(UploadFileVO.builder()
                                .name(eegFile.getOriginalFilename())
                                .description(ex.getMessage())
                                .build());
                    }

                    log.error(ex.getMessage(), ex);
                }
            }
        }

        if (Objects.nonNull(stimulationFiles)) {
            uploadStimulationFile(stimulationFiles, uploadFileVOList);
        }

        return uploadFileVOList;
    }

    public void uploadStimulationFile(MultipartFile[] stimulationFiles, List<UploadFileVO> uploadFileVOList) {
        for (MultipartFile stimulationFile : stimulationFiles) {
            try {
                stimulationService.uploadStimulationFile(stimulationFile);
            } catch (Exception ex) {
                if (ex.getCause() instanceof ArgsException || ex.getCause() instanceof ServiceException) {
                    uploadFileVOList.add(UploadFileVO.builder()
                            .name(stimulationFile.getOriginalFilename())
                            .description(String.valueOf(((BaseException) ex.getCause()).getHttpCode().getCode()))
                            .build());
                } else {
                    uploadFileVOList.add(UploadFileVO.builder()
                            .name(stimulationFile.getOriginalFilename())
                            .description("上传失败")
                            .build());
                }
            }
        }
    }
}
