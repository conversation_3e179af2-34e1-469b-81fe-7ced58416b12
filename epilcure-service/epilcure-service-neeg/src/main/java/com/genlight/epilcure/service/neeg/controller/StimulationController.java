package com.genlight.epilcure.service.neeg.controller;

import com.genlight.epilcure.api.neeg.enums.FileType;
import com.genlight.epilcure.api.neeg.pojo.dto.StimulationDTO;
import com.genlight.epilcure.api.neeg.pojo.dto.StimulationStatsDTO;
import com.genlight.epilcure.api.neeg.pojo.dto.UploadFileDTO;
import com.genlight.epilcure.api.neeg.pojo.vo.StimulationStatsVO;
import com.genlight.epilcure.api.neeg.pojo.vo.StimulationVO;
import com.genlight.epilcure.api.neeg.pojo.vo.UploadFileVO;
import com.genlight.epilcure.common.core.mvc.response.JsonResult;
import com.genlight.epilcure.service.neeg.service.StimulationService;
import com.genlight.epilcure.service.neeg.service.UploadFileService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

/**
 * <AUTHOR>
 * @version 1.0.0
 */
@RestController
@RequestMapping("/api/eeg/stimulates")
@Tag(name = "StimulationController", description = "刺激相关接口")
public class StimulationController {

    @Resource
    private StimulationService stimulationService;

    @Resource
    private UploadFileService uploadFileService;

    @GetMapping("/{id}")
    @Operation(summary = "根据ID查询刺激数据")
    public JsonResult<StimulationVO> findStimulationById(@PathVariable Long id) {
        return JsonResult.ok(stimulationService.findStimulationById(id));
    }

    @GetMapping("/stats")
    @Operation(summary = "查询刺激数据的统计信息")
    public JsonResult<StimulationStatsVO> findStimulationStats(StimulationStatsDTO stimulationStatsDTO) {
        return JsonResult.ok(stimulationService.findStimulationStats(stimulationStatsDTO));
    }

    @GetMapping
    @Operation(summary = "根据动态条件查询刺激数据")
    public JsonResult<Page<StimulationVO>> findStimulationByPageable(StimulationDTO stimulationDTO, Pageable pageable) {
        return JsonResult.ok(stimulationService.findStimulationByPageable(stimulationDTO, pageable));
    }

    @Operation(summary = "上传刺激数据（Zip文件）")
    @PostMapping(value = "/upload", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public JsonResult<Void> uploadStimulationFile(@RequestPart MultipartFile stimulationFile) {
        stimulationService.uploadStimulationFile(stimulationFile);
        return JsonResult.ok();
    }

    @Operation(summary = "上传刺激数据（Zip文件）")
    @PostMapping(value = "/upload/1_0", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public JsonResult<Void> uploadStimulationFile1_0(@RequestPart MultipartFile stimulationFile) {
        stimulationService.uploadStimulationFile(stimulationFile, "1.0");
        return JsonResult.ok();
    }

    @Operation(summary = "查询上传的刺激数据文件")
    @GetMapping(value = "/upload")
    public JsonResult<Page<UploadFileVO>> findUploadStimulationFiles(UploadFileDTO uploadFileDTO, Pageable pageable) {
        uploadFileDTO.setType(FileType.STIMULATION);
        return JsonResult.ok(uploadFileService.findUploadFileByPageable(uploadFileDTO, pageable));
    }
}
