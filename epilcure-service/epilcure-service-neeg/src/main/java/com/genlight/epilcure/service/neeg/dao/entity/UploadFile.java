package com.genlight.epilcure.service.neeg.dao.entity;

import com.genlight.epilcure.api.neeg.enums.FileStatus;
import com.genlight.epilcure.api.neeg.enums.FileType;
import com.genlight.epilcure.common.core.dao.entity.TEntity;
import com.genlight.epilcure.common.core.dao.generator.annotation.SignOrder;
import jakarta.persistence.*;
import lombok.*;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;
import org.hibernate.annotations.Comment;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0.0
 */
@Setter
@Getter
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString
@Entity
@Cacheable
@DynamicInsert
@DynamicUpdate
@SignOrder(0)
@Table(name = "t_file")
public class UploadFile extends TEntity {

    @Comment("文件上传者")
    @Column
    @SignOrder(1)
    private Long userId;

    @Comment("文件名称")
    @Column(nullable = false, unique = true)
    @SignOrder(2)
    private String name;

    @Comment("文件地址")
    @Column(nullable = false, unique = true)
    @SignOrder(3)
    private String url;

    @Comment("文件类型")
    @Column(nullable = false)
    @Enumerated(EnumType.ORDINAL)
    @SignOrder(4)
    private FileType type;

    @Comment("文件状态")
    @Column(nullable = false)
    @Enumerated(EnumType.ORDINAL)
    @SignOrder(5)
    private FileStatus status;

    @Comment("数据是否带时长")
    @Column(nullable = false)
    @SignOrder(6)
    private Boolean hasDuration;

    @Comment("是否重试旧的解析方式")
    @Column(nullable = false)
    @SignOrder(7)
    private Boolean retryOldParse;

    @Comment("解析方法")
    @Column
    @SignOrder(8)
    private String parseMethod;

    @Comment("描述信息")
    @Column(columnDefinition = "TEXT")
    @SignOrder(9)
    private String description;

    @Comment("文件版本")
    @SignOrder(10)
    private String fileVersion;

    @Column
    @SignOrder(11)
    @Comment("患者ID")
    private Long patientId;

    @Column
    @SignOrder(12)
    @Comment("文件包含数据的开始时间")
    @Temporal(TemporalType.TIMESTAMP)
    private Date dataStartTime;

    @Column
    @SignOrder(13)
    @Comment("文件包含数据的结束时间")
    @Temporal(TemporalType.TIMESTAMP)
    private Date dataEndTime;

    @Column
    @SignOrder(14)
    @Comment("是否解析真实0")
    private Boolean reallyZero;

    @OneToOne
    @JoinColumn(name = "upload_file_id",nullable = true)
    @Comment("关联得事件文件")
    private UploadFile uploadFile;
}
