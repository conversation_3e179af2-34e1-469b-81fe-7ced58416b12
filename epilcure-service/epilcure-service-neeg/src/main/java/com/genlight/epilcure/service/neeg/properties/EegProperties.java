package com.genlight.epilcure.service.neeg.properties;


import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;

@Data
@RefreshScope
@ConfigurationProperties("eeg")
public class EegProperties {
    private EegCalculation eegCalculation = new EegCalculation();

    private EegAnalysis eegAnalysis = new EegAnalysis();

    private EegLfp eegLfp = new EegLfp();

    private Long eventTypeCount;

    @Data
    public static class EegCalculation {
        private String topic;
    }

    @Data
    public static class EegAnalysis {
        private String topic;
    }

    @Data
    public static class EegLfp {
        private String topic;
    }
}
