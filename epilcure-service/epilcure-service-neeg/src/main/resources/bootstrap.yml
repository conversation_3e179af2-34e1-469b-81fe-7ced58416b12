spring:
  main:
    allow-circular-references: true
  application:
    name: neeg-service
  cloud:
    nacos:
      discovery:
        server-addr: ${NACOS_HOST:nacos.cloud.com}:${NACOS_PORT:80}
        username: ${NACOS_USERNAME:nacos}
        password: ${NACOS_PASSWORD:nacos}
        namespace: ${NACOS_DISCOVERY_NAMESPACE:@nacos.discovery.namespace@}
      config:
        server-addr: ${spring.cloud.nacos.discovery.server-addr}
        username: ${spring.cloud.nacos.discovery.username}
        password: ${spring.cloud.nacos.discovery.password}
        namespace: ${NACOS_CONFIG_NAMESPACE:@nacos.config.namespace@}
        group: DEFAULT_GROUP
        prefix: epilcure-${spring.application.name}
        file-extension: yaml
        shared-configs:
          - data-id: application.${spring.cloud.nacos.config.file-extension}
            group: ${spring.cloud.nacos.config.group}
            refresh: true
          - data-id: variables-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}
            group: ${spring.cloud.nacos.config.group}
            refresh: true

  profiles:
    active: @profiles.active@