databaseName: epilcure_neeg
mode:
  type: Standalone
  repository:
    type: JDBC
dataSources:
  ds1:
    dataSourceClassName: com.zaxxer.hikari.HikariDataSource
    driverClassName: com.mysql.cj.jdbc.Driver
    jdbcUrl: **************************************************************************************************************************************************************************************************************************
    #    jdbcUrl: ******************************************************************************************************************************************************************************************
    username: root
    password: XnIlTNlzHX
    #    password: 14tpNGe0sj
    pool-name: Epilcure
    minimum-idle: 32
    maximum-pool-size: 256
    max-lifetime: 1800000
    idle-timeout: 600000
    connection-timeout: 30000

rules:
  - !SHARDING
    tables:
      t_stimulation:
        actualDataNodes: ds1.t_stimulation_${0..9}
        tableStrategy:
          standard:
            shardingColumn: file_id
            shardingAlgorithmName: t_stimulation_file_inline
      t_stimulation_temp:
        actualDataNodes: ds1.t_stimulation_temp_${0..9}
        tableStrategy:
          standard:
            shardingColumn: patient_id
            shardingAlgorithmName: t_stimulation_patient_inline
    shardingAlgorithms:
      t_stimulation_file_inline:
        type: INLINE
        props:
          algorithm-expression: t_stimulation_${file_id % 10}
      t_stimulation_patient_inline:
        type: INLINE
        props:
          algorithm-expression: t_stimulation_temp_${patient_id % 10}