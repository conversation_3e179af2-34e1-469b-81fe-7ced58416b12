package com.genlight.epilcure.service.auth.service;

import com.genlight.epilcure.api.patient.feign.IPatientController;
import com.genlight.epilcure.api.patient.feign.ISurgeryController;
import com.genlight.epilcure.api.patient.pojo.vo.PatientVO;
import com.genlight.epilcure.api.user.feign.IUserController;
import com.genlight.epilcure.api.user.pojo.vo.ActionVO;
import com.genlight.epilcure.api.user.pojo.vo.PermissionVO;
import com.genlight.epilcure.common.auth.bo.SecurityAuthority;
import com.genlight.epilcure.common.core.dao.enums.SmsType;
import com.genlight.epilcure.common.core.dysms.SmsService;
import com.genlight.epilcure.common.core.pojo.bo.ValidSmsBO;
import com.genlight.epilcure.common.core.properties.JWTProperties;
import com.genlight.epilcure.common.core.service.exception.ArgsException;
import com.genlight.epilcure.common.core.service.exception.ServiceException;
import com.genlight.epilcure.common.core.util.JwtUtils;
import com.genlight.epilcure.common.core.util.RSAUtils;
import com.genlight.epilcure.service.auth.dto.PatientLoginDTO;
import com.genlight.epilcure.service.auth.vo.PatientLoginVO;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

@Service
@Slf4j
public class UserDetailsServiceImpl implements UserDetailsService {

    @Autowired
    private IUserController iUserController;

    @Autowired
    private IPatientController iPatientController;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private ISurgeryController iSurgeryController;

    @Resource
    private SmsService smsService;

    @Resource
    private PasswordEncoder passwordEncoder;

    @Resource
    private RSAUtils rsaUtils;

    @Resource
    private JWTProperties jwtProperties;

    @Override
    public UserDetails loadUserByUsername(String s) throws UsernameNotFoundException {

        String failureCount = stringRedisTemplate.opsForValue().get("AuthFailure::" + s);
        if (Objects.nonNull(failureCount) && Integer.parseInt(failureCount) >= 5) {
            throw new ServiceException("异常登录，请5分钟后重试！！！");
        }

        return new com.genlight.epilcure.common.auth.bo.UserDetails(iUserController.loginByMobile(s));
    }

    public PatientLoginVO loginByPatient(PatientLoginDTO patientLoginDTO) {
        PatientVO patientVO = iPatientController.findByMobile(patientLoginDTO.getMobile());

        if (Objects.nonNull(patientLoginDTO.getCode())) {
            if (!patientVO.getIsWhite())
                smsService.checkValid(patientLoginDTO.getMobile(), patientLoginDTO.getUuid(), patientLoginDTO.getCode());
        } else {
            if (Objects.isNull(patientVO.getPassword())) {
                throw new ArgsException("患者[{0}]未设置密码", patientLoginDTO.getMobile());
            }
            if (!passwordEncoder.matches(rsaUtils.decryptPassword(patientLoginDTO.getPassword()), patientVO.getPassword())) {
                throw new ArgsException("密码错误");
            }
        }

        // todo: 获取特定角色权限
        ArrayList<PermissionVO> list = new ArrayList<>();
        list.add(PermissionVO.builder()
                .code("/**.GET")
                .action(ActionVO.builder()
                        .code("GET")
                        .build())
                .build());
        list.add(PermissionVO.builder()
                .code("/**.POST")
                .action(ActionVO.builder()
                        .code("POST")
                        .build())
                .build());
        list.add(PermissionVO.builder()
                .code("/**.PUT")
                .action(ActionVO.builder()
                        .code("PUT")
                        .build())
                .build());
        list.add(PermissionVO.builder()
                .code("/api/eeg/eegEventType/**.DELETE")
                .action(ActionVO.builder()
                        .code("DELETE")
                        .build())
                .build());
        list.add(PermissionVO.builder()
                .code("/api/eeg/eegEvent/**.DELETE")
                .action(ActionVO.builder()
                        .code("DELETE")
                        .build())
                .build());

        Set<SecurityAuthority> collect = list.stream().map(p -> new SecurityAuthority(p.getCode())).collect(Collectors.toSet());
        StringBuffer buffer = new StringBuffer();
        collect.forEach(item -> {
            buffer.append(item.getAuthority());
            buffer.append(",");
        });
        if (!buffer.isEmpty())
            buffer.deleteCharAt(buffer.length() - 1);

        // 用户的 username 和他所具有的权限存入 redis 中。
        stringRedisTemplate.opsForHash().put(JwtUtils.patientAuthoritiesKey, patientVO.getMobile(), buffer.toString());
        return PatientLoginVO.builder()
                .token(JwtUtils.createJWT(patientVO.getMobile(), patientVO.getName(), patientVO.getId(), true, false, patientLoginDTO.getDevice(), patientLoginDTO.getOs(), jwtProperties))
                .patient(patientVO)
                .permissions(list)
                .build();
    }

    public String getCaptcha(String mobile) {
        iPatientController.findByMobile(mobile);
        int captcha = (int) ((Math.random() * 9 + 1) * 100000);
        return smsService.sendSms(mobile, SmsType.Valid, ValidSmsBO.builder().code(String.valueOf(captcha)).build());
    }
}