package com.genlight.epilcure.service.user.dao.repository;

import com.genlight.epilcure.common.core.dao.enums.Status;
import com.genlight.epilcure.service.user.dao.entity.Permission;
import com.genlight.epilcure.service.user.dao.entity.Role;
import com.genlight.epilcure.service.user.dao.entity.User;
import jakarta.persistence.QueryHint;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.jpa.repository.QueryHints;

import java.util.List;
import java.util.Optional;

import static org.hibernate.jpa.QueryHints.HINT_CACHEABLE;

/**
 * <AUTHOR>
 * @version 1.0.0
 */
public interface RoleRepository extends JpaRepository<Role, Long>, JpaSpecificationExecutor<Role> {

    List<Role> findAllByStatus(Status status);

    Page<Role> findAllByUsersContains(User user, Pageable pageable);

    boolean existsByName(String name);

    boolean existsByCode(String code);

    boolean existsByCodeOrName(String code, String name);

    boolean existsByPermissionsContains(Permission permission);

    @QueryHints(@QueryHint(name = HINT_CACHEABLE, value = "true"))
    @Query("SELECT min (r.level) FROM Role r JOIN r.users u WHERE r.status = 1 AND u.id = :id")
    Optional<Integer> getMinRoleLevelByUser(Long id);
}
