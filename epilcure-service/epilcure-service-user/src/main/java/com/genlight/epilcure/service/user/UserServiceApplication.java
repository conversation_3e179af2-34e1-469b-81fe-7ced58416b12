package com.genlight.epilcure.service.user;

import net.bytebuddy.agent.ByteBuddyAgent;
import org.aspectj.weaver.loadtime.Agent;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.security.servlet.SecurityAutoConfiguration;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.instrument.InstrumentationSavingAgent;

import java.lang.instrument.Instrumentation;

/**
 * <AUTHOR>
 * @version 1.0.0
 */
@EnableDiscoveryClient
@SpringBootApplication(exclude = SecurityAutoConfiguration.class, scanBasePackages = "com.genlight.epilcure")
public class UserServiceApplication {

    public static void main(String[] args) {

        Instrumentation instrumentation = ByteBuddyAgent.install();
        Agent.agentmain("", instrumentation);
        InstrumentationSavingAgent.agentmain("", instrumentation);

        SpringApplication.run(UserServiceApplication.class, args);
    }
}
