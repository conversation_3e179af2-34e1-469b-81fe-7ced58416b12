package com.genlight.epilcure.service.user.dao.entity;

import com.genlight.epilcure.common.core.dao.entity.TTree;
import com.genlight.epilcure.common.core.dao.enums.Status;
import com.genlight.epilcure.common.core.dao.generator.annotation.SignOrder;
import jakarta.persistence.*;
import lombok.*;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;
import org.hibernate.annotations.Comment;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import java.util.HashSet;
import java.util.Set;

/**
 * <AUTHOR>
 * @version 1.0.0
 */
@Setter
@Getter
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString
@Entity
@Cacheable
@DynamicInsert
@DynamicUpdate
@SignOrder(0)
@Table(name = "t_dept", indexes = {
        @Index(name = "index_dept_status", columnList = "status")
}, uniqueConstraints = {
        @UniqueConstraint(name = "unique_name_constraint", columnNames = {"org_id", "name"})
})
public class Dept extends TTree<Dept, DeptRelation> {

    @Comment("名称")
    @Column(nullable = false, length = 32)
    @SignOrder(3)
    private String name;

    @Comment("电话")
    @Column(length = 16)
    @SignOrder(4)
    private String phone;

    @Comment("状态（0：禁用，1：启用）")
    @Column(nullable = false)
    @Enumerated(EnumType.ORDINAL)
    @SignOrder(5)
    private Status status;

    @Comment("描述信息")
    @Column(length = 1024)
    @SignOrder(6)
    private String description;

    @ToString.Exclude
    @ManyToOne(optional = false, fetch = FetchType.LAZY)
    private Org org;

    @ToString.Exclude
    @Builder.Default
    @OneToMany(mappedBy = "dept")
    private Set<User> users = new HashSet<>();
}
