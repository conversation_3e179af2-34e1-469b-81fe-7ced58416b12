package com.genlight.epilcure.service.user.controller;

import com.genlight.epilcure.api.user.pojo.dto.RoleDTO;
import com.genlight.epilcure.api.user.pojo.vo.RoleVO;
import com.genlight.epilcure.common.core.mvc.response.JsonResult;
import com.genlight.epilcure.common.core.pojo.valid.Add;
import com.genlight.epilcure.common.core.pojo.valid.Update;
import com.genlight.epilcure.service.user.service.RoleService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 */
@RestController
@RequestMapping("/api/user/roles")
@Tag(name = "RoleController", description = "角色相关接口")
public class RoleController {

    @Resource
    private RoleService roleService;

    @GetMapping("/{id}")
    @Operation(summary = "根据ID查询角色")
    public JsonResult<RoleVO> findRoleById(@PathVariable Long id) {
        return JsonResult.ok(roleService.findRoleById(id));
    }

    @GetMapping("/enabled")
    @Operation(summary = "查询所有启用角色")
    public JsonResult<List<RoleVO>> findAllEnabledRoles() {
        return JsonResult.ok(roleService.findAllEnabledRoles());
    }

    @GetMapping("/users/{id}")
    @Operation(summary = "查询指定用户的拥有的角色")
    public JsonResult<Page<RoleVO>> findHasRolesByUser(@PathVariable Long id, RoleDTO roleDTO, Pageable pageable) {
        return JsonResult.ok(roleService.findRolesByPageable(roleDTO.setUserId(id).setUserEquals(true), pageable));
    }

    @GetMapping("/users/not/{id}")
    @Operation(summary = "查询指定用户的未拥有的角色")
    public JsonResult<Page<RoleVO>> findHasNotRolesByUser(@PathVariable Long id, RoleDTO roleDTO, Pageable pageable) {
        return JsonResult.ok(roleService.findRolesByPageable(roleDTO.setUserId(id).setUserEquals(false), pageable));
    }

    @GetMapping("/permissions/{id}")
    @Operation(summary = "查询指定权限分配的角色")
    public JsonResult<Page<RoleVO>> findHasRolesByPermission(@PathVariable Long id, RoleDTO roleDTO, Pageable pageable) {
        return JsonResult.ok(roleService.findRolesByPageable(roleDTO.setPermissionId(id).setPermissionEquals(true), pageable));
    }

    @GetMapping("/permissions/not/{id}")
    @Operation(summary = "查询指定权限未分配的角色")
    public JsonResult<Page<RoleVO>> findHasNotRolesByPermission(@PathVariable Long id, RoleDTO roleDTO, Pageable pageable) {
        return JsonResult.ok(roleService.findRolesByPageable(roleDTO.setPermissionId(id).setPermissionEquals(false), pageable));
    }

    @GetMapping
    @Operation(summary = "根据动态条件查询角色")
    public JsonResult<Page<RoleVO>> findRolesByPageable(RoleDTO roleDTO, Pageable pageable) {
        return JsonResult.ok(roleService.findRolesByPageable(roleDTO, pageable));
    }

    @PostMapping
    @Operation(summary = "新增角色")
    public JsonResult<RoleVO> addRole(@Validated(Add.class) @RequestBody RoleDTO roleDTO) {
        return JsonResult.ok(roleService.addRole(roleDTO));
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "删除角色")
    public JsonResult<Void> deleteRole(@PathVariable Long id) {
        roleService.deleteRole(id);
        return JsonResult.ok();
    }

    @PutMapping("/{id}")
    @Operation(summary = "修改角色")
    public JsonResult<RoleVO> updateRole(@PathVariable Long id, @Validated(Update.class) @RequestBody RoleDTO roleDTO) {
        return JsonResult.ok(roleService.updateRole(id, roleDTO));
    }

    @PutMapping("/enabled")
    @Operation(summary = "批量启用角色")
    public JsonResult<Void> enabledRoles(@RequestBody List<Long> ids) {
        roleService.enabledRoles(ids);
        return JsonResult.ok();
    }

    @PutMapping("/disabled")
    @Operation(summary = "批量禁用角色")
    public JsonResult<RoleVO> disabledRoles(@RequestBody List<Long> ids) {
        roleService.disabledRoles(ids);
        return JsonResult.ok();
    }

    @PutMapping("/enabled/{id}")
    @Operation(summary = "启用角色")
    public JsonResult<RoleVO> enabledRole(@PathVariable Long id) {
        return JsonResult.ok(roleService.enabledRole(id));
    }

    @PutMapping("/disabled/{id}")
    @Operation(summary = "禁用角色")
    public JsonResult<RoleVO> disabledRole(@PathVariable Long id) {
        return JsonResult.ok(roleService.disabledRole(id));
    }

    @PutMapping("/{id}/users/associated")
    @Operation(summary = "关联用户")
    public JsonResult<Void> associatedUsers(@PathVariable Long id, @RequestBody List<Long> userIds) {
        roleService.associatedUsers(id, userIds);
        return JsonResult.ok();
    }

    @PutMapping("/{id}/users/dissociated")
    @Operation(summary = "解除用户关联")
    public JsonResult<Void> dissociatedUsers(@PathVariable Long id, @RequestBody List<Long> userIds) {
        roleService.dissociatedUsers(id, userIds);
        return JsonResult.ok();
    }

    @PutMapping("/{id}/permissions/associated")
    @Operation(summary = "关联权限")
    public JsonResult<Void> associatedPermissions(@PathVariable Long id, @RequestBody List<Long> permissionIds) {
        roleService.associatedPermissions(id, permissionIds);
        return JsonResult.ok();
    }

    @PutMapping("/{id}/permissions/dissociated")
    @Operation(summary = "解除权限关联")
    public JsonResult<Void> dissociatedPermissions(@PathVariable Long id, @RequestBody List<Long> permissionIds) {
        roleService.dissociatedPermissions(id, permissionIds);
        return JsonResult.ok();
    }
}
