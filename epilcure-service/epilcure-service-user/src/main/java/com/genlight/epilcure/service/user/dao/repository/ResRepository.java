package com.genlight.epilcure.service.user.dao.repository;

import com.genlight.epilcure.common.core.dao.enums.Status;
import com.genlight.epilcure.service.user.dao.entity.Res;
import jakarta.persistence.QueryHint;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.jpa.repository.QueryHints;

import java.util.List;
import java.util.Optional;

import static org.hibernate.jpa.QueryHints.HINT_CACHEABLE;

/**
 * <AUTHOR>
 * @version 1.0.0
 */
public interface ResRepository extends JpaRepository<Res, Long>, JpaSpecificationExecutor<Res> {

    Optional<Res> findByCode(String code);

    Optional<Res> findByName(String name);

    List<Res> findAllByStatus(Status status);

    boolean existsByName(String name);

    boolean existsByCode(String code);

    boolean existsByCodeOrName(String code, String name);

    @QueryHints(@QueryHint(name = HINT_CACHEABLE, value = "true"))
    @Query("FROM Res res JOIN res.permissions p JOIN p.roles r JOIN r.users u WHERE res.status = 1 AND p.status = 1 AND r.status = 1 AND u.status = 1 AND u.id = :id")
    List<Res> findResTreeByUser(Long id);
}
