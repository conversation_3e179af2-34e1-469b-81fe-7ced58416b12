package com.genlight.epilcure.service.user.service;

import com.genlight.epilcure.api.user.pojo.dto.ResDTO;
import com.genlight.epilcure.api.user.pojo.vo.ResVO;
import com.genlight.epilcure.common.core.dao.enums.Status;
import com.genlight.epilcure.common.core.service.BaseTreeSpecificationService;
import com.genlight.epilcure.common.core.service.exception.ServiceException;
import com.genlight.epilcure.service.user.constants.CacheNames;
import com.genlight.epilcure.service.user.dao.entity.Res;
import com.genlight.epilcure.service.user.dao.entity.ResRelation;
import com.genlight.epilcure.service.user.dao.entity.Res_;
import com.genlight.epilcure.service.user.dao.repository.ResRelationRepository;
import com.genlight.epilcure.service.user.dao.repository.ResRepository;
import com.genlight.epilcure.service.user.pojo.convert.ResConvert;
import jakarta.annotation.Resource;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.annotation.Caching;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @version 1.0.0
 */
@Service
@CacheConfig(cacheNames = CacheNames.CACHE_RES)
public class ResService extends BaseTreeSpecificationService<Res, ResVO, ResRelation, Long, ResRepository, ResRelationRepository> {

    @Resource
    private ResConvert resConvert;

    @Resource
    private RedissonClient redissonClient;

    @Resource
    private PermissionService permissionService;

    @Cacheable(key = "#id")
    @Transactional(readOnly = true)
    public ResVO findResById(Long id) {
        Optional<Res> res = repository.findById(id);
        if (res.isEmpty()) {
            throw new ServiceException("查找的资源不存在！");
        }
        return resConvert.po2voByDetail(res.get());
    }

    @Transactional(readOnly = true)
    public Optional<Res> getResById(Long id) {
        return repository.findById(id);
    }

    @Transactional(readOnly = true)
    @Cacheable(cacheNames = CacheNames.CACHE_RES_LIST, key = "'enabled'")
    public List<ResVO> findAllEnabledReses() {
        return resConvert.po2vo(repository.findAllByStatus(Status.ENABLED));
    }

    @Transactional(readOnly = true)
    @Cacheable(cacheNames = CacheNames.CACHE_RES_LIST)
    public List<ResVO> findResTree(ResDTO resDTO) {
        return generateTreeRelationship(repository.findAll(genSpecification(resDTO)));
    }

    @Transactional(readOnly = true)
    @Cacheable(cacheNames = CacheNames.CACHE_RES_LIST)
    public List<ResVO> findResTreeByUser(Long id) {
        return generateTreeRelationship(repository.findResTreeByUser(id));
    }

    @Transactional(readOnly = true)
    @Cacheable(cacheNames = CacheNames.CACHE_RES_LIST)
    public Page<ResVO> findResesByPageable(ResDTO resDTO, Pageable pageable) {
        return resConvert.po2vo(repository.findAll(genSpecification(resDTO), pageable));
    }

    @Transactional
    @CacheEvict(cacheNames = CacheNames.CACHE_RES_LIST, allEntries = true)
    public ResVO addRes(ResDTO resDTO) {
        RLock lock = redissonClient.getLock("addRes-%s-%s".formatted(resDTO.getCode(), resDTO.getName()));
        try {
            if (lock.tryLock(2, 2, TimeUnit.SECONDS)) {
                if (repository.existsByCodeOrName(resDTO.getCode(), resDTO.getName())) {
                    throw new ServiceException("资源名称或者资源代码已经存在！");
                }
                return resConvert.po2vo(repository.saveAndFlush(addParentsRelationship(resDTO.getParentId(), resConvert.dto2po(resDTO))));
            }
            throw new ServiceException("操作太频繁，请稍后再试！");
        } catch (Exception e) {
            throw new RuntimeException(e);
        } finally {
            this.unLock(lock);
        }
    }

    @Transactional
    public Res addOrGetRes(String code) {
        return repository.findByCode(code).orElseGet(() -> addRes(code));
    }

    @Transactional
    @CacheEvict(cacheNames = CacheNames.CACHE_RES_LIST, allEntries = true)
    public Res addOrUpdateRes(Res res) {
        Optional<Res> resOptional = repository.findByCode(res.getCode());
        if (resOptional.isEmpty()) {
            resOptional = repository.findByName(res.getName());
            if (resOptional.isEmpty()) {
                return repository.saveAndFlush(addParentsRelationship(null, res));
            }
        }
        Res r = resOptional.get();
        r.setCode(res.getCode());
        r.setName(res.getName());
        r.setDescription(res.getDescription());
        return repository.saveAndFlush(r);
    }

    @Transactional
    @CacheEvict(cacheNames = CacheNames.CACHE_RES_LIST, allEntries = true)
    private Res addRes(String code) {
        Res res = Res.builder().seq(0).name(code).code(code).status(Status.ENABLED).description(code).build();
        res.getParentRelation().add(ResRelation.builder().parent(res).child(res).depth(0).build());
        return repository.saveAndFlush(addParentsRelationship(null, res));
    }

    @Transactional
    @Caching(evict = {
            @CacheEvict(key = "#id"),
            @CacheEvict(cacheNames = CacheNames.CACHE_RES_LIST, allEntries = true)
    })
    public void deleteRes(Long id) {
        Optional<Res> resOptional = repository.findById(id);
        if (resOptional.isEmpty()) {
            throw new ServiceException("删除的资源不存在！");
        }

        Res res = resOptional.get();
        if (permissionService.existsPermissionByRes(res)) {
            throw new ServiceException("资源已经被权限关联，不能删除！");
        }
        if (relationRepository.existsByParentAndChildNot(res, res)) {
            throw new ServiceException("存在下级资源关联，不能删除！");
        }

        repository.delete(res);
    }

    @Transactional
    @Caching(evict = {
            @CacheEvict(cacheNames = CacheNames.CACHE_RES, allEntries = true),
            @CacheEvict(cacheNames = CacheNames.CACHE_RES_LIST, allEntries = true),
            @CacheEvict(cacheNames = CacheNames.CACHE_PERMISSION, allEntries = true),
            @CacheEvict(cacheNames = CacheNames.CACHE_PERMISSION_LIST, allEntries = true)
    })
    public ResVO updateRes(Long id, ResDTO resDTO) {
        Optional<Res> resOptional = repository.findById(id);
        if (resOptional.isEmpty()) {
            throw new ServiceException("更新的资源不存在！");
        }
        Res res = resOptional.get();
        if (StringUtils.hasText(resDTO.getName()) && !resDTO.getName().equals(res.getName())) {
            if (StringUtils.hasText(resDTO.getCode()) && !resDTO.getCode().equals(res.getCode())) {
                if (repository.existsByCodeOrName(resDTO.getCode(), resDTO.getName())) {
                    throw new ServiceException("更新的资源名称或者资源代码已经存在！");
                }
            } else {
                if (repository.existsByName(resDTO.getName())) {
                    throw new ServiceException("更新的资源名称已经存在！");
                }
            }
        } else if (StringUtils.hasText(resDTO.getCode()) && !resDTO.getCode().equals(res.getCode())) {
            if (repository.existsByCode(resDTO.getCode())) {
                throw new ServiceException("更新的资源代码已经存在！");
            }
        }
        res = resConvert.dto2po(resDTO, res);
        if (Objects.nonNull(resDTO.getParentId()) && (Objects.isNull(res.getParent()) || !resDTO.getParentId().equals(res.getParent().getId()))) {
            if (Objects.nonNull(res.getParent())) {
                if (!res.getParent().getId().equals(resDTO.getParentId())) {
                    if (resDTO.getParentId().intValue() == 0) {
                        clearParentsRelationship(res);
                    } else {
                        updateParentsRelationship(resDTO.getParentId(), res, true);
                    }
                }
            } else {
                updateParentsRelationship(resDTO.getParentId(), res, true);
            }
        }
        return resConvert.po2vo(repository.saveAndFlush(res));
    }

    @Transactional
    public void enabledReses(List<Long> ids) {
        for (Long id : ids) {
            enabledRes(id);
        }
    }

    @Transactional
    @Caching(evict = {
            @CacheEvict(cacheNames = CacheNames.CACHE_RES, allEntries = true),
            @CacheEvict(cacheNames = CacheNames.CACHE_RES_LIST, allEntries = true),
            @CacheEvict(cacheNames = CacheNames.CACHE_PERMISSION, allEntries = true),
            @CacheEvict(cacheNames = CacheNames.CACHE_PERMISSION_LIST, allEntries = true)
    })
    public ResVO enabledRes(Long id) {
        Optional<Res> resOptional = repository.findById(id);
        if (resOptional.isEmpty()) {
            throw new ServiceException("启用的资源不存在！");
        }
        return resConvert.po2vo(repository.saveAndFlush(resOptional.get().setStatus(Status.ENABLED)));
    }

    @Transactional
    public void disabledReses(List<Long> ids) {
        for (Long id : ids) {
            disabledRes(id);
        }
    }

    @Transactional
    @Caching(evict = {
            @CacheEvict(cacheNames = CacheNames.CACHE_RES, allEntries = true),
            @CacheEvict(cacheNames = CacheNames.CACHE_RES_LIST, allEntries = true),
            @CacheEvict(cacheNames = CacheNames.CACHE_PERMISSION, allEntries = true),
            @CacheEvict(cacheNames = CacheNames.CACHE_PERMISSION_LIST, allEntries = true)
    })
    public ResVO disabledRes(Long id) {
        Optional<Res> resOptional = repository.findById(id);
        if (resOptional.isEmpty()) {
            throw new ServiceException("禁用的资源不存在！");
        }
        return resConvert.po2vo(repository.saveAndFlush(resOptional.get().setStatus(Status.DISABLED)));
    }

    private Specification<Res> genSpecification(ResDTO resDTO) {
        return (Root<Res> root, CriteriaQuery<?> query, CriteriaBuilder criteriaBuilder) -> {
            Predicate predicate = criteriaBuilder.conjunction();
            if (StringUtils.hasText(resDTO.getName())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.like(root.get(Res_.name), "%" + resDTO.getName() + "%"));
            }
            if (StringUtils.hasText(resDTO.getCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.like(root.get(Res_.code), "%" + resDTO.getCode() + "%"));
            }
            if (Objects.nonNull(resDTO.getStatus())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get(Res_.status), resDTO.getStatus()));
            }
            if (StringUtils.hasText(resDTO.getDescription())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.like(root.get(Res_.description), "%" + resDTO.getDescription() + "%"));
            }
            query.where(predicate);
            query.orderBy(criteriaBuilder.asc(root.get(Res_.seq)));
            query.orderBy(criteriaBuilder.asc(root.get(Res_.id)));
            return query.getRestriction();
        };
    }

    @Override
    public ResRelation buildRelation(Res parent, Res child, Integer depth) {
        return ResRelation.builder().parent(parent).child(child).depth(depth).build();
    }

    @Override
    public ResVO nodeToVO(Res node) {
        return resConvert.po2vo(node);
    }
}
