package com.genlight.epilcure.service.user.dao.repository;

import com.genlight.epilcure.common.core.dao.enums.Status;
import com.genlight.epilcure.service.user.dao.entity.Dept;
import com.genlight.epilcure.service.user.dao.entity.Org;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 */
public interface DeptRepository extends JpaRepository<Dept, Long>, JpaSpecificationExecutor<Dept> {

    List<Dept> findAllByStatus(Status status);

    boolean existsByOrg(Org org);

    boolean existsByOrgAndName(Org org, String name);

    boolean existsByOrgAndNameAndIdNot(Org org, String name, Long id);
}
