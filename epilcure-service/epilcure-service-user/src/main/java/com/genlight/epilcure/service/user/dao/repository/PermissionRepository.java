package com.genlight.epilcure.service.user.dao.repository;

import com.genlight.epilcure.common.core.dao.enums.Status;
import com.genlight.epilcure.service.user.dao.entity.Action;
import com.genlight.epilcure.service.user.dao.entity.Permission;
import com.genlight.epilcure.service.user.dao.entity.Res;
import jakarta.persistence.QueryHint;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.jpa.repository.QueryHints;

import java.util.List;
import java.util.Optional;

import static org.hibernate.jpa.QueryHints.HINT_CACHEABLE;

/**
 * <AUTHOR>
 * @version 1.0.0
 */
public interface PermissionRepository extends JpaRepository<Permission, Long>, JpaSpecificationExecutor<Permission> {

    List<Permission> findAllByStatus(Status status);

    boolean existsByRes(Res res);

    boolean existsByAction(Action action);

    boolean existsByCode(String code);

    boolean existsByName(String name);

    boolean existsByCodeOrName(String code, String name);

    Optional<Permission> getByCodeOrName(String code, String name);

    @QueryHints(@QueryHint(name = HINT_CACHEABLE, value = "true"))
    @Query("FROM Permission p JOIN p.roles r WHERE p.status = 1 AND r.status = 1 AND p.res.status = 1 AND p.action.status = 1 AND r.id = :id")
    Page<Permission> findPermissionsByRole(Long id, Pageable pageable);

    @QueryHints(@QueryHint(name = HINT_CACHEABLE, value = "true"))
    @Query("FROM Permission p JOIN p.roles r JOIN r.users u WHERE p.status = 1 AND r.status = 1 AND p.res.status = 1 AND p.action.status = 1 AND u.status = 1 AND u.id = :id")
    List<Permission> findPermissionsByUser(Long id);
}
