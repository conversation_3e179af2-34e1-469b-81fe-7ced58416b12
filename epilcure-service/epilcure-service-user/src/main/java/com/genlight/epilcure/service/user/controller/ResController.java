package com.genlight.epilcure.service.user.controller;

import com.fasterxml.jackson.annotation.JsonView;
import com.genlight.epilcure.api.user.pojo.dto.ResDTO;
import com.genlight.epilcure.api.user.pojo.vo.ActionVO;
import com.genlight.epilcure.api.user.pojo.vo.ResVO;
import com.genlight.epilcure.common.core.mvc.response.JsonResult;
import com.genlight.epilcure.common.core.pojo.valid.Add;
import com.genlight.epilcure.common.core.pojo.valid.Update;
import com.genlight.epilcure.common.core.pojo.view.BasicView;
import com.genlight.epilcure.common.core.pojo.view.DetailView;
import com.genlight.epilcure.common.core.pojo.view.TreeView;
import com.genlight.epilcure.service.user.service.ResService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 */
@RestController
@RequestMapping("/api/user/reses")
@Tag(name = "ResController", description = "资源相关接口")
public class ResController {

    @Resource
    private ResService resService;

    @GetMapping("/{id}")
    @JsonView(DetailView.class)
    @Operation(summary = "根据ID查询资源")
    public JsonResult<ResVO> findResById(@PathVariable Long id) {
        return JsonResult.ok(resService.findResById(id));
    }

    @GetMapping("/enabled")
    @JsonView(BasicView.class)
    @Operation(summary = "查询所有启用资源")
    public JsonResult<List<ResVO>> findAllEnabledReses() {
        return JsonResult.ok(resService.findAllEnabledReses());
    }

    @GetMapping("/tree")
    @Operation(summary = "根据动态条件查询资源树")
    @JsonView(TreeView.class)
    public JsonResult<List<ResVO>> findResTree(ResDTO resDTO) {
        return JsonResult.ok(resService.findResTree(resDTO));
    }

    @GetMapping("/tree/users/{id}")
    @Operation(summary = "根据用户查询有权限操作的资源树")
    @JsonView(TreeView.class)
    public JsonResult<List<ResVO>> findResTreeByUser(@PathVariable Long id) {
        return JsonResult.ok(resService.findResTreeByUser(id));
    }

    @GetMapping
    @JsonView(BasicView.class)
    @Operation(summary = "根据动态条件查询资源")
    public JsonResult<Page<ResVO>> findResesByPageable(ResDTO resDTO, Pageable pageable) {
        return JsonResult.ok(resService.findResesByPageable(resDTO, pageable));
    }

    @PostMapping
    @Operation(summary = "新增资源")
    public JsonResult<ResVO> addRes(@Validated(Add.class) @RequestBody ResDTO resDTO) {
        return JsonResult.ok(resService.addRes(resDTO));
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "删除资源")
    public JsonResult<Void> deleteRes(@PathVariable Long id) {
        resService.deleteRes(id);
        return JsonResult.ok();
    }

    @PutMapping("/enabled")
    @Operation(summary = "批量启用资源")
    public JsonResult<Void> enabledReses(@RequestBody List<Long> ids) {
        resService.enabledReses(ids);
        return JsonResult.ok();
    }

    @PutMapping("/disabled")
    @Operation(summary = "批量禁用资源")
    public JsonResult<ActionVO> disabledReses(@RequestBody List<Long> ids) {
        resService.disabledReses(ids);
        return JsonResult.ok();
    }

    @PutMapping("/{id}")
    @Operation(summary = "修改资源")
    public JsonResult<ResVO> updateRes(@PathVariable Long id, @Validated(Update.class) @RequestBody ResDTO resDTO) {
        return JsonResult.ok(resService.updateRes(id, resDTO));
    }

    @PutMapping("/enabled/{id}")
    @Operation(summary = "启用资源")
    public JsonResult<ResVO> enabledRes(@PathVariable Long id) {
        return JsonResult.ok(resService.enabledRes(id));
    }

    @PutMapping("/disabled/{id}")
    @Operation(summary = "禁用资源")
    public JsonResult<ResVO> disabledRes(@PathVariable Long id) {
        return JsonResult.ok(resService.disabledRes(id));
    }
}
