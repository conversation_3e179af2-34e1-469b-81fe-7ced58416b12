package com.genlight.epilcure.service.user.controller;

import com.fasterxml.jackson.annotation.JsonView;
import com.genlight.epilcure.api.user.pojo.dto.DeptDTO;
import com.genlight.epilcure.api.user.pojo.vo.ActionVO;
import com.genlight.epilcure.api.user.pojo.vo.DeptVO;
import com.genlight.epilcure.common.core.mvc.response.JsonResult;
import com.genlight.epilcure.common.core.pojo.valid.Add;
import com.genlight.epilcure.common.core.pojo.valid.Update;
import com.genlight.epilcure.common.core.pojo.view.BasicView;
import com.genlight.epilcure.common.core.pojo.view.DetailView;
import com.genlight.epilcure.common.core.pojo.view.TreeView;
import com.genlight.epilcure.service.user.service.DeptService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 */
@RestController
@RequestMapping("/api/user/depts")
@Tag(name = "DeptController", description = "部门相关接口")
public class DeptController {

    @Resource
    private DeptService deptService;

    @GetMapping("/{id}")
    @JsonView(DetailView.class)
    @Operation(summary = "根据ID查询部门")
    public JsonResult<DeptVO> findDeptById(@PathVariable Long id) {
        return JsonResult.ok(deptService.findDeptById(id));
    }

    @GetMapping("/enabled")
    @JsonView(BasicView.class)
    @Operation(summary = "查询所有启用部门")
    public JsonResult<List<DeptVO>> findAllEnabledDepts() {
        return JsonResult.ok(deptService.findAllEnabledDepts());
    }

    @GetMapping("/tree")
    @JsonView(TreeView.class)
    @Operation(summary = "根据动态条件查询部门树")
    public JsonResult<List<DeptVO>> findDeptTree(DeptDTO resDTO) {
        return JsonResult.ok(deptService.findDeptTree(resDTO));
    }

    @GetMapping
    @JsonView(BasicView.class)
    @Operation(summary = "根据动态条件查询部门")
    public JsonResult<Page<DeptVO>> findDeptsByPageable(DeptDTO resDTO, Pageable pageable) {
        return JsonResult.ok(deptService.findDeptsByPageable(resDTO, pageable));
    }

    @PostMapping
    @Operation(summary = "新增部门")
    public JsonResult<DeptVO> addDept(@Validated(Add.class) @RequestBody DeptDTO resDTO) {
        return JsonResult.ok(deptService.addDept(resDTO));
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "删除部门")
    public JsonResult<Void> deleteDept(@PathVariable Long id) {
        deptService.deleteDept(id);
        return JsonResult.ok();
    }

    @PutMapping("/{id}")
    @Operation(summary = "修改部门")
    public JsonResult<DeptVO> updateDept(@PathVariable Long id, @Validated(Update.class) @RequestBody DeptDTO resDTO) {
        return JsonResult.ok(deptService.updateDept(id, resDTO));
    }

    @PutMapping("/enabled")
    @Operation(summary = "批量启用部门")
    public JsonResult<Void> enabledDepts(@RequestBody List<Long> ids) {
        deptService.enabledDepts(ids);
        return JsonResult.ok();
    }

    @PutMapping("/disabled")
    @Operation(summary = "批量禁用部门")
    public JsonResult<ActionVO> disabledDepts(@RequestBody List<Long> ids) {
        deptService.disabledDepts(ids);
        return JsonResult.ok();
    }

    @PutMapping("/enabled/{id}")
    @Operation(summary = "启用部门")
    public JsonResult<DeptVO> enabledDept(@PathVariable Long id) {
        return JsonResult.ok(deptService.enabledDept(id));
    }

    @PutMapping("/disabled/{id}")
    @Operation(summary = "禁用部门")
    public JsonResult<DeptVO> disabledDept(@PathVariable Long id) {
        return JsonResult.ok(deptService.disabledDept(id));
    }
}
