package com.genlight.epilcure.service.user.feign;

import com.genlight.epilcure.api.user.feign.IUserController;
import com.genlight.epilcure.api.user.pojo.bo.LoginBO;
import com.genlight.epilcure.api.user.pojo.vo.UserVO;
import com.genlight.epilcure.service.user.service.UserService;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @version 1.0.0
 */
@RequestMapping
@RestController(value = "feignUserController")
public class UserController implements IUserController {

    @Resource
    private UserService userService;

    @Override
    public UserVO loginByMobile(String mobile) {
        return userService.loginByMobile(mobile);
    }

    @Override
    public void loginSuccess(LoginBO login) {
        userService.loginSuccess(login);
    }

    @Override
    public UserVO findById(Long id) {
        return userService.findUserById(id);
    }
}
