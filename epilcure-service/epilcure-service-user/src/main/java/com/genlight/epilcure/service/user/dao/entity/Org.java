package com.genlight.epilcure.service.user.dao.entity;

import com.genlight.epilcure.common.core.dao.entity.Address;
import com.genlight.epilcure.common.core.dao.entity.TTree;
import com.genlight.epilcure.common.core.dao.enums.Status;
import com.genlight.epilcure.common.core.dao.generator.annotation.SignOrder;
import jakarta.persistence.*;
import lombok.*;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;
import org.hibernate.annotations.Comment;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import java.util.HashSet;
import java.util.Set;

/**
 * <AUTHOR>
 * @version 1.0.0
 */
@Getter
@Setter
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString
@Entity
@Cacheable
@DynamicInsert
@DynamicUpdate
@SignOrder(0)
@Table(name = "t_org", indexes = {
        @Index(name = "index_org_status", columnList = "status")
})
public class Org extends TTree<Org, OrgRelation> {

    @Comment("名称")
    @Column(nullable = false, unique = true, length = 32)
    @SignOrder(3)
    private String name;

    @Comment("电话")
    @Column(length = 16)
    @SignOrder(4)
    private String phone;

    @Comment("徽标")
    @Column(nullable = false, length = 64)
    @SignOrder(5)
    private String logo;

    @Comment("状态（0：禁用，1：启用）")
    @Column(nullable = false)
    @Enumerated(EnumType.ORDINAL)
    @SignOrder(6)
    private Status status;

    @Comment("描述信息")
    @Column(length = 1024)
    @SignOrder(7)
    private String description;

    @Comment("医院编码")
    private String customCode;

    @ToString.Exclude
    @OneToOne(optional = false, cascade = CascadeType.ALL)
    private Address address;

    @ToString.Exclude
    @Builder.Default
    @OneToMany(mappedBy = "org")
    private Set<User> users = new HashSet<>();

    @Builder.Default
    @ToString.Exclude
    @OneToMany(mappedBy = "org")
    private Set<Dept> depts = new HashSet<>();
}
