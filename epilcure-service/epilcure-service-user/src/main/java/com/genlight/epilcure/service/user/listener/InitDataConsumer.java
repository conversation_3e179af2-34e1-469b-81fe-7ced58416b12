package com.genlight.epilcure.service.user.listener;

import com.genlight.epilcure.common.core.dao.entity.Address;
import com.genlight.epilcure.common.core.dao.enums.Gender;
import com.genlight.epilcure.common.core.dao.enums.Status;
import com.genlight.epilcure.common.rocketmq.RocketMqConstant;
import com.genlight.epilcure.service.user.constants.Constants;
import com.genlight.epilcure.service.user.dao.entity.*;
import com.genlight.epilcure.service.user.service.*;
import org.apache.rocketmq.spring.annotation.MessageModel;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Component;

import java.util.*;

@Component
@RocketMQMessageListener(
        topic = RocketMqConstant.INIT_TOPIC,
        consumerGroup = RocketMqConstant.INIT_TOPIC,
        messageModel = MessageModel.CLUSTERING)
public class InitDataConsumer implements RocketMQListener<Map<String, Object>> {

    @Autowired
    private ResService resService;

    @Autowired
    private ActionService actionService;

    @Autowired
    private RoleService roleService;

    @Autowired
    private OrgService orgService;

    @Autowired
    private DeptService deptService;

    @Autowired
    private UserService userService;

    @Autowired
    private PermissionService permissionService;

    @Autowired
    private PasswordEncoder passwordEncoder;

    private boolean isInitAction = false;

    private final Map<String, Action> actions = new HashMap<>();

    public void initAction() {
        actions.put("GET", actionService.addOrGetAction(Action.builder().status(Status.ENABLED).code("GET").name("查询操作").description("查询相关操作").build()));
        actions.put("PUT", actionService.addOrGetAction(Action.builder().status(Status.ENABLED).code("PUT").name("更新操作").description("更新相关操作").build()));
        actions.put("POST", actionService.addOrGetAction(Action.builder().status(Status.ENABLED).code("POST").name("新增操作").description("新增相关操作").build()));
        actions.put("DELETE", actionService.addOrGetAction(Action.builder().status(Status.ENABLED).code("DELETE").name("删除操作").description("删除相关操作").build()));

        Set<Permission> permissions = addRes(Res.builder()
                .seq(1)
                .name("All")
                .code("/api/**")
                .description("任意接口资源")
                .status(Status.ENABLED)
                .build(), "*");

        if (!permissions.isEmpty()) {
            Role role = roleService.addRole(Role.builder()
                    .code("Admin")
                    .name("超级管理员")
                    .description("拥有所有权限")
                    .status(Status.ENABLED)
                    .level(1)
                    .permissions(permissions)
                    .build());

            Org org = orgService.addOrg(Org.builder()
                    .id(Constants.ROOT_ORG_ID)
                    .seq(1)
                    .name("诺为医疗")
                    .phone("phone")
                    .logo("logo")
                    .status(Status.ENABLED)
                    .description("系统初始化默认生成数据")
                    .address(Address.builder()
                            .country("中国")
                            .province("浙江省")
                            .city("杭州市")
                            .postcode(330100)
                            .latitude(30.24692)
                            .longitude(120.209789)
                            .build())
                    .build());

            Dept dept = deptService.addDept(Dept.builder()
                    .seq(1)
                    .name("云平台")
                    .phone("phone")
                    .status(Status.ENABLED)
                    .description("系统初始化默认生成数据")
                    .org(org)
                    .build());

            userService.addUser(User.builder()
                    .disable(false)
                    .name("超级管理员")
                    .mobile("13533588158")
                    .password(passwordEncoder.encode("123qwe"))
                    .gender(Gender.MALE)
                    .status(Status.ENABLED)
                    .description("系统初始化默认生成数据")
                    .org(org)
                    .dept(dept)
                    .roles(Set.of(role))
                    .build());

        }
        isInitAction = true;
    }

    @Override
    public synchronized void onMessage(Map<String, Object> message) {

        if (!isInitAction) {
            initAction();
        }

        addRes(Res.builder()
                .seq((Integer) message.get("seq"))
                .name((String) message.get("name"))
                .code((String) message.get("code"))
                .description((String) message.get("description"))
                .status(Status.ENABLED)
                .build(), (String) message.get("method"));
    }

    public Set<Permission> addRes(Res res, String methods) {
        res = resService.addOrUpdateRes(res);
        Set<Permission> permissions = new HashSet<>();
        for (Action action : getAction(methods)) {
            Permission permission = permissionService.addOrUpdatePermission(Permission.builder()
                    .name(res.getName() + "(" + action.getCode() + ")")
                    .code(res.getCode() + "." + action.getCode())
                    .status(Status.ENABLED)
                    .action(action)
                    .res(res)
                    .description(res.getDescription() + "(" + action.getCode() + ")")
                    .build());
            if (Objects.nonNull(permission)) {
                permissions.add(permission);
            }
        }
        return permissions;
    }

    public Collection<Action> getAction(String action) {
        if ("*".equals(action)) {
            return actions.values();
        } else {
            return Set.of(actions.get(action));
        }
    }
}
