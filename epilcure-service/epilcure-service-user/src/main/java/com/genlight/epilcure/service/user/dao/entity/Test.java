package com.genlight.epilcure.service.user.dao.entity;

import com.genlight.epilcure.common.core.dao.entity.TEntity;
import com.genlight.epilcure.common.core.dao.enums.Status;
import com.genlight.epilcure.common.core.dao.generator.annotation.SignOrder;
import jakarta.persistence.*;
import lombok.*;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;
import org.hibernate.annotations.Comment;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import java.util.HashSet;
import java.util.Set;

/**
 * <AUTHOR>
 * @version 1.0.0
 */
@Setter
@Getter
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString
@Entity
@Cacheable
@DynamicInsert
@DynamicUpdate
@SignOrder(0)
@Table(name = "t_test", indexes = {
        @Index(name = "index_test_name", columnList = "name"),
        @Index(name = "index_test_status", columnList = "status")
})
public class Test extends TEntity {

    @Comment("测试名单名称")
    @Column(nullable = false, length = 32)
    @SignOrder(1)
    private String name;

    @Comment("测试名单所属项目")
    @Column(nullable = false)
    @SignOrder(2)
    private String projects;

    @Comment("测试名单状态（0：禁用，1：启用，3：删除）")
    @Column(nullable = false)
    @Enumerated(EnumType.ORDINAL)
    @SignOrder(3)
    private Status status;

    @Comment("测试名单描述信息")
    @Column(length = 1024)
    @SignOrder(4)
    private String description;

    @Builder.Default
    @ToString.Exclude
    @ManyToMany
    private Set<User> users = new HashSet<>();
}
