package com.genlight.epilcure.service.user.pojo.convert;

import com.genlight.epilcure.api.user.pojo.dto.ResDTO;
import com.genlight.epilcure.api.user.pojo.vo.ResVO;
import com.genlight.epilcure.common.core.pojo.convert.BaseConvert;
import com.genlight.epilcure.common.core.pojo.convert.qualified.Basic;
import com.genlight.epilcure.service.user.dao.entity.Res;
import org.mapstruct.*;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 */
@Mapper(config = BaseConvert.class)
public interface ResConvert extends BaseConvert<Res, ResVO, ResDTO> {

    @Mapping(target = "seq", expression = "java(java.util.Optional.ofNullable(dto.getSeq()).orElse(0))")
    @Mapping(target = "status", expression = "java(java.util.Optional.ofNullable(dto.getStatus()).orElse(com.genlight.epilcure.common.core.dao.enums.Status.ENABLED))")
    Res dto2po(ResDTO dto);

    @Basic
    @Mapping(target = "parent", ignore = true)
    @BeanMapping(nullValueCheckStrategy = NullValueCheckStrategy.ON_IMPLICIT_CONVERSION)
    ResVO po2vo(Res res);

    @Mapping(target = "parent", qualifiedBy = Basic.class)
    @BeanMapping(nullValueCheckStrategy = NullValueCheckStrategy.ON_IMPLICIT_CONVERSION)
    ResVO po2voByDetail(Res res);

    @IterableMapping(qualifiedBy = Basic.class)
    List<ResVO> po2vo(List<Res> pos);
}