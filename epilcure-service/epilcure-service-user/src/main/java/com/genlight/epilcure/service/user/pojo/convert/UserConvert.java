package com.genlight.epilcure.service.user.pojo.convert;

import com.genlight.epilcure.api.user.pojo.dto.UserDTO;
import com.genlight.epilcure.api.user.pojo.vo.UserVO;
import com.genlight.epilcure.common.core.pojo.convert.BaseConvert;
import com.genlight.epilcure.common.core.pojo.convert.qualified.Basic;
import com.genlight.epilcure.common.core.util.RSAUtils;
import com.genlight.epilcure.service.user.dao.entity.User;
import org.mapstruct.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.password.PasswordEncoder;

/**
 * <AUTHOR>
 * @version 1.0.0
 */
@Mapper(config = BaseConvert.class, uses = {DeptConvert.class, OrgConvert.class})
public abstract class UserConvert implements BaseConvert<User, UserVO, UserDTO> {

    @Autowired
    protected RSAUtils rsaUtils;

    @Autowired
    protected PasswordEncoder passwordEncoder;

    @Mapping(target = "password", expression = "java(java.util.Optional.ofNullable(dto.getPassword()).map(p-> passwordEncoder.encode(rsaUtils.decryptPassword(p))).orElse(null))")
    @Mapping(target = "status", expression = "java(java.util.Optional.ofNullable(dto.getStatus()).orElse(com.genlight.epilcure.common.core.dao.enums.Status.ENABLED))")
    @Mapping(target = "disable", expression = "java(java.util.Optional.ofNullable(dto.getDisable()).orElse(true))")
    public abstract User dto2po(UserDTO dto);

    @Mapping(target = "password", expression = "java(java.util.Optional.ofNullable(dto.getPassword()).map(p-> passwordEncoder.encode(rsaUtils.decryptPassword(p))).orElse(po.getPassword()))")
    public abstract User dto2po(UserDTO dto, @MappingTarget User po);

    @Mapping(target = "password", ignore = true)
    @Mapping(target = "org", qualifiedBy = Basic.class)
    @Mapping(target = "dept", qualifiedBy = Basic.class)
    @BeanMapping(nullValueCheckStrategy = NullValueCheckStrategy.ON_IMPLICIT_CONVERSION)
    public abstract UserVO po2vo(User po);
}
