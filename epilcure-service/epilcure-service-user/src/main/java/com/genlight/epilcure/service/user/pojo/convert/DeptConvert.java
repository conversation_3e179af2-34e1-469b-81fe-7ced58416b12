package com.genlight.epilcure.service.user.pojo.convert;

import com.genlight.epilcure.api.user.pojo.dto.DeptDTO;
import com.genlight.epilcure.api.user.pojo.vo.DeptVO;
import com.genlight.epilcure.common.core.pojo.convert.BaseConvert;
import com.genlight.epilcure.common.core.pojo.convert.qualified.Basic;
import com.genlight.epilcure.service.user.dao.entity.Dept;
import org.mapstruct.*;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 */
@Named("DeptConvert")
@Mapper(config = BaseConvert.class, uses = OrgConvert.class)
public interface DeptConvert extends BaseConvert<Dept, DeptVO, DeptDTO> {

    @Mapping(target = "seq", expression = "java(java.util.Optional.ofNullable(dto.getSeq()).orElse(0))")
    @Mapping(target = "status", expression = "java(java.util.Optional.ofNullable(dto.getStatus()).orElse(com.genlight.epilcure.common.core.dao.enums.Status.ENABLED))")
    Dept dto2po(DeptDTO dto);

    @Basic
    @Mapping(target = "org", ignore = true)
    @Mapping(target = "parent", ignore = true)
    @BeanMapping(nullValueCheckStrategy = NullValueCheckStrategy.ON_IMPLICIT_CONVERSION)
    DeptVO po2vo(Dept dept);

    @Mapping(target = "parent", qualifiedBy = Basic.class)
    @BeanMapping(nullValueCheckStrategy = NullValueCheckStrategy.ON_IMPLICIT_CONVERSION)
    DeptVO po2voByDetail(Dept dept);

    @IterableMapping(qualifiedBy = Basic.class)
    List<DeptVO> po2vo(List<Dept> depts);
}

