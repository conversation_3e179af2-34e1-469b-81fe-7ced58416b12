package com.genlight.epilcure.service.user.pojo.convert;

import com.genlight.epilcure.api.user.pojo.dto.RoleDTO;
import com.genlight.epilcure.api.user.pojo.vo.RoleVO;
import com.genlight.epilcure.common.core.pojo.convert.BaseConvert;
import com.genlight.epilcure.service.user.dao.entity.Role;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

/**
 * <AUTHOR>
 * @version 1.0.0
 */
@Mapper(config = BaseConvert.class)
public interface RoleConvert extends BaseConvert<Role, RoleVO, RoleDTO> {

    @Mapping(target = "level", expression = "java(java.util.Optional.ofNullable(dto.getLevel()).orElse(0))")
    @Mapping(target = "status", expression = "java(java.util.Optional.ofNullable(dto.getStatus()).orElse(com.genlight.epilcure.common.core.dao.enums.Status.ENABLED))")
    Role dto2po(RoleDTO dto);
}

