package com.genlight.epilcure.service.user.controller;

import com.genlight.epilcure.api.user.pojo.dto.UserDTO;
import com.genlight.epilcure.api.user.pojo.vo.UserVO;
import com.genlight.epilcure.common.core.constants.RegexConstants;
import com.genlight.epilcure.common.core.mvc.response.JsonResult;
import com.genlight.epilcure.common.core.pojo.valid.Add;
import com.genlight.epilcure.common.core.pojo.valid.Update;
import com.genlight.epilcure.service.user.service.UserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.constraints.Pattern;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 */
@RestController
@RequestMapping("/api/user/users")
@Tag(name = "UserController", description = "用户相关接口")
public class UserController {

    @Resource
    private UserService userService;

    @GetMapping("/{id}")
    @Operation(summary = "根据ID查询用户")
    public JsonResult<UserVO> findUserById(@PathVariable Long id) {
        return JsonResult.ok(userService.findUserById(id));
    }

    @GetMapping
    @Operation(summary = "根据动态条件查询用户")
    public JsonResult<Page<UserVO>> findUsersByPageable(UserDTO userDTO, Pageable pageable) {
        return JsonResult.ok(userService.findUsersByPageable(userDTO, pageable));
    }

    @GetMapping("/roles/{id}")
    @Operation(summary = "查询指定角色下的所有用户")
    public JsonResult<Page<UserVO>> findUsersByRole(@PathVariable Long id, UserDTO userDTO, Pageable pageable) {
        return JsonResult.ok(userService.findUsersByPageable(userDTO.setRoleId(id).setRoleEquals(true), pageable));
    }

    @GetMapping("/roles/not/{id}")
    @Operation(summary = "查询不包含指定角色的所有用户")
    public JsonResult<Page<UserVO>> findUsersByNotRole(@PathVariable Long id, UserDTO userDTO, Pageable pageable) {
        return JsonResult.ok(userService.findUsersByPageable(userDTO.setRoleId(id).setRoleEquals(false), pageable));
    }

    @GetMapping("/tests/{id}")
    @Operation(summary = "查询指定测试名单下的所有用户")
    public JsonResult<Page<UserVO>> findUsersByTest(@PathVariable Long id, UserDTO userDTO, Pageable pageable) {
        return JsonResult.ok(userService.findUsersByPageable(userDTO.setTestId(id).setTestEquals(true), pageable));
    }

    @GetMapping("/tests/not/{id}")
    @Operation(summary = "查询不包含指定测试名单的所有用户")
    public JsonResult<Page<UserVO>> findUsersByNotTest(@PathVariable Long id, UserDTO userDTO, Pageable pageable) {
        return JsonResult.ok(userService.findUsersByPageable(userDTO.setTestId(id).setTestEquals(false), pageable));
    }

    @PostMapping
    @Operation(summary = "新增用户")
    public JsonResult<UserVO> addUser(@Validated(Add.class) @RequestBody UserDTO userDTO) {
        return JsonResult.ok(userService.addUser(userDTO));
    }

    @PostMapping("/register")
    @Operation(summary = "注册用户")
    public JsonResult<UserVO> registerUser(@Validated(Add.class) @RequestBody UserDTO userDTO) {
        return JsonResult.ok(userService.registerUser(userDTO));
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "删除用户")
    public JsonResult<Void> deleteUser(@PathVariable Long id) {
        userService.deleteUser(id);
        return JsonResult.ok();
    }

    @PutMapping("/{id}")
    @Operation(summary = "修改用户")
    public JsonResult<UserVO> updateUser(@PathVariable Long id, @Validated(Update.class) @RequestBody UserDTO userDTO) {
        return JsonResult.ok(userService.updateUser(id, userDTO));
    }

    @PutMapping
    @Operation(summary = "修改用户（自己）")
    public JsonResult<UserVO> updateUserBySelf(@Validated(Update.class) @RequestBody UserDTO userDTO) {
        return JsonResult.ok(userService.updateUserBySelf(userDTO));
    }

    @PutMapping("/enabled")
    @Operation(summary = "批量启用用户")
    public JsonResult<Void> enabledUsers(@RequestBody List<Long> ids) {
        userService.enabledUsers(ids);
        return JsonResult.ok();
    }

    @PutMapping("/disabled")
    @Operation(summary = "批量禁用用户")
    public JsonResult<UserVO> disabledUsers(@RequestBody List<Long> ids) {
        userService.disabledUsers(ids);
        return JsonResult.ok();
    }

    @PutMapping("/enabled/{id}")
    @Operation(summary = "启用用户")
    public JsonResult<UserVO> enabledUser(@PathVariable Long id) {
        return JsonResult.ok(userService.enabledUser(id));
    }

    @PutMapping("/disabled/{id}")
    @Operation(summary = "禁用用户")
    public JsonResult<UserVO> disabledUser(@PathVariable Long id) {
        return JsonResult.ok(userService.disabledUser(id));
    }

    @PutMapping("/{id}/roles/associated")
    @Operation(summary = "关联角色")
    public JsonResult<Void> associatedRoles(@PathVariable Long id, @RequestBody List<Long> roleIds) {
        userService.associatedRoles(id, roleIds);
        return JsonResult.ok();
    }

    @PutMapping("/{id}/roles/dissociated")
    @Operation(summary = "解除角色关联")
    public JsonResult<Void> dissociatedRoles(@PathVariable Long id, @RequestBody List<Long> roleIds) {
        userService.dissociatedRoles(id, roleIds);
        return JsonResult.ok();
    }

    @PutMapping("/captcha/{mobile}")
    @Operation(summary = "获取验证码")
    public JsonResult<String> getCaptcha(@Validated @Pattern(regexp = RegexConstants.REGEX_MOBILE, message = "用户手机号不合法") @PathVariable String mobile) {
        return JsonResult.ok(userService.getCaptcha(mobile));
    }

    @PutMapping("/password")
    @Operation(summary = "通过验证码修改密码")
    public JsonResult<Void> changePassword(@RequestBody UserDTO userDTO) {
        userService.changePassword(userDTO);
        return JsonResult.ok();
    }
}
