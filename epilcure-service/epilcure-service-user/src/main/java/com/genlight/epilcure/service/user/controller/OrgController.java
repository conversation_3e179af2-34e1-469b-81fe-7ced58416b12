package com.genlight.epilcure.service.user.controller;

import com.fasterxml.jackson.annotation.JsonView;
import com.genlight.epilcure.api.user.pojo.dto.OrgDTO;
import com.genlight.epilcure.api.user.pojo.vo.ActionVO;
import com.genlight.epilcure.api.user.pojo.vo.OrgVO;
import com.genlight.epilcure.common.core.mvc.response.JsonResult;
import com.genlight.epilcure.common.core.pojo.valid.Add;
import com.genlight.epilcure.common.core.pojo.valid.Update;
import com.genlight.epilcure.common.core.pojo.view.BasicView;
import com.genlight.epilcure.common.core.pojo.view.DetailView;
import com.genlight.epilcure.common.core.pojo.view.TreeView;
import com.genlight.epilcure.service.user.service.OrgService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 */
@RestController
@RequestMapping("/api/user/orges")
@Tag(name = "OrgController", description = "组织相关接口")
public class OrgController {

    @Resource
    private OrgService orgService;

    @GetMapping("/{id}")
    @JsonView(DetailView.class)
    @Operation(summary = "根据ID查询组织")
    public JsonResult<OrgVO> findOrgById(@PathVariable Long id) {
        return JsonResult.ok(orgService.findOrgById(id));
    }

    @GetMapping("/enabled")
    @JsonView(BasicView.class)
    @Operation(summary = "查询所有启用组织")
    public JsonResult<List<OrgVO>> findAllEnabledOrges() {
        return JsonResult.ok(orgService.findAllEnabledOrges());
    }

    @GetMapping("/tree")
    @JsonView(TreeView.class)
    @Operation(summary = "根据动态条件查询组织树（含部门）")
    public JsonResult<List<OrgVO>> findOrgTree(OrgDTO orgDTO) {
        return JsonResult.ok(orgService.findOrgTree(orgDTO));
    }

    @GetMapping("/list")
    @JsonView(TreeView.class)
    @Operation(summary = "根据动态条件查询组织列表（含部门）,搜索部门指定deptName")
    public JsonResult<Page<OrgVO>> findOrgesContainDeptByPageable(OrgDTO orgDTO, Pageable pageable) {
        return JsonResult.ok(orgService.findOrgesContainDeptByPageable(orgDTO, pageable));
    }

    @GetMapping
    @JsonView(BasicView.class)
    @Operation(summary = "根据动态条件查询组织")
    public JsonResult<Page<OrgVO>> findOrgesByPageable(OrgDTO orgDTO, Pageable pageable) {
        return JsonResult.ok(orgService.findOrgesByPageable(orgDTO, pageable));
    }

    @PostMapping(consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @Operation(summary = "新增组织")
    public JsonResult<OrgVO> addOrg(@Validated(Add.class) @RequestPart OrgDTO orgDTO, @RequestPart MultipartFile logo) {
        return JsonResult.ok(orgService.addOrg(orgDTO, logo));
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "删除组织")
    public JsonResult<Void> deleteOrg(@PathVariable Long id) {
        orgService.deleteOrg(id);
        return JsonResult.ok();
    }

    @PutMapping(value = "/{id}", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @Operation(summary = "修改组织")
    public JsonResult<OrgVO> updateOrg(@PathVariable Long id, @Validated(Update.class) @RequestPart OrgDTO orgDTO, @RequestPart(required = false) MultipartFile logo) {
        return JsonResult.ok(orgService.updateOrg(id, orgDTO, logo));
    }

    @PutMapping("/enabled")
    @Operation(summary = "批量启用组织")
    public JsonResult<Void> enabledOrges(@RequestBody List<Long> ids) {
        orgService.enabledOrges(ids);
        return JsonResult.ok();
    }

    @PutMapping("/disabled")
    @Operation(summary = "批量禁用组织")
    public JsonResult<ActionVO> disabledOrges(@RequestBody List<Long> ids) {
        orgService.disabledOrges(ids);
        return JsonResult.ok();
    }

    @PutMapping("/enabled/{id}")
    @Operation(summary = "启用组织")
    public JsonResult<OrgVO> enabledOrg(@PathVariable Long id) {
        return JsonResult.ok(orgService.enabledOrg(id));
    }

    @PutMapping("/disabled/{id}")
    @Operation(summary = "禁用组织")
    public JsonResult<OrgVO> disabledOrg(@PathVariable Long id) {
        return JsonResult.ok(orgService.disabledOrg(id));
    }
}
