package com.genlight.epilcure.service.user.dao.entity;

import com.genlight.epilcure.common.core.dao.entity.TEntity;
import com.genlight.epilcure.common.core.dao.enums.Status;
import com.genlight.epilcure.common.core.dao.generator.annotation.SignOrder;
import jakarta.persistence.*;
import lombok.*;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;
import org.hibernate.annotations.Comment;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

/**
 * <AUTHOR>
 * @version 1.0.0
 */
@Setter
@Getter
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString
@Entity
@Cacheable
@DynamicInsert
@DynamicUpdate
@SignOrder(0)
@Table(name = "t_register", indexes = {
        @Index(name = "index_register_name", columnList = "name"),
        @Index(name = "index_register_status", columnList = "status")
})
public class Register extends TEntity {

    @Comment("名字")
    @Column(nullable = false, length = 32)
    @SignOrder(1)
    private String name;

    @Comment("手机号")
    @Column(nullable = false, unique = true, length = 16)
    @SignOrder(2)
    private String mobile;

    @Comment("所属项目")
    @Column(nullable = false, length = 2048)
    @SignOrder(3)
    private String projects;

    @Comment("状态（0：禁用，1：启用，3：删除）")
    @Column(nullable = false)
    @Enumerated(EnumType.ORDINAL)
    @SignOrder(4)
    private Status status;

    @Comment("描述信息")
    @Column(length = 1024)
    @SignOrder(5)
    private String description;

    @ToString.Exclude
    @ManyToOne(optional = false)
    private User operator;

    @ToString.Exclude
    @ManyToOne(optional = false)
    private Org org;

    @ToString.Exclude
    @ManyToOne(optional = false)
    private Dept dept;

    @ToString.Exclude
    @ManyToOne(optional = false)
    private Role role;
}
