package com.genlight.epilcure.service.user.dao.repository;

import com.genlight.epilcure.common.core.dao.enums.Status;
import com.genlight.epilcure.service.user.dao.entity.Action;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @version 1.0.0
 */
public interface ActionRepository extends JpaRepository<Action, Long>, JpaSpecificationExecutor<Action> {

    Optional<Action> findByCode(String code);

    List<Action> findAllByStatus(Status status);

    boolean existsByName(String name);

    boolean existsByCode(String code);

    boolean existsByCodeOrName(String code, String name);
}
