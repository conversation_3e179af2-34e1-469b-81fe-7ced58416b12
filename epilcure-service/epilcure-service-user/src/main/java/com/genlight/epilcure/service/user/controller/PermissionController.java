package com.genlight.epilcure.service.user.controller;

import com.genlight.epilcure.api.user.pojo.dto.PermissionDTO;
import com.genlight.epilcure.api.user.pojo.vo.PermissionVO;
import com.genlight.epilcure.common.core.mvc.response.JsonResult;
import com.genlight.epilcure.common.core.pojo.valid.Add;
import com.genlight.epilcure.common.core.pojo.valid.Update;
import com.genlight.epilcure.service.user.service.PermissionService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 */
@RestController
@RequestMapping("/api/user/permissions")
@Tag(name = "PermissionController", description = "权限相关接口")
public class PermissionController {

    @Resource
    private PermissionService permissionService;

    @GetMapping("/{id}")
    @Operation(summary = "根据ID查询权限")
    public JsonResult<PermissionVO> findPermissionById(@PathVariable Long id) {
        return JsonResult.ok(permissionService.findPermissionById(id));
    }

    @GetMapping("/enabled")
    @Operation(summary = "查询所有启用权限")
    public JsonResult<List<PermissionVO>> findAllEnabledPermissions() {
        return JsonResult.ok(permissionService.findAllEnabledPermissions());
    }

    @GetMapping("/roles/{id}")
    @Operation(summary = "查询指定角色拥有的权限")
    public JsonResult<Page<PermissionVO>> findHasPermissionsByRole(@PathVariable Long id, PermissionDTO permissionDTO, Pageable pageable) {
        return JsonResult.ok(permissionService.findPermissionsByPageable(permissionDTO.setRoleId(id).setRoleEquals(true), pageable));
    }

    @GetMapping("/roles/not/{id}")
    @Operation(summary = "查询指定角色未拥有的权限")
    public JsonResult<Page<PermissionVO>> findHasNotPermissionsByRole(@PathVariable Long id, PermissionDTO permissionDTO, Pageable pageable) {
        return JsonResult.ok(permissionService.findPermissionsByPageable(permissionDTO.setRoleId(id).setRoleEquals(false), pageable));
    }

    @GetMapping
    @Operation(summary = "根据动态条件查询权限")
    public JsonResult<Page<PermissionVO>> findPermissionsByPageable(PermissionDTO permissionDTO, Pageable pageable) {
        return JsonResult.ok(permissionService.findPermissionsByPageable(permissionDTO, pageable));
    }

    @PostMapping
    @Operation(summary = "新增权限")
    public JsonResult<PermissionVO> addPermission(@Validated(Add.class) @RequestBody PermissionDTO permissionDTO) {
        return JsonResult.ok(permissionService.addPermission(permissionDTO));
    }

    @DeleteMapping
    @Operation(summary = "批量删除权限")
    public JsonResult<Void> deletePermissions(@RequestBody List<Long> ids) {
        permissionService.deletePermissions(ids);
        return JsonResult.ok();
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "删除权限")
    public JsonResult<Void> deletePermission(@PathVariable Long id) {
        permissionService.deletePermission(id);
        return JsonResult.ok();
    }

    @PutMapping("/{id}")
    @Operation(summary = "修改权限")
    public JsonResult<PermissionVO> updatePermission(@PathVariable Long id, @Validated(Update.class) @RequestBody PermissionDTO permissionDTO) {
        return JsonResult.ok(permissionService.updatePermission(id, permissionDTO));
    }

    @PutMapping("/enabled")
    @Operation(summary = "批量启用权限")
    public JsonResult<Void> enabledPermissions(@RequestBody List<Long> ids) {
        permissionService.enabledPermissions(ids);
        return JsonResult.ok();
    }

    @PutMapping("/disabled")
    @Operation(summary = "批量禁用权限")
    public JsonResult<PermissionVO> disabledPermissions(@RequestBody List<Long> ids) {
        permissionService.disabledPermissions(ids);
        return JsonResult.ok();
    }

    @PutMapping("/enabled/{id}")
    @Operation(summary = "启用权限")
    public JsonResult<PermissionVO> enabledPermission(@PathVariable Long id) {
        return JsonResult.ok(permissionService.enabledPermission(id));
    }

    @PutMapping("/disabled/{id}")
    @Operation(summary = "禁用权限")
    public JsonResult<PermissionVO> disabledPermission(@PathVariable Long id) {
        return JsonResult.ok(permissionService.disabledPermission(id));
    }

    @PutMapping("/{id}/roles/associated")
    @Operation(summary = "关联角色")
    public JsonResult<Void> associatedRoles(@PathVariable Long id, @RequestBody List<Long> roleIds) {
        permissionService.associatedRoles(id, roleIds);
        return JsonResult.ok();
    }

    @PutMapping("/{id}/roles/dissociated")
    @Operation(summary = "解除角色关联")
    public JsonResult<Void> dissociatedRoles(@PathVariable Long id, @RequestBody List<Long> roleIds) {
        permissionService.dissociatedRoles(id, roleIds);
        return JsonResult.ok();
    }
}
