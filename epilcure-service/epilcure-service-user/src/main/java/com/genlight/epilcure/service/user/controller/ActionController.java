package com.genlight.epilcure.service.user.controller;

import com.genlight.epilcure.api.user.pojo.dto.ActionDTO;
import com.genlight.epilcure.api.user.pojo.vo.ActionVO;
import com.genlight.epilcure.common.core.mvc.response.JsonResult;
import com.genlight.epilcure.common.core.pojo.valid.Add;
import com.genlight.epilcure.common.core.pojo.valid.Update;
import com.genlight.epilcure.service.user.service.ActionService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 */
@RestController
@RequestMapping("/api/user/actions")
@Tag(name = "ActionController", description = "动作相关接口")
public class ActionController {

    @Resource
    private ActionService actionService;

    @GetMapping("/{id}")
    @Operation(summary = "根据ID查询动作")
    public JsonResult<ActionVO> findActionById(@PathVariable Long id) {
        return JsonResult.ok(actionService.findActionById(id));
    }

    @GetMapping("/enabled")
    @Operation(summary = "查询所有启用动作")
    public JsonResult<List<ActionVO>> findAllEnabledActions() {
        return JsonResult.ok(actionService.findAllEnabledActions());
    }

    @GetMapping
    @Operation(summary = "根据动态条件查询动作")
    public JsonResult<Page<ActionVO>> findActionsByPageable(ActionDTO actionDTO, Pageable pageable) {
        return JsonResult.ok(actionService.findActionsByPageable(actionDTO, pageable));
    }

    @PostMapping
    @Operation(summary = "新增动作")
    public JsonResult<ActionVO> addAction(@Validated(Add.class) @RequestBody ActionDTO actionDTO) {
        return JsonResult.ok(actionService.addAction(actionDTO));
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "删除动作")
    public JsonResult<Void> deleteAction(@PathVariable Long id) {
        actionService.deleteAction(id);
        return JsonResult.ok();
    }

    @PutMapping("/{id}")
    @Operation(summary = "修改动作")
    public JsonResult<ActionVO> updateAction(@PathVariable Long id, @Validated(Update.class) @RequestBody ActionDTO actionDTO) {
        return JsonResult.ok(actionService.updateAction(id, actionDTO));
    }

    @PutMapping("/enabled")
    @Operation(summary = "批量启用动作")
    public JsonResult<Void> enabledActions(@RequestBody List<Long> ids) {
        actionService.enabledActions(ids);
        return JsonResult.ok();
    }

    @PutMapping("/disabled")
    @Operation(summary = "批量禁用动作")
    public JsonResult<ActionVO> disabledActions(@RequestBody List<Long> ids) {
        actionService.disabledActions(ids);
        return JsonResult.ok();
    }

    @PutMapping("/enabled/{id}")
    @Operation(summary = "启用动作")
    public JsonResult<ActionVO> enabledAction(@PathVariable Long id) {
        return JsonResult.ok(actionService.enabledAction(id));
    }

    @PutMapping("/disabled/{id}")
    @Operation(summary = "禁用动作")
    public JsonResult<ActionVO> disabledAction(@PathVariable Long id) {
        return JsonResult.ok(actionService.disabledAction(id));
    }
}
