package com.genlight.epilcure.service.user.dao.repository;

import com.genlight.epilcure.service.user.dao.entity.*;
import jakarta.persistence.QueryHint;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.*;

import java.util.Date;
import java.util.List;
import java.util.Optional;

import static org.hibernate.jpa.QueryHints.HINT_CACHEABLE;

/**
 * <AUTHOR>
 * @version 1.0.0
 */
public interface UserRepository extends JpaRepository<User, Long>, JpaSpecificationExecutor<User> {

    boolean existsByOrg(Org org);

    boolean existsByDept(Dept dept);

    boolean existsByRolesContains(Role role);

    boolean existsByTestsContains(Test test);

    boolean existsByMobile(String mobile);

    Page<User> findAllByRolesContains(Role role, Pageable pageable);

    List<User> findAllByRolesContains(Role role);

    Optional<User> findByMobile(String mobile);

    @Modifying
    @Query("UPDATE User SET loginIp = :loginIp, loginTime = :loginTime, loginRegion = :loginRegion WHERE mobile = :mobile")
    void loginSuccess(String loginIp, Date loginTime, String loginRegion, String mobile);

    @QueryHints(@QueryHint(name = HINT_CACHEABLE, value = "true"))
    @Query("FROM User u JOIN u.org o JOIN u.dept d WHERE d.status = 1 AND o.status = 1 AND u.status = 1 AND u.mobile = :mobile")
    Optional<User> getByMobileAndStatus(String mobile);
}
