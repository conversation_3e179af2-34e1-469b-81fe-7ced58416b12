package com.genlight.epilcure.service.user.service;

import com.genlight.epilcure.api.user.pojo.bo.LoginBO;
import com.genlight.epilcure.api.user.pojo.bo.UserBO;
import com.genlight.epilcure.api.user.pojo.dto.UserDTO;
import com.genlight.epilcure.api.user.pojo.vo.UserVO;
import com.genlight.epilcure.common.core.dao.enums.SmsType;
import com.genlight.epilcure.common.core.dao.enums.Status;
import com.genlight.epilcure.common.core.dysms.SmsService;
import com.genlight.epilcure.common.core.pojo.bo.ResetPasswordBO;
import com.genlight.epilcure.common.core.pojo.bo.ValidSmsBO;
import com.genlight.epilcure.common.core.service.BaseSpecificationService;
import com.genlight.epilcure.common.core.service.exception.ServiceException;
import com.genlight.epilcure.common.core.util.RSAUtils;
import com.genlight.epilcure.common.rocketmq.RocketMqConstant;
import com.genlight.epilcure.service.user.constants.CacheNames;
import com.genlight.epilcure.service.user.dao.entity.*;
import com.genlight.epilcure.service.user.dao.repository.RegisterRepository;
import com.genlight.epilcure.service.user.dao.repository.UserRepository;
import com.genlight.epilcure.service.user.pojo.convert.UserConvert;
import jakarta.annotation.Resource;
import jakarta.persistence.criteria.*;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.cache.annotation.*;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.sql.Timestamp;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @version 1.0.0
 */
@Service
@CacheConfig(cacheNames = CacheNames.CACHE_USER)
public class UserService extends BaseSpecificationService<User, Long, UserRepository> {

    @Resource
    private UserConvert userConvert;

    @Resource
    private OrgService orgService;

    @Resource
    private DeptService deptService;

    @Resource
    private RoleService roleService;

    @Resource
    private RSAUtils rsaUtils;

    @Resource
    private PasswordEncoder passwordEncoder;

    @Resource
    private PermissionService permissionService;

    @Resource
    private RegisterRepository registerRepository;

    @Resource
    private RocketMQTemplate userUpdateMqTemplate;

    @Resource
    private RedissonClient redissonClient;

    @Resource
    private SmsService smsService;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Transactional(readOnly = true)
    public UserVO loginByMobile(String mobile) {
        Optional<User> user = repository.getByMobileAndStatus(mobile);
        if (user.isEmpty()) {
            throw new ServiceException("登录失败，该手机号码未注册");
        }
        UserVO userVO = userConvert.po2vo(user.get());
        userVO.setPassword(user.get().getPassword());
        Optional<Integer> minRoleLevel = roleService.getMinRoleLevelByUser(userVO.getId());
        userVO.setOrgIds(orgService.findOrgIdsByUser(userVO.getOrg().getId(), minRoleLevel.orElse(0)));
        userVO.setPermissions(permissionService.findPermissionsByUser(userVO.getId()));
        userVO.setEngineer(userVO.getPermissions().stream().anyMatch(p -> p.getCode().equals("enginner")));
        return userVO;
    }

    @Transactional
    public void loginSuccess(LoginBO loginBO) {
        Optional<User> optional = repository.findByMobile(loginBO.getMobile());
        User user = optional.get();
        user.setLoginIp(loginBO.getIp());
        user.setLoginTime(new Timestamp(new Date().getTime()));
        user.setLoginRegion(loginBO.getRegion());
        repository.save(user);
    }

    @Cacheable(key = "#id")
    @Transactional(readOnly = true)
    public UserVO findUserById(Long id) {
        Optional<User> userOptional = repository.findById(id);
        if (userOptional.isEmpty()) {
            throw new ServiceException("查找的用户[{0}]不存在！", id);
        }
        return userConvert.po2vo(userOptional.get());
    }

    @Transactional(readOnly = true)
    @Cacheable(cacheNames = CacheNames.CACHE_USER_LIST)
    public Page<UserVO> findUsersByRole(Long id, Pageable pageable) {
        return userConvert.po2vo(repository.findAllByRolesContains(Role.builder().id(id).build(), pageable));
    }

    @Transactional(readOnly = true)
    @Cacheable(cacheNames = CacheNames.CACHE_USER_LIST, key = "#root.target.getOrgIds() + '-' + #userDTO + '-' + #pageable")
    public Page<UserVO> findUsersByPageable(UserDTO userDTO, Pageable pageable) {
        return userConvert.po2vo(repository.findAll((Root<User> root, CriteriaQuery<?> query, CriteriaBuilder criteriaBuilder) -> {

            Predicate predicate = criteriaBuilder.conjunction();

            if (StringUtils.hasText(userDTO.getName())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.like(root.get(User_.name), "%" + userDTO.getName() + "%"));
            }
            if (StringUtils.hasText(userDTO.getMobile())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.like(root.get(User_.mobile), "%" + userDTO.getMobile() + "%"));
            }
            if (StringUtils.hasText(userDTO.getDescription())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.like(root.get(User_.description), "%" + userDTO.getDescription() + "%"));
            }
            if (Objects.nonNull(userDTO.getGender())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get(User_.gender), userDTO.getGender()));
            }
            if (Objects.nonNull(userDTO.getStatus())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get(User_.status), userDTO.getStatus()));
            } else {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.notEqual(root.get(User_.status), Status.DELETED));
            }
            if (Objects.nonNull(userDTO.getOrgId())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get(User_.org).get(Org_.id), userDTO.getOrgId()));
            }

            predicate = criteriaBuilder.and(predicate, root.get(User_.org).get(Org_.id).in(getOrgIds()));

            if (Objects.nonNull(userDTO.getDeptId())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get(User_.dept).get(Dept_.id), userDTO.getDeptId()));
            }
            if (Objects.nonNull(userDTO.getRoleId())) {
                if (userDTO.isRoleEquals()) {
                    predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.join(User_.roles).get(Role_.id), userDTO.getRoleId()));
                } else {
                    Subquery<Long> subQuery = query.subquery(Long.class);
                    Root<User> subRoot = subQuery.from(User.class);
                    subQuery.select(subRoot.get(User_.id)).where(criteriaBuilder.equal(subRoot.join(User_.roles).get(Role_.id), userDTO.getRoleId()));
                    predicate = criteriaBuilder.and(predicate, root.get(User_.id).in(subQuery).not());
                }
            }
            if (Objects.nonNull(userDTO.getTestId())) {
                if (userDTO.isTestEquals()) {
                    predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.join(User_.tests).get(Test_.id), userDTO.getTestId()));
                } else {
                    Subquery<Long> subQuery = query.subquery(Long.class);
                    Root<User> subRoot = subQuery.from(User.class);
                    subQuery.select(subRoot.get(User_.id)).where(criteriaBuilder.equal(subRoot.join(User_.tests).get(Test_.id), userDTO.getTestId()));
                    predicate = criteriaBuilder.and(predicate, root.get(User_.id).in(subQuery).not());
                }
            }
            return predicate;
        }, pageable));
    }

    @Transactional
    @CacheEvict(cacheNames = CacheNames.CACHE_USER_LIST, allEntries = true)
    public UserVO addUser(UserDTO userDTO) {

        Optional<Dept> deptOptional = deptService.getDeptById(userDTO.getDeptId());
        if (deptOptional.isEmpty()) {
            throw new ServiceException("部门[{0}]不存在！", userDTO.getDeptId());
        }
        if (!getOrgIds().contains(deptOptional.get().getOrg().getId())) {
            throw new ServiceException("没有权限添加用户到该部门！");
        }

        RLock lock = redissonClient.getLock("addUser-%s".formatted(userDTO.getMobile()));
        try {
            if (lock.tryLock(2, 2, TimeUnit.SECONDS)) {
                if (repository.existsByMobile(userDTO.getMobile())) {
                    throw new ServiceException("用户手机号码[{0}]已经存在！", userDTO.getMobile());
                }

                User user = userConvert.dto2po(userDTO);
                user.setDept(deptOptional.get());
                user.setOrg(user.getDept().getOrg());
                return userConvert.po2vo(repository.saveAndFlush(user));
            }
            throw new ServiceException("操作太频繁，请稍后再试！");
        } catch (Exception e) {
            throw new RuntimeException(e);
        } finally {
            this.unLock(lock);
        }
    }

    @Transactional
    @CacheEvict(cacheNames = CacheNames.CACHE_USER_LIST, allEntries = true)
    public UserVO registerUser(UserDTO userDTO) {
        Optional<Register> registerOptional = registerRepository.getByMobileAndStatus(userDTO.getMobile(), Status.ENABLED);
        if (registerOptional.isEmpty()) {
            throw new ServiceException("当前手机号码不可用，请联系诺为医疗", userDTO.getMobile());
        }

        RLock lock = redissonClient.getLock("registerUser-%s".formatted(userDTO.getMobile()));
        try {
            if (lock.tryLock(2, 2, TimeUnit.SECONDS)) {
                Register register = registerOptional.get();
                if (register.getStatus().equals(Status.DELETED)) {
                    throw new ServiceException("当前号码已注册，请直接登录！", userDTO.getMobile());
                }
                smsService.checkValid(userDTO.getMobile(), userDTO.getCaptchaId(), userDTO.getCaptcha());

                User user = userConvert.dto2po(userDTO);
                user.setOrg(register.getOrg());
                user.setDept(register.getDept());
                user.getRoles().add(register.getRole());
                registerRepository.save(register.setStatus(Status.DELETED));
                return userConvert.po2vo(repository.saveAndFlush(user));
            }
            throw new ServiceException("操作太频繁，请稍后再试！");
        } catch (Exception e) {
            throw new RuntimeException(e);
        } finally {
            this.unLock(lock);
        }
    }

    @Transactional
    @CacheEvict(cacheNames = CacheNames.CACHE_USER_LIST, allEntries = true)
    public void addUser(User user) {
        repository.saveAndFlush(user);
    }


    @Transactional
    @Caching(evict = {
            @CacheEvict(key = "#id"),
            @CacheEvict(cacheNames = CacheNames.CACHE_USER_LIST, allEntries = true)
    })
    public void deleteUser(Long id) {
        Optional<User> userOptional = repository.findById(id);
        if (userOptional.isEmpty()) {
            throw new ServiceException("删除的用户[{0}]不存在！", id);
        }
        User user = userOptional.get();
        if (!getOrgIds().contains(user.getOrg().getId())) {
            throw new ServiceException("没有权限删除用户！");
        }
        repository.save(userOptional.get().setStatus(Status.DELETED));
    }

    @Transactional
    @Caching(evict = {
            @CacheEvict(cacheNames = CacheNames.CACHE_USER_LIST, allEntries = true)
    })
    public UserVO updateUserBySelf(UserDTO userDTO) {
        User user = repository.getReferenceById(getUserId());
        if (StringUtils.hasText(userDTO.getDescription())) {
            user.setDescription(userDTO.getDescription());
        }
        if (Objects.nonNull(userDTO.getGender())) {
            user.setGender(userDTO.getGender());
        }
        if (StringUtils.hasText(userDTO.getOldPassword())) {
            if (!passwordEncoder.matches(rsaUtils.decryptPassword(userDTO.getOldPassword()), user.getPassword())) {
                throw new ServiceException("原密码错误！");
            }
            user.setPassword(passwordEncoder.encode(rsaUtils.decryptPassword(userDTO.getPassword())));
        } else if (StringUtils.hasText(userDTO.getCaptcha()) && StringUtils.hasText(userDTO.getCaptchaId())) {
            smsService.checkValid(user.getMobile(), userDTO.getCaptchaId(), userDTO.getCaptcha());
            user.setPassword(passwordEncoder.encode(rsaUtils.decryptPassword(userDTO.getPassword())));
        }
        if (StringUtils.hasText(userDTO.getName()) && !userDTO.getName().equals(user.getName())) {
            user.setName(userDTO.getName());
            userUpdateMqTemplate.convertAndSend(RocketMqConstant.USER_NAME_UPDATE_TOPIC, UserBO.builder().id(user.getId()).name(userDTO.getName()).build());
        }
        return userConvert.po2vo(repository.saveAndFlush(user));
    }

    @Transactional
    @Caching(put = {
            @CachePut(key = "#id")
    }, evict = {
            @CacheEvict(cacheNames = CacheNames.CACHE_USER_LIST, allEntries = true)
    })
    public UserVO updateUser(Long id, UserDTO userDTO) {
        Optional<User> userOptional = repository.findById(id);
        if (userOptional.isEmpty()) {
            throw new ServiceException("更新的用户[{0}]不存在！", id);
        }
        User user = userOptional.get();
        if (!getOrgIds().contains(user.getOrg().getId())) {
            throw new ServiceException("没有权限更新用户！");
        }
        if (Objects.nonNull(userDTO.getStatus())) {
            if (userDTO.getStatus().equals(Status.DELETED)) {
                throw new ServiceException("不能更新的用户状态为[{0}]！", userDTO.getStatus());
            }
            if (!userDTO.getStatus().equals(user.getStatus()) && userDTO.getStatus().equals(Status.DISABLED) && !user.getDisable()) {
                throw new ServiceException("用户不能被禁用！");
            }
        }

        if (StringUtils.hasText(userDTO.getMobile()) && !userDTO.getMobile().equals(user.getMobile())) {
            if (repository.existsByMobile(userDTO.getMobile())) {
                throw new ServiceException("更新的用户手机号码[{0}]已经存在！", userDTO.getMobile());
            }
        }

        if (Objects.nonNull(userDTO.getDeptId()) && !userDTO.getDeptId().equals(user.getDept().getId())) {
            Optional<Dept> deptOptional = deptService.getDeptById(userDTO.getDeptId());
            if (deptOptional.isEmpty()) {
                throw new ServiceException("部门[{0}]不存在！", userDTO.getDeptId());
            }
            user.setDept(deptOptional.get());
            user.setOrg(user.getDept().getOrg());
        }

        if (StringUtils.hasText(userDTO.getPassword())) {
            String newPassword = rsaUtils.decryptPassword(userDTO.getPassword());
            if (!passwordEncoder.matches(newPassword, user.getPassword()) && userDTO.isSmsNotify()) {
                smsService.sendSms(user.getMobile(), SmsType.RestPassword, ResetPasswordBO.builder().password(newPassword).build());
            }
        }

        if (StringUtils.hasText(userDTO.getName()) && !userDTO.getName().equals(user.getName())) {
            userUpdateMqTemplate.convertAndSend(RocketMqConstant.USER_NAME_UPDATE_TOPIC, UserBO.builder().id(user.getId()).name(userDTO.getName()).build());
        }
        return userConvert.po2vo(repository.saveAndFlush(userConvert.dto2po(userDTO, user)));
    }

    @Transactional
    @Caching(evict = {
            @CacheEvict(cacheNames = CacheNames.CACHE_USER_LIST, allEntries = true)
    })
    public void changePassword(UserDTO userDTO) {
        Optional<User> userOptional = repository.findByMobile(userDTO.getMobile());
        if (userOptional.isEmpty()) {
            throw new ServiceException("更新的用户[{0}]不存在！", userDTO.getMobile());
        }

        User user = userOptional.get();
        if (StringUtils.hasText(userDTO.getCaptcha()) && StringUtils.hasText(userDTO.getCaptchaId())) {
            smsService.checkValid(userDTO.getMobile(), userDTO.getCaptchaId(), userDTO.getCaptcha());
            user.setPassword(passwordEncoder.encode(rsaUtils.decryptPassword(userDTO.getPassword())));
        } else {
            throw new ServiceException("修改密码必须提供验证码！");
        }
        repository.saveAndFlush(user);
    }

    @Transactional
    public void enabledUsers(List<Long> ids) {
        for (Long id : ids) {
            enabledUser(id);
        }
    }

    @Transactional
    @Caching(put = {
            @CachePut(key = "#id")
    }, evict = {
            @CacheEvict(cacheNames = CacheNames.CACHE_USER_LIST, allEntries = true)
    })
    public UserVO enabledUser(Long id) {
        Optional<User> userOptional = repository.findById(id);
        if (userOptional.isEmpty()) {
            throw new ServiceException("启用的用户[{0}]不存在！", id);
        }
        User user = userOptional.get();
        if (!getOrgIds().contains(user.getOrg().getId())) {
            throw new ServiceException("没有权限启用用户！");
        }
        return userConvert.po2vo(repository.saveAndFlush(user.setStatus(Status.ENABLED)));
    }

    @Transactional
    public void disabledUsers(List<Long> ids) {
        for (Long id : ids) {
            disabledUser(id);
        }
    }

    @Transactional
    @Caching(put = {@CachePut(key = "#id")}, evict = {
            @CacheEvict(cacheNames = CacheNames.CACHE_USER_LIST, allEntries = true)
    })
    public UserVO disabledUser(Long id) {
        Optional<User> userOptional = repository.findById(id);
        if (userOptional.isEmpty()) {
            throw new ServiceException("禁用的用户[{0}]不存在！", id);
        }
        User user = userOptional.get();
        if (!getOrgIds().contains(user.getOrg().getId())) {
            throw new ServiceException("没有权限禁用用户！");
        }
        if (!user.getDisable()) {
            throw new ServiceException("用户[{0}]不能禁用！", id);
        }
        return userConvert.po2vo(repository.saveAndFlush(user.setStatus(Status.DISABLED)));
    }

    @Transactional
    @Caching(evict = {
            @CacheEvict(cacheNames = CacheNames.CACHE_USER_LIST, allEntries = true),
            @CacheEvict(cacheNames = CacheNames.CACHE_ROLE_LIST, allEntries = true),
            @CacheEvict(cacheNames = CacheNames.CACHE_PERMISSION_LIST, allEntries = true)
    })
    public void associatedRoles(Long id, List<Long> roleIds) {
        Optional<User> userOptional = repository.findById(id);
        if (userOptional.isEmpty()) {
            throw new ServiceException("用户[{0}]不存在！", id);
        }
        User user = userOptional.get();
        for (Long roleId : roleIds) {
            Optional<Role> role = roleService.getRoleById(roleId);
            if (role.isEmpty()) {
                throw new ServiceException("角色[{0}]不存在！", roleId);
            }
            user.getRoles().add(role.get());
        }
    }

    @Transactional
    @Caching(evict = {
            @CacheEvict(cacheNames = CacheNames.CACHE_USER_LIST, allEntries = true),
            @CacheEvict(cacheNames = CacheNames.CACHE_ROLE_LIST, allEntries = true),
            @CacheEvict(cacheNames = CacheNames.CACHE_PERMISSION_LIST, allEntries = true)
    })
    public void dissociatedRoles(Long id, List<Long> roleIds) {
        Optional<User> userOptional = repository.findById(id);
        if (userOptional.isEmpty()) {
            throw new ServiceException("用户[{0}]不存在！", id);
        }
        User user = userOptional.get();
        for (Long roleId : roleIds) {
            Optional<Role> role = roleService.getRoleById(roleId);
            if (role.isEmpty()) {
                throw new ServiceException("角色[{0}]不存在！", roleId);
            }
            user.getRoles().remove(role.get());
        }
    }

    public String getCaptcha(String mobile) {
        if (!repository.existsByMobile(mobile) && !registerRepository.existsByMobile(mobile)) {
            throw new ServiceException("用户不存在");
        }
        int captcha = (int) ((Math.random() * 9 + 1) * 100000);
        return smsService.sendSms(mobile, SmsType.Valid, ValidSmsBO.builder().code(String.valueOf(captcha)).build());
    }

    @Transactional(readOnly = true)
    public Optional<User> getUserById(Long id) {
        return repository.findById(id);
    }

    @Transactional(readOnly = true)
    public boolean existsUserByOrg(Org org) {
        return repository.existsByOrg(org);
    }

    @Transactional(readOnly = true)
    public boolean existsUserByDept(Dept dept) {
        return repository.existsByDept(dept);
    }

    @Transactional(readOnly = true)
    public boolean existsUserByRole(Role role) {
        return repository.existsByRolesContains(role);
    }

    @Transactional(readOnly = true)
    public boolean existsUserByTest(Test test) {
        return repository.existsByTestsContains(test);
    }
}
