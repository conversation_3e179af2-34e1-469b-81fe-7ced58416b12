package com.genlight.epilcure.service.user.dao.repository;

import com.genlight.epilcure.common.core.dao.repository.TreeRelationRepository;
import com.genlight.epilcure.service.user.dao.entity.Org;
import com.genlight.epilcure.service.user.dao.entity.OrgRelation;
import jakarta.persistence.QueryHint;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.jpa.repository.QueryHints;

import java.util.Set;

import static org.hibernate.jpa.QueryHints.HINT_CACHEABLE;

/**
 * <AUTHOR>
 * @version 1.0.0
 */
public interface OrgRelationRepository extends TreeRelationRepository<Org, OrgRelation, Long> {

    @QueryHints(@QueryHint(name = HINT_CACHEABLE, value = "true"))
    @Query("SELECT DISTINCT o.child.id FROM OrgRelation o WHERE o.child.status = 1 AND o.depth <= :userRoleLevel AND o.parent.id = :userOrg")
    Set<Long> findOrgIdsByUser(Long userOrg, Integer userRoleLevel);

    @QueryHints(@QueryHint(name = HINT_CACHEABLE, value = "true"))
    @Query("SELECT DISTINCT o.child.id FROM OrgRelation o WHERE o.child.status = 1")
    Set<Long> findAllOrgIds();

    @QueryHints(@QueryHint(name = HINT_CACHEABLE, value = "true"))
    @Query("SELECT DISTINCT o.child.id FROM OrgRelation o WHERE o.child.status = 1 And o.child.id <> :id")
    Set<Long> findAllOgrIdsNotContains(Long id);
}
