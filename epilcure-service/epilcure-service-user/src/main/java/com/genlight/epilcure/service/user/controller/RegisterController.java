package com.genlight.epilcure.service.user.controller;

import com.genlight.epilcure.api.user.pojo.dto.RegisterDTO;
import com.genlight.epilcure.api.user.pojo.vo.RegisterVO;
import com.genlight.epilcure.common.core.mvc.response.JsonResult;
import com.genlight.epilcure.common.core.pojo.valid.Add;
import com.genlight.epilcure.common.core.pojo.valid.Update;
import com.genlight.epilcure.service.user.service.RegisterService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 */
@RestController
@RequestMapping("/api/user/registers")
@Tag(name = "RegisterController", description = "注册信息相关接口")
public class RegisterController {

    @Resource
    private RegisterService registerService;

    @GetMapping("/{id}")
    @Operation(summary = "根据ID查询注册信息")
    public JsonResult<RegisterVO> findRegisterById(@PathVariable Long id) {
        return JsonResult.ok(registerService.findRegisterById(id));
    }

    @GetMapping("/exists/{mobile}")
    @Operation(summary = "注册前检查注册信息是否存在")
    public JsonResult<Boolean> existsRegisterByMobile(@PathVariable String mobile) {
        return JsonResult.ok(registerService.existsRegisterByMobile(mobile));
    }

    @GetMapping
    @Operation(summary = "根据动态条件查询注册信息")
    public JsonResult<Page<RegisterVO>> findRegistersByPageable(RegisterDTO registerDTO, Pageable pageable) {
        return JsonResult.ok(registerService.findRegistersByPageable(registerDTO, pageable));
    }

    @PostMapping
    @Operation(summary = "新增注册信息")
    public JsonResult<RegisterVO> addRegister(@Validated(Add.class) @RequestBody RegisterDTO registerDTO) {
        return JsonResult.ok(registerService.addRegister(registerDTO));
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "删除注册信息")
    public JsonResult<Void> deleteRegister(@PathVariable Long id) {
        registerService.deleteRegister(id);
        return JsonResult.ok();
    }

    @PutMapping("/{id}")
    @Operation(summary = "修改注册信息")
    public JsonResult<RegisterVO> updateRegister(@PathVariable Long id, @Validated(Update.class) @RequestBody RegisterDTO registerDTO) {
        return JsonResult.ok(registerService.updateRegister(id, registerDTO));
    }

    @PutMapping("/enabled")
    @Operation(summary = "批量启用注册信息")
    public JsonResult<Void> enabledRegisters(@RequestBody List<Long> ids) {
        registerService.enabledRegisters(ids);
        return JsonResult.ok();
    }

    @PutMapping("/disabled")
    @Operation(summary = "批量禁用注册信息")
    public JsonResult<RegisterVO> disabledRegisters(@RequestBody List<Long> ids) {
        registerService.disabledRegisters(ids);
        return JsonResult.ok();
    }

    @PutMapping("/enabled/{id}")
    @Operation(summary = "启用注册信息")
    public JsonResult<RegisterVO> enabledRegister(@PathVariable Long id) {
        return JsonResult.ok(registerService.enabledRegister(id));
    }

    @PutMapping("/disabled/{id}")
    @Operation(summary = "禁用注册信息")
    public JsonResult<RegisterVO> disabledRegister(@PathVariable Long id) {
        return JsonResult.ok(registerService.disabledRegister(id));
    }
}
