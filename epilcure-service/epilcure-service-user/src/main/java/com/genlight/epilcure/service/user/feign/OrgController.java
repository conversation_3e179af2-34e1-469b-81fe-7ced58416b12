package com.genlight.epilcure.service.user.feign;

import com.genlight.epilcure.api.user.feign.IOrgController;
import com.genlight.epilcure.api.user.pojo.vo.OrgVO;
import com.genlight.epilcure.service.user.service.OrgService;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 */
@RequestMapping
@RestController(value = "feignOrgController")
public class OrgController implements IOrgController {

    @Resource
    private OrgService orgService;

    @Override
    public OrgVO findOrgById(Long id) {
        return orgService.findOrgById(id);
    }

    @Override
    public Boolean existsOrgById(Long id) {
        return orgService.existsById(id);
    }

    @Override
    public List<OrgVO> findOrges() {
        return orgService.findAllEnabledOrges();
    }
}
