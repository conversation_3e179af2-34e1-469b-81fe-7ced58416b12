package com.genlight.epilcure.service.user.service;

import com.genlight.epilcure.api.config.feign.IProjectController;
import com.genlight.epilcure.api.user.pojo.dto.TestDTO;
import com.genlight.epilcure.api.user.pojo.vo.TestVO;
import com.genlight.epilcure.common.core.dao.enums.Status;
import com.genlight.epilcure.common.core.service.BaseSpecificationService;
import com.genlight.epilcure.common.core.service.exception.ServiceException;
import com.genlight.epilcure.service.user.constants.CacheNames;
import com.genlight.epilcure.service.user.dao.entity.Test;
import com.genlight.epilcure.service.user.dao.entity.Test_;
import com.genlight.epilcure.service.user.dao.entity.User;
import com.genlight.epilcure.service.user.dao.entity.User_;
import com.genlight.epilcure.service.user.dao.repository.TestRepository;
import com.genlight.epilcure.service.user.pojo.convert.TestConvert;
import jakarta.annotation.Resource;
import jakarta.persistence.criteria.*;
import org.springframework.cache.annotation.*;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @version 1.0.0
 */
@org.springframework.stereotype.Service
@CacheConfig(cacheNames = CacheNames.CACHE_TEST)
public class TestService extends BaseSpecificationService<Test, Long, TestRepository> {

    @Resource
    private TestConvert testConvert;

    @Resource
    private UserService userService;

    @Resource
    private IProjectController iProjectController;

    @Transactional(readOnly = true)
    public int existsTestById(Long id) {
        return repository.existsById(id) ? 1 : 0;
    }

    @Transactional(readOnly = true)
    public int existsUserByTestId(Long testId, Long userId) {
        return repository.existsByIdAndUsersContains(testId, User.builder().id(userId).build()) ? 1 : 0;
    }

    @Cacheable(key = "#id")
    @Transactional(readOnly = true)
    public TestVO findTestById(Long id) {
        Optional<Test> test = repository.findById(id);
        if (test.isEmpty()) {
            throw new ServiceException("查找的测试名单[{0}]不存在！", id);
        }
        return testConvert.po2vo(test.get());
    }

    @Transactional(readOnly = true)
    @Cacheable(cacheNames = CacheNames.CACHE_TEST_LIST)
    public Page<TestVO> findTestsByPageable(TestDTO testDTO, Pageable pageable) {
        return testConvert.po2vo(repository.findAll((Root<Test> root, CriteriaQuery<?> query, CriteriaBuilder criteriaBuilder) -> {
            Predicate predicate = criteriaBuilder.conjunction();
            if (StringUtils.hasText(testDTO.getName())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.like(root.get(Test_.name), "%" + testDTO.getName() + "%"));
            }

            if (StringUtils.hasText(testDTO.getDescription())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.like(root.get(Test_.description), "%" + testDTO.getDescription() + "%"));
            }

            if (Objects.nonNull(testDTO.getStatus())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get(Test_.status), testDTO.getStatus()));
            }

            if (Objects.nonNull(testDTO.getProjectId())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.like(root.get(Test_.projects), "%" + testDTO.getProjectId() + "%"));
            }

            if (Objects.nonNull(testDTO.getUserId())) {
                if (testDTO.isUserEquals()) {
                    predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.join(Test_.users).get(User_.id), testDTO.getUserId()));
                } else {
                    Subquery<Long> subQuery = query.subquery(Long.class);
                    Root<Test> subRoot = subQuery.from(Test.class);
                    subQuery.select(subRoot.get(Test_.id)).where(criteriaBuilder.equal(subRoot.join(Test_.users).get(User_.id), testDTO.getUserId()));
                    predicate = criteriaBuilder.and(predicate, root.get(Test_.id).in(subQuery).not());
                }
            }
            return predicate;
        }, pageable));
    }

    @Transactional
    @CacheEvict(cacheNames = CacheNames.CACHE_TEST_LIST, allEntries = true)
    public TestVO addTest(TestDTO testDTO) {
        for (Long project : testDTO.getProjects()) {
            if (!iProjectController.existsById(project)) {
                throw new ServiceException("项目ID[{0}]不存在！", project);
            }
        }
        return testConvert.po2vo(repository.saveAndFlush(testConvert.dto2po(testDTO)));
    }

    @Transactional
    @Caching(evict = {
            @CacheEvict(key = "#id"),
            @CacheEvict(cacheNames = CacheNames.CACHE_TEST_LIST, allEntries = true)
    })
    public void deleteTest(Long id) {
        Optional<Test> testOptional = repository.findById(id);
        if (testOptional.isEmpty()) {
            throw new ServiceException("删除的测试名单[{0}]不存在！", id);
        }
        Test test = testOptional.get();
        if (userService.existsUserByTest(test)) {
            throw new ServiceException("测试名单已经被用户关联，不能删除！");
        }
        repository.delete(test);
    }

    @Transactional
    @Caching(put = {
            @CachePut(key = "#id")
    }, evict = {
            @CacheEvict(cacheNames = CacheNames.CACHE_TEST_LIST, allEntries = true)
    })
    public TestVO updateTest(Long id, TestDTO testDTO) {
        Optional<Test> testOptional = repository.findById(id);
        if (testOptional.isEmpty()) {
            throw new ServiceException("更新的测试名单[{0}]不存在！", id);
        }
        Test test = testOptional.get();
        if (Objects.nonNull(testDTO.getProjects()) && !testDTO.getProjects().isEmpty()) {
            for (Long project : testDTO.getProjects()) {
                if (!iProjectController.existsById(project)) {
                    throw new ServiceException("项目ID[{0}]不存在！", project);
                }
            }
        }
        return testConvert.po2vo(repository.saveAndFlush(testConvert.dto2po(testDTO, test)));
    }

    @Transactional
    public void enabledTests(List<Long> ids) {
        for (Long id : ids) {
            enabledTest(id);
        }
    }

    @Transactional
    @Caching(put = {
            @CachePut(key = "#id")
    }, evict = {
            @CacheEvict(cacheNames = CacheNames.CACHE_TEST_LIST, allEntries = true)
    })
    public TestVO enabledTest(Long id) {
        Optional<Test> testOptional = repository.findById(id);
        if (testOptional.isEmpty()) {
            throw new ServiceException("启用的测试名单[{0}]不存在！", id);
        }
        return testConvert.po2vo(repository.saveAndFlush(testOptional.get().setStatus(Status.ENABLED)));
    }

    @Transactional
    public void disabledTests(List<Long> ids) {
        for (Long id : ids) {
            disabledTest(id);
        }
    }

    @Transactional
    @Caching(put = {
            @CachePut(key = "#id")
    }, evict = {
            @CacheEvict(cacheNames = CacheNames.CACHE_TEST_LIST, allEntries = true)
    })
    public TestVO disabledTest(Long id) {
        Optional<Test> testOptional = repository.findById(id);
        if (testOptional.isEmpty()) {
            throw new ServiceException("禁用的测试名单[{0}]不存在！", id);
        }
        return testConvert.po2vo(repository.saveAndFlush(testOptional.get().setStatus(Status.DISABLED)));
    }

    @Transactional
    @Caching(evict = {
            @CacheEvict(cacheNames = CacheNames.CACHE_TEST_LIST, allEntries = true),
            @CacheEvict(cacheNames = CacheNames.CACHE_USER_LIST, allEntries = true)
    })
    public void associatedUsers(Long id, List<Long> userIds) {
        Optional<Test> testOptional = repository.findById(id);
        if (testOptional.isEmpty()) {
            throw new ServiceException("测试名单[{0}]不存在！", id);
        }
        Test test = testOptional.get();
        for (Long userId : userIds) {
            Optional<User> userOptional = userService.getUserById(userId);
            if (userOptional.isEmpty()) {
                throw new ServiceException("用户[{0}]不存在！", userId);
            }
            test.getUsers().add(userOptional.get());
        }
    }

    @Transactional
    @Caching(evict = {
            @CacheEvict(cacheNames = CacheNames.CACHE_TEST_LIST, allEntries = true),
            @CacheEvict(cacheNames = CacheNames.CACHE_USER_LIST, allEntries = true)
    })
    public void dissociatedUsers(Long id, List<Long> userIds) {
        Optional<Test> testOptional = repository.findById(id);
        if (testOptional.isEmpty()) {
            throw new ServiceException("测试名单[{0}]不存在！", id);
        }
        Test test = testOptional.get();
        for (Long userId : userIds) {
            Optional<User> userOptional = userService.getUserById(userId);
            if (userOptional.isEmpty()) {
                throw new ServiceException("用户[{0}]不存在！", userId);
            }
            test.getUsers().remove(userOptional.get());
        }
    }
}
