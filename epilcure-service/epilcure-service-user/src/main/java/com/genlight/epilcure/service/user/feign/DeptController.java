package com.genlight.epilcure.service.user.feign;

import com.genlight.epilcure.api.user.feign.IDeptController;
import com.genlight.epilcure.api.user.pojo.vo.DeptVO;
import com.genlight.epilcure.service.user.service.DeptService;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @version 1.0.0
 */
@RequestMapping
@RestController("feignDeptController")
public class DeptController implements IDeptController {

    @Resource
    private DeptService deptService;

    @Override
    public DeptVO findDeptById(Long id) {
        return deptService.findDeptById(id);
    }

    @Override
    public Boolean existsDeptById(Long id) {
        return deptService.existsById(id);
    }
}
