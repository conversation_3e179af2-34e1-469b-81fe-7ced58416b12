package com.genlight.epilcure.service.user.service;

import com.genlight.epilcure.api.user.pojo.dto.PermissionDTO;
import com.genlight.epilcure.api.user.pojo.vo.PermissionVO;
import com.genlight.epilcure.common.core.dao.enums.Status;
import com.genlight.epilcure.common.core.service.BaseSpecificationService;
import com.genlight.epilcure.common.core.service.exception.ServiceException;
import com.genlight.epilcure.service.user.constants.CacheNames;
import com.genlight.epilcure.service.user.dao.entity.*;
import com.genlight.epilcure.service.user.dao.repository.PermissionRepository;
import com.genlight.epilcure.service.user.pojo.convert.PermissionConvert;
import jakarta.annotation.Resource;
import jakarta.persistence.criteria.*;
import org.springframework.cache.annotation.*;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @version 1.0.0
 */
@Service
@CacheConfig(cacheNames = CacheNames.CACHE_PERMISSION)
public class PermissionService extends BaseSpecificationService<Permission, Long, PermissionRepository> {

    @Resource
    private ResService resService;

    @Resource
    private ActionService actionService;

    @Resource
    private RoleService roleService;

    @Resource
    private PermissionConvert permissionConvert;

    @Cacheable(key = "#id")
    @Transactional(readOnly = true)
    public PermissionVO findPermissionById(Long id) {
        Optional<Permission> permission = repository.findById(id);
        if (permission.isEmpty()) {
            throw new ServiceException("查找的权限[{0}]不存在！", id);
        }
        return permissionConvert.po2vo(permission.get());
    }

    @Transactional(readOnly = true)
    @Cacheable(cacheNames = CacheNames.CACHE_PERMISSION_LIST, key = "'enabled'")
    public List<PermissionVO> findAllEnabledPermissions() {
        return permissionConvert.po2vo(repository.findAllByStatus(Status.ENABLED));
    }

    @Transactional(readOnly = true)
    @Cacheable(cacheNames = CacheNames.CACHE_PERMISSION_LIST)
    public Page<PermissionVO> findPermissionsByRole(Long id, Pageable pageable) {
        return permissionConvert.po2vo(repository.findPermissionsByRole(id, pageable));
    }

    @Transactional(readOnly = true)
    @Cacheable(cacheNames = CacheNames.CACHE_PERMISSION_LIST, key = "'user::' + #id")
    public List<PermissionVO> findPermissionsByUser(Long id) {
        return permissionConvert.po2vo(repository.findPermissionsByUser(id));
    }

    @Transactional(readOnly = true)
    @Cacheable(cacheNames = CacheNames.CACHE_PERMISSION_LIST)
    public Page<PermissionVO> findPermissionsByPageable(PermissionDTO permissionDTO, Pageable pageable) {
        return permissionConvert.po2vo(repository.findAll((Root<Permission> root, CriteriaQuery<?> query, CriteriaBuilder criteriaBuilder) -> {
            Predicate predicate = criteriaBuilder.conjunction();
            if (StringUtils.hasText(permissionDTO.getName())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.like(root.get(Permission_.name), "%" + permissionDTO.getName() + "%"));
            }
            if (StringUtils.hasText(permissionDTO.getDescription())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.like(root.get(Permission_.description), "%" + permissionDTO.getDescription() + "%"));
            }
            if (StringUtils.hasText(permissionDTO.getCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.like(root.get(Permission_.code), "%" + permissionDTO.getCode() + "%"));
            }
            if (Objects.nonNull(permissionDTO.getStatus())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get(Permission_.status), permissionDTO.getStatus()));
            }
            if (Objects.nonNull(permissionDTO.getResId())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get(Permission_.res).get(Res_.id), permissionDTO.getResId()));
            }
            if (Objects.nonNull(permissionDTO.getActionId())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get(Permission_.action).get(Action_.id), permissionDTO.getActionId()));
            }
            if (Objects.nonNull(permissionDTO.getRoleId())) {
                if (permissionDTO.isRoleEquals()) {
                    predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.join(Permission_.roles).get(Role_.id), permissionDTO.getRoleId()));
                } else {
                    Subquery<Long> subQuery = query.subquery(Long.class);
                    Root<Permission> subRoot = subQuery.from(Permission.class);
                    subQuery.select(subRoot.get(Permission_.id)).where(criteriaBuilder.equal(subRoot.join(Permission_.roles).get(Role_.id), permissionDTO.getRoleId()));
                    predicate = criteriaBuilder.and(predicate, root.get(Permission_.id).in(subQuery).not());
                }
            }
            return predicate;
        }, pageable));
    }

    @Transactional
    @CacheEvict(cacheNames = CacheNames.CACHE_PERMISSION_LIST, allEntries = true)
    public PermissionVO addPermission(PermissionDTO permissionDTO) {
        Permission permission = permissionConvert.dto2po(permissionDTO);
        if (Objects.nonNull(permissionDTO.getResId())) {
            Optional<Res> resOptional = resService.getResById(permissionDTO.getResId());
            if (resOptional.isEmpty()) {
                throw new ServiceException("资源[{0}]不存在！", permissionDTO.getResId());
            }
            permission.setRes(resOptional.get());
        } else {
            permission.setRes(resService.addOrGetRes(permissionDTO.getResCode()));
        }
        if (Objects.nonNull(permissionDTO.getActionId())) {
            Optional<Action> actionOptional = actionService.getActionById(permissionDTO.getActionId());
            if (actionOptional.isEmpty()) {
                throw new ServiceException("动作[{0}]不存在！", permissionDTO.getActionId());
            }
            permission.setAction(actionOptional.get());
        } else {
            permission.setAction(actionService.addOrGetAction(permissionDTO.getActionCode()));
        }
        permission.setCode(permission.getRes().getCode() + "." + permission.getAction().getCode());
        permission.setName(permission.getRes().getName() + "(" + permission.getAction().getCode() + ")");
        if (!StringUtils.hasText(permission.getDescription()) && StringUtils.hasText(permission.getRes().getDescription())) {
            permission.setDescription(permission.getRes().getDescription() + "(" + permission.getAction().getCode() + ")");
        }
        if (repository.existsByCodeOrName(permission.getCode(), permission.getName())) {
            throw new ServiceException("权限名称[{0}]或者权限代码[{1}]已经存在！", permission.getName(), permission.getCode());
        }
        return permissionConvert.po2vo(repository.saveAndFlush(permission));
    }

    @Transactional
    @CacheEvict(cacheNames = CacheNames.CACHE_PERMISSION_LIST, allEntries = true)
    public Permission addOrUpdatePermission(Permission permission) {
        Optional<Permission> permissionOptional = repository.getByCodeOrName(permission.getCode(), permission.getName());
        if (permissionOptional.isEmpty()) {
            return repository.saveAndFlush(permission);
        }
        Permission updater = permissionOptional.get();
        if (updater.getRes().equals(permission.getRes()) && updater.getAction().equals(permission.getAction())) {
            updater.setCode(permission.getCode());
            updater.setName(permission.getName());
            updater.setDescription(permission.getDescription());
        }
        return null;
    }

    @Transactional
    public void deletePermissions(List<Long> ids) {
        for (Long id : ids) {
            deletePermission(id);
        }
    }

    @Transactional
    @Caching(evict = {
            @CacheEvict(key = "#id"),
            @CacheEvict(cacheNames = CacheNames.CACHE_PERMISSION_LIST, allEntries = true)
    })
    public void deletePermission(Long id) {
        Optional<Permission> permissionOptional = repository.findById(id);
        if (permissionOptional.isEmpty()) {
            throw new ServiceException("删除的权限[{0}]不存在！", id);
        }
        Permission permission = permissionOptional.get();
        if (roleService.existsRoleByPermission(permission)) {
            throw new ServiceException("权限已经分配给角色，不能删除！");
        }
        repository.delete(permission);
    }

    @Transactional
    @Caching(put = {
            @CachePut(key = "#id")
    }, evict = {
            @CacheEvict(cacheNames = CacheNames.CACHE_PERMISSION_LIST, allEntries = true)
    })
    public PermissionVO updatePermission(Long id, PermissionDTO permissionDTO) {
        Optional<Permission> permissionOptional = repository.findById(id);
        if (permissionOptional.isEmpty()) {
            throw new ServiceException("更新的权限[{0}]不存在！", id);
        }

        Permission permission = permissionOptional.get();
        if (StringUtils.hasText(permissionDTO.getName()) && !permissionDTO.getName().equals(permission.getName())) {
            if (repository.existsByName(permissionDTO.getName())) {
                throw new ServiceException("更新的权限名称[{0}]已经存在！", permission.getName());
            }
        }

        boolean checkCode = false;
        if (Objects.nonNull(permissionDTO.getResId()) && !permissionDTO.getResId().equals(permission.getRes().getId())) {
            Optional<Res> resOptional = resService.getResById(permissionDTO.getResId());
            if (resOptional.isEmpty()) {
                throw new ServiceException("资源[{0}]不存在！", permissionDTO.getResId());
            }
            checkCode = true;
            permission.setRes(resOptional.get());
        } else if (StringUtils.hasText(permissionDTO.getResCode()) && !permissionDTO.getResCode().equals(permission.getRes().getCode())) {
            checkCode = true;
            permission.setRes(resService.addOrGetRes(permissionDTO.getResCode()));
        }
        if (Objects.nonNull(permissionDTO.getActionId()) && !permissionDTO.getActionId().equals(permission.getAction().getId())) {
            Optional<Action> actionOptional = actionService.getActionById(permissionDTO.getActionId());
            if (actionOptional.isEmpty()) {
                throw new ServiceException("动作[{0}]不存在！", permissionDTO.getActionId());
            }
            checkCode = true;
            permission.setAction(actionOptional.get());
        } else if (StringUtils.hasText(permissionDTO.getActionCode()) && !permissionDTO.getActionCode().equals(permission.getAction().getCode())) {
            checkCode = true;
            permission.setAction(actionService.addOrGetAction(permissionDTO.getActionCode()));
        }
        if (checkCode) {
            permission.setCode(permission.getRes().getCode() + "." + permission.getAction().getCode());
            if (repository.existsByCode(permission.getCode())) {
                throw new ServiceException("更新的权限代码[{1}]已经存在！", permission.getCode());
            }
        }
        return permissionConvert.po2vo(repository.saveAndFlush(permissionConvert.dto2po(permissionDTO, permission)));
    }

    @Transactional
    public void enabledPermissions(List<Long> ids) {
        for (Long id : ids) {
            enabledPermission(id);
        }
    }

    @Transactional
    @Caching(put = {
            @CachePut(key = "#id")
    }, evict = {
            @CacheEvict(cacheNames = CacheNames.CACHE_PERMISSION_LIST, allEntries = true)
    })
    public PermissionVO enabledPermission(Long id) {
        Optional<Permission> permissionOptional = repository.findById(id);
        if (permissionOptional.isEmpty()) {
            throw new ServiceException("启用的权限[{0}]不存在！", id);
        }
        return permissionConvert.po2vo(repository.saveAndFlush(permissionOptional.get().setStatus(Status.ENABLED)));
    }

    @Transactional
    public void disabledPermissions(List<Long> ids) {
        for (Long id : ids) {
            disabledPermission(id);
        }
    }

    @Transactional
    @Caching(put = {
            @CachePut(key = "#id")
    }, evict = {
            @CacheEvict(cacheNames = CacheNames.CACHE_PERMISSION_LIST, allEntries = true)
    })
    public PermissionVO disabledPermission(Long id) {
        Optional<Permission> permissionOptional = repository.findById(id);
        if (permissionOptional.isEmpty()) {
            throw new ServiceException("禁用的权限[{0}]不存在！", id);
        }
        return permissionConvert.po2vo(repository.saveAndFlush(permissionOptional.get().setStatus(Status.DISABLED)));
    }

    @Transactional
    @Caching(evict = {
            @CacheEvict(cacheNames = CacheNames.CACHE_USER_LIST, allEntries = true),
            @CacheEvict(cacheNames = CacheNames.CACHE_ROLE_LIST, allEntries = true),
            @CacheEvict(cacheNames = CacheNames.CACHE_PERMISSION_LIST, allEntries = true)
    })
    public void associatedRoles(Long id, List<Long> roleIds) {
        Optional<Permission> permissionOptional = repository.findById(id);
        if (permissionOptional.isEmpty()) {
            throw new ServiceException("权限[{0}]不存在！", id);
        }
        for (Long roleId : roleIds) {
            Optional<Role> role = roleService.getRoleById(roleId);
            if (role.isEmpty()) {
                throw new ServiceException("角色[{0}]不存在！", roleId);
            }
            role.get().getPermissions().add(permissionOptional.get());
        }
    }

    @Transactional
    @Caching(evict = {
            @CacheEvict(cacheNames = CacheNames.CACHE_USER_LIST, allEntries = true),
            @CacheEvict(cacheNames = CacheNames.CACHE_ROLE_LIST, allEntries = true),
            @CacheEvict(cacheNames = CacheNames.CACHE_PERMISSION_LIST, allEntries = true)
    })
    public void dissociatedRoles(Long id, List<Long> roleIds) {
        Optional<Permission> permissionOptional = repository.findById(id);
        if (permissionOptional.isEmpty()) {
            throw new ServiceException("权限[{0}]不存在！", id);
        }
        for (Long roleId : roleIds) {
            Optional<Role> role = roleService.getRoleById(roleId);
            if (role.isEmpty()) {
                throw new ServiceException("角色[{0}]不存在！", roleId);
            }
            role.get().getPermissions().remove(permissionOptional.get());
        }
    }

    @Transactional(readOnly = true)
    public Optional<Permission> getPermissionById(Long id) {
        return repository.findById(id);
    }

    @Transactional(readOnly = true)
    public boolean existsPermissionByRes(Res res) {
        return repository.existsByRes(res);
    }

    @Transactional(readOnly = true)
    public boolean existsPermissionByAction(Action action) {
        return repository.existsByAction(action);
    }
}
