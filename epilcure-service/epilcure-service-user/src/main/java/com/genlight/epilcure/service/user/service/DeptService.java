package com.genlight.epilcure.service.user.service;

import com.genlight.epilcure.api.user.pojo.bo.DeptBO;
import com.genlight.epilcure.api.user.pojo.dto.DeptDTO;
import com.genlight.epilcure.api.user.pojo.vo.DeptVO;
import com.genlight.epilcure.common.core.dao.enums.Status;
import com.genlight.epilcure.common.core.service.BaseTreeSpecificationService;
import com.genlight.epilcure.common.core.service.exception.ServiceException;
import com.genlight.epilcure.common.rocketmq.RocketMqConstant;
import com.genlight.epilcure.service.user.constants.CacheNames;
import com.genlight.epilcure.service.user.dao.entity.*;
import com.genlight.epilcure.service.user.dao.repository.DeptRelationRepository;
import com.genlight.epilcure.service.user.dao.repository.DeptRepository;
import com.genlight.epilcure.service.user.pojo.convert.DeptConvert;
import jakarta.annotation.Resource;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.annotation.Caching;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @version 1.0.0
 */
@Service
@CacheConfig(cacheNames = CacheNames.CACHE_DEPT)
public class DeptService extends BaseTreeSpecificationService<Dept, DeptVO, DeptRelation, Long, DeptRepository, DeptRelationRepository> {

    @Resource
    private OrgService orgService;

    @Resource
    private UserService userService;

    @Resource
    private DeptConvert deptConvert;

    @Resource
    private RedissonClient redissonClient;

    @Resource
    private RocketMQTemplate deptUpdateMqTemplate;

    @Cacheable(key = "'exists::' + #id")
    @Transactional(readOnly = true)
    public boolean existsById(Long id) {
        return repository.existsById(id);
    }

    @Cacheable(key = "#id")
    @Transactional(readOnly = true)
    public DeptVO findDeptById(Long id) {
        Optional<Dept> dept = repository.findById(id);
        if (dept.isEmpty()) {
            throw new ServiceException("查找的部门[{0}]不存在！", id);
        }
        return deptConvert.po2voByDetail(dept.get());
    }

    @Transactional(readOnly = true)
    public Optional<Dept> getDeptById(Long id) {
        return repository.findById(id);
    }

    @Transactional(readOnly = true)
    @Cacheable(cacheNames = CacheNames.CACHE_DEPT_LIST, key = "'enabled'")
    public List<DeptVO> findAllEnabledDepts() {
        return deptConvert.po2vo(repository.findAllByStatus(Status.ENABLED));
    }

    @Transactional(readOnly = true)
    public List<Long> findDeptIdsByUser(Long userDept, Integer userRoleLevel) {
        return relationRepository.findDeptIdsByUser(userDept, userRoleLevel);
    }

    @Transactional(readOnly = true)
    @Cacheable(cacheNames = CacheNames.CACHE_DEPT_LIST)
    public List<DeptVO> findDeptTree(DeptDTO deptDTO) {
        return generateTreeRelationship(repository.findAll(genSpecification(deptDTO)));
    }

    @Transactional(readOnly = true)
    @Cacheable(cacheNames = CacheNames.CACHE_DEPT_LIST)
    public Page<DeptVO> findDeptsByPageable(DeptDTO deptDTO, Pageable pageable) {
        return deptConvert.po2vo(repository.findAll(genSpecification(deptDTO), pageable));
    }

    @Transactional
    @Caching(evict = {
            @CacheEvict(cacheNames = CacheNames.CACHE_ORG, allEntries = true, condition = "#deptDTO.getOrgId() != null"),
            @CacheEvict(cacheNames = CacheNames.CACHE_ORG_LIST, allEntries = true, condition = "#deptDTO.getOrgId() != null"),
            @CacheEvict(cacheNames = CacheNames.CACHE_DEPT_LIST, allEntries = true)
    })
    public DeptVO addDept(DeptDTO deptDTO) {
        Org org = null;
        if (Objects.nonNull(deptDTO.getParentId())) {
            Optional<Dept> parentOptional = repository.findById(deptDTO.getParentId());
            if (parentOptional.isEmpty()) {
                throw new ServiceException("上级部门[{0}]不存在！", deptDTO.getParentId());
            }
            org = parentOptional.get().getOrg();
        }
        if (Objects.isNull(org)) {
            Optional<Org> orgOptional = orgService.findOrg(deptDTO.getOrgId());
            if (orgOptional.isEmpty()) {
                throw new ServiceException("关联的组织[{0}]不存在！", deptDTO.getOrgId());
            }
            org = orgOptional.get();
        }
        RLock lock = redissonClient.getLock("Dept-%s-%s".formatted(org.getId(), deptDTO.getName()));
        try {
            if (lock.tryLock(2, 2, TimeUnit.SECONDS)) {
                if (repository.existsByOrgAndName(org, deptDTO.getName())) {
                    throw new ServiceException("部门名称[{0}]已经存在！", deptDTO.getName());
                }
                return deptConvert.po2vo(repository.saveAndFlush(addParentsRelationship(deptDTO.getParentId(), deptConvert.dto2po(deptDTO).setOrg(org))));
            }
            throw new ServiceException("操作太频繁，请稍后再试！");
        } catch (Exception e) {
            throw new RuntimeException(e);
        } finally {
            this.unLock(lock);
        }
    }

    @Transactional
    @Caching(evict = {
            @CacheEvict(cacheNames = CacheNames.CACHE_DEPT_LIST, allEntries = true)
    })
    public Dept addDept(Dept dept) {
        return repository.saveAndFlush(addParentsRelationship(null, dept));
    }

    @Transactional
    @Caching(evict = {
            @CacheEvict(key = "#id"),
            @CacheEvict(key = "'exists::' + #id"),
            @CacheEvict(cacheNames = CacheNames.CACHE_ORG, allEntries = true),
            @CacheEvict(cacheNames = CacheNames.CACHE_ORG_LIST, allEntries = true),
            @CacheEvict(cacheNames = CacheNames.CACHE_DEPT_LIST, allEntries = true)
    })
    public void deleteDept(Long id) {
        Optional<Dept> deptOptional = repository.findById(id);
        if (deptOptional.isEmpty()) {
            throw new ServiceException("删除的部门[{0}]不存在！", id);
        }
        Dept dept = deptOptional.get();
        if (relationRepository.existsByParentAndChildNot(dept, dept)) {
            throw new ServiceException("存在下级部门关联，不能删除！");
        }
        if (userService.existsUserByDept(dept)) {
            throw new ServiceException("部门下有关联用户，不能删除！");
        }
        repository.delete(dept);
    }

    @Transactional
    @Caching(evict = {
            @CacheEvict(cacheNames = CacheNames.CACHE_USER, allEntries = true),
            @CacheEvict(cacheNames = CacheNames.CACHE_USER_LIST, allEntries = true),
            @CacheEvict(cacheNames = CacheNames.CACHE_ORG, allEntries = true),
            @CacheEvict(cacheNames = CacheNames.CACHE_ORG_LIST, allEntries = true),
            @CacheEvict(cacheNames = CacheNames.CACHE_DEPT, allEntries = true),
            @CacheEvict(cacheNames = CacheNames.CACHE_DEPT_LIST, allEntries = true)
    })
    public DeptVO updateDept(Long id, DeptDTO deptDTO) {
        Optional<Dept> deptOptional = repository.findById(id);
        if (deptOptional.isEmpty()) {
            throw new ServiceException("更新的部门[{0}]不存在！", id);
        }

        boolean updateOrg = false;
        Dept dept = deptOptional.get();
        if (Objects.nonNull(deptDTO.getOrgId()) && !deptDTO.getOrgId().equals(dept.getOrg().getId())) {
            Optional<Org> orgOptional = orgService.findOrg(deptDTO.getOrgId());
            if (orgOptional.isEmpty()) {
                throw new ServiceException("关联的组织[{0}]不存在！", deptDTO.getOrgId());
            }
            dept.setOrg(orgOptional.get());
            updateOrg = true;
        }

        boolean updateName = false;
        if (StringUtils.hasText(deptDTO.getName()) && !deptDTO.getName().equals(dept.getName())) {
            if (repository.existsByOrgAndNameAndIdNot(dept.getOrg(), deptDTO.getName(), id)) {
                throw new ServiceException("部门名称[{0}]已经存在！", deptDTO.getName());
            }
            updateName = true;
        }

        boolean updateParent = false;
        dept = deptConvert.dto2po(deptDTO, dept);
        if (Objects.nonNull(deptDTO.getParentId()) && (Objects.isNull(dept.getParent()) || !deptDTO.getParentId().equals(dept.getParent().getId()))) {
            if (Objects.nonNull(dept.getParent())) {
                if (!dept.getParent().getId().equals(deptDTO.getParentId())) {
                    if (deptDTO.getParentId().intValue() == 0) {
                        clearParentsRelationship(dept);
                    } else {
                        updateParentsRelationship(deptDTO.getParentId(), dept, true);
                    }
                }
            } else {
                updateParentsRelationship(deptDTO.getParentId(), dept, true);
            }
            updateParent = true;
        }

        RLock lock = redissonClient.getLock("Dept-%s-%s".formatted(deptDTO.getOrgId(), deptDTO.getName()));
        try {
            if (lock.tryLock(2, 2, TimeUnit.SECONDS)) {
                if (updateOrg && updateParent) {
                    for (DeptRelation relation : dept.getChildRelation()) {
                        if (!relation.getChild().equals(dept)) {   // 过滤自己
                            Dept child = relation.getChild();
                            if (repository.existsByOrgAndNameAndIdNot(dept.getOrg(), child.getName(), child.getId())) {
                                throw new ServiceException("部门名称[{0}]已经存在！", child.getName());
                            }
                            child.setOrg(dept.getOrg());
                        }
                    }
                }
                if (updateName) {
                    deptUpdateMqTemplate.convertAndSend(RocketMqConstant.DEPT_NAME_UPDATE_TOPIC, DeptBO.builder().id(dept.getId()).name(dept.getName()).build());
                }
                return deptConvert.po2vo(repository.saveAndFlush(dept));
            }
            throw new ServiceException("操作太频繁，请稍后再试！");
        } catch (Exception e) {
            throw new RuntimeException(e);
        } finally {
            this.unLock(lock);
        }
    }

    @Transactional
    public void enabledDepts(List<Long> ids) {
        for (Long id : ids) {
            enabledDept(id);
        }
    }

    @Transactional
    @Caching(evict = {
            @CacheEvict(cacheNames = CacheNames.CACHE_USER, allEntries = true),
            @CacheEvict(cacheNames = CacheNames.CACHE_USER_LIST, allEntries = true),
            @CacheEvict(cacheNames = CacheNames.CACHE_ORG, allEntries = true),
            @CacheEvict(cacheNames = CacheNames.CACHE_ORG_LIST, allEntries = true),
            @CacheEvict(cacheNames = CacheNames.CACHE_DEPT, allEntries = true),
            @CacheEvict(cacheNames = CacheNames.CACHE_DEPT_LIST, allEntries = true)
    })
    public DeptVO enabledDept(Long id) {
        Optional<Dept> deptOptional = repository.findById(id);
        if (deptOptional.isEmpty()) {
            throw new ServiceException("启用的部门[{0}]不存在！", id);
        }
        return deptConvert.po2vo(repository.saveAndFlush(deptOptional.get().setStatus(Status.ENABLED)));
    }

    @Transactional
    public void disabledDepts(List<Long> ids) {
        for (Long id : ids) {
            disabledDept(id);
        }
    }

    @Transactional
    @Caching(evict = {
            @CacheEvict(cacheNames = CacheNames.CACHE_USER, allEntries = true),
            @CacheEvict(cacheNames = CacheNames.CACHE_USER_LIST, allEntries = true),
            @CacheEvict(cacheNames = CacheNames.CACHE_ORG, allEntries = true),
            @CacheEvict(cacheNames = CacheNames.CACHE_ORG_LIST, allEntries = true),
            @CacheEvict(cacheNames = CacheNames.CACHE_DEPT, allEntries = true),
            @CacheEvict(cacheNames = CacheNames.CACHE_DEPT_LIST, allEntries = true)
    })
    public DeptVO disabledDept(Long id) {
        Optional<Dept> deptOptional = repository.findById(id);
        if (deptOptional.isEmpty()) {
            throw new ServiceException("禁用的部门[{0}]不存在！", id);
        }
        return deptConvert.po2vo(repository.saveAndFlush(deptOptional.get().setStatus(Status.DISABLED)));
    }

    private Specification<Dept> genSpecification(DeptDTO deptDTO) {
        return (Root<Dept> root, CriteriaQuery<?> query, CriteriaBuilder criteriaBuilder) -> {
            Predicate predicate = criteriaBuilder.conjunction();
            if (StringUtils.hasText(deptDTO.getName())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.like(root.get(Dept_.NAME), "%" + deptDTO.getName() + "%"));
            }
            if (StringUtils.hasText(deptDTO.getPhone())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.like(root.get(Dept_.PHONE), "%" + deptDTO.getPhone() + "%"));
            }
            if (Objects.nonNull(deptDTO.getParentId())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get(Dept_.PARENT).get(Dept_.ID), deptDTO.getParentId()));
            }
            if (Objects.nonNull(deptDTO.getStatus())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get(Dept_.STATUS), deptDTO.getStatus()));
            }
            if (StringUtils.hasText(deptDTO.getDescription())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.like(root.get(Dept_.DESCRIPTION), "%" + deptDTO.getDescription() + "%"));
            }
            if (Objects.nonNull(deptDTO.getOrgId())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get(Dept_.ORG).get(Org_.ID), deptDTO.getOrgId()));
            } else {
                predicate = criteriaBuilder.and(predicate, root.get(Dept_.ORG).get(Org_.ID).in(getOrgIds()));
            }
            query.where(predicate);
            query.orderBy(criteriaBuilder.asc(root.get(Dept_.SEQ)));
            query.orderBy(criteriaBuilder.asc(root.get(Dept_.ID)));
            return query.getRestriction();
        };
    }

    @Transactional(readOnly = true)
    public boolean existsDeptByOrg(Org org) {
        return repository.existsByOrg(org);
    }

    @Override
    public DeptRelation buildRelation(Dept parent, Dept child, Integer depth) {
        return DeptRelation.builder().parent(parent).child(child).depth(depth).build();
    }

    @Override
    public DeptVO nodeToVO(Dept node) {
        return deptConvert.po2vo(node);
    }
}
