package com.genlight.epilcure.service.user.dao.repository;

import com.genlight.epilcure.common.core.dao.enums.Status;
import com.genlight.epilcure.service.user.dao.entity.Register;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import java.util.Optional;

/**
 * <AUTHOR>
 * @version 1.0.0
 */
public interface RegisterRepository extends JpaRepository<Register, Long>, JpaSpecificationExecutor<Register> {

    boolean existsByMobile(String mobile);

    Optional<Register> getByMobileAndStatus(String mobile, Status status);
}
