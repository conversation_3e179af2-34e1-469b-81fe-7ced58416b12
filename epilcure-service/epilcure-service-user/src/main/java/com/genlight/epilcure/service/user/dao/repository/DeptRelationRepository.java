package com.genlight.epilcure.service.user.dao.repository;

import com.genlight.epilcure.common.core.dao.repository.TreeRelationRepository;
import com.genlight.epilcure.service.user.dao.entity.Dept;
import com.genlight.epilcure.service.user.dao.entity.DeptRelation;
import jakarta.persistence.QueryHint;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.jpa.repository.QueryHints;

import java.util.List;

import static org.hibernate.jpa.QueryHints.HINT_CACHEABLE;

/**
 * <AUTHOR>
 * @version 1.0.0
 */
public interface DeptRelationRepository extends TreeRelationRepository<Dept, DeptRelation, Long> {

    @QueryHints(@QueryHint(name = HINT_CACHEABLE, value = "true"))
    @Query("SELECT d.child.id FROM DeptRelation d WHERE d.child.status = 1 AND d.depth <= :userRoleLevel AND d.parent.id = :userDept")
    List<Long> findDeptIdsByUser(Long userDept, Integer userRoleLevel);
}
