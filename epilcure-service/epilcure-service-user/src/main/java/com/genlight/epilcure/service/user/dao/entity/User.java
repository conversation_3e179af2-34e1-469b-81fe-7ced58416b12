package com.genlight.epilcure.service.user.dao.entity;

import com.genlight.epilcure.common.core.dao.entity.TEntity;
import com.genlight.epilcure.common.core.dao.enums.Gender;
import com.genlight.epilcure.common.core.dao.enums.Status;
import com.genlight.epilcure.common.core.dao.generator.annotation.SignOrder;
import jakarta.persistence.*;
import lombok.*;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;
import org.hibernate.annotations.Comment;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import java.util.Date;
import java.util.HashSet;
import java.util.Set;

/**
 * <AUTHOR>
 * @version 1.0.0
 */
@Setter
@Getter
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString
@Entity
@Cacheable
@DynamicInsert
@DynamicUpdate
@SignOrder(0)
@Table(name = "t_user", indexes = {
        @Index(name = "index_user_name", columnList = "name"),
        @Index(name = "index_user_status", columnList = "status")
})
public class User extends TEntity {

    @Comment("名字")
    @Column(nullable = false, length = 32)
    @SignOrder(1)
    private String name;

    @Comment("手机号")
    @Column(nullable = false, unique = true, length = 16)
    @SignOrder(2)
    private String mobile;

    @Comment("密码")
    @Column(nullable = false, length = 256)
    @SignOrder(3)
    private String password;

    @Comment("性别")
    @Column(nullable = false)
    @Enumerated(EnumType.ORDINAL)
    @SignOrder(4)
    private Gender gender;

    @Comment("状态（0：禁用，1：启用，3：删除）")
    @Column(nullable = false)
    @Enumerated(EnumType.ORDINAL)
    @SignOrder(5)
    private Status status;

    @Comment("是否能禁用")
    @Column(nullable = false)
    @SignOrder(6)
    private Boolean disable;

    @Comment("最后登录时间")
    @Temporal(TemporalType.TIMESTAMP)
    @SignOrder(7)
    private Date loginTime;

    @Comment("最后登录IP")
    @SignOrder(8)
    private String loginIp;

    @Comment("最后登录位置")
    @SignOrder(9)
    private String loginRegion;

    @Comment("描述信息")
    @Column(length = 1024)
    @SignOrder(10)
    private String description;

    @ToString.Exclude
    @ManyToOne(optional = false, fetch = FetchType.LAZY)
    private Org org;

    @ToString.Exclude
    @ManyToOne(optional = false, fetch = FetchType.LAZY)
    private Dept dept;

    @ManyToMany
    @Builder.Default
    @ToString.Exclude
    private Set<Role> roles = new HashSet<>();

    @Builder.Default
    @ToString.Exclude
    @ManyToMany(mappedBy = "users")
    private Set<Test> tests = new HashSet<>();
}
