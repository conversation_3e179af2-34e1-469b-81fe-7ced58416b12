package com.genlight.epilcure.service.user.pojo.convert;

import com.genlight.epilcure.api.user.pojo.dto.OrgDTO;
import com.genlight.epilcure.api.user.pojo.vo.OrgVO;
import com.genlight.epilcure.common.core.mvc.response.RestPage;
import com.genlight.epilcure.common.core.pojo.convert.AddressConvert;
import com.genlight.epilcure.common.core.pojo.convert.BaseConvert;
import com.genlight.epilcure.common.core.pojo.convert.qualified.Basic;
import com.genlight.epilcure.common.core.pojo.convert.qualified.Detail;
import com.genlight.epilcure.service.user.dao.entity.Org;
import org.mapstruct.*;
import org.springframework.data.domain.Page;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0.0
 */
@Mapper(config = BaseConvert.class, uses = {AddressConvert.class, DeptConvert.class})
public interface OrgConvert extends BaseConvert<Org, OrgVO, OrgDTO> {

    @Mapping(target = "seq", expression = "java(java.util.Optional.ofNullable(dto.getSeq()).orElse(0))")
    @Mapping(target = "status", expression = "java(java.util.Optional.ofNullable(dto.getStatus()).orElse(com.genlight.epilcure.common.core.dao.enums.Status.ENABLED))")
    Org dto2po(OrgDTO dto);

    @Basic
    @Mapping(target = "depts", ignore = true)
    @Mapping(target = "parent", ignore = true)
    @BeanMapping(nullValueCheckStrategy = NullValueCheckStrategy.ON_IMPLICIT_CONVERSION)
    OrgVO po2vo(Org org);

    @Mapping(target = "depts", ignore = true)
    @Mapping(target = "parent", qualifiedBy = Basic.class)
    @BeanMapping(nullValueCheckStrategy = NullValueCheckStrategy.ON_IMPLICIT_CONVERSION)
    OrgVO po2voByDetail(Org org);

    @IterableMapping(qualifiedBy = Basic.class)
    List<OrgVO> po2vo(List<Org> orges);

    @Detail
    @Mapping(target = "depts", qualifiedByName = "DeptConvert", qualifiedBy = Basic.class)
    @Mapping(target = "parent", ignore = true)
    @BeanMapping(nullValueCheckStrategy = NullValueCheckStrategy.ON_IMPLICIT_CONVERSION)
    OrgVO po2voContainDept(Org org);

    default Page<OrgVO> po2voContainDept(Page<Org> poPage) {
        return new RestPage<>(poPage.getContent()
                .stream()
                .map(this::po2voContainDept)
                .collect(Collectors.toList()), poPage.getPageable(), poPage.getTotalElements());
    }
}

