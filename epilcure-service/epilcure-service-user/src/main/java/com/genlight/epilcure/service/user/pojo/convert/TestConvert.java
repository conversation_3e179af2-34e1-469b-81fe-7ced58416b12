package com.genlight.epilcure.service.user.pojo.convert;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.genlight.epilcure.api.user.pojo.dto.TestDTO;
import com.genlight.epilcure.api.user.pojo.vo.TestVO;
import com.genlight.epilcure.common.core.pojo.convert.BaseConvert;
import com.genlight.epilcure.service.user.dao.entity.Test;
import org.mapstruct.*;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @version 1.0.0
 */
@Mapper(config = BaseConvert.class)
public abstract class TestConvert implements BaseConvert<Test, TestVO, TestDTO> {

    @Autowired
    protected ObjectMapper objectMapper;

    @Mapping(target = "status", expression = "java(java.util.Optional.ofNullable(dto.getStatus()).orElse(com.genlight.epilcure.common.core.dao.enums.Status.ENABLED))")
    @Mapping(target = "projects", expression = "java(com.genlight.epilcure.common.core.util.FunctionUtils.ThrowingFunction.<List<Long>, String>sneaky(objectMapper::writeValueAsString).apply(dto.getProjects()))")
    public abstract Test dto2po(TestDTO dto);

    @Mapping(target = "projects", expression = "java(java.util.Objects.nonNull(dto.getProjects()) ? com.genlight.epilcure.common.core.util.FunctionUtils.ThrowingFunction.<List<Long>, String>sneaky(objectMapper::writeValueAsString).apply(dto.getProjects()): po.getProjects())")
    public abstract Test dto2po(TestDTO dto, @MappingTarget Test po);

    @Mapping(target = "projects", expression = "java(com.genlight.epilcure.common.core.util.FunctionUtils.ThrowingFunctionTowParams.<String, com.fasterxml.jackson.databind.JavaType, List<Long>>sneaky(objectMapper::readValue).apply(po.getProjects(), com.fasterxml.jackson.databind.type.TypeFactory.defaultInstance().constructCollectionType(List.class, Long.class)))")
    @BeanMapping(nullValueCheckStrategy = NullValueCheckStrategy.ON_IMPLICIT_CONVERSION)
    public abstract TestVO po2vo(Test po);
}
