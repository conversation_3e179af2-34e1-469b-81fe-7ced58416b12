package com.genlight.epilcure.service.user.dao.repository;

import com.genlight.epilcure.common.core.dao.enums.Status;
import com.genlight.epilcure.service.user.dao.entity.Org;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 */
public interface OrgRepository extends JpaRepository<Org, Long>, JpaSpecificationExecutor<Org> {

    List<Org> findAllByStatus(Status status);

    List<Org> findAllByStatusAndIdIn(Status status, List<Long> ids);

    boolean existsByName(String name);
}
