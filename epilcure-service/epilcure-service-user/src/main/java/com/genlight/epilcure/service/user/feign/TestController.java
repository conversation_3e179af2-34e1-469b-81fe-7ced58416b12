package com.genlight.epilcure.service.user.feign;

import com.genlight.epilcure.api.user.feign.ITestController;
import com.genlight.epilcure.api.user.pojo.vo.TestVO;
import com.genlight.epilcure.service.user.service.TestService;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @version 1.0.0
 */
@RequestMapping
@RestController(value = "feignTestController")
public class TestController implements ITestController {

    @Resource
    private TestService testService;

    @Override
    public TestVO findTestById(Long id) {
        return testService.findTestById(id);
    }

    @Override
    public int existsTestById(Long id) {
        return testService.existsTestById(id);
    }

    @Override
    public int existsUserByTestId(Long testId, Long userId) {
        return testService.existsUserByTestId(testId, userId);
    }
}
