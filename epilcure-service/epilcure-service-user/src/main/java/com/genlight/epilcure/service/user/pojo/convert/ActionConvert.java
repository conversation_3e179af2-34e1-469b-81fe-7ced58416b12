package com.genlight.epilcure.service.user.pojo.convert;

import com.genlight.epilcure.api.user.pojo.dto.ActionDTO;
import com.genlight.epilcure.api.user.pojo.vo.ActionVO;
import com.genlight.epilcure.common.core.pojo.convert.BaseConvert;
import com.genlight.epilcure.service.user.dao.entity.Action;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

/**
 * <AUTHOR>
 * @version 1.0.0
 */
@Mapper(config = BaseConvert.class)
public interface ActionConvert extends BaseConvert<Action, ActionVO, ActionDTO> {

    @Mapping(target = "status", expression = "java(java.util.Optional.ofNullable(dto.getStatus()).orElse(com.genlight.epilcure.common.core.dao.enums.Status.ENABLED))")
    Action dto2po(ActionDTO dto);
}
