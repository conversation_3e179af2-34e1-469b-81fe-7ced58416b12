package com.genlight.epilcure.service.user.pojo.convert;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.genlight.epilcure.api.user.pojo.dto.RegisterDTO;
import com.genlight.epilcure.api.user.pojo.vo.RegisterVO;
import com.genlight.epilcure.common.core.pojo.convert.BaseConvert;
import com.genlight.epilcure.common.core.pojo.convert.qualified.Basic;
import com.genlight.epilcure.service.user.dao.entity.Register;
import org.mapstruct.*;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @version 1.0.0
 */
@Mapper(config = BaseConvert.class, uses = {DeptConvert.class, OrgConvert.class, UserConvert.class})
public abstract class RegisterConvert implements BaseConvert<Register, RegisterVO, RegisterDTO> {

    @Autowired
    protected ObjectMapper objectMapper;

    @Mapping(target = "status", expression = "java(java.util.Optional.ofNullable(dto.getStatus()).orElse(com.genlight.epilcure.common.core.dao.enums.Status.ENABLED))")
    @Mapping(target = "projects", expression = "java(com.genlight.epilcure.common.core.util.FunctionUtils.ThrowingFunction.<List<Long>, String>sneaky(objectMapper::writeValueAsString).apply(dto.getProjects()))")
    public abstract Register dto2po(RegisterDTO dto);

    @Mapping(target = "projects", expression = "java(java.util.Objects.nonNull(dto.getProjects()) ? com.genlight.epilcure.common.core.util.FunctionUtils.ThrowingFunction.<List<Long>, String>sneaky(objectMapper::writeValueAsString).apply(dto.getProjects()): po.getProjects())")
    public abstract Register dto2po(RegisterDTO dto, @MappingTarget Register po);

    @Mapping(target = "org", qualifiedBy = Basic.class)
    @Mapping(target = "dept", qualifiedBy = Basic.class)
    @Mapping(target = "projects", expression = "java(com.genlight.epilcure.common.core.util.FunctionUtils.ThrowingFunctionTowParams.<String, com.fasterxml.jackson.databind.JavaType, List<Long>>sneaky(objectMapper::readValue).apply(po.getProjects(), com.fasterxml.jackson.databind.type.TypeFactory.defaultInstance().constructCollectionType(List.class, Long.class)))")
    @BeanMapping(nullValueCheckStrategy = NullValueCheckStrategy.ON_IMPLICIT_CONVERSION)
    public abstract RegisterVO po2vo(Register po);
}
