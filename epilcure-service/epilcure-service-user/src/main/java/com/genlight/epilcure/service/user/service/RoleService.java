package com.genlight.epilcure.service.user.service;

import com.genlight.epilcure.api.user.pojo.dto.RoleDTO;
import com.genlight.epilcure.api.user.pojo.vo.RoleVO;
import com.genlight.epilcure.common.core.dao.enums.Status;
import com.genlight.epilcure.common.core.service.BaseSpecificationService;
import com.genlight.epilcure.common.core.service.exception.ServiceException;
import com.genlight.epilcure.service.user.constants.CacheNames;
import com.genlight.epilcure.service.user.dao.entity.*;
import com.genlight.epilcure.service.user.dao.repository.RoleRepository;
import com.genlight.epilcure.service.user.pojo.convert.RoleConvert;
import jakarta.annotation.Resource;
import jakarta.persistence.criteria.*;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.cache.annotation.*;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @version 1.0.0
 */
@Service
@CacheConfig(cacheNames = CacheNames.CACHE_ROLE)
public class RoleService extends BaseSpecificationService<Role, Long, RoleRepository> {

    @Resource
    private RoleConvert roleConvert;

    @Resource
    private UserService userService;

    @Resource
    private RedissonClient redissonClient;

    @Resource
    private PermissionService permissionService;

    @Cacheable(key = "#id")
    @Transactional(readOnly = true)
    public RoleVO findRoleById(Long id) {
        Optional<Role> role = repository.findById(id);
        if (role.isEmpty()) {
            throw new ServiceException("查找的角色[{0}]不存在！", id);
        }
        return roleConvert.po2vo(role.get());
    }

    @Transactional(readOnly = true)
    public Optional<Role> getRoleById(Long id) {
        return repository.findById(id);
    }

    @Transactional(readOnly = true)
    @Cacheable(cacheNames = CacheNames.CACHE_ROLE_LIST, key = "'enabled'")
    public List<RoleVO> findAllEnabledRoles() {
        return roleConvert.po2vo(repository.findAllByStatus(Status.ENABLED));
    }

    @Transactional(readOnly = true)
    @Cacheable(cacheNames = CacheNames.CACHE_ROLE_LIST)
    public Page<RoleVO> findRolesByUser(Long id, Pageable pageable) {
        return roleConvert.po2vo(repository.findAllByUsersContains(User.builder().id(id).build(), pageable));
    }

    @Transactional(readOnly = true)
    @Cacheable(cacheNames = CacheNames.CACHE_ROLE_LIST)
    public Page<RoleVO> findRolesByPageable(RoleDTO roleDTO, Pageable pageable) {
        return roleConvert.po2vo(repository.findAll((Root<Role> root, CriteriaQuery<?> query, CriteriaBuilder criteriaBuilder) -> {
            Predicate predicate = criteriaBuilder.conjunction();
            if (StringUtils.hasText(roleDTO.getName())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.like(root.get(Role_.name), "%" + roleDTO.getName() + "%"));
            }
            if (StringUtils.hasText(roleDTO.getCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.like(root.get(Role_.code), "%" + roleDTO.getCode() + "%"));
            }
            if (StringUtils.hasText(roleDTO.getDescription())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.like(root.get(Role_.description), "%" + roleDTO.getDescription() + "%"));
            }
            if (Objects.nonNull(roleDTO.getLevel())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get(Role_.level), roleDTO.getLevel()));
            }
            if (Objects.nonNull(roleDTO.getStatus())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get(Role_.status), roleDTO.getStatus()));
            }
            if (Objects.nonNull(roleDTO.getUserId())) {
                if (roleDTO.isUserEquals()) {
                    predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.join(Role_.users).get(User_.id), roleDTO.getUserId()));
                } else {
                    Subquery<Long> subQuery = query.subquery(Long.class);
                    Root<Role> subRoot = subQuery.from(Role.class);
                    subQuery.select(subRoot.get(Role_.id)).where(criteriaBuilder.equal(subRoot.join(Role_.users).get(User_.id), roleDTO.getUserId()));
                    predicate = criteriaBuilder.and(predicate, root.get(Role_.id).in(subQuery).not());
                }
            }
            if (Objects.nonNull(roleDTO.getPermissionId())) {
                if (roleDTO.isPermissionEquals()) {
                    predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.join(Role_.permissions).get(Permission_.id), roleDTO.getPermissionId()));
                } else {
                    Subquery<Long> subQuery = query.subquery(Long.class);
                    Root<Role> subRoot = subQuery.from(Role.class);
                    subQuery.select(subRoot.get(Role_.id)).where(criteriaBuilder.equal(subRoot.join(Role_.permissions).get(Permission_.id), roleDTO.getPermissionId()));
                    predicate = criteriaBuilder.and(predicate, root.get(Role_.id).in(subQuery).not());
                }
            }
            return predicate;
        }, pageable));
    }

    @Transactional
    @CacheEvict(cacheNames = CacheNames.CACHE_ROLE_LIST, allEntries = true)
    public RoleVO addRole(RoleDTO roleDTO) {
        RLock lock = redissonClient.getLock("addRole-%s-%s".formatted(roleDTO.getCode(), roleDTO.getName()));
        try {
            if (lock.tryLock(2, 2, TimeUnit.SECONDS)) {
                if (repository.existsByCodeOrName(roleDTO.getCode(), roleDTO.getName())) {
                    throw new ServiceException("角色名称[{0}]或者角色代码[{1}]已经存在！", roleDTO.getName(), roleDTO.getCode());
                }
                return roleConvert.po2vo(repository.saveAndFlush(roleConvert.dto2po(roleDTO)));
            }
            throw new ServiceException("操作太频繁，请稍后再试！");
        } catch (Exception e) {
            throw new RuntimeException(e);
        } finally {
            this.unLock(lock);
        }
    }

    @Transactional
    @CacheEvict(cacheNames = CacheNames.CACHE_ROLE_LIST, allEntries = true)
    public Role addRole(Role role) {
        if (!repository.existsByCodeOrName(role.getCode(), role.getName())) {
            return repository.saveAndFlush(role);
        }
        return null;
    }

    @Transactional
    @Caching(evict = {
            @CacheEvict(key = "#id"),
            @CacheEvict(cacheNames = CacheNames.CACHE_ROLE_LIST, allEntries = true)
    })
    public void deleteRole(Long id) {
        Optional<Role> roleOptional = repository.findById(id);
        if (roleOptional.isEmpty()) {
            throw new ServiceException("删除的角色[{0}]不存在！", id);
        }
        Role role = roleOptional.get();
        if (userService.existsUserByRole(role)) {
            throw new ServiceException("角色已经被用户关联，不能删除！");
        }
        repository.delete(role);
    }

    @Transactional
    @Caching(put = {
            @CachePut(key = "#id")
    }, evict = {
            @CacheEvict(cacheNames = CacheNames.CACHE_ROLE_LIST, allEntries = true)
    })
    public RoleVO updateRole(Long id, RoleDTO roleDTO) {
        Optional<Role> roleOptional = repository.findById(id);
        if (roleOptional.isEmpty()) {
            throw new ServiceException("更新的角色[{0}]不存在！", id);
        }
        Role role = roleOptional.get();
        if (StringUtils.hasText(roleDTO.getName()) && !roleDTO.getName().equals(role.getName())) {
            if (StringUtils.hasText(roleDTO.getCode()) && !roleDTO.getCode().equals(role.getCode())) {
                if (repository.existsByCodeOrName(roleDTO.getCode(), roleDTO.getName())) {
                    throw new ServiceException("更新的角色名称[{0}]或者角色代码[{1}]已经存在！", roleDTO.getName(), roleDTO.getCode());
                }
            } else {
                if (repository.existsByName(roleDTO.getName())) {
                    throw new ServiceException("更新的角色名称[{0}]已经存在！", roleDTO.getName());
                }
            }
        } else if (StringUtils.hasText(roleDTO.getCode()) && !roleDTO.getCode().equals(role.getCode())) {
            if (repository.existsByCode(roleDTO.getCode())) {
                throw new ServiceException("更新的角色代码[{0}]已经存在！", roleDTO.getCode());
            }
        }
        return roleConvert.po2vo(repository.saveAndFlush(roleConvert.dto2po(roleDTO, role)));
    }

    @Transactional
    public void enabledRoles(List<Long> ids) {
        for (Long id : ids) {
            enabledRole(id);
        }
    }

    @Transactional
    @Caching(put = {
            @CachePut(key = "#id")
    }, evict = {
            @CacheEvict(cacheNames = CacheNames.CACHE_ROLE_LIST, allEntries = true)
    })
    public RoleVO enabledRole(Long id) {
        Optional<Role> roleOptional = repository.findById(id);
        if (roleOptional.isEmpty()) {
            throw new ServiceException("启用的角色[{0}]不存在！", id);
        }
        return roleConvert.po2vo(repository.saveAndFlush(roleOptional.get().setStatus(Status.ENABLED)));
    }

    @Transactional
    public void disabledRoles(List<Long> ids) {
        for (Long id : ids) {
            disabledRole(id);
        }
    }

    @Transactional
    @Caching(put = {
            @CachePut(key = "#id")
    }, evict = {
            @CacheEvict(cacheNames = CacheNames.CACHE_ROLE_LIST, allEntries = true)
    })
    public RoleVO disabledRole(Long id) {
        Optional<Role> roleOptional = repository.findById(id);
        if (roleOptional.isEmpty()) {
            throw new ServiceException("禁用的角色[{0}]不存在！", id);
        }
        return roleConvert.po2vo(repository.saveAndFlush(roleOptional.get().setStatus(Status.DISABLED)));
    }

    @Transactional
    @Caching(evict = {
            @CacheEvict(cacheNames = CacheNames.CACHE_ROLE_LIST, allEntries = true),
            @CacheEvict(cacheNames = CacheNames.CACHE_USER_LIST, allEntries = true),
            @CacheEvict(cacheNames = CacheNames.CACHE_PERMISSION_LIST, allEntries = true)
    })
    public void associatedUsers(Long id, List<Long> userIds) {
        Optional<Role> roleOptional = repository.findById(id);
        if (roleOptional.isEmpty()) {
            throw new ServiceException("角色[{0}]不存在！", id);
        }
        for (Long userId : userIds) {
            Optional<User> userOptional = userService.getUserById(userId);
            if (userOptional.isEmpty()) {
                throw new ServiceException("用户[{0}]不存在！", userId);
            }
            userOptional.get().getRoles().add(roleOptional.get());
        }
    }

    @Transactional
    @Caching(evict = {
            @CacheEvict(cacheNames = CacheNames.CACHE_ROLE_LIST, allEntries = true),
            @CacheEvict(cacheNames = CacheNames.CACHE_USER_LIST, allEntries = true),
            @CacheEvict(cacheNames = CacheNames.CACHE_PERMISSION_LIST, allEntries = true)
    })
    public void dissociatedUsers(Long id, List<Long> userIds) {
        Optional<Role> roleOptional = repository.findById(id);
        if (roleOptional.isEmpty()) {
            throw new ServiceException("角色[{0}]不存在！", id);
        }
        for (Long userId : userIds) {
            Optional<User> userOptional = userService.getUserById(userId);
            if (userOptional.isEmpty()) {
                throw new ServiceException("用户[{0}]不存在！", userId);
            }
            userOptional.get().getRoles().remove(roleOptional.get());
        }
    }

    @Transactional
    @Caching(evict = {
            @CacheEvict(cacheNames = CacheNames.CACHE_ROLE_LIST, allEntries = true),
            @CacheEvict(cacheNames = CacheNames.CACHE_USER_LIST, allEntries = true),
            @CacheEvict(cacheNames = CacheNames.CACHE_PERMISSION_LIST, allEntries = true)
    })
    public void associatedPermissions(Long id, List<Long> permissionIds) {
        Optional<Role> roleOptional = repository.findById(id);
        if (roleOptional.isEmpty()) {
            throw new ServiceException("角色[{0}]不存在！", id);
        }
        Role role = roleOptional.get();
        for (Long permissionId : permissionIds) {
            Optional<Permission> permission = permissionService.getPermissionById(permissionId);
            if (permission.isEmpty()) {
                throw new ServiceException("权限[{0}]不存在！", permissionId);
            }
            role.getPermissions().add(permission.get());
        }
    }

    @Transactional
    @Caching(evict = {
            @CacheEvict(cacheNames = CacheNames.CACHE_ROLE_LIST, allEntries = true),
            @CacheEvict(cacheNames = CacheNames.CACHE_USER_LIST, allEntries = true),
            @CacheEvict(cacheNames = CacheNames.CACHE_PERMISSION_LIST, allEntries = true)
    })
    public void dissociatedPermissions(Long id, List<Long> permissionIds) {
        Optional<Role> roleOptional = repository.findById(id);
        if (roleOptional.isEmpty()) {
            throw new ServiceException("角色[{0}]不存在！", id);
        }
        Role role = roleOptional.get();
        for (Long permissionId : permissionIds) {
            Optional<Permission> permission = permissionService.getPermissionById(permissionId);
            if (permission.isEmpty()) {
                throw new ServiceException("权限[{0}]不存在！", permissionId);
            }
            role.getPermissions().remove(permission.get());
        }
    }

    @Transactional(readOnly = true)
    public boolean existsRoleByPermission(Permission permission) {
        return repository.existsByPermissionsContains(permission);
    }

    @Transactional(readOnly = true)
    public Optional<Integer> getMinRoleLevelByUser(Long id) {
        return repository.getMinRoleLevelByUser(id);
    }
}
