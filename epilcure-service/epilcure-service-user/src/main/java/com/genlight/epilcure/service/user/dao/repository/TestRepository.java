package com.genlight.epilcure.service.user.dao.repository;

import com.genlight.epilcure.service.user.dao.entity.Test;
import com.genlight.epilcure.service.user.dao.entity.User;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

/**
 * <AUTHOR>
 * @version 1.0.0
 */
public interface TestRepository extends JpaRepository<Test, Long>, JpaSpecificationExecutor<Test> {

    boolean existsByIdAndUsersContains(Long testId, User user);
}
