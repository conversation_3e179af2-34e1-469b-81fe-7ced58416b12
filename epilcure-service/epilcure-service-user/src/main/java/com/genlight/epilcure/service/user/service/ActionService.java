package com.genlight.epilcure.service.user.service;

import com.genlight.epilcure.api.user.pojo.dto.ActionDTO;
import com.genlight.epilcure.api.user.pojo.vo.ActionVO;
import com.genlight.epilcure.common.core.dao.enums.Status;
import com.genlight.epilcure.common.core.service.BaseSpecificationService;
import com.genlight.epilcure.common.core.service.exception.ServiceException;
import com.genlight.epilcure.service.user.constants.CacheNames;
import com.genlight.epilcure.service.user.dao.entity.Action;
import com.genlight.epilcure.service.user.dao.entity.Action_;
import com.genlight.epilcure.service.user.dao.repository.ActionRepository;
import com.genlight.epilcure.service.user.pojo.convert.ActionConvert;
import jakarta.annotation.Resource;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.cache.annotation.*;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @version 1.0.0
 */
@Service
@CacheConfig(cacheNames = CacheNames.CACHE_ACTION)
public class ActionService extends BaseSpecificationService<Action, Long, ActionRepository> {

    @Resource
    private ActionConvert actionConvert;

    @Resource
    private RedissonClient redissonClient;

    @Resource
    private PermissionService permissionService;

    @Cacheable(key = "#id")
    @Transactional(readOnly = true)
    public ActionVO findActionById(Long id) {
        Optional<Action> action = repository.findById(id);
        if (action.isEmpty()) {
            throw new ServiceException("查找的动作[{0}]不存在！", id);
        }
        return actionConvert.po2vo(action.get());
    }

    @Transactional(readOnly = true)
    public Optional<Action> getActionById(Long id) {
        return repository.findById(id);
    }

    @Transactional(readOnly = true)
    @Cacheable(cacheNames = CacheNames.CACHE_ACTION_LIST, key = "'enabled'")
    public List<ActionVO> findAllEnabledActions() {
        return actionConvert.po2vo(repository.findAllByStatus(Status.ENABLED));
    }

    @Transactional(readOnly = true)
    @Cacheable(cacheNames = CacheNames.CACHE_ACTION_LIST)
    public Page<ActionVO> findActionsByPageable(ActionDTO actionDTO, Pageable pageable) {
        return actionConvert.po2vo(repository.findAll((Root<Action> root, CriteriaQuery<?> query, CriteriaBuilder criteriaBuilder) -> {
            Predicate predicate = criteriaBuilder.conjunction();
            if (StringUtils.hasText(actionDTO.getName())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.like(root.get(Action_.name), "%" + actionDTO.getName() + "%"));
            }
            if (StringUtils.hasText(actionDTO.getCode())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.like(root.get(Action_.code), "%" + actionDTO.getCode() + "%"));
            }
            if (StringUtils.hasText(actionDTO.getDescription())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.like(root.get(Action_.description), "%" + actionDTO.getDescription() + "%"));
            }
            if (Objects.nonNull(actionDTO.getStatus())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get(Action_.status), actionDTO.getStatus()));
            }
            return predicate;
        }, pageable));
    }

    @Transactional
    @CacheEvict(cacheNames = CacheNames.CACHE_ACTION_LIST, allEntries = true)
    public ActionVO addAction(ActionDTO actionDTO) {
        RLock lock = redissonClient.getLock("Action-%s-%s".formatted(actionDTO.getCode(), actionDTO.getName()));
        try {
            if (lock.tryLock(2, 2, TimeUnit.SECONDS)) {
                if (repository.existsByCodeOrName(actionDTO.getCode(), actionDTO.getName())) {
                    throw new ServiceException("动作名称[{0}]或者动作代码[{1}]已经存在！", actionDTO.getName(), actionDTO.getCode());
                }
                return actionConvert.po2vo(repository.saveAndFlush(actionConvert.dto2po(actionDTO)));
            }
            throw new ServiceException("操作太频繁，请稍后再试！");
        } catch (Exception e) {
            throw new RuntimeException(e);
        } finally {
            this.unLock(lock);
        }
    }

    @Transactional
    public Action addOrGetAction(String code) {
        return repository.findByCode(code).orElseGet(() -> addAction(Action.builder().name(code).code(code).status(Status.ENABLED).description(code).build()));
    }

    @Transactional
    public Action addOrGetAction(Action action) {
        return repository.findByCode(action.getCode()).orElseGet(() -> addAction(action));
    }

    @Transactional
    @CacheEvict(cacheNames = CacheNames.CACHE_ACTION_LIST, allEntries = true)
    private Action addAction(Action action) {
        return repository.saveAndFlush(action);
    }

    @Transactional
    @Caching(evict = {
            @CacheEvict(key = "#id"),
            @CacheEvict(cacheNames = CacheNames.CACHE_ACTION_LIST, allEntries = true)
    })
    public void deleteAction(Long id) {
        Optional<Action> actionOptional = repository.findById(id);
        if (actionOptional.isEmpty()) {
            throw new ServiceException("删除的动作[{0}]不存在！", id);
        }

        Action action = actionOptional.get();
        if (permissionService.existsPermissionByAction(action)) {
            throw new ServiceException("动作已经被权限关联，不能删除！");
        }
        repository.delete(action);
    }

    @Transactional
    @Caching(put = {
            @CachePut(key = "#id")
    }, evict = {
            @CacheEvict(cacheNames = CacheNames.CACHE_ACTION_LIST, allEntries = true),
            @CacheEvict(cacheNames = CacheNames.CACHE_PERMISSION, allEntries = true),
            @CacheEvict(cacheNames = CacheNames.CACHE_PERMISSION_LIST, allEntries = true)
    })
    public ActionVO updateAction(Long id, ActionDTO actionDTO) {
        Optional<Action> actionOptional = repository.findById(id);
        if (actionOptional.isEmpty()) {
            throw new ServiceException("更新的动作[{0}]不存在！", id);
        }

        RLock lock = redissonClient.getLock("Action-%s-%s".formatted(actionDTO.getCode(), actionDTO.getName()));
        try {
            if (lock.tryLock(2, 2, TimeUnit.SECONDS)) {
                Action action = actionOptional.get();
                if (StringUtils.hasText(actionDTO.getName()) && !actionDTO.getName().equals(action.getName())) {
                    if (StringUtils.hasText(actionDTO.getCode()) && !actionDTO.getCode().equals(action.getCode())) {
                        if (repository.existsByCodeOrName(actionDTO.getCode(), actionDTO.getName())) {
                            throw new ServiceException("更新的动作名称[{0}]或者动作代码[{1}]已经存在！", actionDTO.getName(), actionDTO.getCode());
                        }
                    } else {
                        if (repository.existsByName(actionDTO.getName())) {
                            throw new ServiceException("更新的动作名称[{0}]已经存在！", actionDTO.getName());
                        }
                    }
                } else if (StringUtils.hasText(actionDTO.getCode()) && !actionDTO.getCode().equals(action.getCode())) {
                    if (repository.existsByCode(actionDTO.getCode())) {
                        throw new ServiceException("更新的动作代码[{0}]已经存在！", actionDTO.getCode());
                    }
                }
                return actionConvert.po2vo(repository.saveAndFlush(actionConvert.dto2po(actionDTO, action)));
            }
            throw new ServiceException("操作太频繁，请稍后再试！");
        } catch (Exception e) {
            throw new RuntimeException(e);
        } finally {
            this.unLock(lock);
        }
    }

    @Transactional
    public void enabledActions(List<Long> ids) {
        for (Long id : ids) {
            enabledAction(id);
        }
    }

    @Transactional
    @Caching(put = {
            @CachePut(key = "#id")
    }, evict = {
            @CacheEvict(cacheNames = CacheNames.CACHE_ACTION_LIST, allEntries = true),
            @CacheEvict(cacheNames = CacheNames.CACHE_PERMISSION, allEntries = true),
            @CacheEvict(cacheNames = CacheNames.CACHE_PERMISSION_LIST, allEntries = true)
    })
    public ActionVO enabledAction(Long id) {
        Optional<Action> actionOptional = repository.findById(id);
        if (actionOptional.isEmpty()) {
            throw new ServiceException("启用的动作[{0}]不存在！", id);
        }
        return actionConvert.po2vo(repository.saveAndFlush(actionOptional.get().setStatus(Status.ENABLED)));
    }

    @Transactional
    public void disabledActions(List<Long> ids) {
        for (Long id : ids) {
            disabledAction(id);
        }
    }

    @Transactional
    @Caching(put = {
            @CachePut(key = "#id")
    }, evict = {
            @CacheEvict(cacheNames = CacheNames.CACHE_ACTION_LIST, allEntries = true),
            @CacheEvict(cacheNames = CacheNames.CACHE_PERMISSION, allEntries = true),
            @CacheEvict(cacheNames = CacheNames.CACHE_PERMISSION_LIST, allEntries = true)
    })
    public ActionVO disabledAction(Long id) {
        Optional<Action> actionOptional = repository.findById(id);
        if (actionOptional.isEmpty()) {
            throw new ServiceException("禁用的动作[{0}]不存在！", id);
        }
        return actionConvert.po2vo(repository.saveAndFlush(actionOptional.get().setStatus(Status.DISABLED)));
    }
}
