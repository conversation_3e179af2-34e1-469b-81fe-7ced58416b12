package com.genlight.epilcure.service.user.service;

import com.genlight.epilcure.api.config.feign.IProjectController;
import com.genlight.epilcure.api.user.pojo.dto.RegisterDTO;
import com.genlight.epilcure.api.user.pojo.vo.RegisterVO;
import com.genlight.epilcure.common.core.dao.enums.Status;
import com.genlight.epilcure.common.core.service.BaseSpecificationService;
import com.genlight.epilcure.common.core.service.exception.ServiceException;
import com.genlight.epilcure.service.user.constants.CacheNames;
import com.genlight.epilcure.service.user.dao.entity.*;
import com.genlight.epilcure.service.user.dao.repository.RegisterRepository;
import com.genlight.epilcure.service.user.dao.repository.UserRepository;
import com.genlight.epilcure.service.user.pojo.convert.RegisterConvert;
import jakarta.annotation.Resource;
import jakarta.persistence.criteria.CriteriaBuilder;
import jakarta.persistence.criteria.CriteriaQuery;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Root;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.cache.annotation.*;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @version 1.0.0
 */
@Service
@CacheConfig(cacheNames = CacheNames.CACHE_REGISTER)
public class RegisterService extends BaseSpecificationService<Register, Long, RegisterRepository> {

    @Resource
    private RegisterConvert registerConvert;

    @Resource
    private OrgService orgService;

    @Resource
    private DeptService deptService;

    @Resource
    private RoleService roleService;

    @Resource
    private UserRepository userRepository;

    @Resource
    private RedissonClient redissonClient;

    @Resource
    private IProjectController iProjectController;

    @Cacheable(key = "#id")
    @Transactional(readOnly = true)
    public RegisterVO findRegisterById(Long id) {
        Optional<Register> registerOptional = repository.findById(id);
        if (registerOptional.isEmpty()) {
            throw new ServiceException("查找的注册信息[{0}]不存在！", id);
        }
        return registerConvert.po2vo(registerOptional.get());
    }

    @Cacheable(key = "'exists::'+#mobile")
    @Transactional(readOnly = true)
    public boolean existsRegisterByMobile(String mobile) {
        return repository.existsByMobile(mobile);
    }

    @Transactional(readOnly = true)
    @Cacheable(cacheNames = CacheNames.CACHE_REGISTER_LIST, key = "#root.target.getOrgIds + '-' + #registerDTO + '-' + #pageable")
    public Page<RegisterVO> findRegistersByPageable(RegisterDTO registerDTO, Pageable pageable) {
        return registerConvert.po2vo(repository.findAll((Root<Register> root, CriteriaQuery<?> query, CriteriaBuilder criteriaBuilder) -> {

            Predicate predicate = criteriaBuilder.conjunction();

            if (StringUtils.hasText(registerDTO.getName())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.like(root.get(Register_.name), "%" + registerDTO.getName() + "%"));
            }
            if (StringUtils.hasText(registerDTO.getMobile())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.like(root.get(Register_.mobile), "%" + registerDTO.getMobile() + "%"));
            }
            if (StringUtils.hasText(registerDTO.getDescription())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.like(root.get(Register_.description), "%" + registerDTO.getDescription() + "%"));
            }
            if (Objects.nonNull(registerDTO.getStatus())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get(Register_.status), registerDTO.getStatus()));
            }
            if (Objects.nonNull(registerDTO.getOrgId())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get(Register_.org).get(Org_.id), registerDTO.getOrgId()));
            }

            predicate = criteriaBuilder.and(predicate, root.get(Register_.org).get(Org_.id).in(getOrgIds()));

            if (Objects.nonNull(registerDTO.getDeptId())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get(Register_.dept).get(Dept_.id), registerDTO.getDeptId()));
            }
            if (Objects.nonNull(registerDTO.getRoleId())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get(Register_.role).get(Role_.id), registerDTO.getRoleId()));
            }
            return predicate;
        }, pageable));
    }

    @Transactional
    @Caching(evict = {
            @CacheEvict(cacheNames = CacheNames.CACHE_REGISTER_LIST, allEntries = true),
            @CacheEvict(key = "'exists::'+#p0.mobile")
    })
    public RegisterVO addRegister(RegisterDTO registerDTO) {

        for (Long project : registerDTO.getProjects()) {
            if (!iProjectController.existsById(project)) {
                throw new ServiceException("项目ID[{0}]不存在！", project);
            }
        }
        Optional<Dept> deptOptional = deptService.getDeptById(registerDTO.getDeptId());
        if (deptOptional.isEmpty()) {
            throw new ServiceException("部门[{0}]不存在！", registerDTO.getDeptId());
        }
        if (!getOrgIds().contains(deptOptional.get().getOrg().getId())) {
            throw new ServiceException("没有权限添加注册信息到该部门！");
        }
        Optional<Role> roleOptional = roleService.getRoleById(registerDTO.getRoleId());
        if (roleOptional.isEmpty()) {
            throw new ServiceException("角色[{0}]不存在！", registerDTO.getDeptId());
        }

        RLock lock = redissonClient.getLock("addRegister-%s".formatted(registerDTO.getMobile()));
        try {
            if (lock.tryLock(2, 2, TimeUnit.SECONDS)) {
                if (existsRegisterByMobile(registerDTO.getMobile())) {
                    throw new ServiceException("注册信息手机号码[{0}]已经存在！", registerDTO.getMobile());
                }
                if (userRepository.existsByMobile(registerDTO.getMobile())) {
                    throw new ServiceException("注册信息手机号码[{0}]已经存在！", registerDTO.getMobile());
                }

                Register register = registerConvert.dto2po(registerDTO);
                register.setRole(roleOptional.get());
                register.setDept(deptOptional.get());
                register.setOrg(register.getDept().getOrg());
                register.setOperator(userRepository.getReferenceById(getUserId()));
                return registerConvert.po2vo(repository.saveAndFlush(register));
            }
            throw new ServiceException("操作太频繁，请稍后再试！");
        } catch (Exception e) {
            throw new RuntimeException(e);
        } finally {
            this.unLock(lock);
        }
    }

    @Transactional
    @Caching(evict = {
            @CacheEvict(key = "#id"),
            @CacheEvict(cacheNames = CacheNames.CACHE_REGISTER_LIST, allEntries = true)
    })
    public void deleteRegister(Long id) {
        Optional<Register> registerOptional = repository.findById(id);
        if (registerOptional.isEmpty()) {
            throw new ServiceException("删除的注册信息[{0}]不存在！", id);
        }
        Register register = registerOptional.get();
        if (!getOrgIds().contains(register.getOrg().getId())) {
            throw new ServiceException("没有权限删除注册信息！");
        }

        if (register.getStatus().equals(Status.DELETED)) {
            throw new ServiceException("注册信息已使用，不能删除！");
        }
        repository.delete(register);
    }

    @Transactional
    @Caching(put = {
            @CachePut(key = "#id")
    }, evict = {
            @CacheEvict(cacheNames = CacheNames.CACHE_REGISTER_LIST, allEntries = true)
    })
    public RegisterVO updateRegister(Long id, RegisterDTO registerDTO) {
        Optional<Register> registerOptional = repository.findById(id);
        if (registerOptional.isEmpty()) {
            throw new ServiceException("更新的注册信息[{0}]不存在！", id);
        }
        Register register = registerOptional.get();
        if (!getOrgIds().contains(register.getOrg().getId())) {
            throw new ServiceException("没有权限更新注册信息！");
        }

        if (StringUtils.hasText(registerDTO.getMobile()) && !registerDTO.getMobile().equals(register.getMobile())) {
            if (existsRegisterByMobile(registerDTO.getMobile())) {
                throw new ServiceException("更新的注册信息手机号码[{0}]已经存在！", registerDTO.getMobile());
            }
            if (userRepository.existsByMobile(registerDTO.getMobile())) {
                throw new ServiceException("更新的注册信息手机号码[{0}]已经存在！", registerDTO.getMobile());
            }
        }
        if (Objects.nonNull(registerDTO.getProjects()) && !registerDTO.getProjects().isEmpty()) {
            for (Long project : registerDTO.getProjects()) {
                if (!iProjectController.existsById(project)) {
                    throw new ServiceException("项目ID[{0}]不存在！", project);
                }
            }
        }
        if (Objects.nonNull(registerDTO.getDeptId()) && !registerDTO.getDeptId().equals(register.getDept().getId())) {
            Optional<Dept> deptOptional = deptService.getDeptById(registerDTO.getDeptId());
            if (deptOptional.isEmpty()) {
                throw new ServiceException("部门[{0}]不存在！", registerDTO.getDeptId());
            }
            register.setDept(deptOptional.get());
            register.setOrg(register.getDept().getOrg());
        }

        if (Objects.nonNull(registerDTO.getRoleId()) && !registerDTO.getRoleId().equals(register.getRole().getId())) {
            Optional<Role> roleOptional = roleService.getRoleById(registerDTO.getRoleId());
            if (roleOptional.isEmpty()) {
                throw new ServiceException("角色[{0}]不存在！", registerDTO.getDeptId());
            }
            register.setRole(roleOptional.get());
        }
        register.setOperator(userRepository.getReferenceById(getUserId()));
        return registerConvert.po2vo(repository.saveAndFlush(registerConvert.dto2po(registerDTO, register)));
    }

    @Transactional
    public void enabledRegisters(List<Long> ids) {
        for (Long id : ids) {
            enabledRegister(id);
        }
    }

    @Transactional
    @Caching(put = {
            @CachePut(key = "#id")
    }, evict = {
            @CacheEvict(cacheNames = CacheNames.CACHE_REGISTER_LIST, allEntries = true)
    })
    public RegisterVO enabledRegister(Long id) {
        Optional<Register> registerOptional = repository.findById(id);
        if (registerOptional.isEmpty()) {
            throw new ServiceException("启用的注册信息[{0}]不存在！", id);
        }
        Register register = registerOptional.get();
        if (!getOrgIds().contains(register.getOrg().getId())) {
            throw new ServiceException("没有权限启用注册信息！");
        }
        return registerConvert.po2vo(repository.saveAndFlush(register.setStatus(Status.ENABLED)));
    }

    @Transactional
    public void disabledRegisters(List<Long> ids) {
        for (Long id : ids) {
            disabledRegister(id);
        }
    }

    @Transactional
    @Caching(put = {@CachePut(key = "#id")}, evict = {
            @CacheEvict(cacheNames = CacheNames.CACHE_REGISTER_LIST, allEntries = true)
    })
    public RegisterVO disabledRegister(Long id) {
        Optional<Register> registerOptional = repository.findById(id);
        if (registerOptional.isEmpty()) {
            throw new ServiceException("禁用的注册信息[{0}]不存在！", id);
        }
        Register register = registerOptional.get();
        if (!getOrgIds().contains(register.getOrg().getId())) {
            throw new ServiceException("没有权限禁用注册信息！");
        }
        return registerConvert.po2vo(repository.saveAndFlush(register.setStatus(Status.DISABLED)));
    }
}
