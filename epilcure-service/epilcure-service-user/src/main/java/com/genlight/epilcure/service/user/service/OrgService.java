package com.genlight.epilcure.service.user.service;

import com.genlight.epilcure.api.user.pojo.bo.OrgBO;
import com.genlight.epilcure.api.user.pojo.dto.OrgDTO;
import com.genlight.epilcure.api.user.pojo.vo.OrgVO;
import com.genlight.epilcure.common.core.dao.entity.Address_;
import com.genlight.epilcure.common.core.dao.enums.Status;
import com.genlight.epilcure.common.core.pojo.dto.AddressDTO;
import com.genlight.epilcure.common.core.service.BaseTreeSpecificationService;
import com.genlight.epilcure.common.core.service.exception.ServiceException;
import com.genlight.epilcure.common.core.util.MinioUtils;
import com.genlight.epilcure.common.rocketmq.RocketMqConstant;
import com.genlight.epilcure.service.user.constants.CacheNames;
import com.genlight.epilcure.service.user.constants.Constants;
import com.genlight.epilcure.service.user.constants.FileConstants;
import com.genlight.epilcure.service.user.dao.entity.*;
import com.genlight.epilcure.service.user.dao.repository.OrgRelationRepository;
import com.genlight.epilcure.service.user.dao.repository.OrgRepository;
import com.genlight.epilcure.service.user.pojo.convert.OrgConvert;
import jakarta.annotation.Resource;
import jakarta.persistence.criteria.*;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.annotation.Caching;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @version 1.0.0
 */
@Service
@CacheConfig(cacheNames = CacheNames.CACHE_ORG)
public class OrgService extends BaseTreeSpecificationService<Org, OrgVO, OrgRelation, Long, OrgRepository, OrgRelationRepository> {

    @Autowired
    private MinioUtils minioUtils;

    @Resource
    private OrgConvert orgConvert;

    @Resource
    private DeptService deptService;

    @Resource
    private UserService userService;

    @Resource
    private RedissonClient redissonClient;

    @Resource
    private RocketMQTemplate orgUpdateMqTemplate;

    @Cacheable(key = "'exists::' + #id")
    @Transactional(readOnly = true)
    public boolean existsById(Long id) {
        return repository.existsById(id);
    }

    @Cacheable(key = "#id")
    @Transactional(readOnly = true)
    public OrgVO findOrgById(Long id) {
        Optional<Org> org = repository.findById(id);
        if (org.isEmpty()) {
            throw new ServiceException("查找的组织[{0}]不存在！", id);
        }
        return orgConvert.po2voByDetail(org.get());
    }

    @Transactional(readOnly = true)
    public Optional<Org> getOrgById(Long id) {
        return repository.findById(id);
    }

    @Transactional(readOnly = true)
    public List<OrgVO> findAllEnabledOrges() {
        return orgConvert.po2vo(repository.findAllByStatusAndIdIn(Status.ENABLED, getOrgIds()));
    }

    @Transactional(readOnly = true)
    public Set<Long> findOrgIdsByUser(Long userOrg, Integer userRoleLevel) {
//        return relationRepository.findOrgIdsByUser(userOrg, userRoleLevel);
        return switch (userRoleLevel) {
            case 1 -> relationRepository.findAllOrgIds();
            case 2 -> relationRepository.findAllOgrIdsNotContains(Constants.ROOT_ORG_ID);
            case 3 -> Set.of(userOrg);
            case 4 -> Set.of(Constants.ROOT_ORG_ID, userOrg);
            default -> Collections.emptySet();
        };
    }

    @Transactional(readOnly = true)
    @Cacheable(cacheNames = CacheNames.CACHE_ORG_LIST, key = "'tree::' + #orgDTO+'-'+ #root.target.getOrgIds()")
    public List<OrgVO> findOrgTree(OrgDTO orgDTO) {
        return generateTreeRelationship(repository.findAll(genSpecification(orgDTO)));
    }

    @Transactional(readOnly = true)
    @Cacheable(cacheNames = CacheNames.CACHE_ORG_LIST, key = "'list::' + #orgDTO + #pageable+'-'+ #root.target.getOrgIds()")
    public Page<OrgVO> findOrgesContainDeptByPageable(OrgDTO orgDTO, Pageable pageable) {
        Page<OrgVO> orgVOS = orgConvert.po2voContainDept(repository.findAll(genSpecification(orgDTO), pageable));
        if (StringUtils.hasText(orgDTO.getDeptName())) {
            orgVOS.getContent().forEach(orgVO -> orgVO.getDepts().removeIf(deptVO -> !deptVO.getName().contains(orgDTO.getDeptName())));
        }
        return orgVOS;
    }

    @Transactional(readOnly = true)
    @Cacheable(cacheNames = CacheNames.CACHE_ORG_LIST)
    public Page<OrgVO> findOrgesByPageable(OrgDTO orgDTO, Pageable pageable) {
        return orgConvert.po2vo(repository.findAll(genSpecification(orgDTO), pageable));
    }

    @Transactional
    @CacheEvict(cacheNames = CacheNames.CACHE_ORG_LIST, allEntries = true)
    public OrgVO addOrg(OrgDTO orgDTO, MultipartFile logo) {
        RLock lock = redissonClient.getLock("addOrg-%s".formatted(orgDTO.getName()));
        try {
            if (lock.tryLock(2, 2, TimeUnit.SECONDS)) {
                if (repository.existsByName(orgDTO.getName())) {
                    throw new ServiceException("组织名称[{0}]已经存在！", orgDTO.getName());
                }
                Org org = orgConvert.dto2po(orgDTO);
                if (Objects.nonNull(logo)) {
                    org.setLogo(minioUtils.upload(FileConstants.ORG_LOGO, logo));
                }
                return orgConvert.po2vo(repository.saveAndFlush(addParentsRelationship(orgDTO.getParentId(), org)));
            }
            throw new ServiceException("操作太频繁，请稍后再试！");
        } catch (Exception e) {
            throw new RuntimeException(e);
        } finally {
            this.unLock(lock);
        }
    }

    @Transactional
    @CacheEvict(cacheNames = CacheNames.CACHE_ORG_LIST, allEntries = true)
    public Org addOrg(Org org) {
        Optional<Org> orgOptional = repository.findById(Constants.ROOT_ORG_ID);
        if (orgOptional.isEmpty()) {
            return repository.saveAndFlush(addParentsRelationship(null, org));
        }
        return null;
    }

    @Transactional
    @Caching(evict = {
            @CacheEvict(key = "#id"),
            @CacheEvict(key = "'exists::' + #id"),
            @CacheEvict(cacheNames = CacheNames.CACHE_DEPT, allEntries = true),
            @CacheEvict(cacheNames = CacheNames.CACHE_ORG_LIST, allEntries = true)
    })
    public void deleteOrg(Long id) {
        Optional<Org> orgOptional = repository.findById(id);
        if (orgOptional.isEmpty()) {
            throw new ServiceException("删除的组织[{0}]不存在！", id);
        }
        Org org = orgOptional.get();
        if (relationRepository.existsByParentAndChildNot(org, org)) {
            throw new ServiceException("存在下级组织关联，不能删除！");
        }
        if (deptService.existsDeptByOrg(org)) {
            throw new ServiceException("组织已经被部门关联，不能删除！");
        }
        if (userService.existsUserByOrg(org)) {
            throw new ServiceException("组织下有关联用户，不能删除！");
        }
        repository.delete(org);
    }

    @Transactional
    @Caching(evict = {
            @CacheEvict(cacheNames = CacheNames.CACHE_DEPT, allEntries = true),
            @CacheEvict(cacheNames = CacheNames.CACHE_USER, allEntries = true),
            @CacheEvict(cacheNames = CacheNames.CACHE_USER_LIST, allEntries = true),
            @CacheEvict(cacheNames = CacheNames.CACHE_ORG, allEntries = true),
            @CacheEvict(cacheNames = CacheNames.CACHE_ORG_LIST, allEntries = true)
    })
    public OrgVO updateOrg(Long id, OrgDTO orgDTO, MultipartFile logo) {
        Optional<Org> orgOptional = repository.findById(id);
        if (orgOptional.isEmpty()) {
            throw new ServiceException("更新的组织[{0}]不存在！", id);
        }
        boolean updateName = false;
        Org org = orgOptional.get();
        if (StringUtils.hasText(orgDTO.getName()) && !orgDTO.getName().equals(org.getName())) {
            if (repository.existsByName(orgDTO.getName())) {
                throw new ServiceException("更新的组织名称[{0}]已经存在！", orgDTO.getName());
            }
            updateName = true;
        }
        org = orgConvert.dto2po(orgDTO, org);
        if (Objects.nonNull(logo)) {
            if (StringUtils.hasText(org.getLogo())) {
                if (!minioUtils.removeObject(org.getLogo())) {
                    throw new ServiceException("组织Logo[{0}]删除失败！", org.getLogo());
                }
            }
            org.setLogo(minioUtils.upload(FileConstants.ORG_LOGO, logo));
        }
        if (Objects.nonNull(orgDTO.getParentId()) && (Objects.isNull(org.getParent()) || !orgDTO.getParentId().equals(org.getParent().getId()))) {
            if (Objects.nonNull(org.getParent())) {
                if (!org.getParent().getId().equals(orgDTO.getParentId())) {
                    if (orgDTO.getParentId().intValue() == 0) {
                        clearParentsRelationship(org);
                    } else {
                        updateParentsRelationship(orgDTO.getParentId(), org, true);
                    }
                }
            } else {
                updateParentsRelationship(orgDTO.getParentId(), org, true);
            }
        }
        if (updateName) {
            orgUpdateMqTemplate.convertAndSend(RocketMqConstant.ORG_NAME_UPDATE_TOPIC, OrgBO.builder().id(org.getId()).name(org.getName()).build());
        }
        return orgConvert.po2vo(repository.saveAndFlush(org));
    }

    @Transactional
    public void enabledOrges(List<Long> ids) {
        for (Long id : ids) {
            enabledOrg(id);
        }
    }

    @Transactional
    @Caching(evict = {
            @CacheEvict(cacheNames = CacheNames.CACHE_DEPT, allEntries = true),
            @CacheEvict(cacheNames = CacheNames.CACHE_USER, allEntries = true),
            @CacheEvict(cacheNames = CacheNames.CACHE_USER_LIST, allEntries = true),
            @CacheEvict(cacheNames = CacheNames.CACHE_ORG, allEntries = true),
            @CacheEvict(cacheNames = CacheNames.CACHE_ORG_LIST, allEntries = true)
    })
    public OrgVO enabledOrg(Long id) {
        Optional<Org> orgOptional = repository.findById(id);
        if (orgOptional.isEmpty()) {
            throw new ServiceException("启用的组织[{0}]不存在！", id);
        }
        return orgConvert.po2vo(repository.saveAndFlush(orgOptional.get().setStatus(Status.ENABLED)));
    }

    @Transactional
    public void disabledOrges(List<Long> ids) {
        for (Long id : ids) {
            disabledOrg(id);
        }
    }

    @Transactional
    @Caching(evict = {
            @CacheEvict(cacheNames = CacheNames.CACHE_DEPT, allEntries = true),
            @CacheEvict(cacheNames = CacheNames.CACHE_USER, allEntries = true),
            @CacheEvict(cacheNames = CacheNames.CACHE_USER_LIST, allEntries = true),
            @CacheEvict(cacheNames = CacheNames.CACHE_ORG, allEntries = true),
            @CacheEvict(cacheNames = CacheNames.CACHE_ORG_LIST, allEntries = true)
    })
    public OrgVO disabledOrg(Long id) {
        Optional<Org> orgOptional = repository.findById(id);
        if (orgOptional.isEmpty()) {
            throw new ServiceException("禁用的组织[{0}]不存在！", id);
        }
        return orgConvert.po2vo(repository.saveAndFlush(orgOptional.get().setStatus(Status.DISABLED)));
    }

    private Specification<Org> genSpecification(OrgDTO orgDTO) {
        return (Root<Org> root, CriteriaQuery<?> query, CriteriaBuilder criteriaBuilder) -> {
            Predicate predicate = criteriaBuilder.conjunction();

            if (Long.class != query.getResultType()) {
                root.fetch(Org_.address);
            }

            if (StringUtils.hasText(orgDTO.getName())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.like(root.get(Org_.name), "%" + orgDTO.getName() + "%"));
            }
            if (StringUtils.hasText(orgDTO.getPhone())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.like(root.get(Org_.phone), "%" + orgDTO.getPhone() + "%"));
            }
            if (Objects.nonNull(orgDTO.getParentId())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get(Org_.parent).get(Org_.id), orgDTO.getParentId()));
            }
            if (Objects.nonNull(orgDTO.getStatus())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get(Org_.status), orgDTO.getStatus()));
            }
            if (StringUtils.hasText(orgDTO.getDescription())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.like(root.get(Org_.description), "%" + orgDTO.getDescription() + "%"));
            }
            if (StringUtils.hasText(orgDTO.getDeptName())) {
                Subquery<Long> subQuery = query.subquery(Long.class);
                Root<Dept> subRoot = subQuery.from(Dept.class);
                subQuery.select(subRoot.get(Dept_.org).get(Org_.id)).where(criteriaBuilder.like(subRoot.get(Dept_.name), "%" + orgDTO.getDeptName() + "%"));
                predicate = criteriaBuilder.and(predicate, root.get(Org_.id).in(subQuery));
            }
            if (Objects.nonNull(orgDTO.getAddress())) {
                AddressDTO address = orgDTO.getAddress();
                if (StringUtils.hasText(address.getProvince())) {
                    predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get(Org_.address).get(Address_.province), address.getProvince()));
                }
                if (StringUtils.hasText(address.getCity())) {
                    predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get(Org_.address).get(Address_.city), address.getCity()));
                }
                if (StringUtils.hasText(address.getDistrict())) {
                    predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get(Org_.address).get(Address_.district), address.getDistrict()));
                }
                if (StringUtils.hasText(address.getStreet())) {
                    predicate = criteriaBuilder.and(predicate, criteriaBuilder.like(root.get(Org_.address).get(Address_.street), "%" + address.getStreet() + "%"));
                }
                if (Objects.nonNull(address.getPostcode())) {
                    predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get(Org_.address).get(Address_.postcode), address.getPostcode()));
                }
            }
            List<Long> orgIds = getOrgIds();
            if (Objects.nonNull(orgIds)) {
                query.where(criteriaBuilder.and(predicate, root.get(Org_.id).in(orgIds)));
            }

            query.orderBy(criteriaBuilder.asc(root.get(Org_.seq)));
            query.orderBy(criteriaBuilder.asc(root.get(Org_.id)));
            return query.getRestriction();
        };
    }

    public Optional<Org> findOrg(Long id) {
        return repository.findById(id);
    }

    @Override
    public OrgRelation buildRelation(Org parent, Org child, Integer depth) {
        return OrgRelation.builder().parent(parent).child(child).depth(depth).build();
    }

    @Override
    public OrgVO nodeToVO(Org node) {
        return orgConvert.po2vo(node).setDepts(deptService.generateTreeRelationship(node.getDepts()));
    }
}
