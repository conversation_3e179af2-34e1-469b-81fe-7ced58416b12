package com.genlight.epilcure.service.user.dao.entity;

import com.genlight.epilcure.common.core.dao.entity.TEntity;
import com.genlight.epilcure.common.core.dao.enums.Status;
import com.genlight.epilcure.common.core.dao.generator.annotation.SignOrder;
import jakarta.persistence.*;
import lombok.*;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;
import org.hibernate.annotations.Comment;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import java.util.HashSet;
import java.util.Set;

/**
 * <AUTHOR>
 * @version 1.0.0
 */
@Setter
@Getter
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString
@Entity
@Cacheable
@DynamicInsert
@DynamicUpdate
@SignOrder(0)
@Table(name = "t_permission", indexes = {
        @Index(name = "index_permission_status", columnList = "status")
})
public class Permission extends TEntity {

    @Comment("名称")
    @Column(nullable = false, unique = true, length = 128)
    @SignOrder(1)
    private String name;

    @Comment("代码")
    @Column(nullable = false, unique = true, length = 128)
    @SignOrder(2)
    private String code;

    @Comment("状态（0：禁用，1：启用）")
    @Column(nullable = false)
    @Enumerated(EnumType.ORDINAL)
    @SignOrder(3)
    private Status status;

    @Comment("描述信息")
    @Column(length = 1024)
    @SignOrder(4)
    private String description;

    @ToString.Exclude
    @ManyToOne(optional = false)
    private Res res;

    @ToString.Exclude
    @ManyToOne(optional = false)
    private Action action;

    @Builder.Default
    @ToString.Exclude
    @ManyToMany(mappedBy = "permissions")
    private Set<Role> roles = new HashSet<>();
}
