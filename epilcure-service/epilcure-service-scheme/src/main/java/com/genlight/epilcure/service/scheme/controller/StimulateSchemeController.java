package com.genlight.epilcure.service.scheme.controller;

import com.fasterxml.jackson.annotation.JsonView;
import com.genlight.epilcure.api.scheme.pojo.dto.StimulateSchemeDTO;
import com.genlight.epilcure.api.scheme.pojo.vo.StimulateSchemeVO;
import com.genlight.epilcure.common.core.mvc.response.JsonResult;
import com.genlight.epilcure.common.core.pojo.valid.Add;
import com.genlight.epilcure.service.scheme.service.StimulateSchemeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @Date 2022/6/18 15:46
 * @Version 1.0.0
 **/
@RestController
@RequestMapping("/api/scheme/stimulateSchemes")
@Tag(name = "StimulateSchemeController", description = "刺激方案相关接口")
public class StimulateSchemeController {
    @Resource
    private StimulateSchemeService stimulateSchemeService;

    @PostMapping
    @JsonView(Add.class)
    @Operation(summary = "添加刺激方案")
    public JsonResult<StimulateSchemeVO> add(@Valid @RequestBody StimulateSchemeDTO stimulateSchemeDTO) {
        return JsonResult.ok(stimulateSchemeService.addStimulateScheme(stimulateSchemeDTO));
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "删除刺激方案")
    public JsonResult<String> delete(@PathVariable String id) {
        stimulateSchemeService.deleteById(id);
        return JsonResult.ok("删除刺激方案成功");
    }

    @GetMapping("/{id}")
    @Operation(summary = "根据Id获取刺激方案")
    public JsonResult<StimulateSchemeVO> find(@PathVariable String id) {
        return JsonResult.ok(stimulateSchemeService.findById(id));
    }

    @GetMapping("/findBySchemeId/{schemeId}")
    @Operation(summary = "根据方案id获取刺激方案")
    public JsonResult<StimulateSchemeVO> findBySchemeId(@PathVariable String schemeId) {
        return JsonResult.ok(stimulateSchemeService.findBySchemeId(schemeId));
    }

    @PutMapping("/{id}")
    @Operation(summary = "修改刺激方案信息")
    public JsonResult<StimulateSchemeVO> update(@PathVariable String id, @Valid @RequestBody StimulateSchemeDTO stimulateSchemeDTO) {
        return JsonResult.ok(stimulateSchemeService.update(id, stimulateSchemeDTO));
    }
}
