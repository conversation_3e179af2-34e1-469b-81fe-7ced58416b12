package com.genlight.epilcure.service.scheme.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.genlight.epilcure.api.config.feign.IAlgorithmController;
import com.genlight.epilcure.api.config.pojo.bo.AlgorithmBO;
import com.genlight.epilcure.api.config.pojo.vo.AlgorithmParamVO;
import com.genlight.epilcure.api.config.pojo.vo.AlgorithmVO;
import com.genlight.epilcure.api.device.feign.IBcmController;
import com.genlight.epilcure.api.device.pojo.vo.BcmVO;
import com.genlight.epilcure.api.patient.feign.IPatientDeviceController;
import com.genlight.epilcure.api.patient.pojo.vo.PatientDeviceVO;
import com.genlight.epilcure.api.patient.pojo.vo.SurgeryElectrodeVO;
import com.genlight.epilcure.api.scheme.pojo.dto.DetectionAlgorithmParamDTO;
import com.genlight.epilcure.api.scheme.pojo.dto.DetectionSchemeDTO;
import com.genlight.epilcure.api.scheme.pojo.dto.SchemeDTO;
import com.genlight.epilcure.api.scheme.pojo.vo.DetectionSchemeVO;
import com.genlight.epilcure.common.core.constants.HttpCode;
import com.genlight.epilcure.common.core.service.BaseService;
import com.genlight.epilcure.common.core.service.exception.ArgsException;
import com.genlight.epilcure.common.core.service.exception.ServiceException;
import com.genlight.epilcure.service.scheme.constants.CacheConstants;
import com.genlight.epilcure.service.scheme.dao.entity.DetectionScheme;
import com.genlight.epilcure.service.scheme.dao.entity.Scheme;
import com.genlight.epilcure.service.scheme.dao.repository.DetectionSchemeRepository;
import com.genlight.epilcure.service.scheme.pojo.convert.DetectionSchemeConvert;
import jakarta.annotation.Resource;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.annotation.Caching;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @Date 2022/6/26 11:54
 * @Version 1.0.0
 **/
@Service
@CacheConfig(cacheNames = CacheConstants.CACHE_NAMES_DETECTION_SCHEME)
public class DetectionSchemeService extends BaseService<DetectionScheme, String, DetectionSchemeRepository> {
    @Resource
    private DetectionSchemeConvert detectionSchemeConvert;

    @Resource
    private IAlgorithmController iAlgorithmController;

    @Resource
    private DetectionAlgorithmParamService detectionAlgorithmParamService;
    @Resource
    private IPatientDeviceController iPatientDeviceController;

    @Resource
    private RedissonClient redissonClient;

    @Resource
    private SchemeService schemeService;

    @Resource
    private IBcmController bcmController;

    /**
     * 添加检测方案
     *
     * @param detectionSchemeDTOList
     * @return
     */
    @Transactional
    public List<DetectionSchemeVO> addDetectionScheme(List<DetectionSchemeDTO> detectionSchemeDTOList) {
        ArrayList<DetectionSchemeVO> detectionSchemes = new ArrayList<>();

        Optional<DetectionSchemeDTO> dtoOptional = detectionSchemeDTOList.stream().filter(d -> Objects.nonNull(d.getTouchPoint().getPolarity()) && d.getTouchPoint().getPolarity().size() > 4 && d.getTouchPoint().getPolarity().get(4) != 0).findFirst();
        final int polarity = dtoOptional.isEmpty() ? -1 : dtoOptional.get().getTouchPoint().getPolarity().get(4).intValue();

        RLock obtain = redissonClient.getLock("addDetectionScheme-%s".formatted(Objects.nonNull(detectionSchemeDTOList.get(0).getPatientId()) ? detectionSchemeDTOList.get(0).getPatientId() : detectionSchemeDTOList.get(0).getBcmMac()));
        try {
            if (obtain.tryLock(3000, 3000, TimeUnit.MILLISECONDS)) {
                detectionSchemeDTOList.forEach(detectionSchemeDTO -> {
                    if (Objects.nonNull(detectionSchemeDTO.getId())) {
                        if (!detectionSchemeDTO.getId().equals("0")) {
                            if (repository.existsById(detectionSchemeDTO.getId())) {
                                throw new ArgsException("检测方案[{0}]已存在", HttpCode.I_SCHEME_ID_EXISTS, detectionSchemeDTO.getId());
                            }
                        } else {
                            detectionSchemeDTO.setId(null);
                        }

                    }

                    if (Objects.isNull(detectionSchemeDTO.getPatientId())) {
                        BcmVO bcmVO = bcmController.findByMac(detectionSchemeDTO.getBcmMac());
                        detectionSchemeDTO.setPatientId(bcmVO.getPatientId());
                    }


                    PatientDeviceVO patientDeviceVO = iPatientDeviceController.findByPatientId(detectionSchemeDTO.getPatientId());

                    Optional<SurgeryElectrodeVO> electrodeVOOptional = patientDeviceVO.getSurgeryElectrodes().stream().filter(e -> e.getPosition().equals(detectionSchemeDTO.getTouchPoint().getPosition())).findFirst();

                    if (electrodeVOOptional.isEmpty()) {
                        throw new ArgsException("此通道[{0}]没有设备", detectionSchemeDTO.getTouchPoint().getPosition());
                    }

                    SurgeryElectrodeVO surgeryElectrodeVO = electrodeVOOptional.get();
                    detectionSchemeDTO.setLocation(surgeryElectrodeVO.getLocation());
                    DetectionScheme detectionScheme = detectionSchemeConvert.dto2po(detectionSchemeDTO);
                    detectionScheme.setUserId(getUserId());
                    detectionScheme = repository.save(detectionScheme);
                    DetectionSchemeVO detectionSchemeVO = detectionSchemeConvert.po2vo(detectionScheme);
                    if (Objects.nonNull(detectionSchemeDTO.getAlgorithmId())) {
                        if (Objects.isNull(detectionSchemeDTO.getDetectionAlgorithmParams())) {
                            throw new ArgsException("算法参数不能为空");
                        }

                        AlgorithmVO algorithmVO = iAlgorithmController.find(detectionSchemeDTO.getAlgorithmId());

                        int positive = 0;
                        int negative = 0;
                        for (Integer integer : detectionSchemeDTO.getTouchPoint().getPolarity()) {
                            if (integer.equals(1)) {
                                positive++;
                            } else if (integer.equals(2)) {
                                negative++;
                            }
                        }

                        if (positive != 1 || negative != 1) {
                            throw new ArgsException("需要分配一对(+,-)极");
                        }

                        if (polarity != -1 && detectionSchemeDTO.getTouchPoint().getPolarity().size() > 4) {
                            if (detectionSchemeDTO.getTouchPoint().getPolarity().get(4) != polarity && detectionSchemeDTO.getTouchPoint().getPolarity().get(4) != 0) {
                                throw new ArgsException("多通道电极壳体极性不能相反");
                            }
                        }

                        for (AlgorithmParamVO algorithmParam : algorithmVO.getAlgorithmParams()) {
                            Optional<DetectionAlgorithmParamDTO> optional = detectionSchemeDTO.getDetectionAlgorithmParams().stream().filter(a -> a.getAlgorithmParamId().equals(algorithmParam.getId())).findFirst();
                            if (optional.isEmpty()) {
                                throw new ArgsException("算法[{0}]需要参数[{1}]", algorithmVO.getName(), algorithmParam.getId());
                            }
                            try {
                                if (Objects.isNull(detectionSchemeVO.getDetectionAlgorithmParams())) {
                                    detectionSchemeVO.setDetectionAlgorithmParams(new ArrayList<>());
                                }
                                detectionSchemeVO.getDetectionAlgorithmParams().add(detectionAlgorithmParamService.addAlgorithmParam(detectionScheme, algorithmParam, optional.get()));
                            } catch (JsonProcessingException e) {
                                throw new RuntimeException(e);
                            }
                        }
                        detectionScheme.setAlgorithmName(algorithmVO.getName());

                    }
                    detectionSchemes.add(detectionSchemeVO);
                });
                return detectionSchemes;
            } else {
                throw new ServiceException("服务忙,请稍后再试");
            }
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        } finally {
            unLock(obtain);
        }

    }

    @Transactional
    @CacheEvict(key = "#p0")
    public boolean deleteDetectionScheme(String id) {
        if (!repository.existsById(id)) {
            throw new ArgsException("检测方案Id[{0}]不存在", id);
        }
        repository.deleteById(id);
        return true;
    }

    @Transactional(readOnly = true)
    @Cacheable(key = "#p0")
    public DetectionSchemeVO find(String id) {
        Optional<DetectionScheme> optional = repository.findById(id);
        if (optional.isEmpty()) {
            throw new ArgsException("检测方案Id[{0}]不存在");
        }
        return detectionSchemeConvert.po2vo(optional.get());
    }

    @Transactional(readOnly = true)
    @Cacheable(cacheNames = CacheConstants.CACHE_NAMES_DETECTION_SCHEME_FINDS)
    public List<DetectionSchemeVO> findsBySchemeId(String schemeId) {
        return detectionSchemeConvert.po2voBySummary(repository.findBySchemesId(schemeId));
    }

    @Transactional(readOnly = true)
    public List<DetectionSchemeVO> findNearestByPatientId(SchemeDTO schemeDTO) {
        Optional<Scheme> lastScheme = schemeService.fetchLastScheme(schemeDTO);
        if (lastScheme.isEmpty()) {
            return null;
        }
        return detectionSchemeConvert.po2vo(lastScheme.get().getDetectionSchemes().stream().toList());
    }

    @Transactional
    @Caching(evict = {
            @CacheEvict(key = "#p0")
    })
    public DetectionSchemeVO update(String id, DetectionSchemeDTO detectionSchemeDTO) throws JsonProcessingException {
        Optional<DetectionScheme> optional = repository.findById(id);
        if (optional.isEmpty()) {
            throw new ArgsException("检测方案Id[{0}]不存在");
        }


        AlgorithmVO algorithmVO = iAlgorithmController.find(detectionSchemeDTO.getAlgorithmId());

        DetectionScheme detectionScheme = optional.get();
        detectionSchemeConvert.dto2po(detectionSchemeDTO, detectionScheme);
        DetectionSchemeVO detectionSchemeVO = detectionSchemeConvert.po2vo(repository.save(detectionScheme));

        for (AlgorithmParamVO algorithmParam : algorithmVO.getAlgorithmParams()) {
            Optional<DetectionAlgorithmParamDTO> optionalParam = detectionSchemeDTO.getDetectionAlgorithmParams().stream().filter(a -> a.getAlgorithmParamId() == algorithmParam.getId()).findFirst();
            if (optionalParam.isEmpty()) {
                continue;
            }
            if (Objects.isNull(detectionSchemeVO.getDetectionAlgorithmParams())) {
                detectionSchemeVO.setDetectionAlgorithmParams(new ArrayList<>());
            }
            detectionSchemeVO.getDetectionAlgorithmParams().add(detectionAlgorithmParamService.updateAlgorithmParam(detectionScheme, algorithmParam, optionalParam.get()));
        }


        return detectionSchemeConvert.po2vo(repository.save(detectionScheme));
    }

    @Transactional
    @CacheEvict(key = "#p0")
    public DetectionSchemeVO updateScheme(String id, Scheme scheme) {
        Optional<DetectionScheme> optional = repository.findById(id);
        if (optional.isEmpty()) {
            throw new ArgsException("检测方案Id[{0}]不存在", id);
        }
        DetectionScheme detectionScheme = optional.get();

        scheme.getDetectionSchemes().add(detectionScheme);
        return detectionSchemeConvert.po2vo(repository.save(detectionScheme));
    }

    @Caching(evict = {
            @CacheEvict(cacheNames = {CacheConstants.CACHE_NAMES_DETECTION_SCHEME_FINDS, CacheConstants.CACHE_NAMES_DETECTION_SCHEME}, allEntries = true)
    })
    @Transactional
    public boolean updateAlgorithmName(AlgorithmBO algorithmBO) {
        List<DetectionScheme> schemeList = repository.findByAlgorithmId(algorithmBO.getId());
        schemeList.stream().forEach(s -> {
            s.setAlgorithmName(algorithmBO.getName());
        });
        repository.saveAll(schemeList);
        return true;
    }

}
