package com.genlight.epilcure.service.device.dao.entity;

import com.genlight.epilcure.api.device.enums.DeviceStatus;
import com.genlight.epilcure.common.core.dao.entity.TEntity;
import com.genlight.epilcure.common.core.dao.generator.annotation.SignOrder;
import jakarta.persistence.*;
import lombok.*;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;
import org.hibernate.annotations.Comment;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import java.util.Date;

@Setter
@Getter
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@Cacheable
@DynamicInsert
@DynamicUpdate
@MappedSuperclass
public class DeviceBase<T extends ModelBase> extends TEntity {

    @Comment("病人Id")
    @Column
    @SignOrder(1)
    private Long patientId;

    @Comment("设备状态")
    @Column(nullable = false)
    @SignOrder(2)
    private DeviceStatus deviceStatus;

    @Comment("批号")
    @Column(nullable = false)
    @SignOrder(3)
    private String batchNo;

    @Comment("录入者")
    @Column(nullable = false)
    @SignOrder(4)
    private Long userId;

    @Comment("用户手机号码")
    @Column(nullable = false)
    @SignOrder(5)
    private String userMobile;

    @Comment("签名")
    @Column(nullable = false, unique = true)
    @SignOrder(6)
    private String sn;

    @Comment("型号Id")
    @ManyToOne(fetch = FetchType.LAZY)
    @ToString.Exclude
    private T model;

    @Comment("植入时间")
    @SignOrder(7)
    private Date implantationDate;
}

