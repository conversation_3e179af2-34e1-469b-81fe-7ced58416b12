package com.genlight.epilcure.service.device.feign;

import com.genlight.epilcure.api.device.feign.IBppModelController;
import com.genlight.epilcure.api.device.pojo.dto.BppModelDTO;
import com.genlight.epilcure.api.device.pojo.vo.BppModelVO;
import com.genlight.epilcure.service.device.service.BppModelService;
import io.swagger.v3.oas.annotations.Hidden;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController(value = "feignBppModelController")
@RequestMapping
@Hidden
public class BppModelController implements IBppModelController {
    @Resource
    private BppModelService bppModelService;

    @Override
    public Boolean exist(Long id) {
        return bppModelService.exist(id);
    }

    @Override
    public List<BppModelVO> finds(Long diseaseId) {
        return bppModelService.findsModel(BppModelDTO.builder().diseaseId(diseaseId).build());
    }
}
