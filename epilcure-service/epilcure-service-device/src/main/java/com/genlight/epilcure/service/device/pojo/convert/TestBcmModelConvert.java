package com.genlight.epilcure.service.device.pojo.convert;

import com.genlight.epilcure.api.device.pojo.dto.TestBcmModelDTO;
import com.genlight.epilcure.api.device.pojo.vo.TestBcmModelVO;
import com.genlight.epilcure.common.core.pojo.convert.BaseConvert;
import com.genlight.epilcure.service.device.dao.entity.TestBcmModel;
import org.mapstruct.Mapper;

@Mapper(config = BaseConvert.class)
public interface TestBcmModelConvert extends BaseConvert<TestBcmModel, TestBcmModelVO, TestBcmModelDTO> {

}
