package com.genlight.epilcure.service.device.pojo.dto;

import com.fasterxml.jackson.annotation.JsonView;
import com.genlight.epilcure.common.core.pojo.dto.BaseDTO;
import com.genlight.epilcure.common.core.pojo.valid.Add;
import com.genlight.epilcure.common.core.pojo.valid.Find;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
@SuperBuilder
@NoArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@Schema(name = "BcmRechargeDTO", description = "刺激器充电信息")
public class BcmRechargeDTO extends BaseDTO {

    @Schema(description = "充电日期")
    @NotNull(message = "充电日期不能为空", groups = {Add.class})
    private Date rechargeDate;

    @Schema(description = "充电结束时间")
    @NotNull(message = "充电结束日期不能为空", groups = Add.class)
    private Date rechargeEndDate;

    @Schema(description = "开始电量")
    @NotNull(message = "开始电量不能为空", groups = Add.class)
    private Integer rechargeStart;

    @Schema(description = "结束电量")
    @NotNull(message = "结束电量不能为空", groups = Add.class)
    private Integer rechargeEnd;

    @Schema(description = "电压开始")
    private Integer voltageStart;

    @Schema(description = "电压结束")
    private Integer voltageEnd;

    @Schema(description = "刺激器签名")
    @NotNull(message = "刺激器签名不能为空", groups = {Add.class, Find.class})
    private String bcmSn;

    @Schema(description = "开始时间")
    @JsonView(Find.class)
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
    private Date startDate;
    @Schema(description = "开始时间")
    @JsonView(Find.class)
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
    private Date endDate;
}
