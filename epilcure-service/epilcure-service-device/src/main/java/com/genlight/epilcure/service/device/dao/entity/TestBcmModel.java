package com.genlight.epilcure.service.device.dao.entity;

import com.genlight.epilcure.common.core.dao.generator.annotation.SignOrder;
import jakarta.persistence.Cacheable;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;


@Setter
@Getter
@SuperBuilder
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@Entity
@Cacheable
@DynamicInsert
@DynamicUpdate
@SignOrder(0)
@Table(name = "t_test_bcm_model")
public class TestBcmModel extends ModelBase {
}
