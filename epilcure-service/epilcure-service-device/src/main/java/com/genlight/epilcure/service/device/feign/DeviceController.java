package com.genlight.epilcure.service.device.feign;

import com.genlight.epilcure.api.device.feign.IDeviceController;
import com.genlight.epilcure.api.device.pojo.bo.DeviceBO;
import com.genlight.epilcure.service.device.service.DeviceFacadeService;
import io.swagger.v3.oas.annotations.Hidden;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController(value = "feignDeviceController")
@RequestMapping
@Hidden
public class DeviceController implements IDeviceController {

    @Resource
    private DeviceFacadeService deviceFacadeService;

    @Override
    public DeviceBO updateDevice(DeviceBO deviceBO) {
        return deviceFacadeService.updateDevice(deviceBO);
    }

    @Override
    public DeviceBO findDevice(DeviceBO deviceBO) {
        return deviceFacadeService.findDevice(deviceBO);
    }

    @Override
    public void expireDevice(DeviceBO deviceBO) {
        deviceFacadeService.destroyOtherDevice(deviceBO);
    }
}
