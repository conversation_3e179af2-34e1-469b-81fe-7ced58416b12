package com.genlight.epilcure.service.device.service;

import com.genlight.epilcure.api.config.feign.IDiseaseController;
import com.genlight.epilcure.api.device.pojo.dto.TestBcmDTO;
import com.genlight.epilcure.api.device.pojo.dto.TestBcmModelDTO;
import com.genlight.epilcure.api.device.pojo.vo.TestBcmModelVO;
import com.genlight.epilcure.api.device.pojo.vo.TestBcmVO;
import com.genlight.epilcure.api.patient.feign.IPatientDeviceController;
import com.genlight.epilcure.common.core.service.exception.ArgsException;
import com.genlight.epilcure.common.core.service.exception.ServiceException;
import com.genlight.epilcure.service.device.dao.entity.TestBcm;
import com.genlight.epilcure.service.device.dao.entity.TestBcmModel;
import com.genlight.epilcure.service.device.dao.repository.TestBcmModelRepository;
import com.genlight.epilcure.service.device.dao.repository.TestBcmRepository;
import com.genlight.epilcure.service.device.pojo.convert.TestBcmConvert;
import com.genlight.epilcure.service.device.pojo.convert.TestBcmModelConvert;
import jakarta.annotation.Resource;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;


@Service
@CacheConfig(cacheNames = "bcm")
public class TestBcmService extends DeviceService<TestBcmModel, TestBcm, TestBcmModelVO, TestBcmModelDTO, TestBcmVO, TestBcmDTO, TestBcmModelRepository, TestBcmRepository, TestBcmModelConvert, TestBcmConvert> {


    @Resource
    private RedissonClient redissonClient;

    @Resource
    private IPatientDeviceController iPatientDeviceController;

    @Resource
    private IDiseaseController iDiseaseController;

    @Override
    @Transactional
    public TestBcmVO addDevice(TestBcmDTO bcmDTO) {
        RLock lock = redissonClient.getLock("addBCMDevice-%s".formatted(bcmDTO.getMac()));
        try {
            if (lock.tryLock(2, 2, TimeUnit.SECONDS)) {
                boolean b = repository.existsByMac(bcmDTO.getMac());
                Assert.isTrue(!b, "mac地址已存在");
                return super.addDevice(bcmDTO);
            }
            throw new ServiceException("操作太频繁，请稍后再试！");
        } catch (Exception e) {
            throw new ArgsException(e.getMessage());
        } finally {
            this.unLock(lock);
        }
    }

    @Transactional
    public TestBcmVO updateRecharge(String bcmSn, Short count) {
        TestBcmVO bcmVO = findBySn(bcmSn);
        if (!bcmVO.getPatientId().equals(getUserId())) {
            throw new ArgsException("此刺激器不属于[{0}]", getUserId());
        }

        if (bcmVO.getRechargeCount() > count) {
            throw new ArgsException("充电次数只能递增");
        }
        return updateDevice(bcmVO.getId(), TestBcmDTO.builder().rechargeCount(count).build());
    }

    @Transactional(readOnly = true)
    public TestBcmVO findByMac(String mac) {

        Optional<TestBcm> optional = repository.findByMac(mac);
        if (optional.isEmpty()) {
            throw new ArgsException("刺激器Mac[{0}]不存在", mac);
        }

        return convert.po2vo(optional.get());
    }

    @Transactional(readOnly = true)
    public void checkBcmSn(String bcmSn) {
        if (!bcmSn.contains("TS"))
            bcmSn = "TS" + bcmSn;
        TestBcmVO bcmVO = findBySn(bcmSn);
        if (Objects.isNull(bcmVO)) {
            throw new ServiceException("刺激器序列号不存在");
        }
    }
}
