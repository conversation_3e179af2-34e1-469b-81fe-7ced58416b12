package com.genlight.epilcure.service.device.service;

import com.genlight.epilcure.api.device.pojo.dto.BppParamDTO;
import com.genlight.epilcure.api.device.pojo.vo.BppParamVO;
import com.genlight.epilcure.api.device.pojo.vo.BppVO;
import com.genlight.epilcure.common.core.dao.enums.Status;
import com.genlight.epilcure.common.core.service.BaseService;
import com.genlight.epilcure.service.device.dao.entity.Bpp;
import com.genlight.epilcure.service.device.dao.entity.BppParam;
import com.genlight.epilcure.service.device.dao.repository.BppParamRepository;
import com.genlight.epilcure.service.device.pojo.convert.BppParamConvert;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Service
public class BppParamService extends BaseService<BppParam, Long, BppParamRepository> {
    @Resource
    private BppParamConvert bcmParamConvert;
    @Resource
    private BppService bppService;

    @Transactional
    public BppParamVO addBcmParam(BppParamDTO bcmParamDTO) {
        BppVO bppVO = bppService.findBySn(bcmParamDTO.getBppSn());

        List<BppParam> list = repository.findByBppIdAndStatus(bppVO.getId(), Status.ENABLED);
        if (Objects.nonNull(list))
            list.forEach(b -> b.setStatus(Status.DISABLED));

        BppParam bppParam = bcmParamConvert.dto2po(bcmParamDTO);
        bppParam.setBpp(Bpp.builder().id(bppVO.getId()).build());
        bppParam.setUserId(getUserId());
        return bcmParamConvert.po2vo(repository.save(bppParam));
    }

    @Transactional
    public BppParamVO findBySn(String sn) {
        BppVO bppVO = bppService.findBySn(sn);
        Optional<BppParam> optional = repository.findByBppIdAndStatus(bppVO.getId(), Status.ENABLED).stream().findFirst();
        if (optional.isEmpty()) {
            return null;
        }

        return bcmParamConvert.po2vo(optional.get());

    }
}
