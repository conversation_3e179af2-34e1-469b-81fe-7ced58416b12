package com.genlight.epilcure.service.device.pojo.dto;

import com.genlight.epilcure.common.core.pojo.dto.BaseDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;

import java.util.Date;


@Data
@SuperBuilder
@NoArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@Schema(name = "RechargeCountLogDTO", description = "刺激器充电信息")
public class RechargeCountLogDTO extends BaseDTO {

    private String bcmSn;

    private Long count;

    private Date readDate;
}
