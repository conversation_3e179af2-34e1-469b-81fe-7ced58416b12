package com.genlight.epilcure.service.device.service;

import com.genlight.epilcure.api.device.pojo.dto.TestBcmModelDTO;
import com.genlight.epilcure.api.device.pojo.vo.TestBcmModelVO;
import com.genlight.epilcure.service.device.dao.entity.TestBcmModel;
import com.genlight.epilcure.service.device.dao.repository.TestBcmModelRepository;
import com.genlight.epilcure.service.device.pojo.convert.TestBcmModelConvert;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.stereotype.Service;

@Service
@CacheConfig(cacheNames = "testBcmModel")
public class TestBcmModelService extends ModelService<TestBcmModelRepository, TestBcmModel, TestBcmModelDTO, TestBcmModelVO, TestBcmModelConvert> {
}
