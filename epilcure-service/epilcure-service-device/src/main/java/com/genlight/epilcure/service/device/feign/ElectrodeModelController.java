package com.genlight.epilcure.service.device.feign;

import com.genlight.epilcure.api.device.feign.IElectrodeModelController;
import com.genlight.epilcure.api.device.pojo.dto.ElectrodeModelDTO;
import com.genlight.epilcure.api.device.pojo.vo.ElectrodeModelVO;
import com.genlight.epilcure.service.device.service.ElectrodeModelService;
import io.swagger.v3.oas.annotations.Hidden;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController(value = "feignElectordeModelController")
@RequestMapping
@Hidden
public class ElectrodeModelController implements IElectrodeModelController {
    @Resource
    private ElectrodeModelService electrodeModelService;

    @Override
    public Boolean exist(Long id) {
        return electrodeModelService.exist(id);
    }

    @Override
    public ElectrodeModelVO find(Long id) {
        return electrodeModelService.getModel(id);
    }

    @Override
    public List<ElectrodeModelVO> finds(Long diseaseId) {
        return electrodeModelService.findsModel(ElectrodeModelDTO.builder().diseaseId(diseaseId).build());
    }
}
