package com.genlight.epilcure.service.device.feign;

import com.genlight.epilcure.api.device.feign.IBcmController;
import com.genlight.epilcure.api.device.pojo.dto.BcmDTO;
import com.genlight.epilcure.api.device.pojo.vo.BcmVO;
import com.genlight.epilcure.service.device.service.BcmService;
import io.swagger.v3.oas.annotations.Hidden;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @Date 2022/6/5 21:36
 * @Version 1.0.0
 **/
@RestController(value = "feignBcmController")
@RequestMapping
@Hidden
public class BcmController implements IBcmController {
    @Resource
    private BcmService bcmService;

    @Override
    public BcmVO find(Long id) {
        return bcmService.findById(id);
    }

    @Override
    public BcmVO find(Long id, Long modelId) {
        return bcmService.findByIdAndModel(id, modelId);
    }

    @Override
    public BcmVO find(String bcmSn) {
        return bcmService.findBySn(bcmSn);
    }

    @Override
    public BcmVO findByMac(String bcmMac) {
        return bcmService.findByMac(bcmMac);
    }

    @Override
    public Boolean exist(Long id) {
        return bcmService.existDevice(id);
    }

    @Override
    public BcmVO update(Long id, BcmDTO bcmDTO) {
        return bcmService.updateDevice(id, bcmDTO);
    }
}
