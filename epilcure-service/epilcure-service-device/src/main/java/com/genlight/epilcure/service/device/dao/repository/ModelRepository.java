package com.genlight.epilcure.service.device.dao.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.repository.NoRepositoryBean;

@NoRepositoryBean
public interface ModelRepository<ModelBase extends com.genlight.epilcure.service.device.dao.entity.ModelBase, ID> extends JpaRepository<ModelBase, ID>, JpaSpecificationExecutor<ModelBase> {
    boolean existsByModel(String model);
}
