package com.genlight.epilcure.service.device.dao.repository;

import com.genlight.epilcure.api.device.enums.DeviceStatus;
import com.genlight.epilcure.api.device.pojo.vo.DeviceGroupBatchNoVO;
import com.genlight.epilcure.service.device.dao.entity.Electrode;
import com.genlight.epilcure.service.device.pojo.vo.DeviceStatisticsVO;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;

import java.util.Date;
import java.util.List;

public interface ElectrodeRepository extends DeviceRepository<Electrode, Long> {

    @Modifying
    @Query("update Electrode set deviceStatus=:deviceStatus where patientId=:patientId and deviceStatus=com.genlight.epilcure.api.device.enums.DeviceStatus.used and id not in(:ids)")
    void expireDevice(DeviceStatus deviceStatus, Long patientId, List<Long> ids);

    @Query(value = "select new com.genlight.epilcure.api.device.pojo.vo.DeviceGroupBatchNoVO(t.batchNo," +
            "(select count(c) from Electrode c where c.batchNo = t.batchNo and (c.deviceStatus=0 or c.deviceStatus=1 or c.deviceStatus=4))" +
            ",(select count(c) from Electrode c where c.batchNo = t.batchNo and (c.deviceStatus=0))" +
            ",(select count(c) from Electrode c where c.batchNo = t.batchNo and (c.deviceStatus=1))" +
            ",(select count(c) from Electrode c where c.batchNo = t.batchNo and (c.deviceStatus=4)))" +
            "from Electrode t " +
            "where 1=1 and (:modelId is null or t.model.id =:modelId)" +
            "group by t.batchNo")
    @Override
    List<DeviceGroupBatchNoVO> getGroupBatchNoVOs(Long modelId);

    @Query(value = "select new com.genlight.epilcure.service.device.pojo.vo.DeviceStatisticsVO(b.model.model,count(b)) " +
            "from Electrode b " +
            "join ElectrodeModel em on em.id=b.model.id where b.patientId <> 0 and (b.updateTime>:startDate or :startDate is null) and (b.updateTime<:endDate or :endDate is null) and em.diseaseId=:diseaseId " +
            "group by b.model.id")
    @Override
    List<DeviceStatisticsVO> statistics(Date startDate, Date endDate, Long diseaseId);

    @Query("select t.batchNo from Electrode t where 1=1 and (:modelId is null or :modelId=t.model.id) group by t.batchNo")
    @Override
    List<String> findGroupByBatchNo(Long modelId);
}
