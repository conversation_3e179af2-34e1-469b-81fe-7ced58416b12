package com.genlight.epilcure.service.device.dao.entity;

import com.genlight.epilcure.api.device.pojo.enums.ElectrodeType;
import com.genlight.epilcure.common.core.dao.generator.annotation.SignOrder;
import jakarta.persistence.*;
import lombok.*;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;
import org.hibernate.annotations.Comment;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

@Setter
@Getter
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@Entity
@Cacheable
@DynamicInsert
@DynamicUpdate
@SignOrder(0)
@Table(name = "t_electrode_model", uniqueConstraints = {@UniqueConstraint(name = "idx_u_model", columnNames = "model")})
public class ElectrodeModel extends ModelBase {
    @Comment("触点面积")
    @Column(nullable = false)
    @SignOrder(5)
    private Float touchArea;
    @Comment("备注")
    @Column(length = 2048)
    @SignOrder(6)
    private String remark;
    @Enumerated(EnumType.ORDINAL)
    @Comment("0:皮层,1:深度")
    @Column(nullable = false)
    @SignOrder(7)
    private ElectrodeType type;
}
