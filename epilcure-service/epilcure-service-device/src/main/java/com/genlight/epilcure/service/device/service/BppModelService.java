package com.genlight.epilcure.service.device.service;

import com.genlight.epilcure.api.device.pojo.dto.BppModelDTO;
import com.genlight.epilcure.api.device.pojo.vo.BppModelVO;
import com.genlight.epilcure.service.device.dao.entity.BppModel;
import com.genlight.epilcure.service.device.dao.repository.BppModelRepository;
import com.genlight.epilcure.service.device.pojo.convert.BppModelConvert;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.stereotype.Service;

@Service
@CacheConfig(cacheNames = "bppModel")
public class BppModelService extends ModelService<BppModelRepository, BppModel, BppModelDTO, BppModelVO, BppModelConvert> {
}
