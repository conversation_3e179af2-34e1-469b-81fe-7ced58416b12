package com.genlight.epilcure.service.device.dao.repository;

import com.genlight.epilcure.api.device.enums.DeviceStatus;
import com.genlight.epilcure.api.device.pojo.vo.DeviceGroupBatchNoVO;
import com.genlight.epilcure.service.device.dao.entity.DeviceBase;
import com.genlight.epilcure.service.device.pojo.vo.DeviceStatisticsVO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.repository.NoRepositoryBean;

import java.util.Date;
import java.util.List;
import java.util.Optional;

@NoRepositoryBean
public interface DeviceRepository<T extends DeviceBase, ID> extends JpaRepository<T, ID>, JpaSpecificationExecutor<T> {
    T getByPatientIdAndDeviceStatus(Long patientId, DeviceStatus deviceStatus);

    Optional<T> findByIdAndModelId(Long id, Long modelId);

    Optional<T> findBySn(String sn);

    List<DeviceGroupBatchNoVO> getGroupBatchNoVOs(Long modelId);

    List<DeviceStatisticsVO> statistics(Date startDate, Date endDate, Long diseaseId);

    List<String> findGroupByBatchNo(Long modelId);

    void expireDevice(DeviceStatus deviceStatus, Long patientId, List<Long> ids);
}
