package com.genlight.epilcure.service.device.dao.entity;

import com.genlight.epilcure.common.core.dao.entity.TEntity;
import com.genlight.epilcure.common.core.dao.generator.annotation.SignOrder;
import jakarta.persistence.Entity;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import lombok.*;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;
import org.hibernate.annotations.Comment;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import java.util.Date;

@Setter
@Getter
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@SignOrder(0)
@Entity
@DynamicInsert
@DynamicUpdate
@Table(name = "t_recharge_count_log")
public class RechargeCountLog extends TEntity {
    @ManyToOne
    private Bcm bcm;

    @Comment("充电次数")
    private Long count;

    @Comment("读取时间")
    private Date readTime;
}
