package com.genlight.epilcure.service.device.service;

import com.genlight.epilcure.api.device.pojo.vo.BcmVO;
import com.genlight.epilcure.common.core.service.BaseService;
import com.genlight.epilcure.service.device.constants.CacheConstants;
import com.genlight.epilcure.service.device.dao.entity.Bcm;
import com.genlight.epilcure.service.device.dao.entity.BcmRecharge;
import com.genlight.epilcure.service.device.dao.entity.BcmRecharge_;
import com.genlight.epilcure.service.device.dao.entity.Bcm_;
import com.genlight.epilcure.service.device.dao.repository.BcmRechargeRepository;
import com.genlight.epilcure.service.device.pojo.convert.BcmRechargeConvert;
import com.genlight.epilcure.service.device.pojo.dto.BcmRechargeDTO;
import com.genlight.epilcure.service.device.pojo.vo.BcmRechargeStatsVO;
import com.genlight.epilcure.service.device.pojo.vo.BcmRechargeVO;
import jakarta.annotation.Resource;
import jakarta.persistence.criteria.Predicate;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

@Service
@CacheConfig(cacheNames = CacheConstants.CACHE_NAMES_RECHARGE)
public class BcmRechargeService extends BaseService<BcmRecharge, Long, BcmRechargeRepository> {
    @Resource
    private BcmService bcmService;

    @Resource
    private BcmRechargeConvert bcmRechargeConvert;

    @Transactional
    @CacheEvict(allEntries = true)
    public BcmRechargeVO add(BcmRechargeDTO bcmRechargeDTO) {
        BcmVO bcmVO = bcmService.findBySn(bcmRechargeDTO.getBcmSn());

        if (repository.existsByBcmIdAndRechargeDate(bcmVO.getId(), bcmRechargeDTO.getRechargeDate())) {
            return null;
        }

        BcmRecharge bcmRecharge = bcmRechargeConvert.dto2po(bcmRechargeDTO);
        bcmRecharge.setBcm(Bcm.builder().id(bcmVO.getId()).build());
        return bcmRechargeConvert.po2vo(repository.save(bcmRecharge));
    }

    @Transactional
    @CacheEvict(allEntries = true)
    public List<BcmRechargeVO> addBatch(List<BcmRechargeDTO> bcmRechargeDTOS) {
        List<BcmRechargeVO> result = new ArrayList<>();
        bcmRechargeDTOS.forEach(bcmRechargeDTO -> {
            BcmRechargeVO add = add(bcmRechargeDTO);
            if (Objects.nonNull(add))
                result.add(add);
        });

        return result;
    }

    @Transactional
    public BcmRechargeStatsVO stats(BcmRechargeDTO bcmRechargeDTO) {
        BcmVO bcmVO = bcmService.findBySn(bcmRechargeDTO.getBcmSn());
        BcmRechargeStatsVO build = BcmRechargeStatsVO.builder()
                .count(Long.valueOf(bcmVO.getRechargeCount()))
                .rechargeDuration(0L)
                .rechargeFrequency(0d)
                .startAvg(0L)
                .endAvg(0L)
                .voltageStartAvg(0)
                .voltageEndAvg(0)
                .build();

        Date now = bcmRechargeDTO.getStartDate();
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(now);
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        Date start = calendar.getTime();

        calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH));
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        Date end = calendar.getTime();

        List<BcmRecharge> all = repository.stats(start, end, bcmRechargeDTO.getBcmSn());
        all.sort((a, b) -> a.getRechargeDate().before(b.getRechargeDate()) ? 1 : -1);

        List<Long> durations = new ArrayList<>();
        for (int i = 0; i < all.size(); i++) {
            // 计算充电频率
            if (i < all.size() - 1) {
                long interval = all.get(i).getRechargeDate().getTime() - all.get(i + 1).getRechargeDate().getTime();
                double days = interval * 0.001d / 60 / 60 / 24;
                build.setRechargeFrequency(build.getRechargeFrequency() + days);
            }


            // 计算充电时长
            long duration = all.get(i).getRechargeEndDate().getTime() - all.get(i).getRechargeDate().getTime();
            durations.add(duration);


            // 计算开始电量
            long startPower = all.get(i).getRechargeStart();
            long endPower = all.get(i).getRechargeEnd();
            build.setStartAvg(build.getStartAvg() + startPower);
            build.setEndAvg(build.getEndAvg() + endPower);

            // 计算电压
            int startVoltage = all.get(i).getVoltageStart();
            int endVoltage = all.get(i).getVoltageEnd();
            build.setVoltageStartAvg(build.getVoltageStartAvg() + startVoltage);
            build.setVoltageEndAvg(build.getVoltageEndAvg() + endVoltage);
        }


        durations.sort((a, b) -> Math.toIntExact(a - b));

        build.setRechargeDuration(durations.get(durations.size() / 2));
        if (all.size() > 1) {
            build.setRechargeFrequency(build.getRechargeFrequency() / (all.size() - 1))
//                    .setRechargeDuration(build.getRechargeDuration() / all.size())
                    .setStartAvg(build.getStartAvg() / all.size())
                    .setEndAvg(build.getEndAvg() / all.size())
                    .setVoltageStartAvg(build.getVoltageStartAvg() / all.size())
                    .setVoltageEndAvg(build.getVoltageEndAvg() / all.size());
        }
        build.setAvgDuration(1d / ((build.getEndAvg() - build.getStartAvg()) * 0.01 / build.getRechargeFrequency()));
        return build;
    }

    public Specification<BcmRecharge> generateJpaSpecification(BcmRechargeDTO bcmRechargeDTO) {
        return (root, query, criteriaBuilder) -> {
            Predicate predicate = criteriaBuilder.conjunction();
            query.distinct(true);
            if (Objects.nonNull(bcmRechargeDTO.getBcmSn())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get(BcmRecharge_.BCM).get(Bcm_.SN), bcmRechargeDTO.getBcmSn()));
            }
            if (Objects.nonNull(bcmRechargeDTO.getStartDate())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.greaterThan(root.get(BcmRecharge_.RECHARGE_DATE), bcmRechargeDTO.getStartDate()));
            }
            if (Objects.nonNull(bcmRechargeDTO.getEndDate())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.lessThan(root.get(BcmRecharge_.RECHARGE_DATE), bcmRechargeDTO.getEndDate()));
            }
            return predicate;
        };
    }


    @Transactional(readOnly = true)
    @Cacheable
    public Page<BcmRechargeVO> finds(BcmRechargeDTO bcmRechargeDTO, Pageable pageable) {
        return bcmRechargeConvert.po2vo(repository.findAll(generateJpaSpecification(bcmRechargeDTO), pageable));
    }
}
