package com.genlight.epilcure.service.device.pojo.vo;

import com.genlight.epilcure.common.core.pojo.vo.BaseVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;

import java.util.Date;

@Data
@SuperBuilder
@NoArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@Schema(description = "刺激器充电信息View")
public class BcmRechargeVO extends BaseVO {
    @Schema(description = "充电日期")
    private Date rechargeDate;

    @Schema(description = "充电结束时间")
    private Date rechargeEndDate;

    @Schema(description = "开始电量")
    private Integer rechargeStart;

    @Schema(description = "结束电量")
    private Integer rechargeEnd;

    @Schema(description = "刺激器Id")
    private Long bcmId;

    @Schema(description = "电压开始")
    private Integer voltageStart;

    @Schema(description = "电压结束")
    private Integer voltageEnd;
}
