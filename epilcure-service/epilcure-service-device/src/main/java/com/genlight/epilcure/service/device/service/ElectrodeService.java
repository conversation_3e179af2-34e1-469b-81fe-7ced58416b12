package com.genlight.epilcure.service.device.service;

import com.genlight.epilcure.api.device.pojo.dto.ElectrodeDTO;
import com.genlight.epilcure.api.device.pojo.dto.ElectrodeModelDTO;
import com.genlight.epilcure.api.device.pojo.vo.ElectrodeModelVO;
import com.genlight.epilcure.api.device.pojo.vo.ElectrodeVO;
import com.genlight.epilcure.service.device.constants.CacheConstants;
import com.genlight.epilcure.service.device.dao.entity.Electrode;
import com.genlight.epilcure.service.device.dao.entity.ElectrodeModel;
import com.genlight.epilcure.service.device.dao.repository.ElectrodeModelRepository;
import com.genlight.epilcure.service.device.dao.repository.ElectrodeRepository;
import com.genlight.epilcure.service.device.pojo.convert.ElectrodeConvert;
import com.genlight.epilcure.service.device.pojo.convert.ElectrodeModelConvert;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.stereotype.Service;

@Service
@CacheConfig(cacheNames = CacheConstants.CACHE_NAMES_ElECTRODE)
public class ElectrodeService extends DeviceService<ElectrodeModel,
        Electrode,
        ElectrodeModelVO,
        ElectrodeModelDTO,
        ElectrodeVO,
        ElectrodeDTO,
        ElectrodeModelRepository,
        ElectrodeRepository,
        ElectrodeModelConvert,
        ElectrodeConvert> {
}
