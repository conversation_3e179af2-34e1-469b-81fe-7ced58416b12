package com.genlight.epilcure.service.device.feign;

import com.genlight.epilcure.api.device.feign.IElectrodeController;
import com.genlight.epilcure.api.device.pojo.dto.ElectrodeDTO;
import com.genlight.epilcure.api.device.pojo.vo.ElectrodeVO;
import com.genlight.epilcure.service.device.service.ElectrodeService;
import io.swagger.v3.oas.annotations.Hidden;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @Date 2022/6/5 21:39
 * @Version 1.0.0
 **/
@RestController(value = "feignElectrodeController")
@RequestMapping
@Hidden
public class ElectrodeController implements IElectrodeController {

    @Resource
    private ElectrodeService electrodeService;

    @Override
    public Boolean exist(Long id) {
        return electrodeService.existDevice(id);
    }

    @Override
    public ElectrodeVO find(Long id) {
        return electrodeService.findById(id);
    }

    @Override
    public ElectrodeVO find(Long id, Long modelId) {
        return electrodeService.findByIdAndModel(id, modelId);
    }

    @Override
    public ElectrodeVO findsBySn(String sn, Integer position) {
        return electrodeService.findBySn(sn, position);
    }

    @Override
    public ElectrodeVO update(Long id, ElectrodeDTO electrodeDTO) {
        return electrodeService.updateDevice(id, electrodeDTO);
    }
}
