package com.genlight.epilcure.service.device.controller;

import com.fasterxml.jackson.annotation.JsonView;
import com.genlight.epilcure.api.device.pojo.dto.ImpedanceLogDTO;
import com.genlight.epilcure.api.device.pojo.vo.ImpedanceLogVO;
import com.genlight.epilcure.common.core.mvc.response.JsonResult;
import com.genlight.epilcure.common.core.pojo.valid.Add;
import com.genlight.epilcure.service.device.service.ImpedanceLogService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/device/impedance")
@Tag(name = "ImpedanceLogController", description = "阻抗日志相关接口")
public class ImpedanceLogController {

    @Resource
    private ImpedanceLogService impedanceLogService;

    @PostMapping
    @Operation(summary = "添加阻抗日志")
    public JsonResult<Void> add(@JsonView(Add.class) @RequestBody ImpedanceLogDTO impedanceLogDTO) {
        impedanceLogService.add(impedanceLogDTO);
        return JsonResult.ok();
    }

    @PostMapping("/list")
    public JsonResult<Void> add(@JsonView(Add.class) @RequestBody List<ImpedanceLogDTO> impedanceLogDTOS) {
        impedanceLogService.add(impedanceLogDTOS);
        return JsonResult.ok();
    }

    @GetMapping
    @Operation(summary = "根据条件查询阻抗日志")
    public JsonResult<Page<ImpedanceLogVO>> finds(ImpedanceLogDTO impedanceLogDTO, Pageable pageable) {
        return JsonResult.ok(impedanceLogService.finds(impedanceLogDTO, pageable));
    }
}
