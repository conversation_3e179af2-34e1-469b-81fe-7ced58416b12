package com.genlight.epilcure.service.device.service;

import com.genlight.epilcure.api.config.feign.IDiseaseController;
import com.genlight.epilcure.common.core.pojo.convert.BaseConvert;
import com.genlight.epilcure.common.core.service.BaseService;
import com.genlight.epilcure.common.core.service.exception.ArgsException;
import com.genlight.epilcure.common.core.service.exception.ServiceException;
import com.genlight.epilcure.common.core.util.MinioUtils;
import com.genlight.epilcure.service.device.constants.FileConstants;
import com.genlight.epilcure.service.device.dao.entity.ModelBase;
import com.genlight.epilcure.service.device.dao.entity.ModelBase_;
import jakarta.annotation.Resource;
import jakarta.persistence.criteria.Predicate;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

@CacheConfig(cacheNames = "device")
public class ModelService<ModelRepository extends com.genlight.epilcure.service.device.dao.repository.ModelRepository<Model, Long>,
        Model extends ModelBase,
        ModelDTO extends com.genlight.epilcure.api.device.pojo.dto.ModelDTO,
        ModelVO extends com.genlight.epilcure.api.device.pojo.vo.ModelVO,
        ModelConvert extends BaseConvert<Model, ModelVO, ModelDTO>> extends BaseService<Model, Long, ModelRepository> {

    @Resource
    protected IDiseaseController iDiseaseController;
    @Autowired
    private ModelConvert modelConvert;
    @Resource
    private MinioUtils minioUtils;

    @Resource
    private RedissonClient redissonClient;

    @Cacheable(key = "#root.targetClass + #p0.toString()")
    public ModelVO getModel(Long id) {
        Optional<Model> optional = repository.findById(id);
        if (optional.isEmpty()) {
            throw new ArgsException("设备模型Id不存在");
        }
        Model bcmModel = optional.get();
        return modelConvert.po2vo(bcmModel);
    }

    @CacheEvict(key = "#root.targetClass")
    @Transactional
    public ModelVO addModel(ModelDTO bcmModelDTO, MultipartFile iconFile) {

        if (!iDiseaseController.existsById(bcmModelDTO.getDiseaseId())) {
            throw new ArgsException("疾病Id[{0}]不存在");
        }

        RLock lock = redissonClient.getLock("addModel-%s".formatted(bcmModelDTO.getModel()));
        try {
            if (lock.tryLock(2, 2, TimeUnit.SECONDS)) {
                if (repository.existsByModel(bcmModelDTO.getModel())) {
                    throw new ArgsException("设备名已存在");
                }

                // todo: 上传文件到oos
                String upload = minioUtils.upload(FileConstants.Device_DIR, iconFile);
                //******
                Model bcmModel = modelConvert.dto2po(bcmModelDTO);
                bcmModel.setIcon(upload);
                return modelConvert.po2vo(repository.saveAndFlush(bcmModel));
            }
            throw new ServiceException("操作太频繁，请稍后再试！");
        } catch (Exception e) {
            throw new RuntimeException(e);
        } finally {
            this.unLock(lock);
        }
    }

    public List<ModelVO> findsModel(ModelDTO modelDTO) {
        Specification<Model> specification = (root, query, criteriaBuilder) -> {
            Predicate predicate = criteriaBuilder.conjunction();
            if (Objects.nonNull(modelDTO.getModel())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.like(root.get("model"), "%" + modelDTO.getModel() + "%"));
            }
            if (Objects.nonNull(modelDTO.getDiseaseId())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("diseaseId"), modelDTO.getDiseaseId()));
            }
            if (Objects.nonNull(modelDTO.getUpdateTime())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.greaterThanOrEqualTo(root.get(ModelBase_.UPDATE_TIME), modelDTO.getUpdateTime()));
            }

            return predicate;
        };

        return modelConvert.po2vo(repository.findAll(specification));
    }


    public Boolean exist(Long id) {
        return repository.existsById(id);
    }

}
