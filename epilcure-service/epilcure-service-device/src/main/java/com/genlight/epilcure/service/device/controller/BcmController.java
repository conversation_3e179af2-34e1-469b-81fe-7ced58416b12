package com.genlight.epilcure.service.device.controller;

import com.fasterxml.jackson.annotation.JsonView;
import com.genlight.epilcure.api.device.pojo.dto.BcmDTO;
import com.genlight.epilcure.api.device.pojo.dto.ModelDTO.BcmValid;
import com.genlight.epilcure.api.device.pojo.vo.BcmVO;
import com.genlight.epilcure.api.device.pojo.vo.DeviceGroupBatchNoVO;
import com.genlight.epilcure.common.core.mvc.response.JsonResult;
import com.genlight.epilcure.common.core.pojo.valid.Add;
import com.genlight.epilcure.common.core.pojo.valid.Update;
import com.genlight.epilcure.service.device.pojo.dto.RechargeCountLogDTO;
import com.genlight.epilcure.service.device.pojo.vo.DeviceStatisticsVO;
import com.genlight.epilcure.service.device.service.BcmService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;

@RestController
@RequestMapping("/api/device/bcms")
@Tag(name = "BcmController", description = "刺激器相关接口")
public class BcmController {
    @Resource
    private BcmService bcmService;

    @GetMapping("/finds")
    @Operation(summary = "根据条件查询Bcm设备")
    public JsonResult<Page<BcmVO>> findBcmsByPageable(BcmDTO bcmDTO, Pageable pageable) {
        return JsonResult.ok(bcmService.searchDevice(bcmDTO, pageable));
    }

    @GetMapping("/findGroupByBatchNo")
    @Operation(summary = "查询所有批号")
    public JsonResult<List<String>> findGroupByBatchNo(@RequestParam(required = false) Long modelId) {
        return JsonResult.ok(bcmService.findGroupByBatchNo(modelId));
    }

    @GetMapping("/mac/{mac}/{diseaseId}")
    @Operation(summary = "根据mac地址查询刺激器信息")
    public JsonResult<BcmVO> findByMac(@PathVariable String mac) {
        return JsonResult.ok(bcmService.findByMac(mac));
    }

    @PostMapping("/updateRelation/{bppSn}/{bcmMac}")
    @Operation(summary = "关联刺激器与程控仪")
    public JsonResult<BcmVO> updateRelation(@PathVariable String bppSn, @PathVariable String bcmMac) {
        return JsonResult.ok(bcmService.updateRelation(bppSn, bcmMac));
    }

    @PostMapping
    @Operation(summary = "新增bcm设备")
    @JsonView(Add.class)
    public JsonResult<BcmVO> addBcm(@JsonView(BcmValid.class)
                                    @Validated(BcmValid.class)
                                    @RequestBody BcmDTO bcmDTO) {
        return JsonResult.ok(bcmService.addDevice(bcmDTO));
    }

    @PutMapping("/audit")
    @Operation(summary = "审核bcm设备")
    public JsonResult<String> auditBcm(@Validated(Update.class) @RequestBody List<BcmDTO> bcmDTOList) {
        bcmService.auditDevice(bcmDTOList);
        return JsonResult.ok("审核完成");
    }

    @PutMapping("/{id}")
    @Operation(summary = "根据Id修改刺激器信息")
    public JsonResult<BcmVO> update(@PathVariable Long id, @RequestBody BcmDTO bcmDTO) {
        return JsonResult.ok(bcmService.updateDevice(id, bcmDTO));
    }

    @PutMapping("/updateRecharge/{bcmSn}/{count}")
    @Operation(summary = "通过刺激器签名修改充电次数")
    public JsonResult<BcmVO> updateRecharge(@PathVariable String bcmSn, @PathVariable Short count) {
        return JsonResult.ok(bcmService.updateRecharge(bcmSn, count));
    }


    @PutMapping("/updateRecharge")
    @Operation(summary = "批量上传充电次数")
    public JsonResult<Void> updateRecharge(@RequestBody List<RechargeCountLogDTO> rechargeCountLogDTOS) {
        bcmService.updateRecharge(rechargeCountLogDTOS);
        return JsonResult.ok();
    }

    @GetMapping("/findDeviceGroup")
    @Operation(summary = "获取审核统计信息")
    public JsonResult<List<DeviceGroupBatchNoVO>> findDeviceGroup(@RequestParam(required = false) Long modelId) {
        return JsonResult.ok(bcmService.searchNearDeviceGroupBatchNo(modelId));
    }

    @GetMapping("/checkBcmSn/{bcmSn}")
    @Operation(summary = "根据签名检查刺激器状态")
    public JsonResult checkBcmSn(@PathVariable String bcmSn) {
        bcmService.checkBcmSn(bcmSn);
        return JsonResult.ok();
    }

    @GetMapping("/statistics")
    @Operation(summary = "分型号统计设备使用情况")
    public JsonResult<List<DeviceStatisticsVO>> statistics(@RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) Date startDate, @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) Date endDate, @RequestParam Long diseaseId) {
        return JsonResult.ok(bcmService.statistics(startDate, endDate, diseaseId));
    }

    @GetMapping("/{id}")
    @Operation(summary = "根据设备Id查询")
    public JsonResult<BcmVO> findById(@PathVariable Long id) {
        return JsonResult.ok(bcmService.findById(id));
    }
}
