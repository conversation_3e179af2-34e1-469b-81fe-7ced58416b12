package com.genlight.epilcure.service.device.controller;

import com.fasterxml.jackson.annotation.JsonView;
import com.genlight.epilcure.api.device.pojo.dto.BppModelDTO;
import com.genlight.epilcure.api.device.pojo.dto.ModelDTO.BppValid;
import com.genlight.epilcure.api.device.pojo.vo.BppModelVO;
import com.genlight.epilcure.common.core.mvc.response.JsonResult;
import com.genlight.epilcure.common.core.pojo.valid.Add;
import com.genlight.epilcure.service.device.service.BppModelService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/6/8 22:54
 * @Version 1.0.0
 **/
@RestController
@RequestMapping("/api/device/bppModels")
@Tag(name = "BppModelController", description = "程控仪型号相关接口")
public class BppModelController {
    @Resource
    private BppModelService bppModelService;

    @GetMapping
    @Operation(summary = "根据条件获取程控仪型号")
    public JsonResult<List<BppModelVO>> finds(BppModelDTO bppModelDTO) {
        return JsonResult.ok(bppModelService.findsModel(bppModelDTO));
    }

    @PostMapping(consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @Operation(summary = "新增程控仪型号")
    public JsonResult<BppModelVO> addBppModel(@RequestPart("file") MultipartFile file
            , @JsonView(Add.class) @Validated(BppValid.class) @RequestPart BppModelDTO bppModelDTO) {
        return JsonResult.ok(bppModelService.addModel(bppModelDTO, file));
    }
}
