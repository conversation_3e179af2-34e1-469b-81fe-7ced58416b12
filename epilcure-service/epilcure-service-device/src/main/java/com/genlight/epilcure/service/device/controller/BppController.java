package com.genlight.epilcure.service.device.controller;

import com.fasterxml.jackson.annotation.JsonView;
import com.genlight.epilcure.api.device.pojo.dto.BppDTO;
import com.genlight.epilcure.api.device.pojo.dto.ModelDTO.BppValid;
import com.genlight.epilcure.api.device.pojo.vo.BppVO;
import com.genlight.epilcure.api.device.pojo.vo.DeviceGroupBatchNoVO;
import com.genlight.epilcure.common.core.mvc.response.JsonResult;
import com.genlight.epilcure.common.core.pojo.valid.Add;
import com.genlight.epilcure.common.core.pojo.valid.Update;
import com.genlight.epilcure.service.device.pojo.vo.DeviceStatisticsVO;
import com.genlight.epilcure.service.device.service.BppService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;

@RestController
@RequestMapping("/api/device/bpps")
@Tag(name = "BppController", description = "程控仪相关接口")
public class BppController {
    @Resource
    private BppService bppService;

    @GetMapping("/finds")
    @Operation(summary = "根据条件查询程控仪设备")
    public JsonResult<Page<BppVO>> findsByPageable(BppDTO bppDTO, Pageable pageable) {
        return JsonResult.ok(bppService.searchDevice(bppDTO, pageable));
    }

    @GetMapping("/findGroupByBatchNo")
    @Operation(summary = "查询所有批号")
    public JsonResult<List<String>> findGroupByBatchNo(@RequestParam(required = false) Long modelId) {
        return JsonResult.ok(bppService.findGroupByBatchNo(modelId));
    }

    @PostMapping
    @Operation(summary = "新增程控仪设备")
    @JsonView(Add.class)
    public JsonResult<BppVO> addBpp(@JsonView(BppValid.class)
                                    @Validated(BppValid.class)
                                    @RequestBody BppDTO bppDTO) {
        return JsonResult.ok(bppService.addDevice(bppDTO));
    }

    @PutMapping("/audit")
    @Operation(summary = "审核程控仪设备")
    public JsonResult<String> auditBpp(@Validated(Update.class) @RequestBody List<BppDTO> bcmDTOList) {
        bppService.auditDevice(bcmDTOList);
        return JsonResult.ok("审核完成");
    }

    @PutMapping("/{id}")
    @Operation(summary = "根据Id修改程控仪信息")
    public JsonResult<BppVO> update(@PathVariable Long id, @RequestBody BppDTO bppDTO) {
        return JsonResult.ok(bppService.updateDevice(id, bppDTO));
    }

    @PostMapping("/findDeviceGroup")
    @Operation(summary = "获取审核统计信息")
    public JsonResult<List<DeviceGroupBatchNoVO>> findDeviceGroup(@RequestParam(required = false) Long modelId) {
        return JsonResult.ok(bppService.searchNearDeviceGroupBatchNo(modelId));
    }

    @GetMapping("/statistics")
    @Operation(summary = "分型号统计设备使用情况")
    public JsonResult<List<DeviceStatisticsVO>> statistics(@RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) Date startDate, @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) Date endDate, @RequestParam Long diseaseId) {
        return JsonResult.ok(bppService.statistics(startDate, endDate, diseaseId));
    }
}
