package com.genlight.epilcure.service.device.controller;

import com.fasterxml.jackson.annotation.JsonView;
import com.genlight.epilcure.api.device.pojo.dto.ModelDTO;
import com.genlight.epilcure.api.device.pojo.dto.TestBcmModelDTO;
import com.genlight.epilcure.api.device.pojo.vo.TestBcmModelVO;
import com.genlight.epilcure.common.core.mvc.response.JsonResult;
import com.genlight.epilcure.common.core.pojo.valid.Add;
import com.genlight.epilcure.service.device.service.TestBcmModelService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/6/8 22:50
 * @Version 1.0.0
 **/
@RestController
@RequestMapping("/api/device/testBcmModels")
@Tag(name = "TestBcmModelController", description = "刺激器型号相关接口")
public class TestBcmModelController {
    @Resource
    private TestBcmModelService testBcmModelService;

    @GetMapping
    @Operation(summary = "根据条件获取刺激器型号")
    public JsonResult<List<TestBcmModelVO>> finds(TestBcmModelDTO bcmModelDTO) {
        return JsonResult.ok(testBcmModelService.findsModel(bcmModelDTO));
    }

    @PostMapping(consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @Operation(summary = "新增刺激器型号")
    public JsonResult<TestBcmModelVO> addBcmModel(@RequestPart("file") MultipartFile file
            , @JsonView(Add.class) @RequestPart @Validated(ModelDTO.TestBcmValid.class) TestBcmModelDTO testBcmModelDTO) {
        return JsonResult.ok(testBcmModelService.addModel(testBcmModelDTO, file));
    }
}
