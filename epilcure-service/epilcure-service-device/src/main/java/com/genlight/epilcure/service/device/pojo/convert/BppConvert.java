package com.genlight.epilcure.service.device.pojo.convert;

import com.genlight.epilcure.api.device.pojo.dto.BppDTO;
import com.genlight.epilcure.api.device.pojo.vo.BppVO;
import com.genlight.epilcure.common.core.pojo.convert.BaseConvert;
import com.genlight.epilcure.common.core.pojo.convert.qualified.Basic;
import com.genlight.epilcure.service.device.dao.entity.Bpp;
import org.mapstruct.*;

/**
 * <AUTHOR>
 * @Date 2022/5/24 22:13
 * @Version 1.0.0
 **/
@Named("BppConvert")
@Mapper(config = BaseConvert.class)
public interface BppConvert extends BaseConvert<Bpp, BppVO, BppDTO> {
    @Basic
    @Mapping(target = "modelId", source = "model.id")
    @Mapping(target = "modelName", source = "model.model")
    @BeanMapping(nullValueCheckStrategy = NullValueCheckStrategy.ON_IMPLICIT_CONVERSION)
    @Override
    BppVO po2vo(Bpp bpp);

    @Mapping(target = "deviceStatus", expression = "java(java.util.Optional.ofNullable(bppDTO.getDeviceStatus()).orElse(com.genlight.epilcure.api.device.enums.DeviceStatus.produced))")
    @BeanMapping(nullValueCheckStrategy = NullValueCheckStrategy.ON_IMPLICIT_CONVERSION)
    @Override
    Bpp dto2po(BppDTO bppDTO);
}
