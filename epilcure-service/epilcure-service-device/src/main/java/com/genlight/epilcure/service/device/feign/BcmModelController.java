package com.genlight.epilcure.service.device.feign;

import com.genlight.epilcure.api.device.feign.IBcmModelController;
import com.genlight.epilcure.api.device.pojo.dto.BcmModelDTO;
import com.genlight.epilcure.api.device.pojo.vo.BcmModelVO;
import com.genlight.epilcure.service.device.service.BcmModelService;
import io.swagger.v3.oas.annotations.Hidden;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController(value = "feignBcmModelController")
@RequestMapping
@Hidden
public class BcmModelController implements IBcmModelController {
    @Autowired
    private BcmModelService bcmModelService;

    @Override
    public BcmModelVO find(@PathVariable Long id) {
        BcmModelVO model = bcmModelService.getModel(id);
        return model;
    }

    @Override
    public Boolean exist(Long id) {
        return bcmModelService.exist(id);
    }

    @Override
    public List<BcmModelVO> findAll(Long diseaseId) {
        return bcmModelService.findsModel(BcmModelDTO.builder().diseaseId(diseaseId).build());
    }
}
