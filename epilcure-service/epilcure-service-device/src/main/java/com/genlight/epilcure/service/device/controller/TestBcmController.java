package com.genlight.epilcure.service.device.controller;

import com.fasterxml.jackson.annotation.JsonView;
import com.genlight.epilcure.api.device.pojo.dto.ModelDTO;
import com.genlight.epilcure.api.device.pojo.dto.ModelDTO.BcmValid;
import com.genlight.epilcure.api.device.pojo.dto.TestBcmDTO;
import com.genlight.epilcure.api.device.pojo.vo.DeviceGroupBatchNoVO;
import com.genlight.epilcure.api.device.pojo.vo.TestBcmVO;
import com.genlight.epilcure.common.core.mvc.response.JsonResult;
import com.genlight.epilcure.common.core.pojo.valid.Add;
import com.genlight.epilcure.common.core.pojo.valid.Update;
import com.genlight.epilcure.service.device.pojo.vo.DeviceStatisticsVO;
import com.genlight.epilcure.service.device.service.TestBcmService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;

@RestController
@RequestMapping("/api/device/test_bcms")
@Tag(name = "TestBcmController", description = "刺激器相关接口")
public class TestBcmController {
    @Resource
    private TestBcmService testBcmService;

    @GetMapping("/finds")
    @Operation(summary = "根据条件查询Bcm设备")
    public JsonResult<Page<TestBcmVO>> findBcmsByPageable(TestBcmDTO bcmDTO, Pageable pageable) {
        return JsonResult.ok(testBcmService.searchDevice(bcmDTO, pageable));
    }

    @GetMapping("/findGroupByBatchNo")
    @Operation(summary = "查询所有批号")
    public JsonResult<List<String>> findGroupByBatchNo(@RequestParam(required = false) Long modelId) {
        return JsonResult.ok(testBcmService.findGroupByBatchNo(modelId));
    }

    @GetMapping("/mac/{mac}/{diseaseId}")
    @Operation(summary = "根据mac地址查询刺激器信息")
    public JsonResult<TestBcmVO> findByMac(@PathVariable String mac) {
        return JsonResult.ok(testBcmService.findByMac(mac));
    }

    @PostMapping
    @Operation(summary = "新增bcm设备")
    @JsonView(Add.class)
    public JsonResult<TestBcmVO> addBcm(@JsonView(BcmValid.class)
                                        @Validated(ModelDTO.TestBcmValid.class)
                                        @RequestBody TestBcmDTO bcmDTO) {
        return JsonResult.ok(testBcmService.addDevice(bcmDTO));
    }

    @PutMapping("/audit")
    @Operation(summary = "审核bcm设备")
    public JsonResult<String> auditBcm(@Validated(Update.class) @RequestBody List<TestBcmDTO> bcmDTOList) {
        testBcmService.auditDevice(bcmDTOList);
        return JsonResult.ok("审核完成");
    }

    @PutMapping("/{id}")
    @Operation(summary = "根据Id修改刺激器信息")
    public JsonResult<TestBcmVO> update(@PathVariable Long id, @RequestBody TestBcmDTO bcmDTO) {
        return JsonResult.ok(testBcmService.updateDevice(id, bcmDTO));
    }

    @PutMapping("/updateRecharge/{bcmSn}/{count}")
    @Operation(summary = "通过刺激器签名修改充电次数")
    public JsonResult<TestBcmVO> updateRecharge(@PathVariable String bcmSn, @PathVariable Short count) {
        return JsonResult.ok(testBcmService.updateRecharge(bcmSn, count));
    }

    @GetMapping("/findDeviceGroup")
    @Operation(summary = "获取审核统计信息")
    public JsonResult<List<DeviceGroupBatchNoVO>> findDeviceGroup(@RequestParam(required = false) Long modelId) {
        return JsonResult.ok(testBcmService.searchNearDeviceGroupBatchNo(modelId));
    }

    @GetMapping("/checkBcmSn/{bcmSn}")
    @Operation(summary = "根据签名检查刺激器状态")
    public JsonResult checkBcmSn(@PathVariable String bcmSn) {
        testBcmService.checkBcmSn(bcmSn);
        return JsonResult.ok();
    }

    @GetMapping("/statistics")
    @Operation(summary = "分型号统计设备使用情况")
    public JsonResult<List<DeviceStatisticsVO>> statistics(@RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) Date startDate, @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) Date endDate, @RequestParam Long diseaseId) {
        return JsonResult.ok(testBcmService.statistics(startDate, endDate, diseaseId));
    }

    @GetMapping("/{id}")
    @Operation(summary = "根据设备Id查询")
    public JsonResult<TestBcmVO> findById(@PathVariable Long id) {
        return JsonResult.ok(testBcmService.findById(id));
    }
}
