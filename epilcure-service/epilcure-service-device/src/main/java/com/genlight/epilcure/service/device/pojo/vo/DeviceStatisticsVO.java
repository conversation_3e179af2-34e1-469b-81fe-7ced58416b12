package com.genlight.epilcure.service.device.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "设备使用统计View Object")
public class DeviceStatisticsVO implements Serializable {
    private String model;
    private Long count;
}
