package com.genlight.epilcure.service.device.dao.repository;

import com.genlight.epilcure.service.device.dao.entity.BcmRecharge;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;

import java.util.Date;
import java.util.List;

public interface BcmRechargeRepository extends JpaRepository<BcmRecharge, Long>, JpaSpecificationExecutor<BcmRecharge> {

    @Query("""
                select distinct new BcmRecharge(b.rechargeDate,b.rechargeEndDate,b.rechargeStart,b.rechargeEnd,b.voltageStart,b.voltageEnd) from BcmRecharge b 
                where b.rechargeDate between :start and :end
                and b.bcm.sn=:bcmSn
            """)
    List<BcmRecharge> stats(Date start, Date end, String bcmSn);

    Boolean existsByBcmIdAndRechargeDate(Long bcmId, Date rechargeDate);
}
