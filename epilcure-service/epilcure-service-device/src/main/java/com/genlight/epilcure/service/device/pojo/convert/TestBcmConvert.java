package com.genlight.epilcure.service.device.pojo.convert;

import com.genlight.epilcure.api.device.pojo.dto.TestBcmDTO;
import com.genlight.epilcure.api.device.pojo.vo.TestBcmVO;
import com.genlight.epilcure.common.core.pojo.convert.BaseConvert;
import com.genlight.epilcure.service.device.dao.entity.TestBcm;
import org.mapstruct.BeanMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.NullValueCheckStrategy;

@Mapper(config = BaseConvert.class)
public interface TestBcmConvert extends BaseConvert<TestBcm, TestBcmVO, TestBcmDTO> {
    @Mapping(target = "modelId", source = "model.id")
    @Mapping(target = "modelName", source = "model.model")
    @BeanMapping(nullValueCheckStrategy = NullValueCheckStrategy.ON_IMPLICIT_CONVERSION)
    @Override
    TestBcmVO po2vo(TestBcm bcm);

    @Mapping(target = "rechargeCount", expression = "java(java.util.Optional.ofNullable(bcmDTO.getRechargeCount()).orElse((short)0))")
    @Mapping(target = "hardwareVersion", expression = "java(java.util.Optional.ofNullable(bcmDTO.getHardwareVersion()).orElse(\"1.0.0\"))")
    @Mapping(target = "softwareVersion", expression = "java(java.util.Optional.ofNullable(bcmDTO.getSoftwareVersion()).orElse(\"1.0.0\"))")
    @Mapping(target = "deviceStatus", expression = "java(java.util.Optional.ofNullable(bcmDTO.getDeviceStatus()).orElse(com.genlight.epilcure.api.device.enums.DeviceStatus.produced))")
    @BeanMapping(nullValueCheckStrategy = NullValueCheckStrategy.ON_IMPLICIT_CONVERSION)
    @Override
    TestBcm dto2po(TestBcmDTO bcmDTO);
}
