package com.genlight.epilcure.service.device.pojo.convert;

import com.genlight.epilcure.api.device.pojo.dto.ImpedanceLogDTO;
import com.genlight.epilcure.api.device.pojo.vo.ImpedanceLogVO;
import com.genlight.epilcure.common.core.pojo.convert.BaseConvert;
import com.genlight.epilcure.service.device.dao.entity.ImpedanceLog;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(config = BaseConvert.class)
public interface ImpedanceLogConvert extends BaseConvert<ImpedanceLog, ImpedanceLogVO, ImpedanceLogDTO> {

    @Mapping(target = "status", expression = "java(java.util.Optional.ofNullable(impedanceLogDTO.getStatus()).orElse(com.genlight.epilcure.common.core.dao.enums.Status.ENABLED))")
    @Override
    ImpedanceLog dto2po(ImpedanceLogDTO impedanceLogDTO);
}
