package com.genlight.epilcure.service.device.pojo.convert;

import com.genlight.epilcure.common.core.pojo.convert.BaseConvert;
import com.genlight.epilcure.service.device.dao.entity.BcmRecharge;
import com.genlight.epilcure.service.device.pojo.dto.BcmRechargeDTO;
import com.genlight.epilcure.service.device.pojo.vo.BcmRechargeVO;
import org.mapstruct.Mapper;

@Mapper(config = BaseConvert.class)
public interface BcmRechargeConvert extends BaseConvert<BcmRecharge, BcmRechargeVO, BcmRechargeDTO> {
}
