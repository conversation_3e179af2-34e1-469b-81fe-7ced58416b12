package com.genlight.epilcure.service.device.pojo.convert;

import com.genlight.epilcure.api.device.pojo.dto.BcmModelDTO;
import com.genlight.epilcure.api.device.pojo.vo.BcmModelVO;
import com.genlight.epilcure.common.core.pojo.convert.BaseConvert;
import com.genlight.epilcure.service.device.dao.entity.BcmModel;
import org.mapstruct.Mapper;

@Mapper(config = BaseConvert.class)
public interface BcmModelConvert extends BaseConvert<BcmModel, BcmModelVO, BcmModelDTO> {

}
