package com.genlight.epilcure.service.device.service;

import com.genlight.epilcure.api.device.enums.DeviceStatus;
import com.genlight.epilcure.api.device.pojo.vo.DeviceGroupBatchNoVO;
import com.genlight.epilcure.common.core.pojo.convert.BaseConvert;
import com.genlight.epilcure.common.core.service.BaseService;
import com.genlight.epilcure.common.core.service.exception.ArgsException;
import com.genlight.epilcure.service.device.constants.CacheConstants;
import com.genlight.epilcure.service.device.dao.entity.DeviceBase;
import com.genlight.epilcure.service.device.dao.entity.DeviceBase_;
import com.genlight.epilcure.service.device.dao.entity.ModelBase;
import com.genlight.epilcure.service.device.dao.repository.BcmRepository;
import com.genlight.epilcure.service.device.dao.repository.BppRepository;
import com.genlight.epilcure.service.device.dao.repository.DeviceRepository;
import com.genlight.epilcure.service.device.pojo.vo.DeviceStatisticsVO;
import io.seata.spring.annotation.GlobalTransactional;
import jakarta.annotation.Resource;
import jakarta.persistence.criteria.Predicate;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * 设备服务基类
 */
@Slf4j
public class DeviceService<Model extends ModelBase,
        Device extends DeviceBase<Model>,
        ModelVO extends com.genlight.epilcure.api.device.pojo.vo.ModelVO,
        ModelDTO extends com.genlight.epilcure.api.device.pojo.dto.ModelDTO,
        DeviceVO extends com.genlight.epilcure.api.device.pojo.vo.DeviceVO,
        DeviceDTO extends com.genlight.epilcure.api.device.pojo.dto.DeviceDTO<?>,
        ModelRepository extends JpaRepository<Model, Long>, Repository extends DeviceRepository<Device, Long>,
        ModelConvert extends BaseConvert<Model, ModelVO, ModelDTO>, Convert extends BaseConvert<Device, DeviceVO, DeviceDTO>> extends BaseService<Device, Long, Repository> {
    @Autowired
    protected ModelRepository modelRepository;
    @Autowired
    protected ModelConvert modelConvert;
    @Autowired
    protected Convert convert;

    @Resource
    private RedissonClient redissonClient;

    /**
     * 添加设备
     *
     * @param bcmDTO
     * @return
     */
    @Transactional
    public DeviceVO addDevice(DeviceDTO bcmDTO) {

        RLock lock = redissonClient.getLock("addDevice");

        try {
            if (lock.tryLock(2, 2, TimeUnit.SECONDS)) {

                Device bcm = convert.dto2po(bcmDTO);

                Optional<Model> optional = modelRepository.findById(bcmDTO.getModelId());

                if (optional.isEmpty()) {
                    throw new ArgsException("设备类型[{0}]不存在", bcmDTO.getModelId());
                }


                Model model = optional.get();
                if (!bcmDTO.getSn().contains(model.getSn())) {
                    throw new ArgsException("签名[{0}]不合法", bcmDTO.getSn());
                }

                Optional<Device> optionalDevice = repository.findBySn(bcmDTO.getSn());
                if (optionalDevice.isPresent()) {
                    throw new ArgsException("设备签名[{0}]已存在", bcmDTO.getSn());
                }

                bcm.setUserId(getUserId());
                bcm.setUserMobile(getUserName());
                bcm.setModel(model);
                return convert.po2vo(repository.saveAndFlush(bcm));
            } else {
                throw new RuntimeException("服务忙,请稍后再试");
            }
        } catch (Exception e) {
            throw new ArgsException(e.getMessage());
        } finally {
            this.unLock(lock);
        }
    }

    @Transactional
    public void destroyOtherDevice(Long patientId, List<Long> ids) {
        repository.expireDevice(DeviceStatus.destroyed, patientId, ids);
    }

    /**
     * 获取患者使用得设备
     *
     * @param deviceDTO
     * @return
     */
    public DeviceVO getDeviceByPatient(DeviceDTO deviceDTO) {
        Device bcm = repository.getByPatientIdAndDeviceStatus(deviceDTO.getPatientId(), deviceDTO.getDeviceStatus());
        Assert.notNull(bcm, "该患者没有正在使用得刺激器");
        return convert.po2vo(bcm);
    }

    @Transactional(readOnly = true)
    public DeviceVO findBySn(String sn) {
        Optional<Device> optional = repository.findBySn(sn);
        if (optional.isEmpty()) {
            if (repository instanceof BcmRepository) {
                throw new ArgsException("刺激器序列号不存在");
            } else if (repository instanceof BppRepository) {
                throw new ArgsException("程控仪序列号不存在");
            } else {
                throw new ArgsException("电极序列号不存在");
            }

        }
        return convert.po2vo(optional.get());
    }

    @Transactional(readOnly = true)
    public DeviceVO findBySn(String sn, Integer position) {
        Optional<Device> optional = repository.findBySn(sn);
        if (optional.isEmpty()) {
            throw new ArgsException("电极{0}序列号不存在", position.equals(0) ? "一" : "二");
        }
        return convert.po2vo(optional.get());
    }

    @Transactional(readOnly = true)
    public List<String> findGroupByBatchNo(Long modelId) {
        return repository.findGroupByBatchNo(modelId);
    }

    /**
     * 分页搜索设备列表
     *
     * @param bcmDTO
     * @param pageable
     * @return
     */
    @Transactional(readOnly = true)
    public Page<DeviceVO> searchDevice(DeviceDTO bcmDTO, Pageable pageable) {
        Specification<Device> specification = (root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();
            if (StringUtils.hasText(bcmDTO.getBatchNo())) {
                predicates.add(criteriaBuilder.like(root.get("batchNo"), "%" + bcmDTO.getBatchNo() + "%"));
            }
            if (bcmDTO.getUserId() != null) {
                predicates.add(criteriaBuilder.equal(root.get("userId"), bcmDTO.getUserId()));
            }
            if (bcmDTO.getDeviceStatus() != null) {
                predicates.add(criteriaBuilder.equal(root.get("deviceStatus"), bcmDTO.getDeviceStatus()));
            }
            if (Objects.nonNull(bcmDTO.getModelId())) {
                predicates.add(criteriaBuilder.equal(root.join("model").get("id"), bcmDTO.getModelId()));
            }

            if (Objects.nonNull(bcmDTO.getStartDate())) {
                predicates.add(criteriaBuilder.greaterThanOrEqualTo(root.get(DeviceBase_.CREATE_TIME), bcmDTO.getStartDate()));
            }
            if (Objects.nonNull(bcmDTO.getEndDate())) {
                predicates.add(criteriaBuilder.lessThanOrEqualTo(root.get(DeviceBase_.CREATE_TIME), bcmDTO.getEndDate()));
            }
            if (Objects.nonNull(bcmDTO.getUpdateStartDate())) {
                predicates.add(criteriaBuilder.greaterThanOrEqualTo(root.get(DeviceBase_.UPDATE_TIME), bcmDTO.getUpdateStartDate()));
            }
            if (Objects.nonNull(bcmDTO.getUpdateEndDate())) {
                predicates.add(criteriaBuilder.lessThanOrEqualTo(root.get(DeviceBase_.UPDATE_TIME), bcmDTO.getUpdateEndDate()));
            }
            if (Objects.nonNull(bcmDTO.getDiseaseId())) {
                if (Objects.nonNull(bcmDTO.getDiseaseId())) {
                    predicates.add(criteriaBuilder.equal(root.join("model").get("diseaseId"), bcmDTO.getDiseaseId()));
                }
            }
            if (Objects.nonNull(bcmDTO.getSn())) {
                predicates.add(criteriaBuilder.like(root.get(DeviceBase_.SN), "%" + bcmDTO.getSn() + "%"));
            }
            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };
        return convert.po2vo(repository.findAll(specification, pageable));
    }

    /**
     * 审核设备
     *
     * @param bcmDTOList
     * @return
     */
    @Transactional
    public boolean auditDevice(List<DeviceDTO> bcmDTOList) {
        List<Long> ids = new ArrayList<>();
        for (DeviceDTO bcmDTO : bcmDTOList) {
            ids.add(bcmDTO.getId());
        }
        List<Device> allById = repository.findAllById(ids);
        for (Device bcm : allById) {
            Optional<DeviceDTO> optional = bcmDTOList.stream().filter(b -> b.getId().equals(bcm.getId())).findFirst();
            bcm.setDeviceStatus(optional.get().getDeviceStatus());
        }
        repository.saveAll(allById);
        return true;
    }

    @CacheEvict(cacheNames = CacheConstants.CACHE_NAMES_PATIENT_DEVICE_PAGE, allEntries = true)
    @GlobalTransactional(rollbackFor = Exception.class)
    @Transactional
    public DeviceVO updateDevice(Long id, DeviceDTO deviceDTO) {
        log.debug("修改设备信息");
        Optional<Device> optional = repository.findById(id);
        if (optional.isEmpty()) {
            throw new ArgsException("修改的设备Id[{0}]不存在", id);
        }

        Device device = optional.get();
        log.info("手术时间为:{}", deviceDTO.getSurgeryDate());
        if (Objects.nonNull(deviceDTO.getPatientId())) {
            if (Objects.isNull(device.getImplantationDate())) {
                device.setImplantationDate(deviceDTO.getSurgeryDate());
            }
        }

        convert.dto2po(deviceDTO, device);
        return convert.po2vo(repository.save(device));
    }

    @Transactional(readOnly = true)
    public DeviceVO findById(Long id) {

        Optional<Device> optional = repository.findById(id);
        if (optional.isEmpty()) {
            throw new ArgsException("设备Id[{0}]不存在", id);
        }
        return convert.po2vo(optional.get());
    }

    @Transactional(readOnly = true)
    public DeviceVO findByIdAndModel(Long id, Long modelId) {
        Optional<Device> optional = repository.findByIdAndModelId(id, modelId);
        if (optional.isEmpty()) {
            throw new ArgsException("设备Id[{0}]&型号Id[{1}]不存在", id, modelId);
        }
        return convert.po2vo(optional.get());
    }

    public boolean existDevice(Long id) {
        return repository.existsById(id);
    }

    /**
     * 查询设备批号的审核结果统计
     *
     * @return
     */
    @Transactional(readOnly = true)
    public List<DeviceGroupBatchNoVO> searchNearDeviceGroupBatchNo(Long modelId) {
        return repository.getGroupBatchNoVOs(modelId);
    }

    @Transactional(readOnly = true)
    public List<DeviceStatisticsVO> statistics(Date startDate, Date endDate, Long diseaseId) {
        return repository.statistics(startDate, endDate, diseaseId);
    }
}
