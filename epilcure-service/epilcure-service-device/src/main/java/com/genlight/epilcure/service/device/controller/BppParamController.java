package com.genlight.epilcure.service.device.controller;

import com.genlight.epilcure.api.device.pojo.dto.BppParamDTO;
import com.genlight.epilcure.api.device.pojo.vo.BppParamVO;
import com.genlight.epilcure.common.core.mvc.response.JsonResult;
import com.genlight.epilcure.service.device.service.BppParamService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/device/bppParams")
@Tag(name = "BppParamController", description = "程控仪扩展参数相关接口")
public class BppParamController {
    @Resource
    private BppParamService bppParamService;

    @PostMapping
    @Operation(summary = "添加程控仪参数")
    public JsonResult<BppParamVO> add(@RequestBody @Valid BppParamDTO bppParamDTO) {
        return JsonResult.ok(bppParamService.addBcmParam(bppParamDTO));
    }

    @GetMapping("/{sn}")
    @Operation(summary = "根据签名查询程控仪参数")
    public JsonResult<BppParamVO> find(@PathVariable String sn) {
        return JsonResult.ok(bppParamService.findBySn(sn));
    }
}
