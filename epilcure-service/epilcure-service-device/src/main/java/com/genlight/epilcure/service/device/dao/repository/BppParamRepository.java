package com.genlight.epilcure.service.device.dao.repository;

import com.genlight.epilcure.common.core.dao.enums.Status;
import com.genlight.epilcure.service.device.dao.entity.BppParam;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;

public interface BppParamRepository extends JpaRepository<BppParam, Long> {

    List<BppParam> findByBppIdAndStatus(Long bppId, Status status);
}
