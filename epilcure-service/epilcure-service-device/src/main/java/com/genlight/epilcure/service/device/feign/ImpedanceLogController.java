package com.genlight.epilcure.service.device.feign;

import com.genlight.epilcure.api.device.feign.IImpedanceLogController;
import com.genlight.epilcure.api.device.pojo.vo.ImpedanceLogVO;
import com.genlight.epilcure.service.device.service.ImpedanceLogService;
import io.swagger.v3.oas.annotations.Hidden;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


@RestController(value = "feignImpedanceLogController")
@RequestMapping
@Hidden
public class ImpedanceLogController implements IImpedanceLogController {

    @Resource
    private ImpedanceLogService impedanceLogService;

    @Override
    public ImpedanceLogVO findBySn(String sn) {
        return impedanceLogService.findBySn(sn);
    }
}
