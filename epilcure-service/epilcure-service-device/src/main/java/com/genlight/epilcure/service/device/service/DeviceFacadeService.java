package com.genlight.epilcure.service.device.service;


import com.genlight.epilcure.api.device.pojo.bo.DeviceBO;
import com.genlight.epilcure.api.device.pojo.dto.BcmDTO;
import com.genlight.epilcure.api.device.pojo.dto.ElectrodeDTO;
import io.seata.spring.annotation.GlobalTransactional;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Objects;

@Service
public class DeviceFacadeService {
    @Resource
    private BcmService bcmService;

    @Resource
    private BppService bppService;

    @Resource
    private ElectrodeService electrodeService;

    @GlobalTransactional(rollbackFor = Exception.class)
    @Transactional
    public DeviceBO updateDevice(DeviceBO deviceBO) {
        if (Objects.nonNull(deviceBO.getBcm())) {
            deviceBO.setBcm(bcmService.updateDevice(deviceBO.getBcm().getId(), BcmDTO.builder()
                    .deviceStatus(deviceBO.getBcmStatus())
                    .patientId(deviceBO.getPatientId())
                    .surgeryDate(deviceBO.getSurgeryDate())
                    .build()));
        }

        if (Objects.nonNull(deviceBO.getElectrodes()) && !deviceBO.getElectrodes().isEmpty()) {
            deviceBO.setElectrodes(deviceBO.getElectrodes().stream().map(e -> electrodeService.updateDevice(e.getId(), ElectrodeDTO.builder()
                    .deviceStatus(deviceBO.getElectrodeStatus())
                    .patientId(deviceBO.getPatientId())
                    .surgeryDate(deviceBO.getSurgeryDate())
                    .build())).toList());
        }
        return deviceBO;
    }

    @GlobalTransactional(rollbackFor = Exception.class)
    @Transactional
    public void destroyOtherDevice(DeviceBO deviceBO) {
        if (Objects.nonNull(deviceBO.getBcmId())) {
            bcmService.destroyOtherDevice(deviceBO.getPatientId(), List.of(deviceBO.getBcmId()));
        }
        if (Objects.nonNull(deviceBO.getBpp()) && Objects.nonNull(deviceBO.getBpp().getId())) {
            bppService.destroyOtherDevice(deviceBO.getPatientId(), List.of(deviceBO.getBpp().getId()));
        }
        if (Objects.nonNull(deviceBO.getElectrodeIds()) && !deviceBO.getElectrodeIds().isEmpty()) {
            electrodeService.destroyOtherDevice(deviceBO.getPatientId(), deviceBO.getElectrodeIds());
        }
    }

    @Transactional(readOnly = true)
    public DeviceBO findDevice(DeviceBO deviceBO) {
        if (Objects.nonNull(deviceBO.getBcmId())) {
            deviceBO.setBcm(bcmService.findById(deviceBO.getBcmId()));
        } else if (Objects.nonNull(deviceBO.getBcmSn())) {
            deviceBO.setBcm(bcmService.findBySn(deviceBO.getBcmSn()));
        }

        if (Objects.nonNull(deviceBO.getElectrodeIds()) && deviceBO.getElectrodeIds().size() > 0) {
            deviceBO.getElectrodeIds().forEach(e -> {
                deviceBO.getElectrodes().add(electrodeService.findById(e));
            });
        } else if (Objects.nonNull(deviceBO.getElectrodeSns()) && deviceBO.getElectrodeSns().size() > 0) {
            deviceBO.getElectrodeSns().forEach(e -> {
                deviceBO.getElectrodes().add(electrodeService.findBySn(e));
            });
        }
        return deviceBO;
    }
}
