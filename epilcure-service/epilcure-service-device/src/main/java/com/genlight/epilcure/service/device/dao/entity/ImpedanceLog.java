package com.genlight.epilcure.service.device.dao.entity;

import com.genlight.epilcure.api.device.pojo.enums.ImpedanceType;
import com.genlight.epilcure.common.core.dao.entity.TEntity;
import com.genlight.epilcure.common.core.dao.enums.Status;
import com.genlight.epilcure.common.core.dao.generator.annotation.SignOrder;
import jakarta.persistence.*;
import lombok.*;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;
import org.hibernate.annotations.Comment;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import java.util.Date;


@Getter
@Setter
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString
@Entity
@Cacheable
@DynamicInsert
@DynamicUpdate
@SignOrder(0)
@Table(name = "t_impedance_log", indexes = {
        @Index(name = "idx_patientId", columnList = "patientId")
})
public class ImpedanceLog extends TEntity {

    @SignOrder(1)
    @Comment("患者Id")
    @Column(nullable = false)
    private Long patientId;

    @SignOrder(2)
    @Comment("阻抗信息")
    @Column(nullable = false, length = 2048)
    private String impedance;

    @SignOrder(3)
    @Comment("采样时间")
    @Temporal(TemporalType.TIMESTAMP)
    @Column(nullable = false)
    private Date sampleDate;

    @Comment("状态")
    @Column(nullable = false)
    private Status status;

    @Comment("调试医生")
    @Column(nullable = false)
    @SignOrder(4)
    private Long userId;
    @Comment("医生名称")
    @Column(nullable = false)
    @SignOrder(5)
    private String userName;
    @Comment("调试医生手机号码")
    @Column(nullable = false, length = 11)
    @SignOrder(6)
    private String userPhone;
    @Comment("档案Id")
    @SignOrder(7)
    private Long archivesId;

    @Comment("序列号")
    private String sn;

    @Comment("0为日常，1为术中")
    @Enumerated(EnumType.ORDINAL)
    private ImpedanceType type;
}
