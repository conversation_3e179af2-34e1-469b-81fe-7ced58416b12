package com.genlight.epilcure.service.device.service;

import com.genlight.epilcure.api.config.feign.IDiseaseController;
import com.genlight.epilcure.api.device.enums.DeviceStatus;
import com.genlight.epilcure.api.device.pojo.dto.BcmDTO;
import com.genlight.epilcure.api.device.pojo.dto.BcmModelDTO;
import com.genlight.epilcure.api.device.pojo.dto.BppDTO;
import com.genlight.epilcure.api.device.pojo.vo.BcmModelVO;
import com.genlight.epilcure.api.device.pojo.vo.BcmVO;
import com.genlight.epilcure.api.device.pojo.vo.BppVO;
import com.genlight.epilcure.api.patient.feign.IArchivesController;
import com.genlight.epilcure.api.patient.feign.IPatientDeviceController;
import com.genlight.epilcure.api.patient.pojo.dto.PatientDeviceDTO;
import com.genlight.epilcure.api.patient.pojo.vo.PatientDeviceVO;
import com.genlight.epilcure.common.core.service.exception.ArgsException;
import com.genlight.epilcure.common.core.service.exception.ServiceException;
import com.genlight.epilcure.service.device.dao.entity.Bcm;
import com.genlight.epilcure.service.device.dao.entity.BcmModel;
import com.genlight.epilcure.service.device.dao.entity.Bpp;
import com.genlight.epilcure.service.device.dao.entity.RechargeCountLog;
import com.genlight.epilcure.service.device.dao.repository.BcmModelRepository;
import com.genlight.epilcure.service.device.dao.repository.BcmRepository;
import com.genlight.epilcure.service.device.dao.repository.RechargeCountLogRepository;
import com.genlight.epilcure.service.device.pojo.convert.BcmConvert;
import com.genlight.epilcure.service.device.pojo.convert.BcmModelConvert;
import com.genlight.epilcure.service.device.pojo.dto.RechargeCountLogDTO;
import io.seata.spring.annotation.GlobalTransactional;
import jakarta.annotation.Resource;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import java.util.*;
import java.util.concurrent.TimeUnit;


@Service
@CacheConfig(cacheNames = "bcm")
public class BcmService extends DeviceService<BcmModel, Bcm, BcmModelVO, BcmModelDTO, BcmVO, BcmDTO, BcmModelRepository, BcmRepository, BcmModelConvert, BcmConvert> {
    @Resource
    private BppService bppService;

    @Resource
    private RedissonClient redissonClient;

    @Resource
    private IPatientDeviceController iPatientDeviceController;

    @Resource
    private IArchivesController iArchivesController;

    @Resource
    private IDiseaseController iDiseaseController;

    @Resource
    private RechargeCountLogRepository rechargeCountLogRepository;

    @Override
    @Transactional
    public BcmVO addDevice(BcmDTO bcmDTO) {
        RLock lock = redissonClient.getLock("addBCMDevice-%s".formatted(bcmDTO.getMac()));
        try {
            if (lock.tryLock(2, 2, TimeUnit.SECONDS)) {
                boolean b = repository.existsByMac(bcmDTO.getMac());
                Assert.isTrue(!b, "mac地址已存在");
                return super.addDevice(bcmDTO);
            }
            throw new ServiceException("操作太频繁，请稍后再试！");
        } catch (Exception e) {
            throw new ArgsException(e.getMessage());
        } finally {
            this.unLock(lock);
        }
    }

    @GlobalTransactional(rollbackFor = Exception.class)
    @Transactional
    @Override
    public BcmVO updateDevice(Long id, BcmDTO deviceDTO) {
        BcmVO bcmVO = super.updateDevice(id, deviceDTO);
        BppVO bpp = bcmVO.getBpp();
        if (Objects.nonNull(bpp)) {
            bppService.updateDevice(bpp.getId(), BppDTO.builder()
                    .patientId(bcmVO.getPatientId())
                    .deviceStatus(bcmVO.getDeviceStatus())
                    .build());
        }
        return bcmVO;
    }

    @Transactional
    public void updateRecharge(List<RechargeCountLogDTO> rechargeCountLogDTOS) {
        Map<String, List<RechargeCountLogDTO>> map = new HashMap<>();
        rechargeCountLogDTOS.forEach(r -> {
            rechargeCountLogRepository.save(RechargeCountLog.builder()
                    .count(r.getCount())
                    .bcm(Bcm.builder().id(findBySn(r.getBcmSn()).getId()).build())
                    .readTime(r.getReadDate())
                    .build());
            List<RechargeCountLogDTO> list = map.getOrDefault(r.getBcmSn(), new ArrayList<>());
            list.add(r);
            map.put(r.getBcmSn(), list);
        });

        map.forEach((k, v) -> {
            v.sort(Comparator.comparing(RechargeCountLogDTO::getReadDate));
            updateRecharge(k, v.get(v.size() - 1).getCount().shortValue());
        });
    }

    @Transactional
    public BcmVO updateRecharge(String bcmSn, Short count) {
        BcmVO bcmVO = findBySn(bcmSn);
        if (isPatient()) {
            if (!bcmVO.getPatientId().equals(getUserId()))
                throw new ArgsException("此刺激器不属于[{0}]", getUserId());
        }

        if (count < bcmVO.getRechargeCount()) {
            throw new ArgsException("充电次数只能递增");
        }
        return updateDevice(bcmVO.getId(), BcmDTO.builder().rechargeCount(count).build());
    }

    @GlobalTransactional(rollbackFor = Exception.class)
    @Transactional
    public BcmVO updateRelation(String bppSn, String bcmMac) {
        BppVO bppVO = bppService.findBySn(bppSn);
        Optional<Bcm> optional = repository.findByMac(bcmMac);
        if (optional.isEmpty()) {
            throw new ArgsException("刺激器Mac地址不存在");
        }
        Bcm bcm = optional.get();


        if (bcm.getDeviceStatus().equals(DeviceStatus.destroyed)
                || bcm.getDeviceStatus().equals(DeviceStatus.produced)
                || bcm.getDeviceStatus().equals(DeviceStatus.unQualified)) {
            throw new ArgsException("刺激器[{0}]状态不合法", bcmMac);
        }

        if (bppVO.getDeviceStatus().equals(DeviceStatus.destroyed)
                || bppVO.getDeviceStatus().equals(DeviceStatus.produced)
                || bppVO.getDeviceStatus().equals(DeviceStatus.unQualified)) {
            throw new ArgsException("程控仪[{0}]状态不合法", bppSn);
        }

        if (Objects.nonNull(bppVO.getPatientId())) {
            if (bppVO.getPatientId().equals(bcm.getPatientId())) {
                throw new ArgsException("设备已配对,无需再次配对");
            }
        }

        bcm.setBpp(Bpp.builder().id(bppVO.getId()).build());

        if (Objects.nonNull(bcm.getPatientId())) {
            PatientDeviceVO deviceVO = iPatientDeviceController.findByPatientId(bcm.getPatientId());
            bppService.updateDevice(bppVO.getId(), BppDTO.builder()
                    .patientId(bcm.getPatientId())
                    .deviceStatus(DeviceStatus.used)
                    .build());
            iPatientDeviceController.updateBpp(deviceVO.getId(), PatientDeviceDTO.builder()
                    .bppId(bppVO.getId())
                    .bppModelId(bppVO.getModelId())
                    .patientId(bcm.getPatientId())
                    .build());
        }


        return convert.po2vo(repository.save(bcm));
    }

    @Transactional(readOnly = true)
    public BcmVO findByMac(String mac) {

        Optional<Bcm> optional = repository.findByMac(mac);
        if (optional.isEmpty()) {
            throw new ArgsException("刺激器Mac[{0}]不存在", mac);
        }

        return convert.po2vo(optional.get());
    }

    @Transactional(readOnly = true)
    public void checkBcmSn(String bcmSn) {
        if (!bcmSn.contains("ISBS") && !bcmSn.contains("BCM"))
            bcmSn = "ISBS" + bcmSn;
        BcmVO bcmVO = findBySn(bcmSn);
        if (Objects.isNull(bcmVO)) {
            throw new ServiceException("刺激器序列号不存在");
        }
        if (bcmVO.getDeviceStatus() == DeviceStatus.qualified) {
            throw new ServiceException("该刺激器无关联患者");
        }
        if (bcmVO.getDeviceStatus() != DeviceStatus.used) {
            throw new ServiceException("刺激器序列号不合法");
        }
        if (Objects.nonNull(bcmVO.getPatientId()) && !bcmVO.getPatientId().equals(getUserId())) {
            throw new ArgsException("该刺激器不属于你");
        }
    }
}
