package com.genlight.epilcure.service.device.dao.repository;

import com.genlight.epilcure.api.device.pojo.enums.ImpedanceType;
import com.genlight.epilcure.service.device.dao.entity.ImpedanceLog;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;

import java.util.Date;
import java.util.List;

public interface ImpedanceLogRepository extends JpaRepository<ImpedanceLog, Long>, JpaSpecificationExecutor<ImpedanceLog> {

    @Query(value = "from ImpedanceLog s where s.patientId=:patientId and function('date_format',:sampleDate,'%Y-%m-%d') = function('date_format',s.sampleDate,'%Y-%m-%d') and s.status=1")
    List<ImpedanceLog> findByPatientIdAndSampleDate(Long patientId, Date sampleDate);

    ImpedanceLog findBySnAndType(String sn, ImpedanceType type);
}
