package com.genlight.epilcure.service.device.controller;

import com.fasterxml.jackson.annotation.JsonView;
import com.genlight.epilcure.api.device.pojo.dto.ElectrodeModelDTO;
import com.genlight.epilcure.api.device.pojo.dto.ModelDTO;
import com.genlight.epilcure.api.device.pojo.vo.ElectrodeModelVO;
import com.genlight.epilcure.common.core.mvc.response.JsonResult;
import com.genlight.epilcure.common.core.pojo.valid.Add;
import com.genlight.epilcure.service.device.service.ElectrodeModelService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2022/6/8 22:56
 * @Version 1.0.0
 **/
@RestController
@RequestMapping("/api/device/electrodeModels")
@Tag(name = "ElectrodeModelController", description = "电极型号相关接口")
public class ElectrodeModelController {
    @Resource
    private ElectrodeModelService electrodeModelService;

    @GetMapping
    @Operation(summary = "根据条件获取电极型号")
    public JsonResult<List<ElectrodeModelVO>> finds(ElectrodeModelDTO electrodeModelDTO) {
        return JsonResult.ok(electrodeModelService.findsModel(electrodeModelDTO));
    }

    @PostMapping(consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @Operation(summary = "新增电极型号")
    public JsonResult<ElectrodeModelVO> addBppModel(@RequestPart("file") MultipartFile file
            , @JsonView(Add.class) @Validated(ModelDTO.ElectrodeValid.class) @RequestPart ElectrodeModelDTO electrodeModelDTO) {
        return JsonResult.ok(electrodeModelService.addModel(electrodeModelDTO, file));
    }
}
