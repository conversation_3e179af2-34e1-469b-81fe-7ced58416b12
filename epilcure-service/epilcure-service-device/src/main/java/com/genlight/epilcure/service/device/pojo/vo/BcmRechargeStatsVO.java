package com.genlight.epilcure.service.device.pojo.vo;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;

@Data
@SuperBuilder
@NoArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode
@Schema(description = "刺激器充电统计信息View")
public class BcmRechargeStatsVO implements Serializable {

    @Schema(description = "充电次数")
    private Long count;

    @Schema(description = "充电频率")
    private Double rechargeFrequency;

    @Schema(description = "充电时长")
    private Long rechargeDuration;

    @Schema(description = "平均开始电量")
    private Long startAvg;

    @Schema(description = "平均结束电量")
    private Long endAvg;

    @Schema(description = "平均开始电压")
    private Integer voltageStartAvg;

    @Schema(description = "平均结束电压")
    private Integer voltageEndAvg;

    @Schema(description = "平均可用时长")
    private Double avgDuration;

}
