package com.genlight.epilcure.service.device.service;

import com.genlight.epilcure.api.device.feign.IBcmController;
import com.genlight.epilcure.api.device.pojo.dto.ImpedanceLogDTO;
import com.genlight.epilcure.api.device.pojo.enums.ImpedanceType;
import com.genlight.epilcure.api.device.pojo.vo.BcmVO;
import com.genlight.epilcure.api.device.pojo.vo.ImpedanceLogVO;
import com.genlight.epilcure.api.patient.feign.IPatientController;
import com.genlight.epilcure.api.patient.feign.ISurgeryController;
import com.genlight.epilcure.api.patient.pojo.dto.SurgeryDTO;
import com.genlight.epilcure.common.core.dao.enums.Status;
import com.genlight.epilcure.common.core.service.BaseService;
import com.genlight.epilcure.common.core.service.exception.ServiceException;
import com.genlight.epilcure.service.device.dao.entity.ImpedanceLog;
import com.genlight.epilcure.service.device.dao.entity.ImpedanceLog_;
import com.genlight.epilcure.service.device.dao.repository.ImpedanceLogRepository;
import com.genlight.epilcure.service.device.pojo.convert.ImpedanceLogConvert;
import io.seata.spring.annotation.GlobalTransactional;
import jakarta.annotation.Resource;
import jakarta.persistence.criteria.Predicate;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

@Service
public class ImpedanceLogService extends BaseService<ImpedanceLog, Long, ImpedanceLogRepository> {

    @Resource
    private ImpedanceLogConvert impedanceLogConvert;

    @Resource
    private IPatientController iPatientController;

    @Resource
    private RedissonClient redissonClient;

    @Resource
    private IBcmController iBcmController;

    @Resource
    private ISurgeryController iSurgeryController;

    @Transactional
    public void add(List<ImpedanceLogDTO> impedanceLogDTO) {
        impedanceLogDTO.forEach(this::add);
    }

    @Transactional
    @GlobalTransactional(rollbackFor = Exception.class)
    public void add(ImpedanceLogDTO impedanceLogDTO) {

        RLock lock = redissonClient.getLock("add%s".formatted(Objects.nonNull(impedanceLogDTO.getPatientId()) ? impedanceLogDTO.getPatientId() : impedanceLogDTO.getBcmMac()));
        try {
            if (lock.tryLock(3, 3, TimeUnit.SECONDS)) {

                if (Objects.isNull(impedanceLogDTO.getPatientId())) {
                    BcmVO bcmVO = iBcmController.findByMac(impedanceLogDTO.getBcmMac());
                    impedanceLogDTO.setPatientId(bcmVO.getPatientId());
                    impedanceLogDTO.setSn(bcmVO.getSn());
                }

                ImpedanceLog impedanceLog = impedanceLogConvert.dto2po(impedanceLogDTO);
                List<ImpedanceLog> optional = repository.findByPatientIdAndSampleDate(impedanceLogDTO.getPatientId(), impedanceLogDTO.getSampleDate());
                if (Objects.nonNull(optional) && !optional.isEmpty()) {
                    optional.forEach(impedance -> {
                        if (impedance.getSampleDate().before(impedanceLogDTO.getSampleDate()))
                            impedance.setStatus(Status.DISABLED);
                        else
                            impedanceLog.setStatus(Status.DISABLED);
                    });

                }

                impedanceLog.setUserId(getUserId());
                impedanceLog.setUserName(getNikeName());
                impedanceLog.setUserPhone(getUserName());

                // 如果为术中阻抗，尝试同步手术信息
                if (Objects.nonNull(impedanceLogDTO.getType())
                        && impedanceLogDTO.getType().equals(ImpedanceType.Surgery)) {
                    iSurgeryController.updateSurgeryImpedance(SurgeryDTO.builder()
                            .patientId(impedanceLogDTO.getPatientId())
                            .impedance(impedanceLogDTO.getImpedance())
                            .build());
                }


                repository.save(impedanceLog);
            } else {
                throw new ServiceException("服务器忙,请稍后再试");
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        } finally {
            this.unLock(lock);
        }

    }

    @Transactional(readOnly = true)
    public ImpedanceLogVO findBySn(String sn) {
        ImpedanceLog impedanceLog = repository.findBySnAndType(sn, ImpedanceType.Surgery);
        if (Objects.nonNull(impedanceLog)) {
            return impedanceLogConvert.po2vo(impedanceLog);
        } else {
            return null;
        }
    }

    @Transactional(readOnly = true)
    public Page<ImpedanceLogVO> finds(ImpedanceLogDTO impedanceLogDTO, Pageable pageable) {
        Specification<ImpedanceLog> specification = (root, query, criteriaBuilder) -> {
            Predicate predicate = criteriaBuilder.conjunction();
            if (Objects.nonNull(impedanceLogDTO.getPatientId())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get(ImpedanceLog_.PATIENT_ID), impedanceLogDTO.getPatientId()));
            }

            if (Objects.nonNull(impedanceLogDTO.getArchivesId())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get(ImpedanceLog_.archivesId), impedanceLogDTO.getArchivesId()));
            }

            if (Objects.nonNull(impedanceLogDTO.getStartDate())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.greaterThanOrEqualTo(root.get(ImpedanceLog_.SAMPLE_DATE), impedanceLogDTO.getStartDate()));
            }

            if (Objects.nonNull(impedanceLogDTO.getEndDate())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.greaterThanOrEqualTo(root.get(ImpedanceLog_.SAMPLE_DATE), impedanceLogDTO.getEndDate()));
            }

            if (Objects.nonNull(impedanceLogDTO.getStatus())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get(ImpedanceLog_.STATUS), impedanceLogDTO.getStatus()));
            }
            if (Objects.nonNull(impedanceLogDTO.getSn())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get(ImpedanceLog_.sn), impedanceLogDTO.getSn()));
            }
            if (Objects.nonNull(impedanceLogDTO.getType())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get(ImpedanceLog_.type), impedanceLogDTO.getType()));
            }

            return predicate;
        };

        return impedanceLogConvert.po2vo(repository.findAll(specification, pageable));

    }
}