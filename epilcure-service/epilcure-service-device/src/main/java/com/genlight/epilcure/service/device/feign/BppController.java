package com.genlight.epilcure.service.device.feign;

import com.genlight.epilcure.api.device.feign.IBppController;
import com.genlight.epilcure.api.device.pojo.dto.BppDTO;
import com.genlight.epilcure.api.device.pojo.vo.BppVO;
import com.genlight.epilcure.service.device.service.BppService;
import io.swagger.v3.oas.annotations.Hidden;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @Date 2022/6/5 21:38
 * @Version 1.0.0
 **/
@RestController(value = "feignBppController")
@RequestMapping
@Hidden
public class BppController implements IBppController {

    @Resource
    private BppService bppService;

    @Override
    public Boolean exist(Long id) {
        return bppService.existDevice(id);
    }

    @Override
    public BppVO find(Long id) {
        return bppService.findById(id);
    }

    @Override
    public BppVO find(String bppSn) {
        return bppService.findBySn(bppSn);
    }

    @Override
    public BppVO find(Long id, Long modelId) {
        return bppService.findByIdAndModel(id, modelId);
    }

    @Override
    public BppVO update(Long id, BppDTO bppDTO) {
        return bppService.updateDevice(id, bppDTO);
    }
}
