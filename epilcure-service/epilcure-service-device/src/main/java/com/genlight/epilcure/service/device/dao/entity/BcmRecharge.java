package com.genlight.epilcure.service.device.dao.entity;

import com.genlight.epilcure.common.core.dao.entity.TEntity;
import com.genlight.epilcure.common.core.dao.generator.annotation.SignOrder;
import jakarta.persistence.*;
import lombok.*;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;
import org.hibernate.annotations.Comment;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import java.util.Date;

@Setter
@Getter
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@Entity
@Cacheable
@DynamicInsert
@DynamicUpdate
@SignOrder(0)
@Table(name = "t_bcm_recharge")
public class BcmRecharge extends TEntity {
    public BcmRecharge(Date rechargeDate, Date rechargeEndDate, Integer rechargeStart, Integer rechargeEnd, Integer voltageStart, Integer voltageEnd) {
        this.rechargeDate = rechargeDate;
        this.rechargeEndDate = rechargeEndDate;
        this.rechargeStart = rechargeStart;
        this.rechargeEnd = rechargeEnd;
        this.voltageStart = voltageStart;
        this.voltageEnd = voltageEnd;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(nullable = false)
    @Comment("充电日期")
    @SignOrder(1)
    private Date rechargeDate;
    @Temporal(TemporalType.TIMESTAMP)
    @Column(nullable = false)
    @Comment("充电结束时间")
    @SignOrder(2)
    private Date rechargeEndDate;

    @Comment("开始电量")
    @Column(nullable = false)
    @SignOrder(3)
    private Integer rechargeStart;

    @Comment("结束电量")
    @Column(nullable = false)
    @SignOrder(4)
    private Integer rechargeEnd;

    @Comment("电压开始")
    private Integer voltageStart;

    @Comment("电压结束")
    private Integer voltageEnd;
    @ToString.Exclude
    @ManyToOne(fetch = FetchType.LAZY)
    private Bcm bcm;
}
