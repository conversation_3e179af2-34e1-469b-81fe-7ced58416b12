package com.genlight.epilcure.service.device.controller;

import com.fasterxml.jackson.annotation.JsonView;
import com.genlight.epilcure.api.device.pojo.dto.ElectrodeDTO;
import com.genlight.epilcure.api.device.pojo.dto.ModelDTO.ElectrodeValid;
import com.genlight.epilcure.api.device.pojo.vo.DeviceGroupBatchNoVO;
import com.genlight.epilcure.api.device.pojo.vo.ElectrodeVO;
import com.genlight.epilcure.common.core.mvc.response.JsonResult;
import com.genlight.epilcure.common.core.pojo.valid.Add;
import com.genlight.epilcure.common.core.pojo.valid.Update;
import com.genlight.epilcure.service.device.pojo.vo.DeviceStatisticsVO;
import com.genlight.epilcure.service.device.service.ElectrodeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;

@RestController
@RequestMapping("/api/device/electrodes")
@Tag(name = "ElectrodeController", description = "电极相关接口")
public class ElectrodeController {
    @Resource
    private ElectrodeService electrodeService;


    @GetMapping("/finds")
    @Operation(summary = "根据条件查询电极设备")
    public JsonResult<Page<ElectrodeVO>> findsByPageable(ElectrodeDTO electrodeDTO, Pageable pageable) {
        return JsonResult.ok(electrodeService.searchDevice(electrodeDTO, pageable));
    }

    @GetMapping("/findGroupByBatchNo")
    @Operation(summary = "查询所有批号")
    public JsonResult<List<String>> findGroupByBatchNo(@RequestParam(required = false) Long modelId) {
        return JsonResult.ok(electrodeService.findGroupByBatchNo(modelId));
    }


    @PostMapping
    @Operation(summary = "新增电极设备")
    @JsonView(Add.class)
    public JsonResult<ElectrodeVO> addElectrode(@JsonView(ElectrodeValid.class)
                                                @Validated(ElectrodeValid.class)
                                                @RequestBody ElectrodeDTO electrodeDTO) {
        return JsonResult.ok(electrodeService.addDevice(electrodeDTO));
    }

    @PutMapping("/audit")
    @Operation(summary = "审核电极设备")
    public JsonResult<String> auditBpp(@Validated(Update.class) @RequestBody List<ElectrodeDTO> electrodeDTOList) {
        electrodeService.auditDevice(electrodeDTOList);
        return JsonResult.ok("审核完成");
    }

    @PutMapping("/{id}")
    @Operation(summary = "根据Id修改电极信息")
    public JsonResult<ElectrodeVO> update(@PathVariable Long id, @RequestBody ElectrodeDTO electrodeDTO) {
        return JsonResult.ok(electrodeService.updateDevice(id, electrodeDTO));
    }

    @GetMapping("/findDeviceGroup")
    @Operation(summary = "获取审核统计信息")
    public JsonResult<List<DeviceGroupBatchNoVO>> findDeviceGroup(@RequestParam(required = false) Long modelId) {
        return JsonResult.ok(electrodeService.searchNearDeviceGroupBatchNo(modelId));
    }

    @GetMapping("/statistics")
    @Operation(summary = "分型号统计设备使用情况")
    public JsonResult<List<DeviceStatisticsVO>> statistics(@RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) Date startDate, @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) Date endDate, @RequestParam Long diseaseId) {
        return JsonResult.ok(electrodeService.statistics(startDate, endDate, diseaseId));
    }

    @GetMapping("/{id}")
    @Operation(summary = "根据设备Id查询")
    public JsonResult<ElectrodeVO> findById(@PathVariable Long id) {
        return JsonResult.ok(electrodeService.findById(id));
    }
}
