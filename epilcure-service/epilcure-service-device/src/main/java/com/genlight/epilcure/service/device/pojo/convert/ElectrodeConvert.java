package com.genlight.epilcure.service.device.pojo.convert;

import com.genlight.epilcure.api.device.pojo.dto.ElectrodeDTO;
import com.genlight.epilcure.api.device.pojo.vo.ElectrodeVO;
import com.genlight.epilcure.common.core.pojo.convert.BaseConvert;
import com.genlight.epilcure.service.device.dao.entity.Electrode;
import org.mapstruct.BeanMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.NullValueCheckStrategy;

@Mapper(config = BaseConvert.class)
public interface ElectrodeConvert extends BaseConvert<Electrode, ElectrodeVO, ElectrodeDTO> {
    @Mapping(target = "modelId", source = "model.id")
    @Mapping(target = "modelName", source = "model.model")
    @BeanMapping(nullValueCheckStrategy = NullValueCheckStrategy.ON_IMPLICIT_CONVERSION)
    @Override
    ElectrodeVO po2vo(Electrode electrode);

    @Mapping(target = "deviceStatus", expression = "java(java.util.Optional.ofNullable(electrodeDTO.getDeviceStatus()).orElse(com.genlight.epilcure.api.device.enums.DeviceStatus.produced))")
    @BeanMapping(nullValueCheckStrategy = NullValueCheckStrategy.ON_IMPLICIT_CONVERSION)
    @Override
    Electrode dto2po(ElectrodeDTO electrodeDTO);
}
