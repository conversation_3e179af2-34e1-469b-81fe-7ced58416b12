package com.genlight.epilcure.service.device.pojo.convert;

import com.genlight.epilcure.api.device.pojo.dto.BcmDTO;
import com.genlight.epilcure.api.device.pojo.vo.BcmVO;
import com.genlight.epilcure.common.core.pojo.convert.BaseConvert;
import com.genlight.epilcure.common.core.pojo.convert.qualified.Basic;
import com.genlight.epilcure.service.device.dao.entity.Bcm;
import org.mapstruct.BeanMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.NullValueCheckStrategy;

@Mapper(config = BaseConvert.class, uses = BppConvert.class)
public interface BcmConvert extends BaseConvert<Bcm, BcmVO, BcmDTO> {
    @Mapping(target = "bpp", qualifiedByName = "BppConvert", qualifiedBy = Basic.class)
    @Mapping(target = "modelId", source = "model.id")
    @Mapping(target = "modelName", source = "model.model")
    @BeanMapping(nullValueCheckStrategy = NullValueCheckStrategy.ON_IMPLICIT_CONVERSION)
    @Override
    BcmVO po2vo(Bcm bcm);

    @Mapping(target = "rechargeCount", expression = "java(java.util.Optional.ofNullable(bcmDTO.getRechargeCount()).orElse((short)0))")
    @Mapping(target = "hardwareVersion", expression = "java(java.util.Optional.ofNullable(bcmDTO.getHardwareVersion()).orElse(\"1.0.0\"))")
    @Mapping(target = "softwareVersion", expression = "java(java.util.Optional.ofNullable(bcmDTO.getSoftwareVersion()).orElse(\"1.0.0\"))")
    @Mapping(target = "deviceStatus", expression = "java(java.util.Optional.ofNullable(bcmDTO.getDeviceStatus()).orElse(com.genlight.epilcure.api.device.enums.DeviceStatus.produced))")
    @BeanMapping(nullValueCheckStrategy = NullValueCheckStrategy.ON_IMPLICIT_CONVERSION)
    @Override
    Bcm dto2po(BcmDTO bcmDTO);
}
