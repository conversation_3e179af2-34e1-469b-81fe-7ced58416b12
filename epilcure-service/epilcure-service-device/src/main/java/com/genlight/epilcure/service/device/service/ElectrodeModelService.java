package com.genlight.epilcure.service.device.service;

import com.genlight.epilcure.api.device.pojo.dto.ElectrodeModelDTO;
import com.genlight.epilcure.api.device.pojo.vo.ElectrodeModelVO;
import com.genlight.epilcure.service.device.dao.entity.ElectrodeModel;
import com.genlight.epilcure.service.device.dao.repository.ElectrodeModelRepository;
import com.genlight.epilcure.service.device.pojo.convert.ElectrodeModelConvert;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.stereotype.Service;

@Service
@CacheConfig(cacheNames = "electrodeModel")
public class ElectrodeModelService extends ModelService<ElectrodeModelRepository, ElectrodeModel, ElectrodeModelDTO, ElectrodeModelVO, ElectrodeModelConvert> {
}
