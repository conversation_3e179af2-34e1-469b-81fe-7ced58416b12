package com.genlight.epilcure.service.device.service;

import com.genlight.epilcure.api.device.pojo.dto.BppDTO;
import com.genlight.epilcure.api.device.pojo.dto.BppModelDTO;
import com.genlight.epilcure.api.device.pojo.vo.BppModelVO;
import com.genlight.epilcure.api.device.pojo.vo.BppVO;
import com.genlight.epilcure.common.core.service.exception.ServiceException;
import com.genlight.epilcure.service.device.dao.entity.Bpp;
import com.genlight.epilcure.service.device.dao.entity.BppModel;
import com.genlight.epilcure.service.device.dao.repository.BppModelRepository;
import com.genlight.epilcure.service.device.dao.repository.BppRepository;
import com.genlight.epilcure.service.device.pojo.convert.BppConvert;
import com.genlight.epilcure.service.device.pojo.convert.BppModelConvert;
import jakarta.annotation.Resource;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import java.util.concurrent.TimeUnit;

@Service
@CacheConfig(cacheNames = "bpp")
public class BppService extends DeviceService<BppModel, Bpp, BppModelVO, BppModelDTO, BppVO, BppDTO, BppModelRepository, BppRepository, BppModelConvert, BppConvert> {

    @Resource
    private RedissonClient redissonClient;

    @Override
    @Transactional
    public BppVO addDevice(BppDTO bppDTO) {
        RLock lock = redissonClient.getLock("addBPPDevice-%s".formatted(bppDTO.getMac()));
        try {
            if (lock.tryLock(2, 2, TimeUnit.SECONDS)) {
                boolean b = repository.existsByMac(bppDTO.getMac());
                Assert.isTrue(!b, "mac地址已存在");
                return super.addDevice(bppDTO);
            }
            throw new ServiceException("操作太频繁，请稍后再试！");
        } catch (Exception e) {
            throw new RuntimeException(e);
        } finally {
            this.unLock(lock);
        }
    }
}
