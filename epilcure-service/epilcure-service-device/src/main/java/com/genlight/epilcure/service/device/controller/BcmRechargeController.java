package com.genlight.epilcure.service.device.controller;

import com.fasterxml.jackson.annotation.JsonView;
import com.genlight.epilcure.common.core.mvc.response.JsonResult;
import com.genlight.epilcure.common.core.pojo.valid.Add;
import com.genlight.epilcure.common.core.pojo.valid.Find;
import com.genlight.epilcure.service.device.pojo.dto.BcmRechargeDTO;
import com.genlight.epilcure.service.device.pojo.vo.BcmRechargeStatsVO;
import com.genlight.epilcure.service.device.pojo.vo.BcmRechargeVO;
import com.genlight.epilcure.service.device.service.BcmRechargeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/device/bcmRecharges")
@Tag(name = "BcmRechargeController", description = "充电记录相关接口")
public class BcmRechargeController {
    @Resource
    private BcmRechargeService bcmRechargeService;

    @PostMapping
    @Operation(summary = "新增充电记录")
    @JsonView(Add.class)
    public JsonResult<BcmRechargeVO> add(@Validated(Add.class) @RequestBody BcmRechargeDTO bcmRechargeDTO) {
        return JsonResult.ok(bcmRechargeService.add(bcmRechargeDTO));
    }

    @PostMapping("/batchSave")
    @Operation(summary = "新增充电记录")
    @JsonView(Add.class)
    public JsonResult<List<BcmRechargeVO>> add(@Validated(Add.class) @RequestBody List<BcmRechargeDTO> bcmRechargeDTOS) {
        return JsonResult.ok(bcmRechargeService.addBatch(bcmRechargeDTOS));
    }

    @GetMapping("/stats")
    public JsonResult<BcmRechargeStatsVO> stats(@Validated(Find.class) BcmRechargeDTO bcmRechargeDTO) {
        return JsonResult.ok(bcmRechargeService.stats(bcmRechargeDTO));
    }

    @GetMapping
    @Operation(summary = "根据条件查询充电记录")
    @JsonView(Find.class)
    public JsonResult<Page<BcmRechargeVO>> finds(BcmRechargeDTO bcmRechargeDTO, Pageable pageable) {
        return JsonResult.ok(bcmRechargeService.finds(bcmRechargeDTO, pageable));
    }
}
