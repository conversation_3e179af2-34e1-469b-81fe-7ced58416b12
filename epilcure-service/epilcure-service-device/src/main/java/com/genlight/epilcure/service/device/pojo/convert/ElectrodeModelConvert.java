package com.genlight.epilcure.service.device.pojo.convert;

import com.genlight.epilcure.api.device.pojo.dto.ElectrodeModelDTO;
import com.genlight.epilcure.api.device.pojo.vo.ElectrodeModelVO;
import com.genlight.epilcure.common.core.pojo.convert.BaseConvert;
import com.genlight.epilcure.service.device.dao.entity.ElectrodeModel;
import org.mapstruct.Mapper;

@Mapper(config = BaseConvert.class)
public interface ElectrodeModelConvert extends BaseConvert<ElectrodeModel, ElectrodeModelVO, ElectrodeModelDTO> {
}
