package com.genlight.epilcure.service.device.pojo.convert;

import com.genlight.epilcure.api.device.pojo.dto.BppParamDTO;
import com.genlight.epilcure.api.device.pojo.vo.BppParamVO;
import com.genlight.epilcure.common.core.pojo.convert.BaseConvert;
import com.genlight.epilcure.service.device.dao.entity.BppParam;
import org.mapstruct.BeanMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.NullValueCheckStrategy;

@Mapper(config = BaseConvert.class)
public interface BppParamConvert extends BaseConvert<BppParam, BppParamVO, BppParamDTO> {
    @Mapping(target = "status", expression = "java(java.util.Optional.ofNullable(bppParamDTO.getStatus()).orElse(com.genlight.epilcure.common.core.dao.enums.Status.ENABLED))")
    @BeanMapping(nullValueCheckStrategy = NullValueCheckStrategy.ON_IMPLICIT_CONVERSION)
    @Override
    BppParam dto2po(BppParamDTO bppParamDTO);
}
