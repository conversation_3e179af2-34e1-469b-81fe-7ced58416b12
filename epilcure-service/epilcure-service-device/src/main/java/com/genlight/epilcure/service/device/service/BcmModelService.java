package com.genlight.epilcure.service.device.service;

import com.genlight.epilcure.api.device.pojo.dto.BcmModelDTO;
import com.genlight.epilcure.api.device.pojo.vo.BcmModelVO;
import com.genlight.epilcure.service.device.dao.entity.BcmModel;
import com.genlight.epilcure.service.device.dao.repository.BcmModelRepository;
import com.genlight.epilcure.service.device.pojo.convert.BcmModelConvert;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.stereotype.Service;

@Service
@CacheConfig(cacheNames = "bcmModel")
public class BcmModelService extends ModelService<BcmModelRepository, BcmModel, BcmModelDTO, BcmModelVO, BcmModelConvert> {
}
