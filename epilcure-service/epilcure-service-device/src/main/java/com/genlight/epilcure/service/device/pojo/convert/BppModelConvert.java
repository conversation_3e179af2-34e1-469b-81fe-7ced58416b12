package com.genlight.epilcure.service.device.pojo.convert;

import com.genlight.epilcure.api.device.pojo.dto.BppModelDTO;
import com.genlight.epilcure.api.device.pojo.vo.BppModelVO;
import com.genlight.epilcure.common.core.pojo.convert.BaseConvert;
import com.genlight.epilcure.service.device.dao.entity.BppModel;
import org.mapstruct.Mapper;

/**
 * <AUTHOR>
 * @Date 2022/5/24 22:12
 * @Version 1.0.0
 **/
@Mapper(config = BaseConvert.class)
public interface BppModelConvert extends BaseConvert<BppModel, BppModelVO, BppModelDTO> {
}
