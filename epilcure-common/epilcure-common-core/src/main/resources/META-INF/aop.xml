<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE aspectj PUBLIC "-//AspectJ//DTD//EN" "http://www.eclipse.org/aspectj/dtd/aspectj.dtd">
<aspectj>

    <!--    -debug Issue a messages for each class passed to the weaver indicating whether it was woven, excluded or ignored.-->
    <!--    -nowarn Suppress warning messages-->
    <!--    -verbose Issue informational messages about the weaving process.-->
    <!--    -showWeaveInfo Issue informational messages whenever the weaver touches a class file.-->
    <!--    -XnoInline Don't inline around advice.-->
    <!--    -Xreweavable Produce class files that can subsequently be rewoven-->
    <!--    -Xlint:default, -Xlint:ignore, ... Configure lint messages, refer to documentation for meaningfull values-->
    <!--    -Xlintfile:pathToAResource Configure lint messages as specified in the given resource (visible from the classloader for this aop xml file)-->
    <!--    -XmessageHandlerClass:... Provide alternative output destination to stdout/stderr for all weaver messages.-->
    <!--    -XmessageHandlerClass:org.springframework.aop.aspectj.AspectJWeaverMessageHandler-->

    <weaver options="-Xreweavable -Xset:weaveJavaxPackages=true -XmessageHandlerClass:org.springframework.aop.aspectj.AspectJWeaverMessageHandler">
        <include within="com.genlight.epilcure..*"/>
    </weaver>

    <aspects>
        <aspect name="com.genlight.epilcure.common.core.mvc.aspectj.AccessAspect"/>
        <aspect name="com.genlight.epilcure.common.core.mvc.aspectj.LogstashAspect"/>
        <aspect name="com.genlight.epilcure.common.core.mvc.aspectj.FileValidAspect"/>
        <!--        <aspect name="com.genlight.epilcure.common.core.mvc.aspectj.DistributedLockAspect"/>-->
    </aspects>
</aspectj>
