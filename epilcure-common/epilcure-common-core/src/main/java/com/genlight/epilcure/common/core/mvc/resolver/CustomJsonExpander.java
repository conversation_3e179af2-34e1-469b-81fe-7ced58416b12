package com.genlight.epilcure.common.core.mvc.resolver;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import feign.Param.Expander;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;

@Component
@Slf4j
public class CustomJsonExpander implements Expander {
    @Autowired
    private ObjectMapper objectMapper;

    @Override
    public String expand(Object value) {
        try {
            String s = null;
            if (value instanceof Date) {
                s = ((Date) value).getTime() + "";
            } else {
                s = objectMapper.writeValueAsString(value);
            }
            log.debug("feign expander:{}:{}", value.toString(), s);
            return s;
        } catch (JsonProcessingException e) {
            e.printStackTrace();
            return "";
        }
    }

}