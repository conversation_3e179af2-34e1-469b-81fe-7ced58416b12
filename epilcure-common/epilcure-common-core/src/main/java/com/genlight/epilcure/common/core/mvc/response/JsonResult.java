package com.genlight.epilcure.common.core.mvc.response;

import com.genlight.epilcure.common.core.constants.HttpCode;
import com.genlight.epilcure.common.core.service.exception.BaseException;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0.0
 **/
@Data
@Schema(description = "统一的返回")
public class JsonResult<D> implements Serializable {

    @Schema(description = "状态：0为正常，其他为异常")
    private int status;

    @Schema(description = "返回的提示")
    private String msg;

    @Schema(description = "数据状态")
    private String dataStatus;

    @Schema(description = "返回的数据")
    private D data;

    private JsonResult(HttpCode code) {
        this.status = code.getCode();
    }

    private JsonResult(HttpCode code, D data) {
        this.data = data;
        this.status = code.getCode();
    }

    private JsonResult(BaseException e) {
        this.msg = e.getMessage();
        this.status = e.getHttpCode().getCode();
    }

    private JsonResult(BaseException e, D data) {
        this.data = data;
        this.msg = e.getMessage();
        this.status = e.getHttpCode().getCode();
    }

    private JsonResult(int status, D data, String msg) {
        this.status = status;
        this.data = data;
        this.msg = msg;
    }

    private JsonResult(int status, D data, String dataStatus, String msg) {
        this.status = status;
        this.dataStatus = dataStatus;
        this.msg = msg;
    }

    public static <T> JsonResult<T> ok() {
        return new JsonResult<>(HttpCode.SUCCESS);
    }

    public static <T> JsonResult<T> ok(T data) {
        return new JsonResult<>(HttpCode.SUCCESS, data);
    }

    public static <T> JsonResult<T> error() {
        return new JsonResult<>(HttpCode.ERROR);
    }

    public static <T> JsonResult<T> error(String message) {
        return new JsonResult<>(HttpCode.ERROR.getCode(), null, message);
    }

    public static <T> JsonResult<T> error(BaseException e) {
        return new JsonResult<>(e);
    }

    public static <T> JsonResult<T> error(BaseException e, T data) {
        return new JsonResult<>(e, data);
    }

    public static <T> JsonResult<T> of(int status, T data, String msg) {
        return new JsonResult<>(status, data, msg);
    }

    public static <T> JsonResult<T> of(int status, T data, String dataStatus, String msg) {
        return new JsonResult<>(status, data, dataStatus, msg);
    }

    @Override
    public String toString() {
        return "JsonResult{" + "status=" + status + ", msg='" + msg + '\'' + ", data=" + data + '}';
    }
}
