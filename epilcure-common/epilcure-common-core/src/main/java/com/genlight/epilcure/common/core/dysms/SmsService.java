package com.genlight.epilcure.common.core.dysms;

import com.aliyuncs.CommonRequest;
import com.aliyuncs.DefaultAcsClient;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.genlight.epilcure.common.core.constants.HttpCode;
import com.genlight.epilcure.common.core.dao.enums.SmsType;
import com.genlight.epilcure.common.core.mvc.config.DysmsConfig;
import com.genlight.epilcure.common.core.pojo.bo.ValidSmsBO;
import com.genlight.epilcure.common.core.service.exception.ArgsException;
import com.genlight.epilcure.common.core.service.exception.ServiceException;
import jakarta.annotation.Resource;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.io.Serializable;
import java.util.Date;
import java.util.Objects;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

@Service
public class SmsService {
    @Resource
    private DefaultAcsClient client;

    @Resource
    private CommonRequest commonRequest;

    @Resource
    private DysmsConfig dysmsConfig;

    @Resource
    private RedisTemplate<String, String> redisTemplate;

    @Resource
    private ObjectMapper objectMapper;

    public static final String PhoneNumbers = "PhoneNumbers";
    public static final String SignName = "SignName";
    public static final String TemplateCode = "TemplateCode";
    public static final String TemplateParam = "TemplateParam";

    /**
     * 校验验证码
     *
     * @param phone 目标号码
     * @param uid   短信返回的uid
     * @param code  客户端请求的验证码
     */
    public void checkValid(String phone, String uid, String code) {
        checkValid(phone, uid, code, true);
    }

    public void checkValid(String phone, String uid, String code, Boolean needDelete) {
        if (!uid.startsWith(phone)) {
            throw new ArgsException("验证码错误");
        }
        String s = redisTemplate.opsForValue().get(uid);
        try {
            if (Objects.isNull(s) || !code.equals(objectMapper.readValue(s, ValidSmsBO.class).getCode())) {
                throw new ArgsException("验证码错误");
            }
            if (needDelete) {
                redisTemplate.delete(uid);
                redisTemplate.delete("smsPhone" + phone);
            }
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 发送模板短信
     *
     * @param phone   目标号码
     * @param smsType 模板类型
     * @param param   可以为Json串
     * @return 校验码则返回uuId, 否则返回空
     */
    public <T extends Serializable> String sendSms(String phone, SmsType smsType, T param) {


        if (StringUtils.hasText(redisTemplate.opsForValue().get("smsPhone" + phone))) {
            throw new ServiceException(HttpCode.SMS_BUSY, "短信发送频繁,请稍后再试");
        }

        redisTemplate.opsForValue().set("smsPhone" + phone, String.valueOf(new Date().getTime()), dysmsConfig.getInterval(), TimeUnit.SECONDS);

        try {
            String paramStr = objectMapper.writeValueAsString(param);

            commonRequest.putQueryParameter(PhoneNumbers, phone);
            commonRequest.putQueryParameter(SignName, dysmsConfig.getSign());
            commonRequest.putQueryParameter(TemplateCode, dysmsConfig.getCodes().get(smsType.toValue()));
            commonRequest.putQueryParameter(TemplateParam, paramStr);

            client.getCommonResponse(commonRequest);

            if (smsType == SmsType.Valid) {
                String uid = phone + "-" + UUID.randomUUID();
                redisTemplate.opsForValue().set(uid, paramStr, dysmsConfig.getTermValidity(), TimeUnit.SECONDS);
                return uid;
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return "";
    }
}
