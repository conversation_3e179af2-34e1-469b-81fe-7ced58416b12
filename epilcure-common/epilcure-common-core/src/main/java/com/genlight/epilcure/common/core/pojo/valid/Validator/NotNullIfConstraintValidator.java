package com.genlight.epilcure.common.core.pojo.valid.Validator;

import com.genlight.epilcure.common.core.pojo.valid.NotNullIf;
import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import org.springframework.beans.BeanWrapperImpl;

import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0.0
 */
public class NotNullIfConstraintValidator implements ConstraintValidator<NotNullIf, Object> {

    private String checkField;
    private String dependField;

    public void initialize(NotNullIf constraintAnnotation) {
        checkField = constraintAnnotation.checkField();
        dependField = constraintAnnotation.dependField();
    }

    @Override
    public boolean isValid(Object o, ConstraintValidatorContext constraintValidatorContext) {
        final BeanWrapperImpl beanWrapper = new BeanWrapperImpl(o);
        if (Objects.isNull(beanWrapper.getPropertyValue(checkField)) && Objects.isNull(beanWrapper.getPropertyValue(dependField))) {
            constraintValidatorContext.disableDefaultConstraintViolation();
            constraintValidatorContext.buildConstraintViolationWithTemplate(constraintValidatorContext.getDefaultConstraintMessageTemplate())
                    .addPropertyNode(checkField)
                    .addConstraintViolation();
            return false;
        }
        return true;
    }
}