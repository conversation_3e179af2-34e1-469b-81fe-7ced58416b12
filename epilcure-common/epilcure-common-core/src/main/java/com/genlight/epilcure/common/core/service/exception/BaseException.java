package com.genlight.epilcure.common.core.service.exception;

import com.genlight.epilcure.common.core.constants.HttpCode;
import lombok.Getter;

/**
 * <AUTHOR>
 * @version 1.0.0
 **/
@Getter
public class BaseException extends RuntimeException {

    private final HttpCode httpCode;

    public BaseException(HttpCode httpCode) {
        super(httpCode.getMessage());
        this.httpCode = httpCode;
    }

    public BaseException(HttpCode httpCode, String message) {
        super(message);
        this.httpCode = httpCode;
    }

    public BaseException(HttpCode httpCode, Throwable cause) {
        super(cause);
        this.httpCode = httpCode;
    }
}
