package com.genlight.epilcure.common.core.util;

import com.genlight.epilcure.common.core.constants.HttpCode;
import com.genlight.epilcure.common.core.properties.JWTProperties;
import com.genlight.epilcure.common.core.service.exception.ServiceException;
import com.nimbusds.jose.*;
import com.nimbusds.jose.crypto.MACSigner;
import com.nimbusds.jose.crypto.MACVerifier;
import jakarta.servlet.http.HttpServletRequest;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0.0
 **/
@Slf4j
public class JwtUtils {

    public static final String authoritiesKey = "authorities";
    public static final String patientAuthoritiesKey = "patientAuthorities";
    public static final String orgIds = "orgIds";
    public static final String secret = "hello world goodbye thank you very much see you next time";
    public static final String os = "os";
    public static final String device = "device";
    public static final String userIdKey = "userId";
    public static final String usernameKey = "username";
    public static final String nikeName = "nikeName";
    public static final String isPatient = "isPatient";

    public static final String engineer = "engineer";

    static {
        log.info("spring security jwt secret: {}", secret);
    }


    @SneakyThrows
    public static String createJWT(String username, String nikename, Long userId, Boolean isPatient, Boolean engineer, String device, String os, JWTProperties jwtProperties) {
        // jwt 头
        JWSHeader jwsHeader = new JWSHeader.Builder(JWSAlgorithm.HS256)
                .type(JOSEObjectType.JWT)
//        1000 * 60 * 60 * 24 * 3
                .customParam("exp", new Date().getTime() + 1000*jwtProperties.getExpired())
                .build();

        // jwt 荷载
        Map<String, Object> obj = new HashMap<>();
        obj.put(JwtUtils.os, os);
        obj.put(JwtUtils.device, device);
        obj.put(JwtUtils.usernameKey, username);
        obj.put(JwtUtils.userIdKey, userId);
        obj.put(JwtUtils.nikeName, nikename);
        obj.put(JwtUtils.isPatient, isPatient);
        obj.put(JwtUtils.engineer, engineer);

        Payload payload = new Payload(obj);

        // jwt 头 + 荷载 + 密钥 = 签名
        JWSSigner jwsSigner = new MACSigner(secret);
        JWSObject jwsObject = new JWSObject(jwsHeader, payload);

        // 进行签名（根据前两部分生成第三部分）
        jwsObject.sign(jwsSigner);

        // 获得 jwt string
        return jwsObject.serialize();
    }

    @SneakyThrows
    public static String createJWT(String username, String nikeName, Long userId, Boolean engineer, String device, String os,JWTProperties jwtProperties) {
        return createJWT(username, nikeName, userId, false, engineer, device, os,jwtProperties);
    }

    @SneakyThrows
    public static boolean verify(String jwtString) {
        try {
            JWSObject jwsObject = JWSObject.parse(jwtString);
            JWSVerifier jwsVerifier = new MACVerifier(secret);
            if (jwsObject.getHeader().getCustomParams().containsKey("exp")) {
                Long exp = Long.parseLong(jwsObject.getHeader().getCustomParams().get("exp").toString());
                if (new Date().getTime() > exp) {
                    return false;
                }
            }
            return jwsObject.verify(jwsVerifier);
        } catch (Exception ex) {
            throw new ServiceException(HttpCode.AUTHENTICATION, "无效的token");
        }
    }

    @SneakyThrows
    public static String getOS(String jwtString) {
        JWSObject jwsObject = JWSObject.parse(jwtString);
        Map<String, Object> map = jwsObject.getPayload().toJSONObject();
        return map.get(os).toString();
    }

    @SneakyThrows
    public static String getDevice(String jwtString) {
        JWSObject jwsObject = JWSObject.parse(jwtString);
        Map<String, Object> map = jwsObject.getPayload().toJSONObject();
        return map.get(device).toString();
    }

    @SneakyThrows
    public static String getUsernameFromJWT(String jwtString) {
        JWSObject jwsObject = JWSObject.parse(jwtString);
        Map<String, Object> map = jwsObject.getPayload().toJSONObject();
        return (String) map.get(usernameKey);
    }

    @SneakyThrows
    public static Boolean getIsPatient(String jwtString) {
        JWSObject jwsObject = JWSObject.parse(jwtString);
        Map<String, Object> map = jwsObject.getPayload().toJSONObject();
        return (Boolean) map.get(isPatient);
    }

    @SneakyThrows
    public static Boolean getIsEngineer(String jwtString) {
        JWSObject jwsObject = JWSObject.parse(jwtString);
        Map<String, Object> map = jwsObject.getPayload().toJSONObject();
        return (Boolean) map.get(engineer);
    }

    @SneakyThrows
    public static Long getUserIdFormJWT(String jwtString) {
        JWSObject jwsObject = JWSObject.parse(jwtString);
        Map<String, Object> map = jwsObject.getPayload().toJSONObject();
        return Long.parseLong(map.get(userIdKey).toString());
    }

    @SneakyThrows
    public static String getNikeNameFromJWT(String jwtString) {
        JWSObject jwsObject = JWSObject.parse(jwtString);
        Map<String, Object> map = jwsObject.getPayload().toJSONObject();
        return (String) map.get(nikeName);
    }

    @SneakyThrows
    public static String getAuthoritiesFromJwt(String jwtString) {
        JWSObject jwsObject = JWSObject.parse(jwtString);
        Map<String, Object> map = jwsObject.getPayload().toJSONObject();
        return (String) map.get(authoritiesKey);
    }

    @SneakyThrows
    public static String getUsernameFromRequest(HttpServletRequest request) {
        return getUsernameFromJWT(request.getHeader("token"));
    }

}