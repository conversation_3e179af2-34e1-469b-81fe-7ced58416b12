package com.genlight.epilcure.common.core.controller;

import jakarta.annotation.Resource;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.client.ServiceInstance;
import org.springframework.cloud.client.discovery.DiscoveryClient;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.List;

@RestController
@RequestMapping("/health")
public class NacosController {
    @Value("${spring.application.name}")
    private String applicationName;
    @Resource
    private DiscoveryClient discoveryClient;

    @GetMapping
    public ResponseEntity<Object> health() {
        List<ServiceInstance> instances = discoveryClient.getInstances(applicationName);
        Boolean health = false;
        for (ServiceInstance i : instances) {
            try {
                InetAddress[] inetAddresses = InetAddress.getAllByName(InetAddress.getLocalHost().getHostName());
                for (InetAddress inetAddress : inetAddresses) {
                    if (i.getHost().equals(inetAddress.getHostAddress())) {
                        health = true;
                        break;
                    }
                }
                if (health) {
                    break;
                }
            } catch (UnknownHostException e) {
                return new ResponseEntity<>(HttpStatus.SERVICE_UNAVAILABLE);
            }
        }
        if (health) {
            return new ResponseEntity<>(HttpStatus.OK);
        }
        return new ResponseEntity<>(HttpStatus.SERVICE_UNAVAILABLE);
    }
}
