package com.genlight.epilcure.common.core.util;

import com.genlight.epilcure.common.core.dao.generator.SnowflakeAlg;
import com.genlight.epilcure.common.core.dao.properties.SnowflakeAlgProperties;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 雪花算法性能测试
 */
public class SnowflakePerformanceTest {

    public static void main(String[] args) {
        SnowflakeAlg snowflake = new SnowflakeAlg(new SnowflakeAlgProperties());

        // 单线程性能测试
        singleThreadTest(snowflake);

        // 多线程性能测试
        multiThreadTest(snowflake);

        // 内存使用测试
        //memoryUsageTest();
    }

    /**
     * 单线程性能测试
     */
    public static void singleThreadTest(SnowflakeAlg snowflake) {
        System.out.println("=== 单线程性能测试 ===");

        int count = 1000000;
        long startTime = System.currentTimeMillis();

        for (int i = 0; i < count; i++) {
            snowflake.nextId();
        }

        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;

        System.out.println("生成数量: " + count);
        System.out.println("耗时: " + duration + "ms");
        System.out.println("QPS: " + (count * 1000L / duration));
        System.out.println("平均耗时: " + (duration * 1000000L / count) + "ns");
    }

    /**
     * 多线程性能测试
     */
    public static void multiThreadTest(SnowflakeAlg snowflake) {
        System.out.println("\n=== 多线程性能测试 ===");

        int threadCount = 10;
        int countPerThread = 100000;
        CountDownLatch latch = new CountDownLatch(threadCount);
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);

        AtomicLong totalCount = new AtomicLong(0);
        Set<Long> idSet = ConcurrentHashMap.newKeySet();

        long startTime = System.currentTimeMillis();

        for (int i = 0; i < threadCount; i++) {
            executor.submit(() -> {
                try {
                    for (int j = 0; j < countPerThread; j++) {
                        long id = snowflake.nextId();
                        idSet.add(id);
                        totalCount.incrementAndGet();
                    }
                } finally {
                    latch.countDown();
                }
            });
        }

        try {
            latch.await();
            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;

            System.out.println("线程数: " + threadCount);
            System.out.println("每线程生成数: " + countPerThread);
            System.out.println("总生成数: " + totalCount.get());
            System.out.println("唯一ID数: " + idSet.size());
            System.out.println("耗时: " + duration + "ms");
            System.out.println("QPS: " + (totalCount.get() * 1000L / duration));
            System.out.println("重复率: " + (1.0 - (double) idSet.size() / totalCount.get()) * 100 + "%");

        } catch (InterruptedException e) {
            e.printStackTrace();
        } finally {
            executor.shutdown();
        }
    }

}

