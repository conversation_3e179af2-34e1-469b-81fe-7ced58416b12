package com.genlight.epilcure.common.core.service.exception;

import com.genlight.epilcure.common.core.constants.HttpCode;
import lombok.Getter;

import java.text.MessageFormat;
import java.util.Arrays;

/**
 * <AUTHOR>
 * @version 1.0.0
 **/
@Getter
public class ArgsException extends BaseException {

    private String dataStatus;

    public ArgsException() {
        super(HttpCode.ARGUMENT);
    }

    public ArgsException(Throwable cause) {
        super(HttpCode.ARGUMENT, cause);
    }

    public ArgsException(String message, Object... args) {
        super(HttpCode.ARGUMENT, MessageFormat.format(message, Arrays.stream(args).toArray()));
    }

    public ArgsException(String message, HttpCode httpCode, Object... args) {
        super(httpCode, MessageFormat.format(message, Arrays.stream(args).toArray()));
    }

    public ArgsException(String dataStatus, String message, HttpCode httpCode, Object... args) {
        super(httpCode, MessageFormat.format(message, Arrays.stream(args).toArray()));
        this.dataStatus = dataStatus;
    }

    public ArgsException(HttpCode httpCode) {
        super(httpCode);
    }
}
