package com.genlight.epilcure.common.core.mvc.resolver;

import com.genlight.epilcure.common.core.mvc.binder.DeepObjectDataBinder;
import com.genlight.epilcure.common.core.pojo.dto.BaseDTO;
import jakarta.servlet.ServletRequest;
import org.springframework.core.MethodParameter;
import org.springframework.util.Assert;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.context.request.NativeWebRequest;
import org.springframework.web.servlet.mvc.method.annotation.ServletModelAttributeMethodProcessor;


public class DeepObjectHandlerMethodArgumentResolver extends ServletModelAttributeMethodProcessor {
    /**
     * Class constructor.
     *
     * @param annotationNotRequired if "true", non-simple method arguments and
     *                              return values are considered model attributes with or without a
     *                              {@code @ModelAttribute} annotation
     */
    public DeepObjectHandlerMethodArgumentResolver(boolean annotationNotRequired) {
        super(annotationNotRequired);
    }

    public DeepObjectHandlerMethodArgumentResolver() {
        this(false);
    }

    /**
     * Class constructor.
     *
     * @param annotationNotRequired if "true", non-simple method arguments and
     *                              return values are considered model attributes with or without a
     *                              {@code @ModelAttribute} annotation
     */

    @Override
    public boolean supportsParameter(MethodParameter parameter) {
        boolean b = BaseDTO.class.isAssignableFrom(parameter.getParameterType());
        return b;
    }

    /**
     * 将请求绑定至目标binder的target对象
     */
    @Override
    protected void bindRequestParameters(WebDataBinder binder, NativeWebRequest request) {

        ServletRequest servletRequest = request.getNativeRequest(ServletRequest.class);
        Assert.state(servletRequest != null, "No ServletRequest");
        DeepObjectDataBinder deepObjectBinder = new DeepObjectDataBinder(binder.getTarget());
        deepObjectBinder.setConversionService(binder.getConversionService());
        deepObjectBinder.bind(servletRequest);
    }
}
