package com.genlight.epilcure.common.core.pojo.vo;

import com.genlight.epilcure.common.core.dao.enums.Gender;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
@Accessors(chain = true)
public class CollectVO<T> implements Serializable {
    @Schema(description = "流水号")
    private Long id;
    @Schema(description = "备注信息")
    private String remark;

    @Schema(description = "用户id")
    private Long userId;

    @Schema(description = "创建时间")
    private Date createTime;

    @Schema(description = "患者姓名")
    private String name;

    @Schema(description = "患者性别")
    private Gender sex;

    @Schema(description = "患者年龄")
    private Long age;

    @Schema(description = "收藏信息")
    private T t;
}
