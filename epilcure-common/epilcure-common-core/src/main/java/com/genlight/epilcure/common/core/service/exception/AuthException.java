package com.genlight.epilcure.common.core.service.exception;

import com.genlight.epilcure.common.core.constants.HttpCode;

import java.text.MessageFormat;

/**
 * <AUTHOR>
 * @version 1.0.0
 **/
public class AuthException extends BaseException {

    public AuthException() {
        super(HttpCode.AUTHENTICATION);
    }

    public AuthException(Throwable cause) {
        super(HttpCode.AUTHENTICATION, cause);
    }

    public AuthException(String message, Object... args) {
        super(HttpCode.AUTHENTICATION, MessageFormat.format(message, args));
    }
}
