package com.genlight.epilcure.common.core.pojo.convert;

import com.genlight.epilcure.common.core.dao.entity.Address;
import com.genlight.epilcure.common.core.pojo.dto.AddressDTO;
import com.genlight.epilcure.common.core.pojo.vo.AddressVO;
import org.mapstruct.Mapper;

/**
 * <AUTHOR>
 * @version 1.0.0
 **/
@Mapper(config = BaseConvert.class)
public interface AddressConvert extends BaseConvert<Address, AddressVO, AddressDTO> {

}
