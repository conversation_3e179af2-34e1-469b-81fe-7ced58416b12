package com.genlight.epilcure.common.core.pojo.dto;

import com.genlight.epilcure.common.core.pojo.valid.Add;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2022/5/22 21:23
 * @Version 1.0.0
 **/
@Data
@SuperBuilder
@NoArgsConstructor
@Accessors(chain = true)
@Schema(description = "地址信息")
public class AddressDTO implements Serializable {
    @Schema(description = "国家")
    private String country;

    @NotBlank(message = "地址的省份信息不能为空！", groups = {Add.class})
    @Schema(description = "省份")
    private String province;

    @Schema(description = "城市")
    private String city;

    @Schema(description = "区")
    private String district;

    @Schema(description = "街道---详细地址")
    private String street;

    @Schema(description = "邮编")
    private Integer postcode;

    @NotNull(message = "地址的经度信息不能为空！", groups = {Add.class})
    @Schema(description = "经度")
    private Double longitude;

    @NotNull(message = "地址的维度信息不能为空！", groups = {Add.class})
    @Schema(description = "纬度")
    private Double latitude;
}
