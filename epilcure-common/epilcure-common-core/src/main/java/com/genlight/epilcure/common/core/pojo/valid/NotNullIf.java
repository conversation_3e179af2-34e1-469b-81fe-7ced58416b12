package com.genlight.epilcure.common.core.pojo.valid;

import com.genlight.epilcure.common.core.pojo.valid.Validator.NotNullIfConstraintValidator;
import jakarta.validation.Constraint;
import jakarta.validation.Payload;

import java.lang.annotation.*;

/**
 * <AUTHOR>
 * @version 1.0.0
 */
@Documented
@Retention(RetentionPolicy.RUNTIME)
@Repeatable(NotNullIf.List.class)
@Target({ElementType.TYPE, ElementType.ANNOTATION_TYPE})
@Constraint(validatedBy = NotNullIfConstraintValidator.class)
public @interface NotNullIf {

    String checkField();

    String dependField();

    String message() default "{com.genlight.epilcure.common.core.pojo.valid.NotNullIf.message}";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};

    @Documented
    @Retention(RetentionPolicy.RUNTIME)
    @Target({ElementType.TYPE, ElementType.ANNOTATION_TYPE})
    @interface List {
        NotNullIf[] value();
    }
}