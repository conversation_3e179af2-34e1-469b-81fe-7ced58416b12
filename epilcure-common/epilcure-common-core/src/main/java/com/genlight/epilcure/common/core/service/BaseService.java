package com.genlight.epilcure.common.core.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.type.TypeFactory;
import com.genlight.epilcure.common.core.util.JwtUtils;
import jakarta.annotation.Nullable;
import jakarta.annotation.Resource;
import org.redisson.api.RLock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import java.util.List;
import java.util.Objects;

/**
 * Abstract base service classes.
 *
 * @param <T>  JPA Entity object
 * @param <ID> ID of the entity object
 * <AUTHOR>
 * @version 1.0.0
 */
public abstract class BaseService<T, ID, R extends JpaRepository<T, ID>> {

    @Autowired
    protected R repository;

    @Autowired
    StringRedisTemplate stringRedisTemplate;

    @Resource
    private ObjectMapper objectMapper;

    public Long getUserId() {
        try {
            String jwt = SecurityContextHolder.getContext().getAuthentication().getCredentials().toString();
            return JwtUtils.getUserIdFormJWT(jwt);
        } catch (Exception e) {
            return 0L;
        }

    }

    public String getUserName() {
        try {
            String jwt = SecurityContextHolder.getContext().getAuthentication().getCredentials().toString();
            return JwtUtils.getUsernameFromJWT(jwt);
        } catch (Exception ex) {
            return "";
        }

    }

    public String getNikeName() {
        try {
            String jwt = SecurityContextHolder.getContext().getAuthentication().getCredentials().toString();
            return JwtUtils.getNikeNameFromJWT(jwt);
        } catch (Exception ex) {
            return "";
        }

    }

    public Boolean isPatient() {
        String jwt = SecurityContextHolder.getContext().getAuthentication().getCredentials().toString();
        return JwtUtils.getIsPatient(jwt);
    }

    public Boolean isEngineer() {
        String jwt = SecurityContextHolder.getContext().getAuthentication().getCredentials().toString();
        return JwtUtils.getIsEngineer(jwt);
    }

    public List<Long> getOrgIds() {
        try {
            String userName = getUserName();
            HashOperations<String, String, String> operations = stringRedisTemplate.opsForHash();
            String s = operations.get(JwtUtils.orgIds, userName);
            try {
                return objectMapper.readValue(s, TypeFactory.defaultInstance().constructCollectionType(List.class, Long.class));
            } catch (JsonProcessingException e) {
                throw new RuntimeException(e);
            }
        } catch (Exception e) {
            return null;
        }

    }

    protected void unLock(RLock obtain) {
        unLock(obtain, null);
    }

    protected void unLock(RLock obtain, @Nullable Runnable runnable) {
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
            @Override
            public void afterCompletion(int status) {
                TransactionSynchronization.super.afterCompletion(status);
                if (obtain.isLocked() && obtain.isHeldByCurrentThread()) {
                    obtain.unlock();
                }

                if (Objects.nonNull(runnable)) {
                    runnable.run();
                }
            }
        });
    }

//    public T getById(ID id) {
//        return repository.getById(id);
//    }
//
//    public boolean existsById(ID id) {
//        return repository.existsById(id);
//    }
//
//    public <S extends T> boolean exists(Example<S> example) {
//        return repository.exists(example);
//    }
//
//    public long count() {
//        return repository.count();
//    }
//
//    public <S extends T> long count(Example<S> example) {
//        return repository.count(example);
//    }
//
//    public <S extends T> S save(S entity) {
//        return repository.save(entity);
//    }
//
//    public <S extends T> List<S> saveAll(Iterable<S> entities) {
//        return repository.saveAll(entities);
//    }
//
//    public <S extends T> S saveAndFlush(S entity) {
//        return repository.saveAndFlush(entity);
//    }
//
//    public <S extends T> List<S> saveAllAndFlush(Iterable<S> entities) {
//        return repository.saveAllAndFlush(entities);
//    }
//
//    public <S extends T> Optional<S> findOne(Example<S> example) {
//        return repository.findOne(example);
//    }
//
//    public <S extends T, R> R findBy(Example<S> example, Function<FluentQuery.FetchableFluentQuery<S>, R> queryFunction) {
//        return repository.findBy(example, queryFunction);
//    }
//
//    public Optional<T> findById(ID id) {
//        return repository.findById(id);
//    }
//
//    public List<T> findAll() {
//        return repository.findAll();
//    }
//
//    public List<T> findAll(Sort sort) {
//        return repository.findAll(sort);
//    }
//
//    public Page<T> findAll(Pageable pageable) {
//        return repository.findAll(pageable);
//    }
//
//    public <S extends T> List<S> findAll(Example<S> example) {
//        return repository.findAll(example);
//    }
//
//    public <S extends T> List<S> findAll(Example<S> example, Sort sort) {
//        return repository.findAll(example, sort);
//    }
//
//    public <S extends T> Page<S> findAll(Example<S> example, Pageable pageable) {
//        return repository.findAll(example, pageable);
//    }
//
//    public List<T> findAllById(Iterable<ID> ids) {
//        return repository.findAllById(ids);
//    }
//
//    public void delete(T entity) {
//        repository.delete(entity);
//    }
//
//    public void deleteById(ID id) {
//        repository.deleteById(id);
//    }
//
//    public void deleteAllById(Iterable<? extends ID> ids) {
//        repository.deleteAllById(ids);
//    }
//
//    public void deleteAllByIdInBatch(Iterable<ID> ids) {
//        repository.deleteAllByIdInBatch(ids);
//    }
//
//    public void deleteAll() {
//        repository.deleteAll();
//    }
//
//    public void deleteAll(Iterable<? extends T> entities) {
//        repository.deleteAll(entities);
//    }
//
//    public void deleteAllInBatch() {
//        repository.deleteAllInBatch();
//    }
//
//    public void deleteAllInBatch(Iterable<T> entities) {
//        repository.deleteAllInBatch(entities);
//    }
//
//    public void flush() {
//        repository.flush();
//    }
}
