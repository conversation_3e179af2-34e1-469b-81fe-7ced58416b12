package com.genlight.epilcure.common.core.mvc.converter;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.http.MediaType;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 1.0.0
 */
@Component
public class Jackson2HttpMessageConverter extends MappingJackson2HttpMessageConverter {


    public Jackson2HttpMessageConverter(ObjectMapper objectMapper) {
        super(objectMapper);
    }

    @Override
    protected boolean canRead(@Nullable MediaType mediaType) {
        // https://github.com/springdoc/springdoc-openapi/issues/833
        if (MediaType.APPLICATION_OCTET_STREAM.equals(mediaType)) {
            return true;
        }
        return super.canRead(mediaType);
    }
}
