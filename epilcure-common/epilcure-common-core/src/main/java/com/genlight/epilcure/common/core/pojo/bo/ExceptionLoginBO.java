package com.genlight.epilcure.common.core.pojo.bo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ExceptionLoginBO implements Serializable {
    private Date loginTime;
    private String device;
    private String province;
    private String city;
    private String ip;
}
