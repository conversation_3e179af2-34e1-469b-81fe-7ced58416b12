package com.genlight.epilcure.common.core.mvc.aspectj;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.genlight.epilcure.common.core.util.IpUtils;
import com.genlight.epilcure.common.core.util.JwtUtils;
import com.nimbusds.jose.JWSObject;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Configurable;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.util.StringUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;

import java.lang.reflect.Parameter;
import java.util.*;

/**
 * <AUTHOR>
 * @version 1.0.0
 */
@Slf4j
@Aspect
@Configurable
public class LogstashAspect {

    @Autowired
    private ObjectMapper objectMapper;

    @Around("execution(* com.genlight.epilcure.service.*.controller..*.*(..))")
    public Object doAround(ProceedingJoinPoint joinPoint) throws Throwable {

        Map<String, Object> message = new HashMap<>();
        Optional<Authentication> authentication = Optional.ofNullable(SecurityContextHolder.getContext().getAuthentication());
        Optional<String> jwt = authentication.map(Authentication::getCredentials).map(Object::toString);
        if (jwt.isPresent() && StringUtils.hasText(jwt.get())) {
            JWSObject jwsObject = JWSObject.parse(jwt.get());
            Map<String, Object> map = jwsObject.getPayload().toJSONObject();
            message.put("id", map.get(JwtUtils.userIdKey));
            message.put("os", map.get(JwtUtils.os));
            message.put("device", map.get(JwtUtils.device));
            message.put("name", map.get(JwtUtils.nikeName));
        }

        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest servletRequest = Objects.requireNonNull(attributes).getRequest();
        MethodSignature methodSignature = (MethodSignature) joinPoint.getSignature();
        message.put("ip", IpUtils.getIpAddr(servletRequest));
        message.put("class", methodSignature.getDeclaringType().getSimpleName());
        message.put("method", methodSignature.getName());
        message.put("params", printRequestParams(methodSignature.getMethod().getParameters(), joinPoint.getArgs()));
        try {
            Object result = joinPoint.proceed();
            message.put("status", "ok");
            log.info(objectMapper.writeValueAsString(message));
            return result;
        } catch (Exception e) {
            message.put("status", "error");
            message.put("message", e.getMessage());
            log.info(objectMapper.writeValueAsString(message));
            throw e;
        }
    }

    public List<Object> printRequestParams(Parameter[] parameters, Object[] args) {
        List<Object> params = new ArrayList<>();
        for (int i = 0; i < parameters.length; i++) {
            Object arg = args[i];
            if (Objects.nonNull(arg)) {
                Class<?> clazz = parameters[i].getType();
                if (clazz.isArray() && MultipartFile.class.isAssignableFrom(clazz.getComponentType())) {
                    params.add(Arrays.stream(((MultipartFile[]) arg)).map(MultipartFile::getOriginalFilename).toArray());
                } else if (MultipartFile.class.isAssignableFrom(clazz)) {
                    params.add(((MultipartFile) arg).getOriginalFilename());
                } else {
                    params.add(arg);
                }
            }
        }
        return params;
    }
}
