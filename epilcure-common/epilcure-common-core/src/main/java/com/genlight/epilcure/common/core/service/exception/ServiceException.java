package com.genlight.epilcure.common.core.service.exception;

import com.genlight.epilcure.common.core.constants.HttpCode;

import java.text.MessageFormat;

/**
 * <AUTHOR>
 * @version 1.0.0
 **/
public class ServiceException extends BaseException {

    public ServiceException(HttpCode httpCode) {
        super(httpCode);
    }

    public ServiceException(Throwable cause) {
        super(HttpCode.ERROR, cause);
    }

    public ServiceException(String message, Object... args) {
        super(HttpCode.ERROR, MessageFormat.format(message, args));
    }

    public ServiceException(HttpCode httpCode, String msg) {
        super(httpCode, msg);
    }
}
