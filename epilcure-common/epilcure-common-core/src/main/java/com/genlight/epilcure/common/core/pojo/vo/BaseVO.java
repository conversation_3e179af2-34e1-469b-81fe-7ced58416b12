package com.genlight.epilcure.common.core.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
public class BaseVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "主鍵")
    private Long id;

    @Schema(description = "数据签名")
    private String sign;

    @Schema(description = "更新时间")
    private Date updateTime;

    @Schema(description = "创建时间")
    private Date createTime;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        BaseVO baseVO = (BaseVO) o;

        return id.equals(baseVO.id);
    }

    @Override
    public int hashCode() {
        return id.hashCode();
    }
}
