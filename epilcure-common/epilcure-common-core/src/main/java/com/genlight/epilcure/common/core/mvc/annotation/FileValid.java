package com.genlight.epilcure.common.core.mvc.annotation;

import org.springframework.core.annotation.AliasFor;
import org.springframework.util.unit.DataUnit;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;
import java.util.Arrays;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0.0
 */
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.PARAMETER)
public @interface FileValid {


    @AliasFor("size")
    int value() default 5;

    @AliasFor("value")
    int size() default 5;

    String filename() default "^[0-9a-zA-Z_\\-\\.]{1,255}$";

    Format format() default Format.ANY;

    DataUnit unit() default DataUnit.MEGABYTES;

    enum Format {

        ANY(null),
        PDF(new String[]{".pdf"}),
        AR(new String[]{".jpg", ".jpeg", ".png", ".glb", "usdz"}),
        IMAGE(new String[]{".jpg", ".jpeg", ".png"}),
        VIDEO(new String[]{".wmv", ".avi", ".mov", ".3gp", ".mpeg", ".mp4", ".flv", ".swf", ".webm", ".mkv"});

        private final String[] suffixes;

        Format(String[] suffixes) {
            this.suffixes = suffixes;
        }

        public boolean validate(String filename) {
            if (Objects.nonNull(this.suffixes)) {
                String filenameLowerCase = filename.toLowerCase();
                for (String suffix : this.suffixes) {
                    if (filenameLowerCase.endsWith(suffix)) {
                        return true;
                    }
                }
                return false;
            } else {
                return true;
            }
        }

        @Override
        public String toString() {
            return Arrays.toString(suffixes);
        }
    }
}
