package com.genlight.epilcure.common.core.util;

import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.type.TypeFactory;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.function.BiFunction;
import java.util.function.Function;

/**
 * <AUTHOR> maoyan
 * @Date :  20:16
 * @Version :1.0.0
 */
@Slf4j
public class FunctionUtils {
    @FunctionalInterface
    public interface ThrowingFunction<T, R, E extends Exception> {

        R apply(T arg) throws E;

        static <T, R> Function<T, R> sneaky(final ThrowingFunction<? super T, ? extends R, ?> function) {
            return t -> {
                try {
                    return function.apply(t);
                } catch (final Exception e) {
                    log.error("The execution {} args {} throws an exception: ", function, t, e);
                    throw new RuntimeException(e);
                }
            };
        }
    }

    @FunctionalInterface
    public interface ThrowingFunctionTowParams<T, U, R, E extends Exception> {

        R apply(T arg1, U arg2) throws E;

        static <T, U, R> BiFunction<T, U, R> sneaky(final ThrowingFunctionTowParams<? super T, ? super U, ? extends R, ?> function) {
            return (t, u) -> {
                try {
                    return function.apply(t, u);
                } catch (final Exception e) {
                    log.error("The execution {} args {}, {} throws an exception: ", function, t, u, e);
                    throw new RuntimeException(e);
                }
            };
        }
    }

    public static void main(String[] args) {
        ObjectMapper objectMapper = new ObjectMapper();
        String s = "[\"a\",\"b\",\"c\",\"d\",\"e\"]";
        System.out.println(ThrowingFunctionTowParams.<String, JavaType, List<String>>sneaky(objectMapper::readValue)
                .apply(s, TypeFactory.defaultInstance().constructCollectionType(List.class, String.class)));

    }
//    @FunctionalInterface
//    public interface ThrowingFunctionTowParams<T1 extends String, T2 extends CollectionType, R extends List<String>, E extends Exception> {
//
//        List<String> apply(T1 arg, T2 arg1) throws E;
//
//        static <T extends List<Object> , R extends List<String>> Function<T,R> sneaky(final ThrowingFunctionTowParams<? super String, ? super CollectionType, ? extends R, ?> function) {
//            return (t) -> {
//                try {
//                    return (R) function.apply(t.get(0).toString(), (CollectionType)t.get(1));
//                } catch (final Exception e) {
//                    throw new RuntimeException(e);
//                }
////                return null;
//            };
//        }
//    }
//@FunctionalInterface
//public interface ThrowingFunctionTowParams<T1 , T2 , R, E extends Exception> {
//
//    R apply(T1 arg, T2 arg1) throws E;
//
//    static <T1 , T2, R> ThrowingFunctionTowParams<T1, T2, ?, ?> sneaky(final ThrowingFunctionTowParams<T1, T2, ? extends R, ?> function) {
//        return (t1, t2) -> {
//            try {
//                return function.apply(t1, t2);
//            } catch (final Exception e) {
//                log.error("The execution {} args {} {} throws an exception: ", function, t1, t2, e);
//                throw new RuntimeException(e);
//            }
//        };
//    }
//}
}
