package com.genlight.epilcure.common.core.util;

import com.alibaba.nacos.common.utils.IoUtils;
import com.genlight.epilcure.common.core.service.exception.ArgsException;
import io.minio.*;
import io.minio.errors.*;
import io.minio.messages.DeleteError;
import io.minio.messages.DeleteObject;
import io.minio.messages.Item;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2022/6/5 15:26
 * @Version 1.0.0
 * @Descript minio工具类
 **/
@Slf4j
@Component
public class MinioUtils {

    @Value("${minio.bucketName}")
    private String bucketName;

    @Autowired
    private MinioClient minioClient;

    /**
     * description: 判断bucket是否存在，不存在则创建
     */
    public boolean makeBucket(String name) {
        try {
            if (!minioClient.bucketExists(BucketExistsArgs.builder().bucket(name).build())) {
                minioClient.makeBucket(MakeBucketArgs.builder().bucket(name).build());
            }
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
        return true;
    }

    /**
     * 删除存储bucket
     *
     * @param name 存储bucket名称
     * @return Boolean
     */
    public boolean removeBucket(String name) {
        try {
            if (minioClient.bucketExists(BucketExistsArgs.builder().bucket(name).build())) {
                minioClient.removeBucket(RemoveBucketArgs.builder().bucket(name).build());
            }
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
        return true;
    }

    /**
     * description: 上传文件
     *
     * @param multipartFile 上传的文件数组
     * @return: java.lang.String
     */
    public List<String> upload(String dir, MultipartFile[] multipartFile) {
        List<String> names = new ArrayList<>(multipartFile.length);
        for (MultipartFile file : multipartFile) {
            names.add(upload(dir, file));
        }
        return names;
    }

    /**
     * description: 上传文件
     *
     * @param multipartFile 上传的文件
     * @return: java.lang.String
     */
    public String upload(String dir, MultipartFile multipartFile) {
        String filename = rename(dir, Objects.requireNonNull(multipartFile.getOriginalFilename()));
        try (InputStream in = multipartFile.getInputStream()) {
            minioClient.putObject(PutObjectArgs.builder().bucket(bucketName).object(filename).stream(in, in.available(), -1).contentType(multipartFile.getContentType()).build());
        } catch (Exception e) {
            e.printStackTrace();
            throw new ArgsException(e);
        }
        return filename;
    }

    public String uploadZipFile(String dir, String filename, InputStream in) {
        String filePath = rename(dir, Objects.requireNonNull(filename));
        try {
            minioClient.putObject(PutObjectArgs.builder().bucket(bucketName).object(filePath).stream(in, in.available(), -1).contentType("application/zip").build());
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return filePath;
    }

    /**
     * 重命名
     *
     * @param dir      目录
     * @param filename 文件名
     * @return 重命名
     */
    protected String rename(String dir, String filename) {
        String[] split = filename.split("\\.");
        if (split.length > 1) {
            for (int i = 0; i < split.length - 1; i++) {
                dir += split[i];
            }
            dir += System.currentTimeMillis() + '.';
            return dir + split[split.length - 1];
        } else {
            return dir + filename + "-" + System.currentTimeMillis();
        }
    }


    /**
     * description: 下载文件
     *
     * @param fileName 文件路径
     * @return: org.springframework.http.ResponseEntity<byte [ ]>
     */
    public ResponseEntity<byte[]> download(String fileName) {
        try (InputStream in = minioClient.getObject(GetObjectArgs.builder().bucket(bucketName).object(fileName).build()); ByteArrayOutputStream out = new ByteArrayOutputStream()) {
            IoUtils.copy(in, out);
            byte[] bytes = out.toByteArray();
            HttpHeaders headers = new HttpHeaders();
            headers.add("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, StandardCharsets.UTF_8));
            headers.setContentLength(bytes.length);
            headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
            headers.setAccessControlExposeHeaders(List.of("*"));
            return new ResponseEntity<>(bytes, headers, HttpStatus.OK);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 查看文件对象
     *
     * @param bucketName 存储bucket名称
     * @return 存储bucket内文件对象信息
     */
    public Iterable<Result<Item>> listObjects(String bucketName) {
        return minioClient.listObjects(ListObjectsArgs.builder().bucket(bucketName).build());
    }

    /**
     * 获取文件对象
     *
     * @param fileName 文件名
     */
    public InputStream getObject(String fileName) throws ServerException, InsufficientDataException, ErrorResponseException, IOException, NoSuchAlgorithmException, InvalidKeyException, InvalidResponseException, XmlParserException, InternalException {
        return minioClient.getObject(GetObjectArgs.builder().bucket(bucketName).object(fileName).build());
    }

    public Boolean existedObject(String fileName){

        try {
            // 获取对象的元数据
            minioClient.statObject(StatObjectArgs.builder().bucket(bucketName).object(fileName).build());
            return true; // 如果没有抛出异常，文件存在
        } catch (MinioException e) {

            // 处理 MinioException
            if (e.getMessage().contains("NoSuchKey")) {
                return false; // 文件不存在
            }
            // 处理其他 MinioException 异常
            return true;
        } catch (Exception e) {
            // 处理其他异常
            return true;
        }
    }


    /**
     * 删除文件对象
     *
     * @param object 对象名称集合
     */
    public boolean removeObject(String object) {
        return !removeObjects(List.of(object)).iterator().hasNext();
    }

    /**
     * 批量删除文件对象
     *
     * @param objects 对象名称集合
     */
    public Iterable<Result<DeleteError>> removeObjects(List<String> objects) {
        return minioClient.removeObjects(RemoveObjectsArgs.builder().bucket(bucketName).objects(objects.stream().map(DeleteObject::new).collect(Collectors.toList())).build());
    }
}
