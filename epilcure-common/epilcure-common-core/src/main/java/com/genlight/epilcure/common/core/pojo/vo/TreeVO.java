package com.genlight.epilcure.common.core.pojo.vo;

import com.fasterxml.jackson.annotation.JsonView;
import com.genlight.epilcure.common.core.pojo.view.TreeView;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
@SuperBuilder(builderMethodName = "builderTreeVO")
@NoArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class TreeVO<V> extends BaseVO {

    @Builder.Default
    @JsonView(TreeView.class)
    @Schema(description = "子节点")
    private List<V> children = new ArrayList<>();
}
