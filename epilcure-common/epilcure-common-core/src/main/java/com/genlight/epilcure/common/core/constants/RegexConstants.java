package com.genlight.epilcure.common.core.constants;

/**
 * <AUTHOR>
 * @version 1.0.0
 **/
public abstract class RegexConstants {
    public static final String ID = "^[1-9]\\d{5}(18|19|([23]\\d))\\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]$";

    public static final String HK_ID = "^([A-Z]\\d{6,10}(\\(\\w{1}\\))?)$";

    public static final String TW_ID = "^\\d{8}|^[a-zA-Z0-9]{10}|^\\d{18}$";

    public static final String OFFICER_ID = "^[\\u4E00-\\u9FA5](字第)([0-9a-zA-Z]{4,8})(号?)$";

    public static final String FIM_ID = "";

    /**
     * 手机号
     */
    public static final String REGEX_MOBILE = "1[0-9]{10}";

    /**
     * 邮箱
     */
    public static final String REGEX_EMAIL = "^[a-zA-Z0-9_-]+@[a-zA-Z0-9_-]+(\\.[a-zA-Z0-9_-]+)+$";

    /**
     * 密码正则
     */
    public static final String REGEX_PASSWORD = "^(?![0-9]+$)(?![a-zA-Z]+$)[A-Za-z0-9]{6,12}";

    /**
     * mac地址正则表达式
     */
    public static final String REGEX_MAC = "^[A-Fa-f0-9]{2}(:[A-Fa-f0-9]{2}){5}$";

    /**
     * 癫痫刺激器型号签名
     */
    public static final String BCM_MODEL = "^BCM[1-9][0-9]{2}[A-Z]$|^ISBS[1-9][0-9][A-Z]$";

    public static final String TEST_BCM_MODEL = "^TS[0-9]{2}[A-Z]$";

    /**
     * 癫痫程控仪型号签名
     */
    public static final String BPP_MODEL = "^BPP[0-9]{2}[A-Z]$";

    /**
     * 癫痫电极型号签名
     */
    public static final String ELE_MODEL = "^I[CD]E[0-9]{4}[A-Z]$|^[CD]E[0-9]{4}[A-Z]$";

    /**
     * 癫痫刺激器签名
     */
    public static final String EPILEPSY_BCM = "^BCM[1-9][0-9]{2}[A-Z]{2}[0-9]{5}$|^ISBS[1-9][0-9][A-Z]{2}[0-9]{5}$";
    public static final String TEST_BCM = "^TS[0-9]{2}[A-Z]{2}[0-9]{5}$";

    /**
     * 癫痫程控仪签名
     */
    public static final String EPILEPSY_BPP = "^BPP[0-9]{2}[A-Z]{2}[0-9]{5}$";

    /**
     * 电极签名
     */
    public static final String ELE = "^[CD]E[0-9]{4}[A-Z]{2}[0-9]{5}$|^I[CD]E[0-9]{4}[A-Z]{2}[0-9]{5}$";

    /**
     * 合同序列号
     */
    public static final String EPILEPSY_ARCHIVES = "^BCM\\-[1-9][0-9]\\-[A-Z]{2}\\-M\\d{5}$";

    /**
     * 帕金森档案序列号
     */
    public static final String PARKINSON_ARCHIVES = "^iSBS\\-[A-Z]{1}[0-9]{5}$";

    /**
     * 帕金森刺激器签名
     */
    public static final String PARKINSON_BCM = "^iSBS[1-9][0-9]{2}[A-Z]{2}[0-9]{5}";

    /**
     * 批号正则
     */
    public static final String BATCH_DATE = "(([0-9]{3}[1-9]|[0-9]{2}[1-9][0-9]{1}|[0-9]{1}[1-9][0-9]{2}|[1-9][0-9]{3})(((0[13578]|1[02])(0[1-9]|[12][0-9]|3[01]))|" +
            "((0[469]|11)(0[1-9]|[12][0-9]|30))|(02(0[1-9]|[1][0-9]|2[0-8]))))|((([0-9]{2})(0[48]|[2468][048]|[13579][26])|" +
            "((0[48]|[2468][048]|[3579][26])00))0229)$";
}
