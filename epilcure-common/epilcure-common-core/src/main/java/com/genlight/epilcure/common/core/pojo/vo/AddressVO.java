package com.genlight.epilcure.common.core.pojo.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;


/**
 * <AUTHOR>
 * @Date 2022/5/15 22:33
 * @Version 1.0.0
 **/
@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@Schema(description = "地址信息")
public class AddressVO extends BaseVO {

    @Schema(description = "国家")
    private String country;

    @Schema(description = "省份")
    private String province;

    @Schema(description = "城市")
    private String city;

    @Schema(description = "区")
    private String district;

    @Schema(description = "街道---详细地址")
    private String street;

    @Schema(description = "邮编")
    private Integer postcode;

    @NotNull
    @Schema(description = "经度")
    private Double longitude;

    @NotNull
    @Schema(description = "纬度")
    private Double latitude;
}
