package com.genlight.epilcure.common.core.pojo.dto;

import com.fasterxml.jackson.annotation.JsonView;
import com.genlight.epilcure.common.core.dao.enums.Gender;
import com.genlight.epilcure.common.core.pojo.valid.Add;
import com.genlight.epilcure.common.core.pojo.valid.Find;
import com.genlight.epilcure.common.core.pojo.valid.Update;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Null;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;
import org.hibernate.validator.constraints.Length;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * <AUTHOR>
 */

@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class CollectDTO extends BaseDTO {

    @NotNull(message = "关联的实体Id不能为空", groups = Add.class)
    @Null(message = "关联的实体Id不能修改", groups = Update.class)
    @Schema(description = "关联Id")
    private String cId;

    @Schema(description = "备注")
    @Length(max = 2048)
    @NotNull(message = "只允许修改备注信息", groups = Update.class)
    private String remark;

    @Schema(description = "开始时间")
    @JsonView(Find.class)
//    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    @Schema(description = "结束时间")
    @JsonView(Find.class)
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    @Schema(description = "患者姓名")
    @Length(max = 2048)
    @JsonView({Add.class, Find.class})
    @NotNull(message = "患者姓名不为空", groups = Add.class)
    @Null(message = "不允许修改患者姓名", groups = Update.class)
    private String name;

    @Schema(description = "患者性别")
    @JsonView({Add.class, Find.class})
    @NotNull(message = "患者性别不为空", groups = Add.class)
    @Null(message = "不允许修改患者性别", groups = Update.class)
    private Gender sex;

    @Schema(description = "患者年龄")
    @JsonView({Add.class, Find.class})
    @NotNull(message = "患者年龄不为空", groups = Add.class)
    @Null(message = "不允许修改患者年龄", groups = Update.class)
    private Long age;


}
