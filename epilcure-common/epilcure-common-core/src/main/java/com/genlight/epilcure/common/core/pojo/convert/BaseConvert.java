package com.genlight.epilcure.common.core.pojo.convert;

import com.genlight.epilcure.common.core.mvc.response.RestPage;
import org.mapstruct.*;
import org.springframework.data.domain.Page;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0.0
 **/
@MapperConfig(
        componentModel = "spring",
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        unmappedSourcePolicy = ReportingPolicy.IGNORE,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE
)
public interface BaseConvert<Po, Vo, Dto> {

    Po dto2po(Dto dto);

    Po dto2po(Dto dto, @MappingTarget Po po);

    @BeanMapping(nullValueCheckStrategy = NullValueCheckStrategy.ON_IMPLICIT_CONVERSION)
    Vo po2vo(Po po);

    List<Vo> po2vo(List<Po> pos);

    default Page<Vo> po2vo(Page<Po> poPage) {
        return new RestPage<>(poPage.getContent()
                .stream()
                .map(this::po2vo)
                .collect(Collectors.toList()), poPage.getPageable(), poPage.getTotalElements());
    }
}
