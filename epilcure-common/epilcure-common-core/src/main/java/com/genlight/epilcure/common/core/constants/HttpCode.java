package com.genlight.epilcure.common.core.constants;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @version 1.0.0
 **/
@AllArgsConstructor
public enum HttpCode {

    SUCCESS(0, "Success"),
    ERROR(1001, "Service exception"),
    ARGUMENT(1002, "Argument exception"),
    AUTHENTICATION(1003, "Authentication exception"),

    SMS_BUSY(1007, "sms send too many"),
    ARCHIVES_NOT_EXISTS(1101, "Archives not exist"),
    PATIENT_CHANGE_MOBILE_EXISTS(1102, "Mobile is exists"),

    I_SCHEME_ID_EXISTS(1103, "IScheme ID exists"),

    FILE_EXISTS(1104, "File is uploaded"),

    POSITION_EXCEPTION(1105, "surgery electrode position exception"),

    TARGET_EXCEPTION(1106, "surgery electrode target exception"),

    MODEL_NOT_EXISTS_IN_ARCHIVES(1107, "electrode model not exist in archives"),

    STATUS_EXCEPTION(1108, "status exception"),

    USED_EXCEPTION(1109, "device used"),

    SURGERY(1110, "has surgery");

    @Getter
    private final int code;

    @Getter
    private final String message;
}
