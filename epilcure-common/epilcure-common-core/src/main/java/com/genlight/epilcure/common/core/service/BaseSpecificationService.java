package com.genlight.epilcure.common.core.service;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

/**
 * Abstract base specification service classes.
 *
 * @param <T>  JPA Entity object
 * @param <ID> ID of the entity object
 * <AUTHOR>
 * @version 1.0.0
 */
public abstract class BaseSpecificationService<T, ID, R extends JpaRepository<T, ID> & JpaSpecificationExecutor<T>> extends BaseService<T, ID, R> {

//    public long count(Specification<T> spec) {
//        return repository.count(spec);
//    }
//
//    public Optional<T> findOne(Specification<T> spec) {
//        return repository.findOne(spec);
//    }
//
//    public List<T> findAll(Specification<T> spec) {
//        return repository.findAll(spec);
//    }
//
//    public Page<T> findAll(Specification<T> spec, Pageable pageable) {
//        return repository.findAll(spec, pageable);
//    }
//
//    public List<T> findAll(Specification<T> spec, Sort sort) {
//        return repository.findAll(spec, sort);
//    }
}
