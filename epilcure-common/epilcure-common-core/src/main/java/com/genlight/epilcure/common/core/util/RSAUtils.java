package com.genlight.epilcure.common.core.util;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.type.TypeFactory;
import jakarta.annotation.Resource;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.stereotype.Component;

import javax.crypto.Cipher;
import java.security.KeyFactory;
import java.security.PrivateKey;
import java.security.spec.EncodedKeySpec;
import java.security.spec.PKCS8EncodedKeySpec;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;

@Component
@ConditionalOnClass(BadCredentialsException.class)
public class RSAUtils {

    @Value("${rsa.expired}")
    private int expired;

    @Resource
    private ObjectMapper objectMapper;

    private final PrivateKey privateKey;

    public RSAUtils(@Value("${rsa.privateKey}") String privateKey) {
        this.privateKey = this.loadPrivateKey(privateKey);
    }

    public PrivateKey loadPrivateKey(String privateKey) {
        try {
            KeyFactory privateKeyFactory = KeyFactory.getInstance("RSA");
            EncodedKeySpec privateKeySpec = new PKCS8EncodedKeySpec(Base64.getDecoder().decode(privateKey.replaceAll("\n", "")));
            return privateKeyFactory.generatePrivate(privateKeySpec);
        } catch (Exception e) {
            throw new BadCredentialsException(e.getMessage());
        }
    }

    public String decrypt(String cipherText) {
        try {
            Cipher cipher = Cipher.getInstance("RSA/ECB/PKCS1Padding");
            cipher.init(Cipher.DECRYPT_MODE, privateKey);
            return new String(cipher.doFinal(Base64.getDecoder().decode(cipherText)));
        } catch (Exception e) {
            return "{\"timestamp\":" + System.currentTimeMillis() + ",\"password\":\"" + cipherText + "\"}";
        }
    }

    public String decryptPassword(String cipherPassword) {
        try {
            Map<String, String> data = objectMapper.readValue(decrypt(cipherPassword), TypeFactory.defaultInstance().constructMapType(HashMap.class, String.class, String.class));
            long instant = System.currentTimeMillis() - Long.parseLong(data.get("timestamp"));
            if (instant < -expired || instant > expired) {
                throw new BadCredentialsException("Password encrypt expired");
            }
            return data.get("password");
        } catch (Exception e) {
            throw new BadCredentialsException(e.getMessage());
        }
    }
}
