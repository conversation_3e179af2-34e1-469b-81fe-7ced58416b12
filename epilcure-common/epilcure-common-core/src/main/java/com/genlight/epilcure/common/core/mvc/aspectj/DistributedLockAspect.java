package com.genlight.epilcure.common.core.mvc.aspectj;

import com.genlight.epilcure.common.core.mvc.annotation.DistributedLock;
import com.genlight.epilcure.common.core.service.exception.ServiceException;
import jakarta.annotation.Resource;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.DeclarePrecedence;
import org.aspectj.lang.reflect.MethodSignature;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Configurable;
import org.springframework.expression.EvaluationContext;
import org.springframework.expression.Expression;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;

import java.util.Objects;
import java.util.concurrent.TimeUnit;

@Aspect
@Configurable
@DeclarePrecedence("FileValidAspect,AccessAspect,DistributedLockAspect,AnnotationTransactionAspect")
public class DistributedLockAspect {

    @Resource
    private RedissonClient redissonClient;

    private final ExpressionParser expressionParser = new SpelExpressionParser();

    @Around("execution(* com.genlight.epilcure.service.*.service..*.*(..)) && @annotation(lockConfig)")
    public Object doAround(ProceedingJoinPoint joinPoint, DistributedLock lockConfig) throws Throwable {

        // el
        Expression expression = expressionParser.parseExpression(lockConfig.value());
        EvaluationContext context = new StandardEvaluationContext();
        String[] parameterNames = ((MethodSignature) joinPoint.getSignature()).getParameterNames();
        Object[] args = joinPoint.getArgs();
        for (int i = 0; i < parameterNames.length; i++) {
            context.setVariable(parameterNames[i], args[i]);
        }

        // lock
        RLock lock = redissonClient.getLock(Objects.requireNonNull(expression.getValue(context)).toString());
        try {
            if (lock.tryLock(lockConfig.waitTime(), lockConfig.leaseTime(), TimeUnit.SECONDS)) {
                return joinPoint.proceed();
            }
            throw new ServiceException("操作太频繁，请稍后再试！");
        } catch (Exception e) {
            throw new RuntimeException(e);
        } finally {
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }
}
