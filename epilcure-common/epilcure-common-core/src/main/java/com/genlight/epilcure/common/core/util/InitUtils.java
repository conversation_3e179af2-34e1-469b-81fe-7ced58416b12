package com.genlight.epilcure.common.core.util;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.genlight.epilcure.common.core.dao.entity.TEntity;
import com.genlight.epilcure.common.core.dao.entity.TEntitySchemeStringId;
import com.genlight.epilcure.common.core.dao.generator.annotation.SignOrder;
import com.genlight.epilcure.common.core.dao.interceptor.SignVerifyInterceptor;
import com.genlight.epilcure.common.core.dao.repository.TreeRelationRepository;
import io.swagger.v3.core.util.ReflectionUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.persistence.Table;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.AnnotationUtils;
import org.springframework.core.annotation.Order;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.core.type.classreading.CachingMetadataReaderFactory;
import org.springframework.core.type.classreading.MetadataReader;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.lang.annotation.Annotation;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.lang.reflect.ParameterizedType;
import java.util.*;
import java.util.stream.Collectors;

import static com.genlight.epilcure.common.rocketmq.RocketMqConstant.INIT_TOPIC;


/**
 * <AUTHOR>
 * @Date 2022/6/5 15:26
 * @Version 1.0.0
 **/
@Slf4j
@Configuration
@Order(1)
public class InitUtils implements ApplicationRunner, ApplicationContextAware {

    @Value("${init-service:true}")
    private boolean initService;

    @Value("${repair-data:true}")
    private boolean repairData;

    public static Map<Class, List<String>> classSignFields = new HashMap<>();

    public static List<Class> classes = new ArrayList<>();

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private SignVerifyInterceptor signVerifyInterceptor;

    private ApplicationContext applicationContext;

    @Autowired
    private RocketMQTemplate rocketMQTemplate;

    @Override
    public void setApplicationContext(@NotNull ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }

    @Override
    public void run(ApplicationArguments args) throws Exception {

        PathMatchingResourcePatternResolver resolver = new PathMatchingResourcePatternResolver();
        CachingMetadataReaderFactory readerFactory = new CachingMetadataReaderFactory();
        Resource[] resources = resolver.getResources("classpath*:**/dao/entity/**.class");

        log.info(String.valueOf(resources.length));
        for (Resource resource : resources) {
            MetadataReader reader = readerFactory.getMetadataReader(resource);

            Class<?> clazz = Class.forName(reader.getClassMetadata().getClassName());
            if (clazz.isAnnotationPresent(SignOrder.class)) {
                List<Field> fields = ReflectionUtils.getDeclaredFields(clazz);
                fields = fields.stream().filter(f -> Objects.nonNull(f.getDeclaredAnnotation(SignOrder.class))).collect(Collectors.toList());
                fields.stream().sorted(Comparator.comparing(f -> f.getAnnotation(SignOrder.class).value()));
                classSignFields.put(clazz, new ArrayList<>());
                fields.forEach(f -> {
                    classSignFields.get(clazz).add(f.getName());
                });
            }
        }

        if (initService) {
            for (Object controller : applicationContext.getBeansWithAnnotation(RestController.class).values()) {
                Class<?> clazz = controller.getClass();

                // filter feign controller
                if (Arrays.stream(clazz.getInterfaces()).anyMatch(c -> c.isAnnotationPresent(FeignClient.class))) {
                    continue;
                }

                // check @RequestMapping is existed
                Optional<RequestMapping> requestMapping = Optional.ofNullable(clazz.getAnnotation(RequestMapping.class));
                if (requestMapping.isEmpty()) {
                    continue;
                }

                // check @Tag is existed
                Optional<Tag> tag = Optional.ofNullable(clazz.getAnnotation(Tag.class));
                if (tag.isEmpty()) {
                    continue;
                }

                // add module res
                int seq = 1;
                String controllerPath = requestMapping.get().value()[0];
                String controllerName = tag.get().name();
                String controllerDescription = tag.get().description();
                rocketMQTemplate.convertAndSend(INIT_TOPIC, Map.of("seq", 1, "method", "*", "name", controllerName, "code", controllerPath + "/**", "description", controllerDescription));
                for (Method method : clazz.getDeclaredMethods()) {
                    for (Annotation annotation : method.getAnnotations()) {
                        if (annotation.annotationType().isAnnotationPresent(RequestMapping.class)) {
                            RequestMapping mapping = annotation.annotationType().getAnnotation(RequestMapping.class);
                            Object[] value = (Object[]) AnnotationUtils.getAnnotationAttributes(annotation).get("value");
                            String path = Objects.isNull(value) || value.length == 0 ? controllerPath : controllerPath + value[0].toString();
                            try {
                                rocketMQTemplate.convertAndSend(INIT_TOPIC, Map.of("seq", seq++, "method", mapping.method()[0].name(), "name", controllerName + "." + method.getName(), "code", path, "description", method.getAnnotation(Operation.class).summary()));
                            } catch (Exception ex) {
                                log.error(method.getName(), controllerName);
                                throw ex;
                            }

                            break;
                        }
                    }
                }
            }
        }

        log.info(String.format("重置数据签名:%s", repairData));
        if (repairData) {
            SignVerifyInterceptor.skip = true;
            Map<String, Object> map = applicationContext.getBeansWithAnnotation(Table.class);
            Map<String, JpaRepository> jpaRepositoryMap = applicationContext.getBeansOfType(JpaRepository.class);
            jpaRepositoryMap.forEach((k, v) -> {

                Class<?> clazz = null;
                if (((ParameterizedType) ((Class) v.getClass().getGenericInterfaces()[0]).getGenericInterfaces()[0]).getRawType().equals(TreeRelationRepository.class)) {
                    clazz = (Class<?>) ((ParameterizedType) ((Class) v.getClass().getGenericInterfaces()[0]).getGenericInterfaces()[0]).getActualTypeArguments()[1];
                } else {
                    clazz = (Class<?>) ((ParameterizedType) ((Class) v.getClass().getGenericInterfaces()[0]).getGenericInterfaces()[0]).getActualTypeArguments()[0];
                }
                classes.add(clazz);
                if (classSignFields.containsKey(clazz)) {
                    if (Objects.nonNull(clazz.getDeclaredAnnotation(SignOrder.class))) {
                        List all = v.findAll();
                        List<String> signFields = classSignFields.get(clazz);
                        List<Field> fields = ReflectionUtils.getDeclaredFields(clazz);
                        fields = fields.stream().filter(f -> signFields.indexOf(f.getName()) >= 0).collect(Collectors.toList());
                        fields.stream().sorted(Comparator.comparing(f -> f.getDeclaredAnnotation(SignOrder.class).value()));
                        repairSign(all, v, fields, signFields);
                    }
                } else {
                    log.info(String.format("不需要校验的实体%s", clazz.getName()));
                }
            });
            SignVerifyInterceptor.skip = false;
        }
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void repairSign(List all, JpaRepository repository, List<Field> fields, List<String> signFields) {
        all.stream().forEach(a -> {
            JSONObject jsonObject = new JSONObject();
            signFields.forEach(f -> {
                Optional<Field> optional = fields.stream().filter(temp -> temp.getName().equals(f)).findFirst();
                Field field = optional.get();
                field.setAccessible(true);
                try {
                    jsonObject.put(field.getName(), field.get(a));
                } catch (IllegalArgumentException e) {
                    throw new RuntimeException(e);
                } catch (IllegalAccessException e) {
                    throw new RuntimeException(e);
                }
            });
            try {
                if (a instanceof TEntity) {
                    String sign = MD5Util.MD5EncodeUtf8Key(objectMapper.writeValueAsString(jsonObject));
                    if (Objects.isNull(((TEntity) a).getSign()) || !((TEntity) a).getSign().equals(sign)) {
                        ((TEntity) a).setSign(sign);
                        ((TEntity) a).setSignCount(fields.size());
                    }
                } else {
                    ((TEntitySchemeStringId) a).setSign(MD5Util.MD5EncodeUtf8Key(objectMapper.writeValueAsString(jsonObject)));
                    ((TEntitySchemeStringId) a).setSignCount(fields.size());
                }


            } catch (JsonProcessingException e) {
                throw new RuntimeException(e);
            }
        });

        repository.saveAll(all);
    }
}
