package com.genlight.epilcure.common.core.mvc.annotation;

import org.springframework.core.annotation.AliasFor;

import java.lang.annotation.*;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @version 1.0.0
 */
@Inherited
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface DistributedLock {

    @AliasFor("name")
    String value();

    @AliasFor("value")
    String name();

    long waitTime() default 2;

    long leaseTime() default 2;

    TimeUnit unit() default TimeUnit.SECONDS;
}
