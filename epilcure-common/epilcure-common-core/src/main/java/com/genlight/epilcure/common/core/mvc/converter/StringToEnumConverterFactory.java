package com.genlight.epilcure.common.core.mvc.converter;

import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.core.convert.converter.Converter;
import org.springframework.core.convert.converter.ConverterFactory;
import org.springframework.lang.Nullable;
import org.springframework.util.Assert;

/**
 * <AUTHOR>
 */
public class StringToEnumConverterFactory implements ConverterFactory<String, Enum<?>> {

    @NotNull
    @Override
    public <T extends Enum<?>> Converter<String, T> getConverter(@NotNull Class<T> targetType) {
        //noinspection rawtypes,unchecked
        return new StringToEnum(getEnumType(targetType));
    }


    public static Class<?> getEnumType(@NotNull Class<?> targetType) {
        Class<?> enumType = targetType;
        while (enumType != null && !enumType.isEnum()) {
            enumType = enumType.getSuperclass();
        }
        Assert.notNull(enumType, () -> "The target type " + targetType.getName() + " does not refer to an enum");
        return enumType;
    }


    private record StringToEnum<T extends Enum<T>>(Class<T> enumType) implements Converter<String, T> {

        @Override
        @Nullable
        public T convert(String source) {
            if (source.isEmpty()) {
                // It's an empty enum identifier: reset the enum value to null.
                return null;
            }
            if (StringUtils.isNumeric(source)) {
                int index = Integer.parseInt(source);
                if (index < this.enumType.getEnumConstants().length) {
                    return this.enumType.getEnumConstants()[index];
                }
            }
            return Enum.valueOf(this.enumType, source.trim());
        }
    }
}
