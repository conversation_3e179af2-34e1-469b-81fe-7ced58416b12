package com.genlight.epilcure.common.core.service;

import com.genlight.epilcure.common.core.dao.entity.TTree;
import com.genlight.epilcure.common.core.dao.entity.TTreeRelation;
import com.genlight.epilcure.common.core.dao.repository.TreeRelationRepository;
import com.genlight.epilcure.common.core.pojo.vo.TreeVO;
import com.genlight.epilcure.common.core.service.exception.ServiceException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import java.util.*;

/**
 * <AUTHOR>
 * @version 1.0.0
 */
public abstract class BaseTreeSpecificationService<T extends TTree<T, TR>, TV extends TreeVO<TV>, TR extends TTreeRelation<T>, ID, R extends JpaRepository<T, ID> & JpaSpecificationExecutor<T>, TRR extends TreeRelationRepository<T, TR, ID>> extends BaseService<T, ID, R> {

    @Autowired
    protected TRR relationRepository;

    protected T addParentsRelationship(ID parentId, T node) {
        // 如果不是根节点，维护节点与所有上级的关系
        if (Objects.nonNull(parentId)) {
            Optional<T> optional = repository.findById(parentId);
            if (optional.isPresent()) {
                node.setParent(optional.get());
                Collection<TR> relations = node.getParent().getParentRelation(); // 父节点的所有上级关系，包含父节点自己
                for (TR relation : relations) {
                    node.getParentRelation().add(buildRelation(relation.getParent(), node, relation.getDepth() + 1));
                }
            }
        }
        // 新增自关联的关系
        node.getParentRelation().add(buildRelation(node, node, 0));
        return node;
    }

    protected void clearParentsRelationship(T node) {
        // 更新为根节点，清除节点与所有上级关系
        node.setParent(null);
        node.getParentRelation().clear();
        node.getParentRelation().add(buildRelation(node, node, 0));
        repository.saveAndFlush(node);
        // 更新所有下级节点的关系
        updateChildrenRelationship(node);
    }

    protected void updateParentsRelationship(ID parentId, T node, boolean flush) {
        // 更新节点与所有上级关系
        Optional<T> optional = repository.findById(parentId);
        if (optional.isPresent()) {
            // 父节点不存在于自己的下级节点中
            if (flush && relationRepository.existsByParentAndChild(node, optional.get())) {
                throw new ServiceException("节点循环依赖！");
            }
            node.setParent(optional.get());
            node.getParentRelation().clear();
            Collection<TR> relations = node.getParent().getParentRelation();
            for (TR relation : relations) {
                node.getParentRelation().add(buildRelation(relation.getParent(), node, relation.getDepth() + 1));
            }
            // 更新自关联的关系
            node.getParentRelation().add(buildRelation(node, node, 0));
            if (flush) {
                repository.saveAndFlush(node);
                updateChildrenRelationship(node);
            }
        }
    }

    protected void updateChildrenRelationship(T node) {
        // 更新节点所有下级节点的关系
        for (TR relation : node.getChildRelation()) {
            if (!relation.getChild().equals(node)) { // 过滤自己
                //noinspection unchecked
                updateParentsRelationship((ID) relation.getChild().getParent().getId(), relation.getChild(), false);
            }
        }
    }

    public List<TV> generateTreeRelationship(Collection<T> nodes) {
        List<TV> root = new ArrayList<>();
        Map<T, TV> nodeMap = new LinkedHashMap<>();
        for (T n : nodes) {
            Optional<T> parent = Optional.ofNullable(n.getParent());
            if (parent.isPresent() && nodes.contains(parent.get())) {
                if (nodeMap.containsKey(parent.get())) {
                    if (!nodeMap.containsKey(n)) {
                        nodeMap.put(n, nodeToVO(n));
                    }
                    nodeMap.get(parent.get()).getChildren().add(nodeMap.get(n));
                } else {
                    TV parentNode = nodeToVO(parent.get());
                    if (!nodeMap.containsKey(n)) {
                        nodeMap.put(n, nodeToVO(n));
                    }
                    parentNode.getChildren().add(nodeMap.get(n));
                    nodeMap.put(parent.get(), parentNode);
                }
            } else {
                TR relation = null;
                if (parent.isPresent()) {
                    for (TR r : n.getParentRelation()) {
                        if (nodes.contains(r.getParent()) && r.getDepth() != 0 && (Objects.isNull(relation) || relation.getDepth() > r.getDepth())) {
                            relation = r; // 挂最近的一个存在的上级节点中
                        }
                    }
                }

                if (Objects.isNull(relation)) {
                    if (!nodeMap.containsKey(n)) {
                        nodeMap.put(n, nodeToVO(n));
                    }
                    root.add(nodeMap.get(n));
                } else {
                    if (nodeMap.containsKey(relation.getParent())) {
                        if (!nodeMap.containsKey(n)) {
                            nodeMap.put(n, nodeToVO(n));
                        }
                        nodeMap.get(relation.getParent()).getChildren().add(nodeMap.get(n));

                    } else {
                        TV parentNode = nodeToVO(relation.getParent());
                        if (!nodeMap.containsKey(n)) {
                            nodeMap.put(n, nodeToVO(n));
                        }
                        parentNode.getChildren().add(nodeMap.get(n));
                        nodeMap.put(relation.getParent(), parentNode);
                    }
                }
            }
        }
        return root;
    }

    public abstract TR buildRelation(T parent, T child, Integer depth);

    public abstract TV nodeToVO(T node);
}
