package com.genlight.epilcure.common.core.mvc.aspectj;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.genlight.epilcure.common.core.util.IpUtils;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Configurable;
import org.springframework.util.unit.DataSize;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;

import java.lang.reflect.Parameter;
import java.time.Duration;
import java.time.Instant;
import java.util.Enumeration;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0.0
 */
@Slf4j
@Aspect
@Configurable
public class AccessAspect {

    @Autowired
    private ObjectMapper objectMapper;

    @Around("execution(* com.genlight.epilcure.service.*.controller..*.*(..))")
    public Object doAround(ProceedingJoinPoint joinPoint) throws Throwable {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest servletRequest = Objects.requireNonNull(attributes).getRequest();
        MethodSignature methodSignature = (MethodSignature) joinPoint.getSignature();
        if (log.isDebugEnabled()) {
            log.debug("---------------------------------------- << {} start >> ----------------------------------------", methodSignature.getName());
            log.debug("Request URL     : {}", servletRequest.getRequestURL().toString());
            log.debug("Request Method  : {}", servletRequest.getMethod());
            log.debug("Request Class   : {}.{}", methodSignature.getDeclaringTypeName(), methodSignature.getName());
            log.debug("Request IP      : {}", IpUtils.getIpAddr(servletRequest));
            printRequestHeaders(servletRequest);
            printRequestParams(methodSignature.getMethod().getParameters(), joinPoint.getArgs());
        }
        Instant start = Instant.now();
        Object result = joinPoint.proceed();
        if (log.isDebugEnabled()) {
            log.debug("Response Result : {}", objectMapper.writeValueAsString(result));
            log.debug("Request Use Time: {} ms", Duration.between(start, Instant.now()).toMillis());
            log.debug("---------------------------------------- << {} end >>-------------------------------------------", methodSignature.getName());
        }
        return result;
    }

    public void printRequestParams(Parameter[] parameters, Object[] args) throws JsonProcessingException {
        for (int i = 0; i < parameters.length; i++) {
            Object arg = args[i];
            if (Objects.nonNull(arg)) {
                Class<?> clazz = parameters[i].getType();
                if (clazz.isArray() && MultipartFile.class.isAssignableFrom(clazz.getComponentType())) {
                    for (MultipartFile file : (MultipartFile[]) arg) {
                        log.debug("Request file    : {} = {}, Size = {}KB", parameters[i].getName(), file.getOriginalFilename(), DataSize.ofBytes(file.getSize()).toKilobytes());
                    }
                } else if (MultipartFile.class.isAssignableFrom(clazz)) {
                    MultipartFile file = (MultipartFile) arg;
                    log.debug("Request file    : {} = {}, Size = {}KB", parameters[i].getName(), file.getOriginalFilename(), DataSize.ofBytes(file.getSize()).toKilobytes());
                } else {
                    log.debug("Request param   : {} = {}", parameters[i].getName(), objectMapper.writeValueAsString(arg));
                }
            }
        }
    }

    public void printRequestHeaders(HttpServletRequest request) {
        Enumeration<String> headerNames = request.getHeaderNames();
        while (headerNames.hasMoreElements()) {
            String key = headerNames.nextElement();
            log.debug("Request Header  : {} = {}", key, request.getHeader(key));
        }
    }
}
