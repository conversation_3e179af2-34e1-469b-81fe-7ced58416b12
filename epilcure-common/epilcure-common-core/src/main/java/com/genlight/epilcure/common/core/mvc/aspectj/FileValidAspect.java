package com.genlight.epilcure.common.core.mvc.aspectj;

import com.genlight.epilcure.common.core.mvc.annotation.FileValid;
import com.genlight.epilcure.common.core.service.exception.ArgsException;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.DeclarePrecedence;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.util.unit.DataSize;
import org.springframework.web.multipart.MultipartFile;

import java.lang.reflect.Parameter;
import java.util.Objects;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @version 1.0.0
 */
@Aspect
@DeclarePrecedence("FileValidAspect,AccessAspect")
public class FileValidAspect {

    @Before("execution(* com.genlight.epilcure.service.*.controller..*.*(..,@com.genlight.epilcure.common.core.mvc.annotation.FileValid (*),..))")
    public void beforeFileValidMethod(JoinPoint point) {
        Parameter[] parameters = ((MethodSignature) point.getSignature()).getMethod().getParameters();
        for (int i = 0; i < parameters.length; i++) {
            Object arg = point.getArgs()[i];
            if (Objects.nonNull(arg)) {
                Parameter parameter = parameters[i];
                FileValid fileValid = parameter.getAnnotation(FileValid.class);
                if (Objects.nonNull(fileValid)) {
                    Class<?> clazz = parameter.getType();
                    if (clazz.isArray() && MultipartFile.class.isAssignableFrom(clazz.getComponentType())) {
                        for (MultipartFile file : (MultipartFile[]) arg) {
                            this.processFileValid(fileValid, file);
                        }
                    } else if (MultipartFile.class.isAssignableFrom(clazz)) {
                        this.processFileValid(fileValid, (MultipartFile) arg);
                    }
                }
            }
        }
    }

    private void processFileValid(FileValid fileValid, MultipartFile file) {
        if (file.getSize() > DataSize.of(fileValid.size(), fileValid.unit()).toBytes()) {
            throw new ArgsException("The file " +
                    file.getOriginalFilename() +
                    " size exceeds the specified size[" +
                    fileValid.size() +
                    fileValid.unit().toString() +
                    "]");
        }
        if (!fileValid.format().validate(file.getOriginalFilename())) {
            throw new ArgsException("The file " +
                    file.getOriginalFilename() +
                    " format not allowed, the supported formats are " +
                    fileValid.format());
        }
        if (!Pattern.matches(fileValid.filename(), file.getOriginalFilename())) {
            throw new ArgsException("The file " +
                    file.getOriginalFilename() +
                    " filename not allowed, the supported filename pattern are " +
                    fileValid.filename());
        }
    }
}
