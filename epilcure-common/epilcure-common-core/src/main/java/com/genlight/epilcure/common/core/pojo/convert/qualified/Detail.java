package com.genlight.epilcure.common.core.pojo.convert.qualified;

import org.mapstruct.Qualifier;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * Basic->Summary->Detail->All
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Qualifier
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.SOURCE)
public @interface Detail {
}
