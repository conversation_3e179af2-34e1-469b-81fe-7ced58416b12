pipeline {
    agent {
        label 'maven'
    }
    parameters {
        choice(
            name: 'SERVICES',
            choices: [
                'epilcure-service-auth',
                'epilcure-service-user',
                'epilcure-service-scheme',
                'epilcure-service-scale',
                'epilcure-service-patient',
                'epilcure-service-neeg',
                'epilcure-service-ischeme',
                'epilcure-service-device',
                'epilcure-service-config',
                'epilcure-service-algorithm',
                'epilcure-service-feedback',
                'epilcure-gateway'
            ],
            description: '选择要构建和部署的服务'
        )
    }
    environment {
        DOCKER_REGISTRY = 'your-harbor.com/epilcure' // 替换为你的镜像仓库
        DOCKER_CREDENTIALS = credentials('docker-credentials')
        KUBECONFIG = credentials('kubeconfig')
        BUILD_NUMBER = "${env.BUILD_NUMBER}"
        VERSION = '1.0.0' // 从 pom.xml 读取版本
    }
    stages {
        stage('Clone repository') {
            steps {
                git(url: 'http://gitlab.cloud.com/tangzhen/epilcure.git', credentialsId: 'gitlab', branch: 'test', changelog: true, poll: false)
            }
        }
        stage('Build and Push Image with Jib') {
            steps {
                container('maven') {
                    withCredentials([usernamePassword(credentialsId: 'docker-credentials', usernameVariable: 'DOCKER_USERNAME', passwordVariable: 'DOCKER_PASSWORD')]) {
                        sh """
                        mvn -pl ${SERVICES} clean package jib:build \
                            -DskipTests=true \
                            -DsendCredentialsOverHttp=true \
                            -P test \
                            -Djib.to.auth.username=${DOCKER_USERNAME} \
                            -Djib.to.auth.password=${DOCKER_PASSWORD} \
                            -Djib.to.image=${DOCKER_REGISTRY}/${SERVICES}:${VERSION}
                        """
                    }
                }
            }
        }
        stage('Deploy to Kubernetes') {
            steps {
                container('kubectl') {
                    withCredentials([string(credentialsId: 'kubeconfig', variable: 'KUBECONFIG_CONTENT')]) {
                        sh """
                        mkdir -p ~/.kube
                        echo "\${KUBECONFIG_CONTENT}" > ~/.kube/config
                        sed -i 's|\\${docker.registry}|${DOCKER_REGISTRY}|g' k8s/${SERVICES}-deployment.yaml
                        sed -i 's|\\${version}|${VERSION}|g' k8s/${SERVICES}-deployment.yaml
                        sed -i 's|\\${build.number}|${BUILD_NUMBER}|g' k8s/${SERVICES}-deployment.yaml
                        kubectl apply -f k8s/${SERVICES}-deployment.yaml
                        kubectl rollout restart deployment ${SERVICES}-v1 -n epilcure
                        """
                    }
                }
            }
        }
    }
    post {
        success {
            echo "Service ${SERVICES} deployed successfully!"
        }
        failure {
            echo "Deployment failed for service ${SERVICES}."
        }
    }
}